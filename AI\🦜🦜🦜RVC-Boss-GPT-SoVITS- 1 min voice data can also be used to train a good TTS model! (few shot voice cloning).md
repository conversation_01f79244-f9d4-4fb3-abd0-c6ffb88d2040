---
DocFlag:
  - Tested
Updated: 2024-03-18T10:38
tags:
  - AI->-Japanese
  - AI->-Voice
Created: 2024-01-21T10:07
---
[![](https://opengraph.githubassets.com/13aef61e6a7be50e058d2949ac137614b9dc951314766b761456e61f0d86f631/RVC-Boss/GPT-SoVITS)](https://opengraph.githubassets.com/13aef61e6a7be50e058d2949ac137614b9dc951314766b761456e61f0d86f631/RVC-Boss/GPT-SoVITS)
## ==GPT-SoVITS-WebUI==
==A Powerful Few-shot Voice Conversion and Text-to-Speech WebUI.==
[==**English**==](https://github.com/RVC-Boss/GPT-SoVITS/blob/main/README.md) ==|== [==**中文简体**==](https://github.com/RVC-Boss/GPT-SoVITS/blob/main/docs/cn/README.md) ==|== [==**日本語**==](https://github.com/RVC-Boss/GPT-SoVITS/blob/main/docs/ja/README.md)
==Check out our== [==demo video==](https://www.bilibili.com/video/BV12g4y1m7Uw) ==here!==
==few.shot.fine.tuning.demo.mp4==
## ==Features:==
1. ==**Zero-shot TTS:**== ==Input a 5-second vocal sample and experience instant text-to-speech conversion.==
2. ==**Few-shot TTS:**== ==Fine-tune the model with just 1 minute of training data for improved voice similarity and realism.==
3. ==**Cross-lingual Support:**== ==Inference in languages different from the training dataset, currently supporting English, Japanese, and Chinese.==
4. ==**WebUI Tools:**== ==Integrated tools include voice accompaniment separation, automatic training set segmentation, Chinese ASR, and text labeling, assisting beginners in creating training datasets and GPT/SoVITS models.==
## ==Environment Preparation==
==If you are a Windows user (tested with win>=10) you can install directly via the prezip. Just download the== [==prezip==](https://huggingface.co/lj1995/GPT-SoVITS-windows-package/resolve/main/GPT-SoVITS-beta.7z?download=true)==, unzip it and double-click go-webui.bat to start GPT-SoVITS-WebUI.==
### ==Tested Environments==
- ==Python 3.9, PyTorch 2.0.1, CUDA 11==
- ==Python 3.10.13, PyTorch 2.1.2, CUDA 12.3==
==_Note: numba==0.56.4 require py<3.11_==
### ==Quick Install with Conda==
==conda create -n GPTSoVits python=3.9  
conda activate GPTSoVits  
bash install.sh  
==
### ==Install Manually==
### ==Make sure you have the distutils for python3.9 installed==
==sudo apt-get install python3.9-distutils==
### ==Pip Packages==
==pip install torch numpy scipy tensorboard librosa==0.9.2 numba==0.56.4 pytorch-lightning gradio==3.14.0 ffmpeg-python onnxruntime tqdm cn2an pypinyin pyopenjtalk g2p_en chardet==
### ==Additional Requirements==
==If you need Chinese ASR (supported by FunASR), install:==
==pip install modelscope torchaudio sentencepiece funasr==
### ==FFmpeg==
### ==Conda Users==
### ==Ubuntu/Debian Users==
==sudo apt install ffmpeg  
sudo apt install libsox-dev  
conda install -c conda-forge 'ffmpeg<7'  
==
### ==MacOS Users==
### ==Windows Users==
==Download and place== [==ffmpeg.exe==](https://huggingface.co/lj1995/VoiceConversionWebUI/blob/main/ffmpeg.exe) ==and== [==ffprobe.exe==](https://huggingface.co/lj1995/VoiceConversionWebUI/blob/main/ffprobe.exe) ==in the GPT-SoVITS root.==
### ==Pretrained Models==
==Download pretrained models from== [==GPT-SoVITS Models==](https://huggingface.co/lj1995/GPT-SoVITS) ==and place them in== ==`GPT_SoVITS/pretrained_models`====.==
==For Chinese ASR (additionally), download models from== [==Damo ASR Model==](https://modelscope.cn/models/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/files)==,== [==Damo VAD Model==](https://modelscope.cn/models/damo/speech_fsmn_vad_zh-cn-16k-common-pytorch/files)==, and== [==Damo Punc Model==](https://modelscope.cn/models/damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/files) ==and place them in== ==`tools/damo_asr/models`====.==
==For UVR5 (Vocals/Accompaniment Separation & Reverberation Removal, additionally), download models from== [==UVR5 Weights==](https://huggingface.co/lj1995/VoiceConversionWebUI/tree/main/uvr5_weights) ==and place them in== ==`tools/uvr5/uvr5_weights`====.==
## ==Dataset Format==
==The TTS annotation .list file format:==
```plain
vocal_path|speaker_name|language|text
```
==Language dictionary:==
- =='zh': Chinese==
- =='ja': Japanese==
- =='en': English==
==Example:==
```plain
D:\GPT-SoVITS\xxx/xxx.wav|xxx|en|I like playing Genshin.
```
## ==Todo List==
- ==**High Priority:**==
    - ==Localization in Japanese and English.==
    - ==User guide.==
    - ==Japanese and English dataset fine tune training.==
- ==**Features:**==
    - ==Zero-shot voice conversion (5s) / few-shot voice conversion (1min).==
    - ==TTS speaking speed control.==
    - ==Enhanced TTS emotion control.==
    - ==Experiment with changing SoVITS token inputs to probability distribution of vocabs.==
    - ==Improve English and Japanese text frontend.==
    - ==Develop tiny and larger-sized TTS models.==
    - ==Colab scripts.==
    - ==Expand training dataset (2k -> 10k).==
    - ==better sovits base model (enhanced audio quality)==
    - ==model mix==
## ==Credits==
==Special thanks to the following projects and contributors:==
- [==ar-vits==](https://github.com/innnky/ar-vits)
- [==SoundStorm==](https://github.com/yangdongchao/SoundStorm/tree/master/soundstorm/s1/AR)
- [==vits==](https://github.com/jaywalnut310/vits)
- [==TransferTTS==](https://github.com/hcy71o/TransferTTS/blob/master/models.py#L556)
- [==Chinese Speech Pretrain==](https://github.com/TencentGameMate/chinese_speech_pretrain)
- [==contentvec==](https://github.com/auspicious3000/contentvec/)
- [==hifi-gan==](https://github.com/jik876/hifi-gan)
- [==Chinese-Roberta-WWM-Ext-Large==](https://huggingface.co/hfl/chinese-roberta-wwm-ext-large)
- [==fish-speech==](https://github.com/fishaudio/fish-speech/blob/main/tools/llama/generate.py#L41)
- [==ultimatevocalremovergui==](https://github.com/Anjok07/ultimatevocalremovergui)
- [==audio-slicer==](https://github.com/openvpi/audio-slicer)
- [==SubFix==](https://github.com/cronrpc/SubFix)
- [==FFmpeg==](https://github.com/FFmpeg/FFmpeg)
- [==gradio==](https://github.com/gradio-app/gradio)
## ==Thanks to all contributors for their efforts==
  
  
  
```LaTeX
git clone https://github.com/RVC-Boss/GPT-SoVITS.git
cd GPT-SoVITS/
conda create -n GPTSoVits python=3.9
conda activate GPTSoVits
conda env list
bash install.sh
\#pretrain model preparation
Download pretrained models from GPT-SoVITS Models and place them in GPT_SoVITS/pretrained_models.
For Chinese ASR (additionally), download models from Damo ASR Model, Damo VAD Model, and Damo Punc Model and place them in tools/damo_asr/models.
For UVR5 (Vocals/Accompaniment Separation & Reverberation Removal, additionally), download models from UVR5 Weights and place them in tools/uvr5/uvr5_weights.
cd /opt/workspace/GPT-SoVITS/GPT_SoVITS/pretrained_models
git clone https://huggingface.co/lj1995/GPT-SoVITS
mv * ..
cd /opt/workspace/GPT-SoVITS/tools/damo_asr/models
git clone https://www.modelscope.cn/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git
git clone https://www.modelscope.cn/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch.git
git clone https://www.modelscope.cn/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch.git
git clone https://huggingface.co/lj1995/VoiceConversionWebUI/tree/main/uvr5_weights

python webui.py
http://192.168.0.76:9874/
1-GPT-SoVITS-TTS -> 1C推理 -> 是否开启TTS推理WebUI
ImportError: /usr/lib64/libstdc++.so.6: version `GLIBCXX_3.4.32' not found (required by /home/<USER>/miniconda3/envs/GPTSoVits/lib/python3.9/site-packages/pyopenjtalk/openjtalk.cpython-39-x86_64-linux-gnu.so)
Solve:
指向的是/home/<USER>/miniconda3/envs/GPTSoVits/lib/路径而不是/usr/lib64/
(GPTSoVits) [raysheng@MONSTER:~]$ ldd /home/<USER>/miniconda3/envs/GPTSoVits/lib/python3.9/site-packages/pyope.cpython-39-x86_64-linux-gnu.so
        linux-vdso.so.1 (0x00007ffd74d78000)
        libstdc++.so.6 => /home/<USER>/miniconda3/envs/GPTSoVits/lib/libstdc++.so.6 (0x00007fcf75986000)
        libm.so.6 => /lib64/libm.so.6 (0x00007fcf7589b000)
        libgcc_s.so.1 => /home/<USER>/miniconda3/envs/GPTSoVits/lib/libgcc_s.so.1 (0x00007fcf75880000)
        libpthread.so.0 => /lib64/libpthread.so.0 (0x00007fcf7587b000)
        libc.so.6 => /lib64/libc.so.6 (0x00007fcf75672000)
        librt.so.1 => /lib64/librt.so.1 (0x00007fcf7566b000)
        /lib64/ld-linux-x86-64.so.2 (0x00007fcf75d6c000)
export LD_LIBRARY_PATH=~/miniconda3/envs/GPTSoVits/lib
python webui.py
Fixed！
进入推理页面
http://192.168.0.76:9872/
1. 录一段
2. 去https://www.veed.io/edit/e8b78e5f-4877-4735-9ab6-db52abc718f3/subtitles
转成文字，可惜是繁体的
another way: goto openai
a. convert m4a to wav
b. python -> text
3. 或者去https://www.chineseconverter.com/ja/convert/simplified-to-traditional
转成简体
4. 上传m4a, text.then 合成
```