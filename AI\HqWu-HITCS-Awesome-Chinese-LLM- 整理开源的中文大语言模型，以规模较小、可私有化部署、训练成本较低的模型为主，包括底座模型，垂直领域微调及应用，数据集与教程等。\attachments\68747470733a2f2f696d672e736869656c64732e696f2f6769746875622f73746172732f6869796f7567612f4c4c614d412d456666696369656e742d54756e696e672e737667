<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="72" height="20"><linearGradient id="s" x2="0" y2="100%"><stop offset="0" stop-color="#bbb" stop-opacity=".1"/><stop offset="1" stop-opacity=".1"/></linearGradient><clipPath id="r"><rect width="72" height="20" rx="3" fill="#fff"/></clipPath><g clip-path="url(#r)"><rect width="37" height="20" fill="#555"/><rect x="37" width="35" height="20" fill="#007ec6"/><rect width="72" height="20" fill="url(#s)"/></g><g fill="#fff" text-anchor="middle" font-family="Verdana,Geneva,DejaVu Sans,sans-serif" text-rendering="geometricPrecision" font-size="110"><a target="_blank" xlink:href="https://github.com/hiyouga/LLaMA-Efficient-Tuning"><rect width="37" x="0" height="20" fill="rgba(0,0,0,0)"/><text aria-hidden="true" x="195" y="150" fill="#010101" fill-opacity=".3" transform="scale(.1)" textLength="270">stars</text><text x="195" y="140" transform="scale(.1)" fill="#fff" textLength="270">stars</text></a><a target="_blank" xlink:href="https://github.com/hiyouga/LLaMA-Efficient-Tuning/stargazers"><rect width="35" x="37" height="20" fill="rgba(0,0,0,0)"/><text aria-hidden="true" x="535" y="150" fill="#010101" fill-opacity=".3" transform="scale(.1)" textLength="250">1.7k</text><text x="535" y="140" transform="scale(.1)" fill="#fff" textLength="250">1.7k</text></a></g></svg>