---
Updated: 2024-01-28T11:37
tags:
  - AI->-Theory
URL: https://baoyu.io/translations/llm/visual-guides-to-understand-the-basics-of-large-language-models
Created: 2024-01-28T05:58
---
[See all posts](https://baoyu.io/translations)
**一系列工具与文章的汇编，直观易懂地解读复杂的 AI 概念**
图片由作者利用 [unDraw.co](https://undraw.co/) 的免费插图制作
在当今世界，大语言模型（LLM）成为了热门话题。几乎每天都有新的语言模型问世，让人们在 AI 领域怀有一种“不容错过”的紧迫感。尽管如此，许多人仍对大语言模型的基础概念一知半解，难以跟上技术的快速发展。本文致力于为那些想深入了解这些 AI 模型内部原理的读者提供帮助，以便他们能够牢固掌握这些知识。在此，我为您介绍几种工具和文章，以期简化并阐明大语言模型的概念，使之容易被理解。
## 1. 杰伊·阿拉马尔的《图解 Transformer》
这张由作者创作的 GIF 是基于杰伊·阿拉马尔的[**《图解 Transformer》**](https://jalammar.github.io/illustrated-transformer/)。
我敢肯定，你们中的许多人已经对这篇具有标志性意义的文章不陌生。杰伊是最早在技术文章中融入生动视觉效果的先驱之一。只需简单浏览他的博客，你就能领会我的意思。多年来，他引领了众多作者追随其步伐，教程的形式也从单纯的文本和代码转变为富有沉浸感的视觉展示。言归正传，让我们回到《图解 Transformer》。Transformer 架构是构成所有基于 Transformer 的大语言模型（LLMs）的核心。因此，理解其基础知识至关重要，而杰伊的博客正是对此进行了精彩阐述。博客内容涵盖了以下重要概念：
1. Transformer 模型的高层视角
2. 探索 Transformer 的编码和解码组件
3. 自我关注机制
4. 自我关注的矩阵运算
5. 多头关注的原理
6. 位置编码
7. Transformer 架构中的残差连接
8. 解码器的最终线性层和 Softmax 层
9. 模型训练中的损失函数
杰伊还制作了一个名为“[叙事式 Transformer](https://youtu.be/-QH8fRhqFHM)”的视频，为初学者提供了一种更易懂的学习方式。在阅读完这篇博文后，再结合阅读“[注意力就是一切所需](https://arxiv.org/abs/1706.03762)”论文和官方的 [Transformer 博客文章](https://ai.googleblog.com/2017/08/transformer-novel-neural-network.html)，将对主题有更全面的理解。
**链接：** [**https://jalammar.github.io/illustrated-transformer/**](https://baoyu.io/translations/llm/illustrated-transformer)
## 2. 杰伊·阿拉马尔的《图解 GPT-2》
这张由作者创作的 GIF 基于杰伊·阿拉马尔的 [**《图解 GPT-2》**](https://jalammar.github.io/illustrated-gpt2/)。
杰伊·阿拉马尔的又一力作——《图解 GPT-2》。作为[**《图解 Transformer》**](http://the%20illustrated%20transformer,/)的补充，这篇文章通过更多的视觉元素深入解析了 Transformer 的内部机制及其自初始论文发布以来的演进。文章中还特别介绍了 Transformer 在语言模型之外的应用场景。
**链接：** [**https://jalammar.github.io/illustrated-gpt2/**](https://jalammar.github.io/illustrated-gpt2/)
## 3. Brendan Bycroft 的大语言模型可视化
![[1_SYgnZBgB566SpHoTgRrIEw.gif]]
这张 GIF 是由作者基于 Brendan Bycroft 的大语言模型可视化创作的。
大语言模型可视化项目为我们提供了一种逐步了解 OpenAI ChatGPT 背后大语言模型算法的方式。它是深入探索算法的每一个步骤、实时观察整个过程的绝佳资源。
该项目的一大特色是一个包含 3D 效果的网页，展示了类似于 ChatGPT 所采用的小型大语言模型。这个工具通过交互式元素，为用户提供了对单个 token 推理过程的逐步指南。目前，项目已包含以下架构的可视化：
- GPT-2（小型）
- Nano GPT
- GPT-2（超大型）
- GPT-3
**链接：** [**https://bbycroft.net/llm**](https://bbycroft.net/llm)
## 4. 《金融时报》：生成式 AI 的诞生，离不开 Transformer 技术
![[1_i_5gRrHgExSf6Vi1VkmSUA.gif]]
此 GIF 由作者根据[《金融时报》关于生成式 AI 和 Transformer 技术的报道](https://ig.ft.com/generative-ai/)(FT) 制作 | 本作品遵循 FT 的共享政策。
感谢《金融时报》的[视觉故事团队](https://www.ft.com/visual-and-data-journalism)和[**Madhumita Murgia**](https://www.ft.com/madhumita-murgia)使用生动的视觉材料深入浅出地解释了大型语言模型（LLM）的核心原理，尤其突出了**自我关注机制**和**Transformer 架构**的重要性。
🔗 [**https://ig.ft.com/generative-ai/**](https://baoyu.io/pages/ft/generative-ai)
## 5.OpenAI 的分词工具体验
![[1_B38qgNuh9GJRdbuzFEgJqQ.webp]]
截图作者 | 来源: [OpenAI 的分词工具文档](https://platform.openai.com/tokenizer)
大型语言模型 (Large language models) 通过一种称为**令牌 (tokens)** 的数字序列来处理文本。分词器把文本转换为这些令牌。OpenAI 提供的[分词工具](https://platform.openai.com/tokenizer)可以让我们实际操作，看看特定文本是如何被转换为令牌的，还能了解到文本中令牌的总数。
链接: [https://platform.openai.com/tokenizer](https://platform.openai.com/tokenizer)
## 6. Simon Willison 分析 GPT 分词器的独到见解
![[1_MgBufFQmS8D1ErrL2bu4Ag.gif]]
GIF 由作者根据[Simon Willison 对 GPT 分词器的分析](https://lnkd.in/eXTcia8Z)制作
尽管我们已经提到 OpenAI 提供了一个用于探索令牌工作原理的分词工具，但 Simon Willison 创造了他自己的分词工具，它更有趣，更具启发性。这个工具存在于[Observable 笔记本](https://observablehq.com/@simonw/gpt-tokenizer)中，能够将文本转换为令牌，再将令牌转换回文本，并对完整的令牌表进行搜索。
Simon 的分析提供了一些关键洞见：• 大多数英文常用词只对应一个令牌。• 有些词的令牌前会带有空格，这有助于更高效地编码完整句子。• 非英语文本的分词可能不太高效。• **异常令牌**可能导致一些出乎意料的行为。
🔗 [**https://lnkd.in/eXTcia8Z**](https://lnkd.in/eXTcia8Z)
## 7. Greg Kamradt 的 Chunkviz：文本分块可视化工具
![[1_uHZCYjwCQD3qRIplvPYzGw.gif]]
GIF 由作者基于[Chunkviz 应用](https://chunkviz.up.railway.app/)制作，遵循 MIT 许可证协议。
在构建大型语言模型 (LLM) 应用时，一种常用的方法是将大段文本分解成小块，这被称为“分块”。这一过程对于确保你的文档能够适应模型的处理能力范围至关重要。所谓“上下文窗口”指的是模型一次能够处理的最大文本长度。不过，分块有多种不同的策略，而这正是 Chunkviz 工具的亮点所在。它允许用户从四种不同的[LangChainAI](https://python.langchain.com/docs/modules/data_connection/document_transformers/)分割器中选择分块策略，并展示这些策略如何影响文本的处理。目前，你可以通过这个工具直观地了解不同的文本分割和分块策略。
🔗 [**https://chunkviz.up.railway.app/**](https://chunkviz.up.railway.app/)
## 8. 机器学习模型：记忆还是泛化？ - PAIR 团队的互动探索
![[1_vVbEFu8Fx4SgHXPz8NaTRQ.gif]]
这个 GIF 是作者基于 [机器学习模型是记忆还是泛化？](https://pair.withgoogle.com/explorables/grokking/) 这一互动探索内容创作的，遵循 MIT 许可共享。
Google PAIR 团队的互动探索内容是一系列交互式文章，它们用互动媒介来简化复杂的 AI 主题。这篇特别的文章深入探讨了泛化与记忆的问题，着重探究一个关键议题：**大型语言模型 (LLMs) 是否真的理解了这个世界，还是仅仅在重复它们大量训练数据中的信息？**
作者通过对一个小型模型的训练过程进行详细研究，带领读者进行一次富有洞察力的探险之旅。他们对找到的解决方案进行逆向工程分析，生动地展示了机制解释性（Mechanistic Interpretability）这一令人兴奋的新兴领域。
**🔗** [**https://pair.withgoogle.com/explorables/grokking/**](https://pair.withgoogle.com/explorables/grokking/)
## 结论
我们审视了一些极有价值的工具和文章，它们努力把复杂的技术术语转换成容易理解的形式。我一直是互动视觉格式呈现技术概念的坚定支持者。这让我想起了我以前的一篇文章，那篇文章聚焦于用直观的方式解释标准机器学习概念的工具。
[互动方式学习机器学习概念](https://towardsdatascience.com/learn-machine-learning-concepts-interactively-6c3f64518da2)
本文强调的工具和文章旨在降低对初学者和爱好者的学习门槛，使学习过程更加引人入胜和易于接触。我打算不断更新这篇文章，加入更多我发现的类似资源。同时，我也欢迎并期待读者的宝贵建议。