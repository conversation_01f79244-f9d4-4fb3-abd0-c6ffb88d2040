---
DocFlag:
  - Reference
  - Tested
  - Testing
Updated: ""
tags:
  - AI
  - AI/Theory
Created: 2023-08-31T15:11
---
梯度
求导公式
泛化误差的分析不等式
softmax
weight decay
小批量模拟大批量梯度下降效果
梯度裁剪
激活函数
神经网络里的dropout/normalization/激活函数的顺序应该如何
Relu and tan<PERSON><PERSON>
sigmoid
GELU
Attention is All you need
Sinusoidal position Encoding
Rotary Position Embedding
Machine Learning Loss Functions
注意力汇聚
Transformer Implementation
对齐（句子间逻辑上关系）
选择Token的嵌入维度大小
距离近似计算
RAG Chuck分片技术
随机变量之和的方差等于成对协方差之和。
随机变量总和的方差就是各个变量方差的总和
卷积
计算参数多少 占用内存
transformer sizing
Calculating GPU memory for serving LLMs
Google Model Explorer
计算复杂度
BatchNormalization
RNN
自回归模型
马尔可夫模型
因果关系
标准RNN公式和Perplexity
門控循環單元（GRU)
三体小说训练
LSTM
LSTM+Informer
LoRA
QA-LoRA
VeRA
贝叶斯定理
拉格朗日公式
泰勒展开公式
对Transformer的优化变形
StreamLLM[GitHub - mit-han-lab/streaming-llm: Efficient Streaming Language Models with Attention Sinks](https://github.com/mit-han-lab/streaming-llm)
Quantization for LLM transformer
AWQ Activation-aware Weight Quantization
Look Ahead Decoding
Mixture-of-Depths: Dynamically allocating compute in transformer-based language models
llama介绍
LLAMA unique feature
Finetuning LLama
Merge Perf lora checkpoint to pretrained base model
SwinTransformer
加载swin model 时候遇到的问题
Diffusion Transformer (DiT)
强化学习（ReInforcement Learning)
使用RL来选择Feature
深度学习程序集
数据早期分析
利用PandaAI来做初始分析
数据的相关性
数据的预处理
截断和填充
PowerTransformer, OrdinalEncoder, PyOD
KMeans Speedup with Faiss
权重初始化
数值分类
不平衡数据集技术
RoC and AUC
K则分类
随机森林
SHAP值
Load模型后预估
Symbolic Regression 符号回归
Logistic 回归
SOM算法分类
自然语言模型
AI模型線上Play
Youtube 上微調教學
微調的一些鏈接和數據集
微調環境搭建和sample
Code copilot微調
分词器 embedding
減少模型
Self-instruct
Embedding Vector DB
GPT Cache
FastChat 用法
Code Llama
ChatGLM 微調
Azure 用法
Yarn-Llama
Open interpreter
使用卷积做文本情感分析
中文停用词表
Mixture of Experts 技术
采用不同的模型（RNN、CNN，Transformer）文本分类
Tensorboard 用法
Prompt 优化
图像模型
pre_trained models以及设定每层的学习率
微调
图像数据集
Yolo的使用和自己标注
声音模型
降噪的处理
数据增强 (Augmentation)
通用模型
GAN model
打包后计划输出
分类问题指标
Kaggle 技巧
提高训练效果方法
WANDB 使用技巧
动态显示Loss或者accuracy without wandb
Bagging (Bootstrap AGGrgratING)
Boosting (GBDT XGBoost）
Stacking
LazyPredict
对模型调参
超参数优化和NAS
KL散度
DPO数学解释
SPD
DeformableAttention
自适应参数学习
对于量化的详细解释
替换类
保存权重并且推送到haggingface
加载量化模型
Packing Weights mode 2bit/4bits
Unpack Weights
Bert Model Post Train and merge
llama.cpp 格式转换
推荐系统
DeepCTR-Torch
issue
推荐给相似用户的程序
自注意句子embedding
python special feature
推荐系统SVD提取特征
向量操作
dataframe regex
向量squeeze and reshape
替换默认print函数
Remove background
Debug code with icecream tool
提高Pandas库在进行数据追加操作时性能的方法
Mojo
AutoML
对算法benchmark
录屏程序
L40S and A4000 MultiGPUs
控制GPU的最大输出功率
书刊以及网站
数据集取得
有待尝试
Simple Transformer
实现
人工智能学习班

```Python
print("CUDA availability:", torch.cuda.is_available())
if torch.cuda.is_available():
    print("CUDA version:", torch.version.cuda)
else:
    print("CUDA not available.")
```

# 梯度

## 求导公式

![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled.png|Untitled.png]]

$\begin{align*}\text{导数基本运算法则} & \\\hline\text{加减} &: F(x) = f(x) \pm g(x) \\\text{导数} &: F'(x) = f'(x) \pm g'(x) \\\hline\text{乘法} &: F(x) = f(x) \cdot g(x) \\\text{导数} &: F'(x) = f'(x) \cdot g(x) + f(x) \cdot g'(x) \\\hline\text{除法} &: F(x) = \frac{f(x)}{g(x)} \\\text{导数} &: F'(x) = \frac{f'(x) \cdot g(x) - g'(x) \cdot f(x)}{g^2(x)} \\\hline\text{复杂函数} & \\\text{例如} &: F(x) = f[g(x)] \\\text{导数} &: F'(x) = f'[g(x)] \cdot g'(x) \\\end{align*}$
![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 1.png|Untitled 1.png]]
![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 2.png|Untitled 2.png]]
![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 3.png|Untitled 3.png]]

```JavaScript
import numpy as np
import scipy.stats as stats
scipy.stats.norm.pdf(x, loc=0, scale=1)
def normal(x, mu, sigma):
    p = 1 / math.sqrt(2 * math.pi * sigma**2)
    return p * np.exp(-0.5 / sigma**2 * (x - mu)**2)
```

## 泛化误差的分析不等式

```LaTeX
https://baike.baidu.com/item/%E6%9F%AF%E8%A5%BF-%E6%96%BD%E7%93%A6%E8%8C%A8%E4%B8%8D%E7%AD%89%E5%BC%8F/53360160
```

$(f(x) - \hat{f}(x))^2 \leq \mathbb{E}[(f(x) - h(x))^2] \Leftrightarrow (\mathbb{E}[h(x)])^2 \leq \mathbb{E}[h(x)^2]$

## softmax

![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 4.png|Untitled 4.png]]

## weight decay

![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 5.png|Untitled 5.png]]
if activate funtion is ReLU. then
![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 6.png|Untitled 6.png]]

![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 7.png|Untitled 7.png]]
![[Notion/AI/🦜🦜🦜DeepLearningResearch Memo List/attachments/Untitled 8.png|Untitled 8.png]]
![[Untitled 9.png]]

```Python
def net(X):
    return softmax(torch.matmul(X.reshape((-1, W.shape[0])), W) + b)
def cross_entropy(y_hat, y):
    return - torch.log(y_hat[range(len(y_hat)), y])
net = nn.Sequential(nn.Flatten(), nn.Linear(784, 10))
def init_weights(m):
    if type(m) == nn.Linear:
        nn.init.normal_(m.weight, std=0.01)
net.apply(init_weights);
loss = nn.CrossEntropyLoss(reduction='none')
trainer = torch.optim.SGD(net.parameters(), lr=0.1)
num_epochs = 10
d2l.train_c
h3(net, train_iter, test_iter, loss, num_epochs, trainer)
def accuracy(y_hat, y):  #@save
    """计算预测正确的数量"""
    if len(y_hat.shape) > 1 and y_hat.shape[1] > 1:
        y_hat = y_hat.argmax(axis=1)
    cmp = y_hat.type(y.dtype) == y
    return float(cmp.type(y.dtype).sum())
accuracy(y_hat, y) / len(y)
def evaluate_accuracy(net, data_iter):  #@save
    """计算在指定数据集上模型的精度"""
    if isinstance(net, torch.nn.Module):
        net.eval()  # 将模型设置为评估模式
    metric = Accumulator(2)  # 正确预测数、预测总数
    with torch.no_grad():
        for X, y in data_iter:
            metric.add(accuracy(net(X), y), y.numel())
    return metric[0] / metric[1]
class Accumulator:  #@save
    """在n个变量上累加"""
    def __init__(self, n):
        self.data = [0.0] * n
    def add(self, *args):
        self.data = [a + float(b) for a, b in zip(self.data, args)]
    def reset(self):
        self.data = [0.0] * len(self.data)
    def __getitem__(self, idx):
        return self.data[idx]
evaluate_accuracy(net, test_iter)
```

## 小批量模拟大批量梯度下降效果

```JavaScript
# 伪代码
for epoch in range(epochs):
    for i, (data, labels) in enumerate(dataloader): # 每次获取 200 个样本
        outputs = model(data)
        loss = criterion(outputs, labels)
        loss.backward()  # 累积梯度
  
        if (i+1) % 5 == 0:  # 每处理完 1000 个样本，执行一次梯度下降
            optimizer.step()
            optimizer.zero_grad()  # 这时才清零梯度
```

## 梯度裁剪

如果梯度长度超过sita，那么投影回长度sita
$\begin{equation}g \leftarrow \min \left( 1, \frac{\theta}{\|g\|} \right) g\end{equation}$

```Python
Method 1:
def grad_clipping(net, theta):  #@save
    """裁剪梯度"""
    if isinstance(net, nn.Module):
        params = [p for p in net.parameters() if p.requires_grad]
    else:
        params = net.params
    norm = torch.sqrt(sum(torch.sum((p.grad ** 2)) for p in params))
    if norm > theta:
        for param in params:
            param.grad[:] *= theta / norm
====================================================
Method 2:
# clip避免梯度爆炸
    for param in self.policy_net.parameters():  
        param.grad.data.clamp_(-1, 1)
===================================================================================
将梯度的范数限制在MAX_NORM以内，防止梯度爆炸。
MAX_NORM = 1.0
NUM_EPOCHS = 100
for i in np.arange(start_epoch, NUM_EPOCHS+start_epoch):
    train_loss = 0
    for X, y in train_loader:
        logits = net.forward(X)
        loss = criterion(logits, y)
        optimizer.zero_grad()
        loss.backward()
        nn.utils.clip_grad_norm_(net.parameters(), max_norm=MAX_NORM)
        optimizer.step()
  
        train_loss += loss.item()
    for name, param in net.named_parameters():
        gradients[name].append(np.mean(param.grad.detach().numpy()))
    net.eval()
    val_loss = 0
    with torch.no_grad():
        for X_val, y_val in val_loader:
            logits_val = net.forward(X_val)
            val_loss = criterion(logits_val, y_val)
            val_loss += val_loss.item()
    train_losses.append(train_loss/len(train_loader))
    val_losses.append(val_loss/len(val_loader))
  
    if (i+1) % 100 == 0:
        torch.save(
            net.state_dict(),
            f'model/model_epoch_{i+1}.pth'
        )
  
    print(f"Epoch {i+1}: Loss: {train_loss/len(train_loader):.4f} | Val Loss: {val_loss/len(val_loader):.4f} ####################", end='\r', flush=True)
===========================================  
```

# 激活函数

## 神经网络里的dropout/normalization/激活函数的顺序应该如何

```JavaScript
卷积/全连接层 -> 批归一化 -> 激活函数(如ReLU) -> Dropout
实践中的发现:
然而,许多实践者发现将BN放在激活函数之后效果更好。顺序变为:
卷积/全连接层 -> 激活函数(如ReLU) -> 批归一化 -> Dropout
========
什么是dropout、normalization和激活函数?
a) Dropout:
是一种正则化技术,用于防止过拟合。
在训练过程中随机"丢弃"一部分神经元,使网络不过度依赖某些特征。
b) Normalization (以Batch Normalization为例):
用于标准化每一层的输入,使数据分布更稳定。
有助于解决内部协变量偏移问题,加速训练过程。
c) 激活函数:
引入非线性,增加模型的表达能力。
常见的有ReLU、Sigmoid、Tanh等。
==========================================
Claude Answer
全连接层 -> Batch Normalization -> 激活函数 -> Dropout
有没有其他可能的顺序?
是的,也有其他可能的顺序,比如:
全连接层 -> 激活函数 -> Batch Normalization -> Dropout
全连接层 -> Dropout -> Batch Normalization -> 激活函数
==========================================
ChatGPT
same as claude
==========================================
Llama
希望dropout发生在激活函数之前，因为这样可以使得网络更难过拟合（因为随机丢弃了一些神经元）。如果放在激活函数之后，那么即使某些神经元被丢弃了，激活函数也会重新引入一些非线性特征，从而可能导致过拟合。

mlp -> normalization -> dropout -> activate function 
==========================================
import torch
import torch.nn as nn
class SimpleNet(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim, dropout_rate=0.5):
        super(SimpleNet, self).__init__()
        self.fc = nn.Linear(input_dim, hidden_dim)
        self.bn = nn.BatchNorm1d(hidden_dim)
        self.ln = nn.LayerNorm(hidden_dim)  # 添加 LayerNorm
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_rate)
        self.out = nn.Linear(hidden_dim, output_dim)
    def forward(self, x):
        x = self.fc(x)
        if x.size(0) > 1:
            x = self.bn(x)  # 使用 BatchNorm
        else:
            x = self.ln(x)  # 使用 LayerNorm
        x = self.relu(x)
        x = self.dropout(x)
        x = self.out(x)
        return x
# 创建一个简单的网络实例
net = SimpleNet(input_dim=10, hidden_dim=20, output_dim=2)
# 解决方案1：使用更大的批次大小
x_batch = torch.randn(32, 10)  # 批次大小为32
# 解决方案2：使用单个样本并切换到评估模式
x_single = torch.randn(1, 10)
net.eval()  # 切换到评估模式
# 让我们看看数据如何在网络中流动
def print_layer_outputs(x, mode="训练"):
    with torch.no_grad():
        fc_out = net.fc(x)
        if x.size(0) > 1:
            norm_out = net.bn(fc_out)
        else:
            norm_out = net.ln(fc_out)
        relu_out = net.relu(norm_out)
        dropout_out = net.dropout(relu_out)
        final_out = net.out(dropout_out)
    print(f"\n{mode}模式输出 (输入形状: {x.shape}):")
    print("全连接层输出:", fc_out.shape)
    print("Norm层输出:", norm_out.shape)
    print("ReLU输出:", relu_out.shape)
    print("Dropout输出:", dropout_out.shape)
    print("最终输出:", final_out.shape)
# 打印批处理模式的输出
net.train()
print_layer_outputs(x_batch, "训练（批处理）")
# 打印单个样本的输出（评估模式）
net.eval()
print_layer_outputs(x_single, "评估（单个样本）")
```

## Relu and tanh

![[Untitled 10.png]]
![[Untitled 11.png]]
![[Untitled 12.png]]
![[Untitled 13.png]]
![[Untitled 14.png]]

## sigmoid

```JavaScript

x = torch.arange(-8.0, 8.0, 0.1, requires_grad=True)
y = torch.relu(x)
y = torch.sigmoid(x)

对sigmoid求导，为 sigmod(x)(1-sigmod(x))
```

## GELU

$\begin{equation}\operatorname{GELU}(x) \approx 0.5 x\left(1+\tanh \left(\sqrt{\frac{2}{\pi}} x\right)\right)\end{equation}$
$\begin{equation}\operatorname{GELU}(x) \approx 0.5 x\left(1+\operatorname{erf}\left(\frac{x}{\sqrt{2}}\right)\right)\end{equation}$

```Python
def gelu(x):
    return 0.5 * x * (1.0 + torch.erf(x / math.sqrt(2.0)))
```

---

# Attention is All you need

![[Untitled 15.png]]
![[Untitled 16.png]]
[Decoder-Only Transformers, ChatGPTs specific Transformer, Clearly Explained!!! (youtube.com)](https://www.youtube.com/watch?v=bQ5BoolX9Ag)
![[Untitled 17.png]]

## Sinusoidal position Encoding

![[Untitled 18.png]]
|   |   |   |   |
|---|---|---|---|
||**Sinusoidal Positional Encoding**|**Positional Encoding (e.g., One-hot)**||
|Learnability|Not Learnable|Learnable||
|Generalization|High|Low||
|Computational Complexity|Low|Low||
|Sequence Capturing|High|Medium||
|Memory Usage|Low|High||
------------------------

---

```Python
def generate_positional_encoding(position, d_model):
    # 初始化一个位置编码矩阵，形状为 (position, d_model)
    pos_encoding = np.zeros((position, d_model))
    # 生成位置索引
    position = np.arange(position)[:, np.newaxis]
    # 生成维度索引
    div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
    # 计算正弦和余弦编码
    pos_encoding[:, 0::2] = np.sin(position * div_term)
    pos_encoding[:, 1::2] = np.cos(position * div_term)
    return pos_encoding
# 生成一个示例的位置编码
position = 50  # 序列长度
d_model = 512  # 编码维度
pos_encoding = generate_positional_encoding(position, d_model)
# 可视化位置编码
plt.figure(figsize=(12, 8))
plt.pcolormesh(pos_encoding, cmap='viridis')
plt.xlabel('Embedding Dimensions')
plt.ylabel('Sequence Position')
plt.colorbar()
plt.title('Sinusoidal Positional Encoding')
plt.show()
```

**Rotary Position Embedding**[**#**](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#rotary-position-embedding)

## **Rotary Position Embedding**

![[Untitled 19.png]]
![[Untitled 20.png]]
--------------------

![[Untitled 21.png]]
--------------------

![[Untitled 22.png]]

## Machine Learning Loss Functions

![[Untitled 23.png]]
[[EMO：基于最优传输思想设计的分类损失函数 - 知乎]]

```Python
import torch
import torch.nn.functional as F
# 假设outputs是模型的输出，label_ids是目标标签的one-hot编码
# outputs和label_ids的形状都是(batch_size, seq_length, vocab_size)
# 首先，我们需要得到预测的嵌入向量和目标的嵌入向量
# 假设embedding是一个预先训练好的嵌入层
embedding = model.get_embedding_layer()
# 获取嵌入向量
p = embedding(outputs)  # (batch_size, seq_length, embedding_size)
t = embedding(label_ids)  # (batch_size, seq_length, embedding_size)
# 单位化嵌入向量
p_norm = F.normalize(p, p=2, dim=2)
t_norm = F.normalize(t, p=2, dim=2)
# 计算单位向量之间的点积
dot_product = (p_norm * t_norm).sum(dim=2)
# 计算损失
emd_loss = 1 - dot_product  # (batch_size, seq_length)
# 求和得到整体损失
loss_value = emd_loss.sum()
在这个例子中，model.get_embedding_layer() 是假设你的模型中有一个方法可以获取预训练的嵌入层。embedding(outputs) 和 embedding(label_ids) 
是使用这个嵌入层将预测和目标的one-hot编码转换成嵌入向量。然后使用 F.normalize 函数对嵌入向量进行单位化。最后，计算预测和目标之间的单位向量
的点积，以此来计算EMD损失，并对损失求和得到整体损失。
需要注意的是，这里的损失计算方式与常规的交叉熵损失或其他损失函数不同，因为它专门针对嵌入向量之间的距离进行优化。如果你的模型不是基于嵌入向量的，
或者有其他特定的损失计算需求，可能需要进一步调整这段代码以适应你的模型结构和数据。
```

[[深度学习的多个loss如何平衡 & 有哪些「魔改」损失函数，曾经拯救了你的深度学习模型？ - GiantPandaCV]]
NCE Loss
[[使用一个特别设计的损失来处理类别不均衡的数据集]]

```JavaScript
import torch
import torch.nn as nn
import torch.nn.functional as F
class NCELoss(nn.Module):
    def __init__(self, noise_dist, num_noise_samples):
        super(NCELoss, self).__init__()
        self.noise_dist = noise_dist
        self.num_noise_samples = num_noise_samples
    def forward(self, input, target):
        # input: 模型的原始输出
        # target: 目标词汇的索引
        batch_size = input.size(0)
        noise_samples = torch.multinomial(self.noise_dist, batch_size * self.num_noise_samples, replacement=True)
        # 构造二元标签：正样本为1，负样本为0
        label = torch.cat([torch.ones(batch_size), torch.zeros(batch_size * self.num_noise_samples)]).to(input.device)
        # 合并目标词汇和噪声词汇
        combined_samples = torch.cat([target.unsqueeze(1), noise_samples.view(batch_size, -1)], dim=1).view(-1)
        # 计算损失
        logits = input[torch.arange(batch_size).unsqueeze(1), combined_samples].view(-1)
        loss = F.binary_cross_entropy_with_logits(logits, label)
        return loss
# 使用示例
vocab_size = 10000  # 假设词汇量大小
num_noise_samples = 100  # 噪声样本数量
noise_dist = torch.ones(vocab_size) / vocab_size  # 假设均匀分布的噪声
nce_loss = NCELoss(noise_dist, num_noise_samples)
input = torch.randn(32, vocab_size)  # 模型输出
target = torch.randint(0, vocab_size, (32,))  # 目标词汇索引
loss = nce_loss(input, target)
print(loss)
```

**pair wise loss**

```LaTeX
loss += -torch.log(torch.sigmoid(c_truncated_reward - r_truncated_reward)).mean()
```

$$
数学公式设 c 为截断的奖励 ('c_truncated_reward')， r 为另一个截断的奖励 ('r_truncated_reward') 。则 pairwise loss 的数学公式为:\operatorname{loss}=-\frac{1}{N} \sum_{i=1}^N \log \left(\sigma\left(c_i-r_i\right)\right)
$$

$$
\begin{equation}
  \text { 其中， } \sigma(x)=\frac{1}{1+e^{-x}} \text { 是 Sigmoid 函数， } N \text { 是 batch 中的样本数量。 }
  \end{equation}
$$

## 注意力汇聚

$$
\begin{equation}f(x)=\sum_{i=1}^n \frac{K\left(x-x_i\right)}{\sum_{j=1}^n K\left(x-x_j\right)} y_i\end{equation}
$$

When K 为高斯核

$$
\begin{equation}
  K(u)=\frac{1}{\sqrt{2 \pi}} \exp \left(-\frac{u^2}{2}\right)
  \end{equation}
$$

将高斯核代入可以得到:

$$


$$

  \begin{aligned}
  f(x) & =\sum_{i=1}^n \alpha\left(x, x_i\right) y_i \\
  & =\sum_{i=1}^n \frac{\exp \left(-\frac{1}{2}\left(x-x_i\right)^2\right)}{\sum_{j=1}^n \exp \left(-\frac{1}{2}\left(x-x_j\right)^2\right)} y_i \\
  & =\sum_{i=1}^n \operatorname{softmax}\left(-\frac{1}{2}\left(x-x_i\right)^2\right) y_i .
  \end{aligned}

$$


$$

带参数w

$$
\begin{equation}
\begin{aligned}
f(x) & =\sum_{i=1}^n \alpha\left(x, x_i\right) y_i \\
& =\sum_{i=1}^n \frac{\exp \left(-\frac{1}{2}\left(\left(x-x_i\right) w\right)^2\right)}{\sum_{j=1}^n \exp \left(-\frac{1}{2}\left(\left(x-x_j\right) w\right)^2\right)} y_i \\
& =\sum_{i=1}^n \operatorname{softmax}\left(-\frac{1}{2}\left(\left(x-x_i\right) w\right)^2\right) y_i .
\end{aligned}
\end{equation}
$$

## Source code

```JavaScript
class NWKernelRegression(nn.Module):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.w = nn.Parameter(torch.rand((1,), requires_grad=True))
    def forward(self, queries, keys, values):
        # queries和attention_weights的形状为(查询个数，"键-值"对个数)
        queries = queries.repeat_interleave(keys.shape[1]).reshape((-1, keys.shape[1]))
        self.attention_weights = nn.functional.softmax(
            -((queries - keys) * self.w)**2 / 2, dim=1)
        # values的形状为(查询个数，"键-值"对个数)
        return torch.bmm(self.attention_weights.unsqueeze(1),
                         values.unsqueeze(-1)).reshape(-1)
net = NWKernelRegression()
loss = nn.MSELoss(reduction='none')
trainer = torch.optim.SGD(net.parameters(), lr=0.5)
animator = d2l.Animator(xlabel='epoch', ylabel='loss', xlim=[1, 5])
for epoch in range(5):
    trainer.zero_grad()
    l = loss(net(x_train, keys, values), y_train)
    l.sum().backward()
    trainer.step()
    print(f'epoch {epoch + 1}, loss {float(l.sum()):.6f}')
    animator.add(epoch + 1, float(l.sum()))
# keys的形状:(n_test，n_train)，每一行包含着相同的训练输入（例如，相同的键）
keys = x_train.repeat((n_test, 1))
# value的形状:(n_test，n_train)
values = y_train.repeat((n_test, 1))
y_hat = net(x_test, keys, values).unsqueeze(1).detach()
plot_kernel_reg(y_hat)
```

## **加性注意力**

$$
\begin{equation}
a(\mathbf{q}, \mathbf{k})=\mathbf{w}_v^{\top} \tanh \left(\mathbf{W}_q \mathbf{q}+\mathbf{W}_k \mathbf{k}\right) \in \mathbb{R}
\end{equation}
$$

## **缩放点积注意力**

$$
\begin{equation}
a(\mathbf{q}, \mathbf{k})=\mathbf{w}_v^{\top} \tanh \left(\mathbf{W}_q \mathbf{q}+\mathbf{W}_k \mathbf{k}\right) \in \mathbb{R}
\end{equation}
$$

## Transformer Implementation

```Python
#/opt/workspace/app/d2l-zh/pytorch/chapter_attention-mechanisms/transformer.ipynb
# Include encode and decode both part -- BERD
import math
import pandas as pd
import torch
from torch import nn
from d2l import torch as d2l
#@save
class PositionWiseFFN(nn.Module):
    """基于位置的前馈网络"""
    def __init__(self, ffn_num_input, ffn_num_hiddens, ffn_num_outputs,
                 **kwargs):
        super(PositionWiseFFN, self).__init__(**kwargs)
        self.dense1 = nn.Linear(ffn_num_input, ffn_num_hiddens)
        self.relu = nn.ReLU()
        self.dense2 = nn.Linear(ffn_num_hiddens, ffn_num_outputs)
    def forward(self, X):
        return self.dense2(self.relu(self.dense1(X)))
#@save
class AddNorm(nn.Module):
    """残差连接后进行层规范化"""
    def __init__(self, normalized_shape, dropout, **kwargs):
        super(AddNorm, self).__init__(**kwargs)
        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(normalized_shape)
    def forward(self, X, Y):
        return self.ln(self.dropout(Y) + X)
#@save
def transpose_qkv(X, num_heads):
    """为了多注意力头的并行计算而变换形状"""
    # 输入X的形状:(batch_size，查询或者"键-值"对的个数，num_hiddens)
    # 输出X的形状:(batch_size，查询或者"键-值"对的个数，num_heads，
    # num_hiddens/num_heads)
    X = X.reshape(X.shape[0], X.shape[1], num_heads, -1)
    # 输出X的形状:(batch_size，num_heads，查询或者"键-值"对的个数,
    # num_hiddens/num_heads)
    X = X.permute(0, 2, 1, 3)
    # 最终输出的形状:(batch_size*num_heads,查询或者"键-值"对的个数,
    # num_hiddens/num_heads)
    return X.reshape(-1, X.shape[2], X.shape[3])

#@save
def transpose_output(X, num_heads):
    """逆转transpose_qkv函数的操作"""
    X = X.reshape(-1, num_heads, X.shape[1], X.shape[2])
    X = X.permute(0, 2, 1, 3)
    return X.reshape(X.shape[0], X.shape[1], -1)
#@save
def masked_softmax(X, valid_lens):
    """通过在最后一个轴上掩蔽元素来执行softmax操作"""
    # X:3D张量，valid_lens:1D或2D张量
    if valid_lens is None:
        return nn.functional.softmax(X, dim=-1)
    else:
        shape = X.shape
        if valid_lens.dim() == 1:
            valid_lens = torch.repeat_interleave(valid_lens, shape[1])
        else:
            valid_lens = valid_lens.reshape(-1)
        # 最后一轴上被掩蔽的元素使用一个非常大的负值替换，从而其softmax输出为0
        X = d2l.sequence_mask(X.reshape(-1, shape[-1]), valid_lens,
                              value=-1e6)
        return nn.functional.softmax(X.reshape(shape), dim=-1)

#@save
class DotProductAttention(nn.Module):
    """缩放点积注意力"""
    def __init__(self, dropout, **kwargs):
        super(DotProductAttention, self).__init__(**kwargs)
        self.dropout = nn.Dropout(dropout)
    # queries的形状：(batch_size，查询的个数，d)
    # keys的形状：(batch_size，"键-值"对的个数，d)
    # values的形状：(batch_size，"键-值"对的个数，值的维度)
    # valid_lens的形状:(batch_size，)或者(batch_size，查询的个数)
    def forward(self, queries, keys, values, valid_lens=None):
        d = queries.shape[-1]
        # 设置transpose_b=True为了交换keys的最后两个维度
        scores = torch.bmm(queries, keys.transpose(1,2)) / math.sqrt(d)
        self.attention_weights = masked_softmax(scores, valid_lens)
        return torch.bmm(self.dropout(self.attention_weights), values)
#@save
class MultiHeadAttention(nn.Module):
    """多头注意力"""
    def __init__(self, key_size, query_size, value_size, num_hiddens,
                 num_heads, dropout, bias=False, **kwargs):
        super(MultiHeadAttention, self).__init__(**kwargs)
        self.num_heads = num_heads
        self.attention = DotProductAttention(dropout)
        self.W_q = nn.Linear(query_size, num_hiddens, bias=bias)
        self.W_k = nn.Linear(key_size, num_hiddens, bias=bias)
        self.W_v = nn.Linear(value_size, num_hiddens, bias=bias)
        self.W_o = nn.Linear(num_hiddens, num_hiddens, bias=bias)
    def forward(self, queries, keys, values, valid_lens):
        # queries，keys，values的形状:
        # (batch_size，查询或者"键-值"对的个数，num_hiddens)
        # valid_lens　的形状:
        # (batch_size，)或(batch_size，查询的个数)
        # 经过变换后，输出的queries，keys，values　的形状:
        # (batch_size*num_heads，查询或者"键-值"对的个数，
        # num_hiddens/num_heads)
        queries = transpose_qkv(self.W_q(queries), self.num_heads)
        keys = transpose_qkv(self.W_k(keys), self.num_heads)
        values = transpose_qkv(self.W_v(values), self.num_heads)
        if valid_lens is not None:
            # 在轴0，将第一项（标量或者矢量）复制num_heads次，
            # 然后如此复制第二项，然后诸如此类。
            valid_lens = torch.repeat_interleave(
                valid_lens, repeats=self.num_heads, dim=0)
        # output的形状:(batch_size*num_heads，查询的个数，
        # num_hiddens/num_heads)
        output = self.attention(queries, keys, values, valid_lens)
        # output_concat的形状:(batch_size，查询的个数，num_hiddens)
        output_concat = transpose_output(output, self.num_heads)
        return self.W_o(output_concat)
#@save
class EncoderBlock(nn.Module):
    """Transformer编码器块"""
    def __init__(self, key_size, query_size, value_size, num_hiddens,
                 norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,
                 dropout, use_bias=False, **kwargs):
        super(EncoderBlock, self).__init__(**kwargs)
        self.attention = MultiHeadAttention(
            key_size, query_size, value_size, num_hiddens, num_heads, dropout,
            use_bias)
        self.addnorm1 = AddNorm(norm_shape, dropout)
        self.ffn = PositionWiseFFN(
            ffn_num_input, ffn_num_hiddens, num_hiddens)
        self.addnorm2 = AddNorm(norm_shape, dropout)
    def forward(self, X, valid_lens):
        Y = self.addnorm1(X, self.attention(X, X, X, valid_lens))
        return self.addnorm2(Y, self.ffn(Y))
#@save
class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, num_hiddens, dropout, max_len=1000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(dropout)
        # 创建一个足够长的P
        self.P = torch.zeros((1, max_len, num_hiddens))
        X = torch.arange(max_len, dtype=torch.float32).reshape(
            -1, 1) / torch.pow(10000, torch.arange(
            0, num_hiddens, 2, dtype=torch.float32) / num_hiddens)
        self.P[:, :, 0::2] = torch.sin(X)
        self.P[:, :, 1::2] = torch.cos(X)
    def forward(self, X):
        X = X + self.P[:, :X.shape[1], :].to(X.device)
        return self.dropout(X)
#@save
class TransformerEncoder(d2l.Encoder):
    """Transformer编码器"""
    def __init__(self, vocab_size, key_size, query_size, value_size,
                 num_hiddens, norm_shape, ffn_num_input, ffn_num_hiddens,
                 num_heads, num_layers, dropout, use_bias=False, **kwargs):
        super(TransformerEncoder, self).__init__(**kwargs)
        self.num_hiddens = num_hiddens
        self.embedding = nn.Embedding(vocab_size, num_hiddens)
        self.pos_encoding = PositionalEncoding(num_hiddens, dropout)
        self.blks = nn.Sequential()
        for i in range(num_layers):
            self.blks.add_module("block"+str(i),
                EncoderBlock(key_size, query_size, value_size, num_hiddens,
                             norm_shape, ffn_num_input, ffn_num_hiddens,
                             num_heads, dropout, use_bias))
    def forward(self, X, valid_lens, *args):
        # 因为位置编码值在-1和1之间，
        # 因此嵌入值乘以嵌入维度的平方根进行缩放，
        # 然后再与位置编码相加。
        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))
        self.attention_weights = [None] * len(self.blks)
        for i, blk in enumerate(self.blks):
            X = blk(X, valid_lens)
            self.attention_weights[i] = blk.attention.attention.attention_weights
        return X
class DecoderBlock(nn.Module):
    """解码器中第i个块"""
    def __init__(self, key_size, query_size, value_size, num_hiddens,
                 norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,
                 dropout, i, **kwargs):
        super(DecoderBlock, self).__init__(**kwargs)
        self.i = i
        self.attention1 = MultiHeadAttention(
            key_size, query_size, value_size, num_hiddens, num_heads, dropout)
        self.addnorm1 = AddNorm(norm_shape, dropout)
        self.attention2 = MultiHeadAttention(
            key_size, query_size, value_size, num_hiddens, num_heads, dropout)
        self.addnorm2 = AddNorm(norm_shape, dropout)
        self.ffn = PositionWiseFFN(ffn_num_input, ffn_num_hiddens,
                                   num_hiddens)
        self.addnorm3 = AddNorm(norm_shape, dropout)
    def forward(self, X, state):
        enc_outputs, enc_valid_lens = state[0], state[1]
        # 训练阶段，输出序列的所有词元都在同一时间处理，
        # 因此state[2][self.i]初始化为None。
        # 预测阶段，输出序列是通过词元一个接着一个解码的，
        # 因此state[2][self.i]包含着直到当前时间步第i个块解码的输出表示
        if state[2][self.i] is None:
            key_values = X
        else:
            key_values = torch.cat((state[2][self.i], X), axis=1)
        state[2][self.i] = key_values
        if self.training:
            batch_size, num_steps, _ = X.shape
            # dec_valid_lens的开头:(batch_size,num_steps),
            # 其中每一行是[1,2,...,num_steps]
            dec_valid_lens = torch.arange(
                1, num_steps + 1, device=X.device).repeat(batch_size, 1)
        else:
            dec_valid_lens = None
        # 自注意力
        X2 = self.attention1(X, key_values, key_values, dec_valid_lens)
        Y = self.addnorm1(X, X2)
        # 编码器-解码器注意力。
        # enc_outputs的开头:(batch_size,num_steps,num_hiddens)
        Y2 = self.attention2(Y, enc_outputs, enc_outputs, enc_valid_lens)
        Z = self.addnorm2(Y, Y2)
        return self.addnorm3(Z, self.ffn(Z)), state
class TransformerDecoder(d2l.AttentionDecoder):
    def __init__(self, vocab_size, key_size, query_size, value_size,
                 num_hiddens, norm_shape, ffn_num_input, ffn_num_hiddens,
                 num_heads, num_layers, dropout, **kwargs):
        super(TransformerDecoder, self).__init__(**kwargs)
        self.num_hiddens = num_hiddens
        self.num_layers = num_layers
        self.embedding = nn.Embedding(vocab_size, num_hiddens)
        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)
        self.blks = nn.Sequential()
        for i in range(num_layers):
            self.blks.add_module("block"+str(i),
                DecoderBlock(key_size, query_size, value_size, num_hiddens,
                             norm_shape, ffn_num_input, ffn_num_hiddens,
                             num_heads, dropout, i))
        self.dense = nn.Linear(num_hiddens, vocab_size)
    def init_state(self, enc_outputs, enc_valid_lens, *args):
        return [enc_outputs, enc_valid_lens, [None] * self.num_layers]
    def forward(self, X, state):
        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))
        self._attention_weights = [[None] * len(self.blks) for _ in range (2)]
        for i, blk in enumerate(self.blks):
            X, state = blk(X, state)
            # 解码器自注意力权重
            self._attention_weights[0][
                i] = blk.attention1.attention.attention_weights
            # "编码器-解码器"自注意力权重
            self._attention_weights[1][
                i] = blk.attention2.attention.attention_weights
        return self.dense(X), state
    @property
    def attention_weights(self):
        return self._attention_weights
#@save
class MaskedSoftmaxCELoss(nn.CrossEntropyLoss):
    """带遮蔽的softmax交叉熵损失函数"""
    # pred的形状：(batch_size,num_steps,vocab_size)
    # label的形状：(batch_size,num_steps)
    # valid_len的形状：(batch_size,)
    def forward(self, pred, label, valid_len):
        weights = torch.ones_like(label)
        weights = sequence_mask(weights, valid_len)
        self.reduction='none'
        unweighted_loss = super(MaskedSoftmaxCELoss, self).forward(
            pred.permute(0, 2, 1), label)
        weighted_loss = (unweighted_loss * weights).mean(dim=1)
        return weighted_loss
#@save
def train_seq2seq(net, data_iter, lr, num_epochs, tgt_vocab, device):
    """训练序列到序列模型"""
    def xavier_init_weights(m):
        if type(m) == nn.Linear:
            nn.init.xavier_uniform_(m.weight)
        if type(m) == nn.GRU:
            for param in m._flat_weights_names:
                if "weight" in param:
                    nn.init.xavier_uniform_(m._parameters[param])
    net.apply(xavier_init_weights)
    net.to(device)
    optimizer = torch.optim.Adam(net.parameters(), lr=lr)
    loss = MaskedSoftmaxCELoss()
    net.train()
    animator = d2l.Animator(xlabel='epoch', ylabel='loss',
                     xlim=[10, num_epochs])
    for epoch in range(num_epochs):
        timer = d2l.Timer()
        metric = d2l.Accumulator(2)  # 训练损失总和，词元数量
        for batch in data_iter:
            optimizer.zero_grad()
            X, X_valid_len, Y, Y_valid_len = [x.to(device) for x in batch]
            bos = torch.tensor([tgt_vocab['<bos>']] * Y.shape[0],
                          device=device).reshape(-1, 1)
            dec_input = torch.cat([bos, Y[:, :-1]], 1)  # 强制教学
            Y_hat, _ = net(X, dec_input, X_valid_len)
            l = loss(Y_hat, Y, Y_valid_len)
            l.sum().backward()	# 损失函数的标量进行"反向传播"
            d2l.grad_clipping(net, 1)
            num_tokens = Y_valid_len.sum()
            optimizer.step()
            with torch.no_grad():
                metric.add(l.sum(), num_tokens)
        if (epoch + 1) % 10 == 0:
            animator.add(epoch + 1, (metric[0] / metric[1],))
    print(f'loss {metric[0] / metric[1]:.3f}, {metric[1] / timer.stop():.1f} '
        f'tokens/sec on {str(device)}')
class EncoderDecoder(nn.Module):
    """编码器-解码器架构的基类"""
    def __init__(self, encoder, decoder, **kwargs):
        super(EncoderDecoder, self).__init__(**kwargs)
        self.encoder = encoder
        self.decoder = decoder
    def forward(self, enc_X, dec_X, *args):
        enc_outputs = self.encoder(enc_X, *args)
        dec_state = self.decoder.init_state(enc_outputs, *args)
        return self.decoder(dec_X, dec_state)
def sequence_mask(X, valid_len, value=0):
    """在序列中屏蔽不相关的项"""
    maxlen = X.size(1)
    mask = torch.arange((maxlen), dtype=torch.float32,
                        device=X.device)[None, :] < valid_len[:, None]
    X[~mask] = value
    return X
num_hiddens, num_layers, dropout, batch_size, num_steps = 32, 2, 0.1, 64, 10
lr, num_epochs, device = 0.005, 200, d2l.try_gpu()
ffn_num_input, ffn_num_hiddens, num_heads = 32, 64, 4
key_size, query_size, value_size = 32, 32, 32
norm_shape = [32]
train_iter, src_vocab, tgt_vocab = d2l.load_data_nmt(batch_size, num_steps)
encoder = TransformerEncoder(
    len(src_vocab), key_size, query_size, value_size, num_hiddens,
    norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,
    num_layers, dropout)
decoder = TransformerDecoder(
    len(tgt_vocab), key_size, query_size, value_size, num_hiddens,
    norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,
    num_layers, dropout)
net = EncoderDecoder(encoder, decoder)
train_seq2seq(net, train_iter, lr, num_epochs, tgt_vocab, device)
engs = ['go .', "i lost .", 'he\'s calm .', 'i\'m home .']
fras = ['va !', 'j\'ai perdu .', 'il est calme .', 'je suis chez moi .']
for eng, fra in zip(engs, fras):
    translation, dec_attention_weight_seq = d2l.predict_seq2seq(
        net, eng, src_vocab, tgt_vocab, num_steps, device, True)
    print(f'{eng} => {translation}, ',
          f'bleu {d2l.bleu(translation, fra, k=2):.3f}')
enc_attention_weights = torch.cat(net.encoder.attention_weights, 0).reshape((num_layers, num_heads,
    -1, num_steps))
enc_attention_weights.shape
d2l.show_heatmaps(
    enc_attention_weights.cpu(), xlabel='Key positions',
    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],
    figsize=(7, 3.5))
dec_attention_weights_2d = [head[0].tolist()
                            for step in dec_attention_weight_seq
                            for attn in step for blk in attn for head in blk]
dec_attention_weights_filled = torch.tensor(
    pd.DataFrame(dec_attention_weights_2d).fillna(0.0).values)
dec_attention_weights = dec_attention_weights_filled.reshape((-1, 2, num_layers, num_heads, num_steps))
dec_self_attention_weights, dec_inter_attention_weights = \
    dec_attention_weights.permute(1, 2, 3, 0, 4)
dec_self_attention_weights.shape, dec_inter_attention_weights.shape
# Plusonetoincludethebeginning-of-sequencetoken
d2l.show_heatmaps(
    dec_self_attention_weights[:, :, :, :len(translation.split()) + 1],
    xlabel='Key positions', ylabel='Query positions',
    titles=['Head %d' % i for i in range(1, 5)], figsize=(7, 3.5))
d2l.show_heatmaps(
    dec_inter_attention_weights, xlabel='Key positions',
    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],
    figsize=(7, 3.5))
```

## Transformer implement — 利用已知tokenizer

```Python
# Like GPT only contain decode part and use existing known tokenizer
/opt/workspace/raylab/myTransformer/transfomer-V5.ipynb
import torch.nn.functional as F
from torch import Tensor
from transformers import AutoTokenizer, AutoModel
model_path = "/opt/aibase/bge-reranker-large"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
# model = AutoModel.from_pretrained(model_path)
def read_text_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
  
    paragraphs = []
    for line in lines:
        line = line.replace('"', '').replace('"', '')
        parts = line.strip().split('。')
  
        # 过滤掉空的部分
        parts = [part for part in parts if part.strip()]
        if parts:
            paragraphs.append(parts)
    random.shuffle(paragraphs)
    return paragraphs
input_texts = [token for paragraph in paragraphs for token in paragraph]
batch_dict = tokenizer(input_texts, max_length=512, padding=True, truncation=True, return_tensors='pt', add_special_tokens=True)
max_length = max(len(encoding.tokens) for encoding in batch_dict.encodings)
print('max_length:', max_length)
class SeqDataLoader:  #@save
    """加载序列数据的迭代器"""
    def __init__(self, batch_dict, batch_size ):
        self.batch_dict = batch_dict
        self.batch_size = batch_size
        self.data_iter_fn = seq_data_iter
    def __iter__(self):
        return self.data_iter_fn(self.batch_dict, self.batch_size)
def seq_data_iter(batch_dict, batch_size):
    """使用顺序分区生成一个小批量子序列"""
    # 获取数据和掩码
    data = batch_dict['input_ids']
    mask = batch_dict['attention_mask']
    # 计算总的批次数量
    num_batches = (len(data) + batch_size - 1) // batch_size
    # 逐批次输出数据
    for i in range(num_batches):
        start = i * batch_size
        end = start + batch_size
        batch_data = data[start:end]
        batch_mask = mask[start:end]
        # 确保所有批次具有相同的形状
        if len(batch_data) < batch_size:
            pad_len = batch_size - len(batch_data)
            batch_data = torch.cat([batch_data, torch.zeros(pad_len, batch_data.shape[1], dtype=batch_data.dtype)])
            batch_mask = torch.cat([batch_mask, torch.zeros(pad_len, batch_mask.shape[1], dtype=batch_mask.dtype)])
  
        yield batch_data, batch_mask
          
def load_dataset(batch_dict, batch_size):
    """返回数据集的迭代器"""
    return SeqDataLoader(batch_dict,batch_size)

# #@save
class PositionWiseFFN(nn.Module):
    """基于位置的前馈网络"""
    def __init__(self, ffn_num_input, ffn_num_hiddens, ffn_num_outputs,
                 **kwargs):
        super(PositionWiseFFN, self).__init__(**kwargs)
        self.dense1 = nn.Linear(ffn_num_input, ffn_num_hiddens)
        self.relu = nn.ReLU()
        self.dense2 = nn.Linear(ffn_num_hiddens, ffn_num_outputs)
    def forward(self, X):
        return self.dense2(self.relu(self.dense1(X)))
#@save
class AddNorm(nn.Module):
    """残差连接后进行层规范化"""
    def __init__(self, normalized_shape, dropout, **kwargs):
        super(AddNorm, self).__init__(**kwargs)
        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(normalized_shape)
    def forward(self, X, Y):
        return self.ln(self.dropout(Y) + X)
#@save
def transpose_qkv(X, num_heads):
    """为了多注意力头的并行计算而变换形状"""
    # 检查输入维度是否能够被num_heads整除
    num_hiddens = X.shape[-1]
    if num_hiddens % num_heads != 0:
        raise ValueError(f"num_hiddens must be divisible by num_heads. Received num_hiddens: {num_hiddens}, num_heads: {num_heads}")
    # 继续变换形状
    new_shape = X.shape[:-1] + (num_heads, num_hiddens // num_heads)
    X = X.reshape(new_shape)
  
    # 输出X的形状:(batch_size，num_heads，查询或者"键-值"对的个数, num_hiddens/num_heads)
    X = X.permute(0, 2, 1, 3)
    # 最终输出的形状:(batch_size*num_heads, 查询或者"键-值"对的个数, num_hiddens/num_heads)
    return X.reshape(-1, X.shape[2], X.shape[3])

#@save
def transpose_output(X, num_heads):
    """逆转transpose_qkv函数的操作"""
    # 将输入形状从(batch_size*num_heads, seq_len, num_hiddens/num_heads)
    # 转换回(batch_size, seq_len, num_hiddens)
    reshaped_X = X.reshape(-1, num_heads, X.shape[1], X.shape[2])
    # 转换维度为(batch_size, seq_len, num_heads, num_hiddens/num_heads)
    transposed_X = reshaped_X.permute(0, 2, 1, 3)
    # 合并最后两个维度来得到最终形状
    output = transposed_X.reshape(transposed_X.shape[0], transposed_X.shape[1], -1)
    return output
class DotProductAttention(nn.Module):
    """缩放点积注意力"""
    def __init__(self, dropout, **kwargs):
        super(DotProductAttention, self).__init__(**kwargs)
        self.dropout = nn.Dropout(dropout)
    def forward(self, queries, keys, values, attention_mask=None):
        d = queries.shape[-1]
        scores = torch.bmm(queries, keys.transpose(1, 2)) / math.sqrt(d)
        # 创建一个下三角矩阵，保证自注意力机制的正确性
        seq_len = queries.size(1)
        mask = torch.triu(torch.ones((seq_len, seq_len), device=queries.device), diagonal=1).bool()
        # 如果提供了attention_mask，更新掩码以忽略填充token
        if attention_mask is not None:
            # 重新排列attention_mask以与scores的形状匹配
            expanded_mask = attention_mask.unsqueeze(1).expand(-1, seq_len, -1)
            mask = mask | ~expanded_mask.bool()  # 确保mask是布尔型
        # 使用掩码更新scores
        scores = scores.masked_fill(mask, -1e9)
        self.attention_weights = nn.functional.softmax(scores, dim=-1)
        return torch.bmm(self.dropout(self.attention_weights), values)

class MultiHeadAttention(nn.Module):
    """多头注意力"""
    def __init__(self, key_size, query_size, value_size, num_hiddens,
                 num_heads, dropout, bias=False, **kwargs):
        super(MultiHeadAttention, self).__init__(**kwargs)
        self.num_heads = num_heads
        self.attention = DotProductAttention(dropout)
        self.W_q = nn.Linear(query_size, num_hiddens, bias=bias)
        self.W_k = nn.Linear(key_size, num_hiddens, bias=bias)
        self.W_v = nn.Linear(value_size, num_hiddens, bias=bias)
        self.W_o = nn.Linear(num_hiddens, num_hiddens, bias=bias)
    def forward(self, queries, keys, values, attention_mask=None):
        # queries，keys，values的形状:
        # (batch_size，查询或者"键-值"对的个数，num_hiddens)
        # attention_mask的形状: (batch_size, seq_len)
        # 经过变换后，输出的queries，keys，values　的形状:
        # (batch_size*num_heads，查询或者"键-值"对的个数，
        # num_hiddens/num_heads)
        queries = transpose_qkv(self.W_q(queries), self.num_heads)
        keys = transpose_qkv(self.W_k(keys), self.num_heads)
        values = transpose_qkv(self.W_v(values), self.num_heads)
        if attention_mask is not None:
            # 重排attention_mask以匹配多头的形状
            attention_mask = attention_mask.repeat_interleave(self.num_heads, dim=0)
        # output的形状:(batch_size*num_heads，查询的个数，
        # num_hiddens/num_heads)
        output = self.attention(queries, keys, values, attention_mask)
        # output_concat的形状:(batch_size，查询的个数，num_hiddens)
        output_concat = transpose_output(output, self.num_heads)
        return self.W_o(output_concat)
#@save
class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, num_hiddens, dropout, max_len=1000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(dropout)
        # 创建一个足够长的P
        self.P = torch.zeros((1, max_len, num_hiddens))
        X = torch.arange(max_len, dtype=torch.float32).reshape(
            -1, 1) / torch.pow(10000, torch.arange(
            0, num_hiddens, 2, dtype=torch.float32) / num_hiddens)
        self.P[:, :, 0::2] = torch.sin(X)
        self.P[:, :, 1::2] = torch.cos(X)
    def forward(self, X):
        X = X + self.P[:, :X.shape[1], :].to(X.device)
        return self.dropout(X)
class DecoderBlock(nn.Module):
    """解码器中第i个块"""
    def __init__(self, key_size, query_size, value_size, num_hiddens,
                 norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,
                 dropout, **kwargs):
        super(DecoderBlock, self).__init__(**kwargs)
        self.attention1 = MultiHeadAttention(
            key_size, query_size, value_size, num_hiddens, num_heads, dropout)
        self.addnorm1 = AddNorm(norm_shape, dropout)
        self.ffn = PositionWiseFFN(ffn_num_input, ffn_num_hiddens,
                                   num_hiddens)
        self.addnorm3 = AddNorm(norm_shape, dropout)
        self.num_heads = num_heads
    def forward(self, X, attention_mask=None):
        key_values = X
        # 使用attention_mask替代valid_lens
        X2 = self.attention1(X, key_values, key_values, attention_mask)
        Y = self.addnorm1(X, X2)
        # 前馈网络
        Y = self.ffn(Y)
        return self.addnorm3(Y, Y)
class RayTransformer(d2l.AttentionDecoder):
    def __init__(self, vocab_size, key_size, query_size, value_size,
                 num_hiddens, norm_shape, ffn_num_input, ffn_num_hiddens,
                 num_heads, num_layers, dropout, **kwargs):
        super(RayTransformer, self).__init__(**kwargs)
        self.num_hiddens = num_hiddens
        self.num_layers = num_layers
        self.embedding = nn.Embedding(vocab_size, num_hiddens)
        self.pos_encoding = PositionalEncoding(num_hiddens, dropout)
        self.blks = nn.Sequential()
        for i in range(num_layers):
            self.blks.add_module("block" + str(i),
                                 DecoderBlock(key_size, query_size, value_size, num_hiddens,
                                              norm_shape, ffn_num_input, ffn_num_hiddens,
                                              num_heads, dropout))
        self.dense = nn.Linear(num_hiddens, vocab_size)
    def init_state(self, src, *args):
        return [src]
    def forward(self, X, attention_mask=None):
        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))
        self._attention_weights = [None] * len(self.blks)
        for i, blk in enumerate(self.blks):
            X = blk(X, attention_mask)
            self._attention_weights[i] = blk.attention1.attention.attention_weights
        return self.dense(X)
    @property
    def attention_weights(self):
        return self._attention_weights
# 假设vocab_size是您的词汇表大小
vocab_size = 250002
\#num_hiddens 应该是 num_heads 的整数倍
num_hiddens, num_layers, dropout = 32, 8, 0.1
lr, num_epochs = 0.005, 3
num_heads = 8
device = d2l.try_gpu()  # 获取设备
ffn_num_input, ffn_num_hiddens, num_heads = num_hiddens, num_hiddens, num_heads
key_size, query_size, value_size = num_hiddens, num_hiddens, num_hiddens
norm_shape = [num_hiddens]
model = RayTransformer(
    vocab_size, key_size, query_size, value_size, num_hiddens,
    norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,
    num_layers, dropout).to(device)
import os
# os.environ['CUDA_LAUNCH_BLOCKING'] = "1"
from tqdm import tqdm, trange
def grad_clipping(net, theta):  #@save
    """裁剪梯度"""
    if isinstance(net, nn.Module):
        params = [p for p in net.parameters() if p.requires_grad]
    else:
        params = net.params
    norm = torch.sqrt(sum(torch.sum((p.grad ** 2)) for p in params))
    if norm > theta:
        for param in params:
            param.grad[:] *= theta / norm
# 检查预训练权重文件是否存在
pretrained_weights_path = 'TransformerV5-bgetoken.pth'
if os.path.exists(pretrained_weights_path):
    model.load_state_dict(torch.load(pretrained_weights_path))
else:
    # 使用xavier_uniform_初始化模型权重
    def init_weights(m):
        if isinstance(m, (nn.Linear, nn.Conv1d)):
            nn.init.xavier_uniform_(m.weight)
    model.apply(init_weights)
class MaskedSoftmaxCELoss(nn.CrossEntropyLoss):
    """带遮蔽的softmax交叉熵损失函数"""
    # pred的形状：(batch_size,num_steps,vocab_size)
    # label的形状：(batch_size,num_steps)
    # attention_mask的形状：(batch_size,num_steps)
    def forward(self, pred, label, attention_mask):
        self.reduction='none'
        unweighted_loss = super(MaskedSoftmaxCELoss, self).forward(
			pred.permute(0, 2, 1), label)
        weighted_loss = (unweighted_loss * attention_mask).mean(dim=1)
        # 在这个场景中，l 是一个包含了每个序列元素损失的张量，其形状与输入序列 Y 
        # 中的元素数量相匹配。由于损失是在每个元素上单独计算的，我们需要将这些损
        # 失合并成一个标量值，以便进行反向传播。
        return weighted_loss.sum()
class TextGenerator(nn.Module):
    """A callback to generate text from a trained model.
    1. Feed some starting prompt to the model
    2. Predict probabilities for the next token
    3. Sample the next token and add it to the next input
    Arguments:
        max_tokens: Integer, the number of tokens to be generated after prompt.
        start_tokens: List of integers, the token indices for the starting prompt.
        index_to_word: List of strings, obtained from the TextVectorization layer.
        top_k: Integer, sample from the `top_k` token predictions.
        print_every: Integer, print after this many epochs.
    """
    def __init__(
        self, max_tokens, start_tokens, top_k=10, print_every=1, device="cpu", maxlen=280
    ):
        self.max_tokens = max_tokens
        self.start_tokens = start_tokens
#         self.index_to_word = index_to_word
        self.print_every = print_every
        self.k = top_k
        self.device = device
        self.maxlen = maxlen
    def sample_from(self, logits):
        logits, indices = torch.topk(logits, k=self.k, sorted=True)
        logits = logits.to(self.device)
        indices = indices.to(self.device)
        indices = np.asarray(indices).astype("int32")
   
        softmax=nn.Softmax(dim=0)
        preds = softmax(logits)
        preds = np.asarray(preds).astype("float32")
#         return np.random.choice(indices, p=preds) THIS IS THE CORRECT CODE, BUT HAD TO COMMENT IT AS
#.        PROBABILITIES HAVE NAN AND I HAD TO VERIFY PIPELINE, BELOW LINE WILL BE REMOVED ONCE NAN ISSUE 
#.        IS RESOLVED
        return np.random.choice(indices, p=preds)
#         return np.random.choice(5, 1, p=[0.1, 0, 0.3, 0.6, 0])
    def detokenize(self, number):
        return tokenizer.decode(number)
    def on_epoch_end(self, epoch, logs=None):
        start_tokens = [_ for _ in self.start_tokens]
        if (epoch + 1) % self.print_every != 0:
            return
        num_tokens_generated = 0
        tokens_generated = []
        while num_tokens_generated <= self.max_tokens:
            pad_len = self.maxlen - len(start_tokens)
            sample_index = len(start_tokens) - 1
            if pad_len < 0:
                data = start_tokens[:self.maxlen]
                sample_index = self.maxlen - 1
            elif pad_len > 0:
                data = start_tokens + [0] * pad_len
            else:
                data = start_tokens
          
            data = torch.Tensor(np.array([data])).type(torch.int32)
            data = data.to(device)
      
            y = model(data)
      
            sample_token = self.sample_from(y[0][sample_index])
            tokens_generated.append(sample_token)
            start_tokens.append(sample_token)
            num_tokens_generated = len(tokens_generated)
        txt = " ".join(
            [self.detokenize(_) for _ in self.start_tokens + tokens_generated]
        )
        print(f"generated text:\n{txt}\n")
max_length = max(len(encoding.tokens) for encoding in batch_dict.encodings)
start_prompt = "三体组织"
start_tokens=tokenizer(start_prompt)['input_ids']
# start_tokens = [word_to_index.get(_, 1) for _ in start_prompt.split()]
num_tokens_generated = 40
# text_gen_callback = TextGenerator(num_tokens_generated, start_tokens, vocab)
# 定义损失函数和优化器
lr, num_epochs = 0.1, 100
loss = MaskedSoftmaxCELoss()
# loss = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=lr)
# weight_decay = 1e-2
# optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
# T_0 控制余弦的周期，T_mult 是每个周期的倍数， eta_min 是学习率的最小值
scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=100, T_mult=2, eta_min=1e-7)
stop_file = 'stopme'
# 训练循环
model.train()
for epoch in trange(num_epochs, desc='Epochs', colour='blue'):
    total_loss, total_samples = 0, 0
    ifzerogredient = 0
    for i, (X, attention_mask) in enumerate(tqdm(data_iter, desc='Batch', colour='green')):
        X, attention_mask = X.to(device), attention_mask.to(device)
  
        # 分割训练和验证数据
        if i % 10 == 0:  # 每10个批次使用一个批次作为验证集
            model.eval()  # 设置为评估模式
            with torch.no_grad():
                input_ids = X[:, :-1].to(device)
                attention_mask_in = attention_mask[:, :-1].to(device)
                label_ids = X[:, 1:].to(device)
                outputs = model(input_ids, attention_mask_in)          
                # label_ids = F.one_hot(label_ids, num_classes=vocab_size).type(torch.float32)
                attention_mask_out = attention_mask[:, 1:].to(device)
                val_loss = loss(outputs, label_ids,attention_mask_out).item()
                # val_loss = loss(outputs, label_ids).item()
                # 记录到wandb
                wandb.log({"epoch*batch": epoch*i , "val_loss": val_loss})
                TextGenerator(40, start_tokens,  maxlen=max_length).on_epoch_end(epoch);
            model.train()  # 回到训练模式
            continue
        # 训练逻辑
        # if not start from cls, we add manually but in our case, already there
        # cls = torch.tensor([tokenizer.cls_token_id] * label_ids.shape[0], device=device).reshape(-1, 1)
        # input_ids = torch.cat([cls, X[:, :-1]], 1).to(device)  # 强制教学
        input_ids = X[:, :-1].to(device)
        attention_mask_in = attention_mask[:, :-1].to(device)
        # lable_ids shape (batch_size, seq_len)
        label_ids = X[:, 1:].to(device)
        # outputs shape (batch_size, seq_len, vocab_size)
        outputs = model(input_ids, attention_mask_in)
        # label_ids = F.one_hot(label_ids, num_classes=vocab_size).type(torch.float32)
        attention_mask_out = attention_mask[:, 1:].to(device)
        loss_value = loss(outputs, label_ids, attention_mask_out)
        # loss_value = loss(outputs, label_ids)
  
        # 累积梯度
        optimizer.zero_grad()
        loss_value.backward()
        grad_clipping(model, 1)
        #小批量模拟大批量梯度下降效果
        # if (ifzerogredient + 1) % 5 == 0:
        optimizer.step()
        scheduler.step()
  
            # ifzerogredient = 0
        # else:
        #     ifzerogredient += 1
        total_loss += loss_value.item() * label_ids.shape[0]
        total_samples += label_ids.shape[0]
        current_lr = scheduler.get_last_lr()[0]
        # 记录到wandb
        wandb.log({"epoch*batch": epoch*i, "train_loss": total_loss / total_samples, "learning_rate": current_lr})
        if os.path.exists(stop_file):
            print("stop file exists, stop training")      
            break  
  
    with torch.no_grad():
        TextGenerator(40, start_tokens,  maxlen=max_length).on_epoch_end(epoch);
  
    print(f"Epoch {epoch + 1}, Loss: {total_loss / total_samples}")  
    torch.save(model.state_dict(), 'TransformerV5-bgetoken.pth')
  
    if os.path.exists(stop_file):  
        break
def generate_text(decoder, device, start_string, num_tokens, num_steps=280):
    """使用给定的解码器模型和起始字符串生成文本"""
    input_tokens = tokenizer(start_string, max_length=128, padding=True, truncation=True, return_tensors='pt', add_special_tokens=False)['input_ids'].squeeze().to(device)
    # 手动添加填充
    max_length = num_steps
    pad_token_id = tokenizer.pad_token_id
    padding_length = max_length - input_tokens.shape[0]
    if padding_length > 0:
        padding = torch.full((padding_length,), pad_token_id, dtype=torch.long, device=device)
        input_tokens = torch.cat([input_tokens, padding], dim=0)
    output_tokens = []
    for _ in range(num_tokens):
        # 确保输入长度为32
        input_chunk = input_tokens[-max_length:]
  
        # 预测下一个词元
        output = decoder(input_chunk.unsqueeze(0))
        next_token_idx = output.argmax(dim=2)[:,-1]
        next_token_idx = next_token_idx.reshape((1,))
        # 仅当预测的词元不是 pad 时才添加到序列中
        if next_token_idx.item() != pad_token_id:
            input_tokens = torch.cat((input_tokens, next_token_idx), dim=0)
            output_tokens.append(next_token_idx.item())
    # 将生成的词元转换为字符串，排除 pad 词元
    generated_text = tokenizer.decode([token for token in output_tokens if token != pad_token_id], skip_special_tokens=True)
    return start_string + " " + generated_text
    # return start_string + " " + " ".join(tokenizer.convert_ids_to_tokens(output_tokens))
generate_text(model, device, "三体组织", 40)

![[Untitled 31.png]]
![[Untitled 32.png]]

\#Visualization
[CNN Explainer (poloclub.github.io)](https://poloclub.github.io/cnn-explainer/)

https://github.com/Machine-Learning-Tokyo/Interactive_Tools

![[Screenshot_2023-11-17_122039.png]]
-------------------------------------

---

### BatchNormalization

$\begin{equation}\mathrm{BN}(\mathbf{x})=\boldsymbol{\gamma} \odot \frac{\mathbf{x}-\hat{\boldsymbol{\mu}}_{\mathcal{B}}}{\hat{\boldsymbol{\sigma}}_{\mathcal{B}}}+\boldsymbol{\beta}\end{equation}$
$\begin{equation}\begin{aligned}& \hat{\boldsymbol{\mu}}_{\mathcal{B}}=\frac{1}{|\mathcal{B}|} \sum_{\mathbf{x} \in \mathcal{B}} \mathbf{x} \\& \hat{\boldsymbol{\sigma}}_{\mathcal{B}}^2=\frac{1}{|\mathcal{B}|} \sum_{\mathbf{x} \in \mathcal{B}}\left(\mathbf{x}-\hat{\boldsymbol{\mu}}_{\mathcal{B}}\right)^2+\epsilon .\end{aligned}\end{equation}$
$\begin{equation}\mathbf{h}=\phi(\mathrm{BN}(\mathbf{W} \mathbf{x}+\mathbf{b}))\end{equation}$

```Python
def batch_norm(X, gamma, beta, moving_mean, moving_var, eps, momentum):
    # 通过is_grad_enabled来判断当前模式是训练模式还是预测模式
    if not torch.is_grad_enabled():
        # 如果是在预测模式下，直接使用传入的移动平均所得的均值和方差
        X_hat = (X - moving_mean) / torch.sqrt(moving_var + eps)
    else:
        assert len(X.shape) in (2, 4)
        if len(X.shape) == 2:
            # 使用全连接层的情况，计算特征维上的均值和方差
            mean = X.mean(dim=0)
            var = ((X - mean) ** 2).mean(dim=0)
        else:
            # 使用二维卷积层的情况，计算通道维上（axis=1）的均值和方差。
            # 这里我们需要保持X的形状以便后面可以做广播运算
            mean = X.mean(dim=(0, 2, 3), keepdim=True)
            var = ((X - mean) ** 2).mean(dim=(0, 2, 3), keepdim=True)
        # 训练模式下，用当前的均值和方差做标准化
        X_hat = (X - mean) / torch.sqrt(var + eps)
        # 更新移动平均的均值和方差
        moving_mean = momentum * moving_mean + (1.0 - momentum) * mean
        moving_var = momentum * moving_var + (1.0 - momentum) * var
    Y = gamma * X_hat + beta  # 缩
```

BatchNorm vs Layer Norm

![[Untitled 33.png]]

# RNN

## 自回归模型

$P\left(x_1, \ldots, x_T\right)=\prod_{t=1}^T P\left(x_t \mid x_{t-1}, \ldots, x_1\right)$

## 马尔可夫模型

$P\left(x_1, \ldots, x_T\right)=\prod_{t=1}^T P\left(x_t \mid x_{t-1}\right) \text { 当 } P\left(x_1 \mid x_0\right)=P\left(x_1\right) \text {. }$

$\begin{aligned}P\left(x_{t+1} \mid x_{t-1}\right) & =\frac{\sum_{x_t} P\left(x_{t+1}, x_t, x_{t-1}\right)}{P\left(x_{t-1}\right)} \\& =\frac{\sum_{x_t} P\left(x_{t+1} \mid x_t, x_{t-1}\right) P\left(x_t, x_{t-1}\right)}{P\left(x_{t-1}\right)} \\& =\sum_{x_t} P\left(x_{t+1} \mid x_t\right) P\left(x_t \mid x_{t-1}\right)\end{aligned}$

## 因果关系

$P\left(x_1, \ldots, x_T\right)=\prod_{t=T}^1 P\left(x_t \mid x_{t+1}, \ldots, x_T\right)$

## 标准RNN公式和Perplexity

![[Untitled 34.png]]
$\mathbf{H}_t=\phi\left(\mathbf{X}_t \mathbf{W}_{x h}+\mathbf{H}_{t-1} \mathbf{W}_{h h}+\mathbf{b}_h\right)$
$\mathbf{O}_t=\mathbf{H}_t \mathbf{W}_{h q}+\mathbf{b}_q .$
loss 计算
$\exp \left(-\frac{1}{n} \sum_{t=1}^n \log P\left(x_t \mid x_{t-1}, \ldots, x_1\right)\right)$

```Python
/opt/workspace/GPT-SoVITS/TestAll.ipynb
import math
from nltk.lm import MLE
from nltk.lm.preprocessing import padded_everygram_pipeline
# 准备训练数据
train_text = [
    "The quick brown fox jumps over the lazy dog .",
    "The quick brown fox jumps over the lazy cat .",
    "The quick brown rat jumps over the lazy dog .",
    "The quick brown rat jumps over the lazy cat ."
]
# 将文本转化为n-gram序列
n = 3
train_data, padded_vocab = padded_everygram_pipeline(n, train_text)
# 训练最大似然估计的n-gram语言模型
model = MLE(n)
model.fit(train_data, padded_vocab)
# 待评估的句子
test_text = "The quick brown fox jumps over the lazy dog ."
# 计算句子单词的概率
def calc_prob(model, words):
    m = len(words)
    p = 1.0
    for i in range(m):
        p *= model.score(words[i], words[0:i])
    return p
# 将句子切分成单词序列
words = test_text.split()
# 计算概率和perplexity
prob = calc_prob(model, words)
perplexity = math.exp(-math.log(prob) / len(words))
print(f"概率: {prob:.8f}")
print(f"Perplexity: {perplexity:.4f}")
```

## 門控循環單元（GRU)

![[Untitled 35.png]]
$\begin{equation}\begin{aligned}\boldsymbol{R}_t & =\sigma\left(\boldsymbol{X}_t \boldsymbol{W}_{x r}+\boldsymbol{H}_{t-1} \boldsymbol{W}_{h r}+\boldsymbol{b}_r\right), \\\boldsymbol{Z}_t & =\sigma\left(\boldsymbol{X}_t \boldsymbol{W}_{x z}+\boldsymbol{H}_{t-1} \boldsymbol{W}_{h z}+\boldsymbol{b}_z\right) \\\tilde{\boldsymbol{H}}_t & =\tanh \left(\boldsymbol{X}_t \boldsymbol{W}_{x h}+\left(\boldsymbol{R}_t \odot \boldsymbol{H}_{t-1}\right) \boldsymbol{W}_{h h}+\boldsymbol{b}_h\right) \\\boldsymbol{H}_t & =\boldsymbol{Z}_t \odot \boldsymbol{H}_{t-1}+\left(1-\boldsymbol{Z}_t\right) \odot \tilde{\boldsymbol{H}}_t\end{aligned}\end{equation}$

### 三体小说训练

[【精选】python jieba分词及中文词频统计_python jieba一共有多少个词_Tao_Shimmer的博客-CSDN博客](https://blog.csdn.net/m0_51566872/article/details/121199795#Jieba%E5%BA%93%E7%AE%80%E4%BB%8B)

```JavaScript
def tokenize_chinese(lines, token='word'):  
    """将文本行拆分为单词或字符词元"""
    if token == 'word':
        return [list(jieba.cut(line)) for line in lines]
    elif token == 'char':
        return [list(line) for line in lines]
    else:
        print('错误：未知词元类型：' + token)
```

## LSTM

![[Untitled 36.png]]
$\begin{equation}\begin{aligned}\boldsymbol{I}_t & =\sigma\left(\boldsymbol{X}_t \boldsymbol{W}_{x i}+\boldsymbol{H}_{t-1} \boldsymbol{W}_{h i}+\boldsymbol{b}_i\right) \\\boldsymbol{F}_t & =\sigma\left(\boldsymbol{X}_t \boldsymbol{W}_{x f}+\boldsymbol{H}_{t-1} \boldsymbol{W}_{h f}+\boldsymbol{b}_f\right) \\\boldsymbol{O}_t & =\sigma\left(\boldsymbol{X}_t \boldsymbol{W}_{x o}+\boldsymbol{H}_{t-1} \boldsymbol{W}_{h o}+\boldsymbol{b}_o\right) \\\tilde{\boldsymbol{C}}_t & =\tanh \left(\boldsymbol{X}_t \boldsymbol{W}_{x c}+\boldsymbol{H}_{t-1} \boldsymbol{W}_{h c}+\boldsymbol{b}_c\right) \\\boldsymbol{C}_t & =\boldsymbol{F}_t \odot \boldsymbol{C}_{t-1}+\boldsymbol{I}_t \odot \tilde{\boldsymbol{C}}_t \\\boldsymbol{H}_t & =\boldsymbol{O}_t \odot \tanh \left(\boldsymbol{C}_t\right)\end{aligned}\end{equation}$

### LSTM+Informer

![[Untitled 37.png]]

```LaTeX
# My source code
/opt/workspace/researcher/Informer2020
\#github
https://github.com/zhouhaoyi/Informer2020
#在线课程
【吹爆！2024最容易出论文创新点的两个时间序列模型【LSTM】+【Informer】原理详解+项目实战+源码复现！草履虫看了都能直接提出论文创新点！】https://www.bilibili.com/video/BV11e411m7rx?vd_source=5d1bbf198c88d7ab028d1586155bbdb5
https://paperswithcode.com/paper/stock-and-market-index-prediction-using
from datetime import datetime
from keras.callbacks import EarlyStopping, ModelCheckpoint
from keras.models import Sequential # This import is for example purposes
import os
class Timer:
    # Dummy Timer class implementation
    def start(self):
        pass
class Model:
		def __init__(self):
        self.model = Sequential() # This is a placeholder for the actual model
    def compile_model(self):
        # This method should be implemented with the specific model compilation details
        # For example:
        self.model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    def train(self, x, y, epochs, batch_size, save_dir):
        timer = Timer()
        timer.start()
  
        print('[Model] Training Started')
        print('[Model] %s epochs, %s batch size' % (epochs, batch_size))
  
        save_fname = os.path.join(save_dir, '%s-e%s.h5' % (datetime.now().strftime('%d%m%Y-%H%M%S'), str(epochs)))
        callbacks = [
            EarlyStopping(monitor='val_loss', patience=2),
            ModelCheckpoint(filepath=save_fname, monitor='val_loss', save_best_only=True)
        ]
        # The rest of the training code will go here. This is usually where you would
        # compile your model, and fit it to the data using the `model.fit` method.
        # However, that part of the code is not visible in the image provided.
				# Compile the model
        self.compile_model()
        # Fit the model to the training data
        self.model.fit(x, y, batch_size=batch_size, epochs=epochs, validation_split=0.2, callbacks=callbacks)
        print('[Model] Training Completed. Model saved as %s' % save_fname)
	
# Example usage:
# Assuming `x_train`, `y_train` are your data and labels respectively
# and `save_directory` is the directory where you want to save your models.
# model_instance = Model()
# model_instance.train(x_train, y_train, epochs=10, batch_size=64, save_dir=save_directory)

ProbAttention的核心
def _prob_QK(self, Q, K, sample_k, n_top): # n_top: c*ln(L_q)
        # Q [B, H, L, D]
        B, H, L_K, E = K.shape
        _, _, L_Q, _ = Q.shape
        # calculate the sampled Q_K
        K_expand = K.unsqueeze(-3).expand(B, H, L_Q, L_K, E)
        index_sample = torch.randint(L_K, (L_Q, sample_k)) # real U = U_part(factor*ln(L_k))*L_q
        K_sample = K_expand[:, :, torch.arange(L_Q).unsqueeze(1), index_sample, :]
        Q_K_sample = torch.matmul(Q.unsqueeze(-2), K_sample.transpose(-2, -1)).squeeze(-2)
        # find the Top_k query with sparisty measurement
        M = Q_K_sample.max(-1)[0] - torch.div(Q_K_sample.sum(-1), L_K)
        M_top = M.topk(n_top, sorted=False)[1] #此处是索引
        # use the reduced Q to calculate Q_K
        Q_reduce = Q[torch.arange(B)[:, None, None],
                     torch.arange(H)[None, :, None],
                     M_top, :] # factor*ln(L_q)
        Q_K = torch.matmul(Q_reduce, K.transpose(-2, -1)) # factor*ln(L_q)*L_k
        return Q_K, M_top
```

# LoRA

[[Understanding LoRA-derived Techniques for Optimal LLM Fine-tuning]]

```LaTeX
https://magazine.sebastianraschka.com/p/practical-tips-for-finetuning-llms
如果您曾经使用 Lora 微调 LLM，并且在将适配器与基本模型合并后感到失望，那么可能会发生这种情况的原因如下：
 👉🏼 不正确的 r 和 alpha 值。这些是由微调背后的目的决定的最重要的值。
在进一步介绍 Lora 的细节之前，以下是 Lora 的通俗工作方式。（随意跳过这一部分）
假设您有一个 1000x1000 = 1,000,000 个权重的权重矩阵。
我们没有进行反向传播和修改所有 1,000,000 个权重，而是确定一个等级，假设在本例中我们选择 5。
A = 矩阵 [1000x5] B = 矩阵 [5x1000]
即使点积的尺寸与初始权重矩阵相同，也只会更改 （1000x5）+（5x1000） = 10,000 个权重，即初始权重的 0.01%。
在将这些权重与基本模型合并时，这就是 alpha 的用武之地。
我们计算合并新学习权重的比例。缩放比例越高，权重的变化就越大。
缩放 = alpha/rank（r）
权重 += （lora_B @ lora_A） * 缩放比例
 👉🏼 现在，继续我的观察：
假设您正在微调用于教学的基本模型。拥有较低的排名 （r） 值将是最好的选择，因为您希望教模型回答问题，而不是继续提出问题。具有较低的 r 值不仅可以使模型利用其从预训练阶段学习的大部分知识，还可以使其响应不那么僵化。
在指导对基本模型进行微调时，低秩效果很好，在不深入研究高级概念的情况下向模型传授格式。这就像玩“我是用户，你是有用的助手！”的游戏，强调所需的输出结构。
但是，如果你正在微调一个模型以进行领域适应，或者将某种知识嵌入其中，那么拥有更高的r值将是理想的，因为它会改变更多的权重。
我所说的低 r 值是指 32ish 左右，高 r 值对应于 128 左右。据我观察，实际限制是 256。
现在来到 alpha，这是一个比例因子，它基本上告诉了权重将与基础模型合并的比例。一般来说，根据我的观察，人们建议使用 alpha = 2*r，这使得新学习的权重比原始模型的权重更“响亮”或更突出。但情况并非总是如此。
我在下面链接了一个博客，其中 @rasbt 使用不同的 r 和 alpha 模型进行了多个实验。他们观察到 r = 256 和 alpha = 512 以及 r = 256 和 alpha = 128 时的峰值性能，即 0.5 的比例。此外，要记住的另一点是，alpha 是训练后可以安全降低的少数参数之一，而不会出现明显的缺点。
总之，平衡 R 和 alpha 值是关键。图层选择和数据集大小也是成功微调的主要因素，这一切都取决于微调的目的。
```

## QA-LoRA

![[Untitled 38.png]]

## VeRA

![[Untitled 39.png]]

# 贝叶斯定理

$\begin{equation}P(A \mid B)=\frac{P(B \mid A) P(A)}{P(B \mid A) P(A)+P\left(B \mid A^C\right) P\left(A^C\right)}\end{equation}$

[先验概率、后验概率、最大似然估计_h后验概率_离谱、的博客-CSDN博客](https://blog.csdn.net/u011582757/article/details/69486557)
X: 先验概率 50%
Y: 发现衣物条件下出轨概率 5%
Z: 发现衣物条件下没出轨概率 5%

$$
\begin{aligned}
\text { 后验概率 } & =\frac{X{ }^* Y}{X * Y+(1-X)^* Z} \\
& =\frac{5 \%{ }^* 50 \%}{5 \%{ }^* 50 \%+(1-5 \%) * 5 \%} \\
& =34 \%
\end{aligned}
$$

---

---

# 拉格朗日公式

$结构化风险描述的是在给定的训练集中，某个模型的泛化误差与其在该训练集上的经验误差之间的差异。它可以被写成两部分——经验性的损失函数与正则化项之和。具体的公式可以表示为：L(θ, X) = f(θ) + λg(θ)其中，f(θ) 为经验损失函数，g(θ) 为正则化项。$
$L(w,b,α) = 0.5 * ||w||^2 - ∑_(i=1)^N α_i (y_i(w · x_i + b) - 1)这里，w 和 b 为模型参数，α_i 是拉格朗日乘子。$
[如何直观地理解拉格朗日插值法？ - 知乎 (zhihu.com)](https://www.zhihu.com/question/58333118)

# 泰勒展开公式

$泰勒（Taylor）变换公式是一种数学工具，用于在给定点附近近似一个函数。对于在点 a 具有 n 阶导数的函数 f(x)，泰勒级数（或泰勒展开）可以表示为：$
$\begin{equation}f(x) = f(a) + f'(a)(x - a) + \frac{f''(a)}{2!} (x - a)^2 + \dots + \frac{f^{(n)}(a)}{n!} (x - a)^n + R_n(x)\end{equation}$
$其中，Rn(x) 是剩余项，f(n)(a) 是 f(x) 在 a 点的 n 阶导数，n! 是 n 的阶乘。$

# 对Transformer的优化变形

## StreamLLM

[GitHub - mit-han-lab/streaming-llm: Efficient Streaming Language Models with Attention Sinks](https://github.com/mit-han-lab/streaming-llm)
![[Untitled 40.png]]
--------------------

## Quantization for LLM transformer

### AWQ Activation-aware Weight Quantization

![[Untitled 41.png]]
https://github.com/mit-han-lab/llm-awq

```JavaScript
git clone https://github.com/mit-han-lab/llm-awq
cd llm-awq
conda create -n awq python=3.10 -y
conda activate awq
pip install --upgrade pip  # enable PEP 660 support
pip install -e .
```

## Look Ahead Decoding

```Python
https://github.com/hao-ai-lab/LookaheadDecoding\#use-lookahead-decoding-in-your-own-code
https://lmsys.org/blog/2023-11-21-lookahead-decoding/
# install from pip
pip install lade
# install from the source
git clone https://github.com/hao-ai-lab/LookaheadDecoding.git
cd LookaheadDecoding
pip install -r requirements.txt
pip install -e .
python minimal.py \#no Lookahead decoding
USE_LADE=1 LOAD_LADE=1 python minimal.py \#use Lookahead decoding, 1.6x speedup

import lade
lade.augment_all()
lade.config_lade(LEVEL=5, WINDOW_SIZE=7, GUESS_SET_SIZE=7, DEBUG=0) 
\#LEVEL, WINDOW_SIZE and GUESS_SET_SIZE are three important configurations (N,W,G) in lookahead decoding, please refer to our blog!
\#You can obtain a better performance by tuning LEVEL/WINDOW_SIZE/GUESS_SET_SIZE on your own device.
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name, torch_dtype=torch.float16, device_map=torch_device)
model_inputs = tokenizer(input_text, return_tensors='pt').to(torch_device)
greedy_output = model.generate(**model_inputs, max_new_tokens=1024) \#speedup obtained
```

![[Untitled 42.png]]

## **Mixture-of-Depths: Dynamically allocating compute in transformer-based language models**

```Python
https://arxiv.org/abs/2404.02258

https://github.com/epfml/llm-baselines/blob/mixture_of_depth/src/models/mod.py
/opt/workspace/researcher/llm-baselines/src/models/mod.py
import torch
import torch.nn as nn

class MoDBlock(nn.Module):
    """The Mixtures of Depth Block that dynamically which tokens to process in a block.
    To use it with the GPT2 base model, just pass the flag --mixture_of_depth to the training script.
    TODO: MLP that learns token dropping for autoregressive sampling
    """
    def __init__(self, config, block):
        super().__init__()
        self.block = block(config)  # block is att + mlp
        self.mod_router = nn.Linear(config.n_embd, 1, bias=False)
        # capacity factor is between [0,1),
        # where for 1 we recover a vanilla transformer
        # (i.e. all tokens passed through block)
        self.capacity_factor = config.mod_capacity_factor
        self.top_k = int(self.capacity_factor * config.sequence_length)
    def forward(self, x):
        # [batch_size, sequence_length, n_embd]
        B, T, C = x.shape
        # inference time optimization: sequence length can
        # be smaller than seq len during training
        top_k = min(self.top_k, int(self.capacity_factor * T))
        """STEP 1: get logits and top_k tokens"""
        # [batch_size, sequence_length, 1]
        router_logits = self.mod_router(x)
        # weights and selected tokens: [batch_size, top_k, 1]
        weights, selected_tokens = torch.topk(router_logits, top_k, dim=1, sorted=False)
        # IMPORTANT: need to sort indices to keep causal order for those tokens that
        # are processed in a block
        selected_tokens, index = torch.sort(selected_tokens, dim=1)
        weights = torch.gather(weights, dim=1, index=index)
        """STEP 2: expand indices to process batches with _reduced_ seqlen"""
        # We need to expand indices' dimensions from
        # [batch_size, top_k, 1] to [batch_size, top_k, n_embd] for gathering
        indices_expanded = selected_tokens.expand(-1, -1, C)
        # [batch_size, top_k, n_embd]
        top_k_tokens = torch.gather(x, 1, indices_expanded)
        top_k_tokens_processed = self.block(top_k_tokens)
        """STEP 3: combine results"""
        # Create a full batch index array,
        # then update the original array
        batch_indices = torch.arange(B).view(B, 1).expand(-1, top_k)
        result = x.clone()
        result[batch_indices, selected_tokens.squeeze(-1), :] += (
            weights * top_k_tokens_processed
        )
        return result
  
对代码的具体实例解释如下：  
https://claude.ai/chat/b476b076-70f4-496b-9b8b-0a138f48b767
  
这段代码定义了一个名为`MoDBlock`的PyTorch模块,它是一个用于动态选择要在块中处理的令牌的混合深度块。以下是对该模块的详细解释:
1. `__init__`方法:
   - 初始化`MoDBlock`模块。
   - 接受两个参数:`config`(配置对象)和`block`(表示注意力和MLP的块函数)。
   - 创建一个线性层`mod_router`,用于学习令牌丢弃。
   - 从配置中获取`capacity_factor`(容量因子),它控制要处理的令牌的比例。
   - 计算`top_k`,表示要处理的令牌数量。
2. `forward`方法:
   - 接受输入张量`x`,形状为`[batch_size, sequence_length, n_embd]`。
   - 第一步:获取`router_logits`并选择`top_k`个令牌。
     - 使用`mod_router`对输入`x`进行线性变换,得到`router_logits`。
     - 使用`torch.topk`函数选择`router_logits`中`top_k`个最大值对应的令牌,得到`selected_tokens`和对应的权重`weights`。
     - 对`selected_tokens`进行排序,以保持因果顺序。
   - 第二步:扩展索引以处理具有减少序列长度的批次。
     - 将`selected_tokens`的维度扩展为`[batch_size, top_k, n_embd]`,得到`indices_expanded`。
     - 使用`torch.gather`函数根据`indices_expanded`从`x`中收集`top_k`个令牌,得到`top_k_tokens`。
     - 将`top_k_tokens`传递给`block`进行处理,得到`top_k_tokens_processed`。
   - 第三步:组合结果。
     - 创建一个完整的批次索引数组`batch_indices`。
     - 克隆输入`x`得到`result`。
     - 使用`batch_indices`和`selected_tokens`更新`result`中对应位置的值,将处理后的令牌乘以权重并加到原始值上。
   - 返回更新后的`result`。
`MoDBlock`的目的是在处理令牌时引入动态选择机制。通过学习`mod_router`的权重,模块可以自适应地决定哪些令牌应该被处理。这样可以减少计算量并提高效率,特别是在处理长序列时。`capacity_factor`参数控制要处理的令牌的比例,允许在计算效率和模型性能之间进行权衡。
总的来说,`MoDBlock`通过动态选择要处理的令牌来优化Transformer模型的计算,同时尽可能保持模型的性能。
```

# llama介绍

![[Untitled 43.png]]

## LLAMA unique feature

```Python

https://www.youtube.com/watch?v=Mn_9W1nCFLo
# create llma from scratch
https://www.youtube.com/watch?v=oM4VmoabDAI
RMSNormal
Rotary PE
Group Multi-Query Attention
SwiGLU
Inference strategies:
Greedy -> Argmax
Beam Search -> Top k until the best one
Temperature
Random Sampling
Top K
Top P  <= Llama 
```

![[Untitled 44.png]]
![[Untitled 45.png]]
![[Untitled 46.png]]
![[Untitled 47.png]]
![[Untitled 48.png]]

## Finetuning LLama

```Python
/opt/workspace/llama/finetuning/gpt_oracle_trainer.ipynb
# Split the data into train and test sets, with 90% in the train set
train_df = df.sample(frac=0.9, random_state=42)
test_df = df.drop(train_df.index)
# Save the dataframes to .jsonl files
train_df.to_json('train.jsonl', orient='records', lines=True)
test_df.to_json('test.jsonl', orient='records', lines=True)
%pip install -q accelerate==0.21.0 peft==0.4.0 bitsandbytes==0.40.2 transformers==4.31.0 trl==0.4.7
import os
import torch
from datasets import load_dataset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
    HfArgumentParser,
    TrainingArguments,
    pipeline,
    logging,
)
from peft import LoraConfig, PeftModel
from trl import SFTTrainer
# Load datasets
train_dataset = load_dataset('json', data_files='train.jsonl', split="train")
valid_dataset = load_dataset('json', data_files='test.jsonl', split="train")
# Preprocess datasets
train_dataset_mapped = train_dataset.map(lambda examples: {'text': [f'[INST] <<SYS>>\n{system_message.strip()}\n<</SYS>>\n\n' + prompt + ' [/INST] ' + response for prompt, response in zip(examples['prompt'], examples['response'])]}, batched=True)
valid_dataset_mapped = valid_dataset.map(lambda examples: {'text': [f'[INST] <<SYS>>\n{system_message.strip()}\n<</SYS>>\n\n' + prompt + ' [/INST] ' + response for prompt, response in zip(examples['prompt'], examples['response'])]}, batched=True)
compute_dtype = getattr(torch, bnb_4bit_compute_dtype)
bnb_config = BitsAndBytesConfig(
    load_in_4bit=use_4bit,
    bnb_4bit_quant_type=bnb_4bit_quant_type,
    bnb_4bit_compute_dtype=compute_dtype,
    bnb_4bit_use_double_quant=use_nested_quant,
)
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    quantization_config=bnb_config,
    device_map=device_map
)
model.config.use_cache = False
model.config.pretraining_tp = 1
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
tokenizer.pad_token = tokenizer.eos_token
tokenizer.padding_side = "right"
peft_config = LoraConfig(
    lora_alpha=lora_alpha,
    lora_dropout=lora_dropout,
    r=lora_r,
    bias="none",
    task_type="CAUSAL_LM",
)
# ValueError: paged_adamw_32bit is not a valid OptimizerNames, please select one of 
# ['adamw_hf', 'adamw_torch', 'adamw_torch_fused', 'adamw_torch_xla', 'adamw_apex_fused',
# 'adafactor', 'adamw_bnb_8bit', 'adamw_anyprecision', 'sgd', 'adagrad']
# Set training parameters
training_arguments = TrainingArguments(
    output_dir=output_dir,
    num_train_epochs=num_train_epochs,
    per_device_train_batch_size=per_device_train_batch_size,
    gradient_accumulation_steps=gradient_accumulation_steps,
    optim=optim,
    save_steps=save_steps,
    logging_steps=logging_steps,
    learning_rate=learning_rate,
    weight_decay=weight_decay,
    fp16=fp16,
    bf16=bf16,
    max_grad_norm=max_grad_norm,
    max_steps=max_steps,
    warmup_ratio=warmup_ratio,
    group_by_length=group_by_length,
    lr_scheduler_type=lr_scheduler_type,
    report_to="all",
    evaluation_strategy="steps",
    eval_steps=5  # Evaluate every 20 steps
)
# Set supervised fine-tuning parameters
trainer = SFTTrainer(
    model=model,
    train_dataset=train_dataset_mapped,
    eval_dataset=valid_dataset_mapped,  # Pass validation dataset here
    peft_config=peft_config,
    dataset_text_field="text",
    max_seq_length=max_seq_length,
    tokenizer=tokenizer,
    args=training_arguments,
    packing=packing,
)
trainer.train()
trainer.model.save_pretrained(new_model)
from transformers import pipeline
prompt = f"[INST] <<SYS>>\n{system_message}\n<</SYS>>\n\nWhat might a typical training YAML look like?/INST]" # replace the command here with something relevant to your task
num_new_tokens = 250  # change to the number of new tokens you want to generate
# Count the number of tokens in the prompt
num_prompt_tokens = len(tokenizer(prompt)['input_ids'])
# Calculate the maximum length for the generation
max_length = num_prompt_tokens + num_new_tokens
gen = pipeline('text-generation', model=model, tokenizer=tokenizer, max_length=max_length)
result = gen(prompt)
print(result[0]['generated_text'].replace(prompt, '').split('<</response>>')[0])
model_path = "./llama-2-7b-custom"  # change to your preferred path
# Reload model in FP16 and merge it with LoRA weights
base_model = AutoModelForCausalLM.from_pretrained(
    model_name,
    low_cpu_mem_usage=True,
    return_dict=True,
    torch_dtype=torch.float16,
    device_map=device_map,
)
model = PeftModel.from_pretrained(base_model, new_model)
model = model.merge_and_unload()
# Reload tokenizer to save it
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
tokenizer.pad_token = tokenizer.eos_token
tokenizer.padding_side = "right"
# Save the merged model
model.save_pretrained(model_path)
tokenizer.save_pretrained(model_path)
```

### Merge Perf lora checkpoint to pretrained base model

```Python
https://stackoverflow.com/questions/76764265/combine-base-model-with-my-peft-adapters-to-generate-new-model
Try this. Basic steps are to:
1/ load the base model
2/ train the base model
3/ save the LoRA adapter
4/ reload the base model at half/full precision
5/ merge the LoRA weights with the base model
6/ save
base_model = AutoModelForCausalLM.from_pretrained(“base_model”, load_in_8bit=True, torch_dtype=torch.float16, device_map=“auto”)
base_model = prepare_model_for_int8_training(base_model)
peft_model = get_peft_model(base_model, peft_config)
training_args = TrainingArguments()
trainer = Trainer()
trainer.train()
peft_model.save_pretrained(lora_adapter, save_adapter=True, save_config=True)
model_to_merge = PeftModel.from_pretrained(AutoModelForCausalLM.from_pretrained(base_model).to(“cuda”), lora_adapter)
merged_model = model_to_merge.merge_and_unload()
merged_model.save_pretrained(merged_model)
```

[[merge_adapters.py]]

# SwinTransformer

https://github.com/microsoft/Swin-Transformer
![[Untitled 49.png]]

```Python
# 课程
https://www.bilibili.com/video/BV1ZG411v7rU/?p=12&spm_id_from=pageDriver
\#github
https://github.com/microsoft/Swin-Transformer/blob/main/get_started.md
#一次性生成 QKV
def forward(self, x, mask=None):
    """
    Args:
        x: input features with shape of (num_windows*B, N, C)
        mask: (0/-inf) mask with shape of (num_windows, Wh*Ww, Wh*Ww) or None
    """
    B_, N, C = x.shape
    qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
    print(qkv.shape)
  
    q, k, v = qkv[0], qkv[1], qkv[2]
    print(q.shape)
    print(k.shape)
    print(v.shape)
  
    q = q * self.scale
    attn = (q @ k.transpose(-2, -1))
    print(attn.shape)
    # ... 可能的代码继续处理attn和v以生成输出 ...
Make sure you have latest timm version
pip install --upgrade timm
\#pls verify
/opt/workspace/researcher/Swin-Transformer/swinTF_test.ipynb
import os
import torch
import torchvision.datasets as datasets
root = 'data/imagenet'
def get_imagenet(root, train = True, transform = None, target_transform = None):
    if train:
        root = os.path.join(root, 'train')
    else:
        root = os.path.join(root, 'val')
    return datasets.ImageFolder(root = root,
                               transform = transform,
                               target_transform = target_transform)
#\#--------------------------------------------------------
def evaluate_loss(data_iter, net, devices):
    l_sum, n = 0.0, 0
    for features, labels in data_iter:
        features, labels = features.to(devices[0]), labels.to(devices[0])
        outputs = net(features)
        l = loss(outputs, labels)
        l_sum += l.sum()
        n += labels.numel()
    return (l_sum / n).to('cpu')
model = timm.create_model('swin_tiny_patch4_window7_224', pretrained=True, num_classes=120)
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)
loss = nn.CrossEntropyLoss(reduction='none')
# Training
# loss function
criterion = nn.CrossEntropyLoss()
# optimizer
optimizer = optim.Adam(model.parameters(), lr=lr)
# scheduler
scheduler = StepLR(optimizer, step_size=1, gamma=gamma)
for epoch in range(epochs):
    for data, label in tqdm(train_loader):
  
        data = data.to(device)
        label = label.to(device)
        output = model(data)
        loss = criterion(output, label)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        acc = (output.argmax(dim=1) == label).float().mean()
        epoch_accuracy += acc / len(train_loader)
        epoch_loss += loss / len(train_loader)
\#Example
https://qiita.com/Kentea/items/b0e3ae03834d65f6ca41
https://www.kaggle.com/code/pokmppokmp/train-swin384-models
!!!!Swin Transformer实战：使用 Swin Transformer实现图像分类。
https://zhuanlan.zhihu.com/p/507176783
\#Model download
https://www.kaggle.com/datasets/tanlikesmath/swin-transformer/download?datasetVersionNumber=3
https://www.kaggle.com/datasets/tanlikesmath/swin-transformer/data
https://github.com/microsoft/Swin-Transformer/blob/main/MODELHUB.md
\#imagenet subset
https://www.kaggle.com/c/imagenet-object-localization-challenge/data
https://zhuanlan.zhihu.com/p/378991398
wget http://cs231n.stanford.edu/tiny-imagenet-200.zip
```

## 加载swin model 时候遇到的问题

```JavaScript
https://blog.csdn.net/weixin_41504611/article/details/129172232
，目前没有发现下载的文件在哪里？！！
```

# Diffusion Transformer (DiT)

[[解读OpenAI Sora文生视频技术原理]]

# 强化学习（ReInforcement Learning)

[[🦜🦜🦜Reinforcement Learning Memo List]]

```Python
https://www.boyuai.com/elites/course/xVqhU42F5IDky94x/video/6uJI-xIs4wqMU56NSbZRB
```

## 使用RL来选择Feature

```Python
https://www.notion.so/netcaster/Reinforcement-Learning-for-feature-selection-ab3aeb9f2ab6484892dec1308e7fe11f?pvs=4
https://github.com/blefo/FSRLearning.git
pip install FSRLearning
\#Get the pandas DataFrame from the csv file (15 features, 690 rows)
australian_data = pd.read_csv('australian_data.csv', header=None)
\#DataFrame with the features
X = australian_data.drop(14, axis=1)
\#DataFrame with the labels
y = australian_data[14]
from FSRLearning import Feature_Selector_RL
fsrl_obj = Feature_Selector_RL(feature_number=
results = fsrl_obj.fit_predict(X, y)
fsrl_obj.compare_with_benchmark(X, y, results)
fsrl_obj.get_plot_ratio_exploration()
fsrl_obj.get_feature_strengh(results)
fsrl_obj.get_depth_of_visited_states()
```

# 深度学习程序集

## 数据早期分析

```Python
Loan Default Prediction Dataset
https://www.kaggle.com/datasets/nikhil1e9/loan-default
This dataset has been taken from Coursera's Loan Default Prediction Challenge and will provide you the opportunity to tackle one of the most industry-relevant 
machine learning problems with a unique dataset that will put your modeling skills to the test. The dataset contains 255,347 rows and 18 columns in total.
import pandas as pd
import numpy as np
from autogluon.tabular import TabularDataset, TabularPredictor
train_data = TabularDataset('https://autogluon.s3.amazonaws.com/datasets/Inc/train.csv')
test_data = TabularDataset('https://autogluon.s3.amazonaws.com/datasets/Inc/test.csv')
print(train_data.head())
print(train_data.describe())
print(train_data.columns)
print(test_data.head())
print(test_data.describe())
train_data.shape
distinct_values = loaddf['Default'].unique() #列里所有值
loaddf['Default'].value_counts() #各种值的个数
# Drop the 'LoanID' column as it is not useful for the model training
loaddf.drop(columns=['LoanID'], inplace=True)
# Check for missing values in the DataFrame
missing_values = loaddf.isnull().sum()
# check missing value
df.isna().sum()
#丢弃掉有大量miss value的列
null_sum = data.isnull().sum()
data.columns[null_sum < len(data) * 0.3]
data.drop(columns=data.columns[null_sum > len(data) * 0.3], inplace=True)
# check outcome distribution
df['output'].value_counts()
predictor = TabularPredictor(label='class').fit(train_data, time_limit=120)  # Fit models for 120s
leaderboard = predictor.leaderboard(test_data)
predictor.fit_summary(show_plot=True)
train_data.columns
df = pd.DataFrame(train_data)
df.info()
list(set(df.dtypes.tolist()))
df_num = df.select_dtypes(include = ['float64', 'int64'])
df_num.head()
df_num.hist(figsize=(16, 20), bins=50, xlabelsize=8, ylabelsize=8);
Id, label = 'Id', 'SalePrice'
predictor = TabularPredictor(label=label).fit(
  train_data.drop(columns=[Id]),
	hyperparameters='multimodal',
	# ag_args_fit={'num_gpus': 1},
	num_stack_levels=1, num_bag_folds=5)
score_df = df_num[['Score']]
score_df = score_df[score_df['Score'] <= 1]
score_df.describe() #大致数据分布
import matplotlib.pyplot as plt
# plot the histogram
fig, ax = plt.subplots(figsize=(20, 16))
n, bins, patches = ax.hist(score_df, bins=100, density=False, histtype='bar', color='blue', alpha=0.75, orientation='horizontal')
ax.set_xlabel('Frequency')
ax.set_ylabel('Score')
ax.set_title('Histogram of Score')
ax.set_yticks(bins)
plt.show()
correlation_matrix = df_num[['Score', 'SubmissionCount']].corr()
import seaborn as sns
plt.figure(figsize=(8, 6))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm')
plt.title('Correlation Between Score and SubmissionCount')
plt.show()
correlation_coefficient = correlation_matrix.loc['Score', 'SubmissionCount']
correlation_coefficient

```

```Python
# Number of features to plot
num_features = len(train.columns) - 2  # Excluding the first and last columns
# Define the layout of subplots (rows x columns). Adjust as needed.
num_cols = 4  # Example: 4 rows
num_rows = (num_features + num_cols - 1) // num_cols  # Calculate columns needed
# Create a figure and a grid of subplots
fig, axes = plt.subplots(num_rows, num_cols, figsize=(20, 15))
# Flatten the axes array for easy iteration
axes = axes.flatten()
# Loop through each feature and plot
for i, col in enumerate(train.columns[1:-1]):
    sns.histplot(train[col], kde=True, ax=axes[i])
    axes[i].set_title(col)
# Hide any unused subplots
for i in range(num_features, len(axes)):
    axes[i].set_visible(False)
# Adjust layout
plt.tight_layout()
# Show plot
plt.show()
```

```Python
/opt/workspace/GPT-SoVITS/SimplebirdTest.ipynb
```

### 利用PandaAI来做初始分析

```Python
/opt/workspace/raylab/PlayPandasAI.ipynb
```

### 数据的相关性

```Python
比皮尔逊算法更好的
https://medium.com/towards-data-science/a-new-coefficient-of-correlation-64ae4f260310
造成这种差异的原因可能有以下几点:
皮尔逊相关系数假设变量之间存在线性关系,而Xicor方法对非线性关系更为敏感。如果两个变量之间的关系不是完全线性的,
那么皮尔逊相关系数可能会低估它们之间的关联程度。
皮尔逊相关系数对异常值更加敏感,而Xicor方法对异常值的处理更为稳健。如果数据中存在一些极端值,它们可能会对皮尔逊相关系数产生较大影响。
样本量的大小和数据分布的形状也会影响相关系数的估计。不同的方法在不同的数据特征下表现可能有所不同。
```

```Python
from sklearn.datasets import load_iris
from scipy.stats import pearsonr
import numpy as np
from numpy import array, random, arange
def xicor(X, Y, ties=True):
    random.seed(42)
    n = len(X)
    order = array([i[0] for i in sorted(enumerate(X), key=lambda x: x[1])])
    if ties:
        l = array([sum(y >= Y[order]) for y in Y[order]])
        r = l.copy()
        for j in range(n):
            if sum([r[j] == r[i] for i in range(n)]) > 1:
                tie_index = array([r[j] == r[i] for i in range(n)])
                r[tie_index] = random.choice(r[tie_index] - arange(0, sum([r[j] == r[i] for i in range(n)])), sum(tie_index), replace=False)
        return 1 - n*sum( abs(r[1:] - r[:n-1]) ) / (2*sum(l*(n - l)))
    else:
        r = array([sum(y >= Y[order]) for y in Y[order]])
        return 1 - 3 * sum( abs(r[1:] - r[:n-1]) ) / (n**2 - 1)
  
  
data = load_iris()
X = data.data[:, 0]  # 萼片长度
Y = data.data[:, 1]  # 萼片宽度
corr, _ = pearsonr(X, Y)
print(f"皮尔逊相关系数: {corr:.3f}")
corr_xicor = xicor(X, Y)
print(f"Xicor相关系数: {corr_xicor:.3f}")
  
皮尔逊相关系数: -0.118
Xicor相关系数: 0.220
```
### Sankey 图(包含数据流动和比例)
[[Visualise a Confusion Matrix Using Sankey Diagram]]

## 数据的预处理

```Python
train_data = pd.read_csv(download('kaggle_house_train'))
print(train_data.iloc[0:4, [0, 1, 2, 3, -3, -2, -1]])
all_features = pd.concat((train_data.iloc[:, 1:-1], test_data.iloc[:, 1:]))
# 若无法获得测试数据，则可根据训练数据计算均值和标准差
numeric_features = all_features.dtypes[all_features.dtypes != 'object'].index
all_features[numeric_features] = all_features[numeric_features].apply(
    lambda x: (x - x.mean()) / (x.std()))
# 计算各列的平均值
# mean_values = all_features[numeric_features].mean()
# 在标准化数据之后，所有均值消失，因此我们可以将缺失值设置为0
all_features[numeric_features] = all_features[numeric_features].fillna(0)
# all_features[numeric_features] = all_features[numeric_features].fillna(mean_values)
all_features
# “Dummy_na=True”将“na”（缺失值）视为有效的特征值，并为其创建指示符特征
all_features = pd.get_dummies(all_features, dummy_na=True)
n_train = train_data.shape[0]
# print(all_features[:n_train].dtypes)
# print(all_features[:n_train].values.dtype)
all_features = all_features.astype('float64')
print(all_features[:n_train].values.dtype)
all_features
train_features = torch.tensor(all_features[:n_train].values, dtype=torch.float32).to('cuda')
test_features = torch.tensor(all_features[n_train:].values, dtype=torch.float32).to('cuda')
train_labels = torch.tensor(
    train_data.SalePrice.values.reshape(-1, 1), dtype=torch.float32).to('cuda')
print(test_features)
print(train_labels)
# 对于数值字段进行正则化，去除Target Label列
numeric_features = loaddf.columns[(loaddf.dtypes != 'object') & (loaddf.columns != 'Default')]
loaddf[numeric_features] = loaddf[numeric_features].apply(
    lambda x: (x - x.mean()) / (x.std()))
# 计算各列的平均值
# mean_values = all_features[numeric_features].mean()
# 在标准化数据之后，所有均值消失，因此我们可以将缺失值设置为0。也可以是平均值
loaddf[numeric_features] = loaddf[numeric_features].fillna(0)
# all_features[numeric_features] = all_features[numeric_features].fillna(mean_values)
loaddf.head()
#对string和object列进行处理，Label Encoding 或 One-Hot Encoding
categorical_features = loaddf.columns[loaddf.dtypes == 'object']
df_encoded = pd.get_dummies(loaddf, columns=categorical_features, dummy_na=True)
#都转成float64，以便计算
df_encoded = df_encoded.astype('float64')
print(df_encoded[:df_encoded.shape[0]].values.dtype)
```

### 截断和填充

```Python
num_steps = 500 # 序列⻓度
train_features = torch.tensor([d2l.truncate_pad(
vocab[line], num_steps, vocab['<pad>']) for line in train_tokens])
print(train_features.shape)
```

### PowerTransformer, OrdinalEncoder, PyOD

```JavaScript
Mastering Customer Segmentation with LLM
https://towardsdatascience.com/mastering-customer-segmentation-with-llm-3d9008235f41\#3a33
机器学习：数据的准备和探索——特征提取和降维
https://stackabuse.com/k-means-elbow-method-and-silhouette-analysis-with-yellowbrick-and-scikit-learn/
六种常见数据降维方法简介及代码实现
https://zhuanlan.zhihu.com/p/159285110
机器学习：数据的准备和探索——特征提取和降维
https://zhuanlan.zhihu.com/p/290926857
import pandas as pd # dataframe manipulation
import numpy as np # linear algebra
# data visualization
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import plotly.express as px
import plotly.graph_objects as go
import seaborn as sns
import shap
# sklearn 
from sklearn.cluster import KMeans
from sklearn.preprocessing import PowerTransformer, OrdinalEncoder
from sklearn.pipeline import Pipeline
from sklearn.manifold import TSNE
from sklearn.metrics import silhouette_score, silhouette_samples, accuracy_score, classification_report
from pyod.models.ecod import ECOD
from yellowbrick.cluster import KElbowVisualizer
import lightgbm as lgb
import prince

df = pd.read_csv("train.csv", sep = ";")
df = df.iloc[:, 0:8]
pipe = Pipeline([('ordinal', OrdinalEncoder()), ('scaler', PowerTransformer())])
pipe_fit = pipe.fit(df)
data = pd.DataFrame(pipe_fit.transform(df), columns = df.columns)
data
--------------------------------------------------------
from pyod.models.ecod import ECOD
clf = ECOD()
clf.fit(data)
outliers = clf.predict(data) 
data["outliers"] = outliers
# Data without outliers
data_no_outliers = data[data["outliers"] == 0]
data_no_outliers = data_no_outliers.drop(["outliers"], axis = 1)
# Data with Outliers
data_with_outliers = data.copy()
data_with_outliers = data_with_outliers.drop(["outliers"], axis = 1)
print(data_no_outliers.shape) -> (40691, 8)
print(data_with_outliers.shape) -> (45211, 8)
--------------------------------------------------------
\#Matplotlib cannot find basic fonts
import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'DeJavu Serif'
plt.rcParams['font.serif'] = ['Times New Roman']

-- embs is array list, emb[0] is tensor sharpe is (1,192) 
-- and we need to change to ( {wav file count}, 192}
for i in range(len(embs)):
    embs[i] = embs[i].reshape(-1)
  
embs_new = np.array(embs)
from yellowbrick.cluster import KElbowVisualizer, SilhouetteVisualizer
plt.rcParams['font.family'] = 'DeJavu Serif'
plt.rcParams['font.serif'] = ['Times New Roman']
# Instantiate the clustering model and visualizer
km = KMeans(init="k-means++", random_state=42, n_init="auto")
visualizer = KElbowVisualizer(km, k=(2,9), metric='silhouette', timings=False, locate_elbow=True)
# visualizer = KElbowVisualizer(km, k=(2,9), metric='calinski_harabasz', locate_elbow=True )
# visualizer = KElbowVisualizer(km, k=(2,10),  metric='distortion',locate_elbow=True )
visualizer.fit(embs_new)        # Fit the data to the visualizer
visualizer.show()
visualizer.elbow_value_
model_4clust = KMeans(n_clusters = 2,  random_state=42, n_init="auto")
sil_visualizer = SilhouetteVisualizer(model_4clust)
sil_visualizer.fit(embs_new)  
sil_visualizer.show()
from sklearn.metrics import davies_bouldin_score, silhouette_score, silhouette_samples
import matplotlib.cm as cm
def make_Silhouette_plot(X, n_clusters):
    plt.xlim([-0.1, 1])
    plt.ylim([0, len(X) + (n_clusters + 1) * 10])
    clusterer = KMeans(n_clusters=n_clusters, max_iter = 1000, n_init = 10, init = 'k-means++', random_state=10)
    cluster_labels = clusterer.fit_predict(X)
    silhouette_avg = silhouette_score(X, cluster_labels)
    print(
        "For n_clusters =", n_clusters,
        "The average silhouette_score is :", silhouette_avg,
    )
# Compute the silhouette scores for each sample
    sample_silhouette_values = silhouette_samples(X, cluster_labels)
    y_lower = 10
    for i in range(n_clusters):
        ith_cluster_silhouette_values = sample_silhouette_values[cluster_labels == i]
        ith_cluster_silhouette_values.sort()
        size_cluster_i = ith_cluster_silhouette_values.shape[0]
        y_upper = y_lower + size_cluster_i
        color = cm.nipy_spectral(float(i) / n_clusters)
        plt.fill_betweenx(
            np.arange(y_lower, y_upper),
            0,
            ith_cluster_silhouette_values,
            facecolor=color,
            edgecolor=color,
            alpha=0.7,
        )
        plt.text(-0.05, y_lower + 0.5 * size_cluster_i, str(i))
        y_lower = y_upper + 10
        plt.title(f"The Silhouette Plot for n_cluster = {n_clusters}", fontsize=26)
        plt.xlabel("The silhouette coefficient values", fontsize=24)
        plt.ylabel("Cluster label", fontsize=24)
        plt.axvline(x=silhouette_avg, color="red", linestyle="--")
        plt.yticks([])  
        plt.xticks([-0.1, 0, 0.2, 0.4, 0.6, 0.8, 1])
range_n_clusters = list(range(2,9))
for n_clusters in range_n_clusters:
    print(f"N cluster: {n_clusters}")
    make_Silhouette_plot(embs_new, n_clusters)   
    plt.savefig('Silhouette_plot_{}.png'.format(n_clusters))
    plt.close()

# Elbow Method to find the optimal number of clusters
sse = []  # Sum of Squared Errors
cluster_range = range(1, 10)  # Testing from 1 to 9 clusters

for k in cluster_range:
    kmeans = KMeans(n_clusters=k, random_state=0)
    kmeans.fit(df[['comp1', 'comp2']])
    sse.append(kmeans.inertia_)
```

### KMeans Speedup with Faiss

```Python
 Faiss is roughly 20x faster than KMeans from Sklearn, which is an insane speedup
 
 import faiss
 kmeans = faiss.Kmeans(d=1024, k=8）
 kmeans.train(x_train)
 
 vs
 
 from sklearn.cluster import KMeans
 kmeans = KMeans(8).fit(x_train)
```

```Python
# 对于数值字段进行正则化，去除Target Label列
numeric_features = loaddf.columns[(loaddf.dtypes != 'object') & (loaddf.columns != 'Default')]
loaddf[numeric_features] = loaddf[numeric_features].apply(
    lambda x: (x - x.mean()) / (x.std()))
# 计算各列的平均值
# mean_values = all_features[numeric_features].mean()
# 在标准化数据之后，所有均值消失，因此我们可以将缺失值设置为0。也可以是平均值
loaddf[numeric_features] = loaddf[numeric_features].fillna(0)
# all_features[numeric_features] = all_features[numeric_features].fillna(mean_values)
loaddf.head()
#对string和object列进行处理，Label Encoding 或 One-Hot Encoding
categorical_features = loaddf.columns[loaddf.dtypes == 'object']
df_encoded = pd.get_dummies(loaddf, columns=categorical_features, dummy_na=True)
#都转成float64，以便计算
df_encoded = df_encoded.astype('float64')
print(df_encoded[:df_encoded.shape[0]].values.dtype)
#把数据转到GPU上变为tensor
train_features = torch.tensor(all_features[:n_train].values, dtype=torch.float32).to('cuda')
test_features = torch.tensor(all_features[n_train:].values, dtype=torch.float32).to('cuda')
train_labels = torch.tensor(
    train_data.SalePrice.values.reshape(-1, 1), dtype=torch.float32).to('cuda')
print(test_features)
print(train_labels)
#分割Train and Test 数据
train_data,eval_data = train_test_split(train,test_size=0.2,stratify=train['number'])
X = df_encoded.drop('Default', axis=1)
y = df_encoded['Default']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
X_train.shape, X_test.shape, y_train.shape, y_test.shape
```

基本下面没说什么

[How Synthetic Data Can Be Used for Large Language Models | by ODSC - Open Data Science | Sep, 2023 | Medium --- 合成数据如何用于大型语言模型 |作者：ODSC - 开放数据科学 | 2023 年 9 月 |中等的](https://odsc.medium.com/how-synthetic-data-can-be-used-for-large-language-models-21d78966271)

## 权重初始化

```Python
# 权重初始化
https://zhuanlan.zhihu.com/p/188701989
def weight_init(m):
    if isinstance(m, nn.Linear):
        nn.init.xavier_normal_(m.weight)
        nn.init.constant_(m.bias, 0)
    # 也可以判断是否为conv2d，使用相应的初始化方式 
    elif isinstance(m, nn.Conv2d):
        nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
     # 是否为批归一化层
    elif isinstance(m, nn.BatchNorm2d):
        nn.init.constant_(m.weight, 1)
        nn.init.constant_(m.bias, 0)
=============================================
### Another Sample
# 权重初始化，默认xavier
def init_network(model, method='xavier', exclude='embedding', seed=123):
    torch.manual_seed(seed)
    print(model.named_parameters())
    is_transformer = model.__class__.__name__.lower() == "transformer"
    for name, w in model.named_parameters():
        if exclude not in name:
            if 'weight' in name:
                if is_transformer:
                    num_layers = len(model.encoders) + 1
                    # 仅当模型为Transformer时使用这种初始化方式
                    torch.nn.init.normal_(w, mean=0.0, std=0.02 / math.sqrt(2 * num_layers))
                elif method == 'xavier':
                    nn.init.xavier_normal_(w)
                elif method == 'kaiming':
                    nn.init.kaiming_normal_(w)
                else:
                    nn.init.normal_(w)
            elif 'bias' in name:
                nn.init.constant_(w, 0)
            else:
                pass
```

## 数值分类

```Python
https://www.kaggle.com/code/gusthema/house-prices-prediction-using-tfdf
\#Random Forest 
# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report
# 加载Iris数据集
from sklearn.datasets import load_iris
data = load_iris()
df = pd.DataFrame(data.data, columns=data.feature_names)
df['target'] = data.target
df.head()
# 数据分布可视化
sns.pairplot(df, hue='target')
plt.show()
\#print(dataset_df['SalePrice'].describe())
\#plt.figure(figsize=(9, 8))
\#sns.distplot(dataset_df['SalePrice'], color='g', bins=100, hist_kws={'alpha': 0.4});
# 数据切分
X = df.drop('target', axis=1)
y = df['target']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
X_train.shape, X_test.shape, y_train.shape, y_test.shape
# 使用随机森林进行模型训练
rf_model = RandomForestClassifier(random_state=42)
rf_model.fit(X_train, y_train)
# 预测测试集
y_pred = rf_model.predict(X_test)
y_pred
# 模型评估
accuracy = accuracy_score(y_test, y_pred)
conf_matrix = confusion_matrix(y_test, y_pred)
class_report = classification_repo
```

#使用Autoglun training

```Python
https://auto.gluon.ai/stable/tutorials/tabular/advanced/tabular-gpu.html
train_data, test_data = train_test_split(df_encoded, test_size=0.2, stratify=df_encoded['Default'], random_state=42)
train_data = TabularDataset(train_data)
test_data = TabularDataset(test_data)
predictor = TabularPredictor(label='Default').fit(
    train_data,
	hyperparameters='multimodal',
	# ag_args_fit={'num_gpus': 1},
	num_stack_levels=1, num_bag_folds=5)
predictor.fit_summary(show_plot=True)
preds = predictor.predict(test_data.drop(columns=[id]))
preds.head()
new_data = pd.concat([test_data[:], preds], axis=1)
new_data.head()
leaderboard = predictor.leaderboard(new_data)
test_score = predictor.evaluate(test_data)
print(test_score)
#继续上一次的trainning
https://auto.gluon.ai/stable/tutorials/multimodal/advanced_topics/continuous_training.html
test_score = predictor.evaluate(test_data)
print(test_score)
predictor_2 = MultiModalPredictor.load(model_path)  # you can also use the `predictor` we assigned above
train_data_2 = train_data.drop(train_data_1.index).sample(n=subsample_size, random_state=0)
predictor_2.fit(train_data_2, time_limit=60)
```

```Python
# Re-importing the libraries and data for plotting
import matplotlib.pyplot as plt
import numpy as np
# Example numpy array
arr = np.array([1, 2, 2, 3, 3, 3, 4, 4, 5])
# Get unique values and their counts
unique_values, counts = np.unique(arr, return_counts=True)
# Plotting
plt.figure(figsize=(8, 6))
plt.bar(unique_values, counts, color='blue', alpha=0.7, label='Frequency')
plt.xlabel('Unique Values')
plt.ylabel('Frequency')
plt.title('Frequency of Unique Values in Array')
plt.xticks(unique_values)
plt.yticks(np.arange(0, max(counts)+1))
plt.legend()
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.show()
```

### 不平衡数据集技术

数字领域

```Python
SMOTE（Synthetic Minority Over-sampling Technique）算法是一种处理不平衡数据集的技术，特别用于处理分类问题中的类别不平衡。它的核心思想是通过生成合成样本来增加少数类别的样本数量，从而达到类别平衡的目的。
SMOTE算法原理
随机选择少数类样本：从少数类的样本中随机选择一个样本。
找到邻居：在少数类样本中找到该样本的K个最近邻居（K通常是用户指定的参数）。
生成合成样本：对于每一个邻居，根据以下公式生成新的合成样本：
新样本=原样本+(邻居样本−原样本)×随机数
其中，随机数是在0到1之间的随机数。
重复步骤：重复以上步骤直到达到期望的类别平衡
```

```Python
pip install imbalanced-learn
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from imblearn.over_sampling import SMOTE
from collections import Counter
# 生成2D不平衡数据集
\#weights=[0.1, 0.9]  这里控制数据不均衡度
X, y = make_classification(n_classes=2, n_features=2, n_redundant=0, n_informative=2,
                           n_clusters_per_class=1, weights=[0.1, 0.9], n_samples=200, random_state=42)
# 应用SMOTE
sm = SMOTE(random_state=42)
X_res, y_res = sm.fit_resample(X, y)
# 可视化
plt.figure(figsize=(12, 5))
# 原始数据集
plt.subplot(1, 2, 1)
plt.scatter(X[y == 0][:, 0], X[y == 0][:, 1], label="Class 0", alpha=0.5)
plt.scatter(X[y == 1][:, 0], X[y == 1][:, 1], label="Class 1", alpha=0.5)
plt.title("Original Dataset")
plt.xlabel("Feature 1")
plt.ylabel("Feature 2")
plt.legend()
# 经SMOTE处理后的数据集
plt.subplot(1, 2, 2)
plt.scatter(X_res[y_res == 0][:, 0], X_res[y_res == 0][:, 1], label="Class 0", alpha=0.5)
plt.scatter(X_res[y_res == 1][:, 0], X_res[y_res == 1][:, 1], label="Class 1", alpha=0.5)
plt.title("Dataset after SMOTE")
plt.xlabel("Feature 1")
plt.ylabel("Feature 2")
plt.legend()
plt.tight_layout()
plt.show()
```

图像领域使用SMOTE

```Python
对于图像数据，直接应用SMOTE算法可能并不是最佳选择，因为SMOTE是基于数值特征空间中的插值来生成新样本的，这对于像素级的图像数据可能不太适用。在图像数据的情况下，简单的像素级插值可能会导致生成的图像失真或不自然。然而，有一些方法可以在图像数据上模仿SMOTE的效果：
数据增强（Data Augmentation）
对于图像数据，一种常见的处理小样本问题的方法是数据增强。这包括旋转、缩放、翻转、裁剪、颜色变换等操作，从而从现有图像中生成新的变体。数据增强可以提高模型的泛化能力，尽管它不增加不同的样本数量，但增加了样本的多样性。
特征空间的SMOTE
另一种方法是将图像转换到特征空间（例如使用卷积神经网络（CNN）提取的特征），然后在这个特征空间上应用SMOTE。这样，合成的特征可以转换回图像空间，尽管这种方法可能需要复杂的技术来确保生成的图像是有意义的。
生成对抗网络（GAN）
生成对抗网络（GAN）可以用来生成新的图像样本。GAN通过训练一个生成网络来生成新的图像样本，同时训练一个判别网络来区分真实图像和生成的图像。这种方法可以生成高质量且多样的图像，但需要大量的计算资源和专业知识。
```

```Python
from tensorflow.keras.preprocessing.image import ImageDataGenerator
# 创建一个ImageDataGenerator实例，定义数据增强的参数
datagen = ImageDataGenerator(
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    fill_mode='nearest')
# 应用于图像数据集
# 这里假设你有一个图像数据集X和它们的标签y
# datagen.fit(X)
# 使用flow或flow_from_directory等方法来生成增强的数据
```

### RoC and AUC

```Python
https://en.wikipedia.org/wiki/Receiver_operating_characteristic
import torch
import torch.nn as nn
import torch.optim as optim
# 一个简单的二分类模型
class SimpleBinaryModel(nn.Module):
    def __init__(self):
        super(SimpleBinaryModel, self).__init__()
        self.fc = nn.Linear(10, 1)  # 假设有10个特征
    def forward(self, x):
        return self.fc(x)
# 代理损失函数 - Pairwise Ranking Loss
def pairwise_ranking_loss(y_pred, y_true):
    # 获取正负样本的预测值
    positive_preds = y_pred[y_true == 1]
    negative_preds = y_pred[y_true == 0]
    # 计算所有正负样本对的损失
    loss = 1 - torch.sigmoid(positive_preds - negative_preds.view(-1, 1))
    return loss.mean()
# 创建模型、优化器和一些模拟数据
model = SimpleBinaryModel()
optimizer = optim.Adam(model.parameters(), lr=0.001)
# 模拟一些数据
x = torch.randn(100, 10)  # 100个样本，每个样本10个特征
y = torch.randint(0, 2, (100,))  # 100个二分类标签
# 训练循环
for epoch in range(100):
    optimizer.zero_grad()
    outputs = model(x).squeeze()
    loss = pairwise_ranking_loss(outputs, y)
    loss.backward()
    optimizer.step()
    if epoch % 10 == 0:
        print(f"Epoch {epoch}, Loss: {loss.item()}")
```

### K则分类

```Python
from sklearn.model_selection import StratifiedKFold
skf = StratifiedKFold(n_splits=5,shuffle=True,random_state=2023)
prediction_df = pd.DataFrame()
for fold_n, (trn_idx, val_idx) in enumerate(skf.split(train, train['number'])):
    print(f'fold {fold_n} training...')
    train_data = train.iloc[trn_idx]
    eval_data = train.iloc[val_idx]
    trainset = Leaf_Dataset(train_data, transform = transforms_train)
    evalset = Leaf_Dataset(eval_data, transform = transforms_test)
    train_loader = torch.utils.data.DataLoader(trainset, batch_size=32, shuffle=True, drop_last=False)
    eval_loader = torch.utils.data.DataLoader(evalset, batch_size=32, shuffle=False, drop_last=False)
    predictions = train_model(train_loader, eval_loader)
    prediction_df[f'fold_{fold_n}'] = predictions
```

### 随机森林

```JavaScript
随机森林是一种基于多棵决策树的集成学习方法，它能够处理回归和分类问题。
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score
# 数据准备
X, y = make_classification(n_samples=1000, n_features=20, n_informative=15, n_redundant=5, random_state=42, weights=[0.99])
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# 模型选择
clf = RandomForestClassifier()
# 模型训练
clf.fit(X_train, y_train)
# 模型评估
y_pred = clf.predict_proba(X_test)[:, 1]
roc_auc = roc_auc_score(y_test, y_pred)
print(f'ROC AUC Score: {roc_auc}')
```

### SHAP**值**

```LaTeX
使用 SHAP 的 TreeExplainer 来解释随机森林模型的预测。TreeExplainer 是针对树模型（如决策树、随机森林、梯度提升树等）优化的解释器。
SHAP 值是一种解释模型预测的方法，基于博弈论中的Shapley值。对于每一个预测，SHAP 值解释了每个特征对于模型预测与平均预测差异的贡献。
https://towardsdatascience.com/shapley-values-clearly-explained-a7f7ef22b104
/opt/workspace/researcher/cellGenerateTest.ipynb
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
data = pd.read_csv("https://raw.githubusercontent.com/Garve/towards_data_science/main/Symbolic%20Regression/free_fall.csv")
X = data[["h", "g"]]
y = data["t"]
X_train, X_test, y_train, y_test = train_test_split(X, y, random_state=123)
rf = RandomForestRegressor(random_state=123).fit(X_train, y_train)
print(rf.score(X_test, y_test))
import shap
te = shap.TreeExplainer(rf, feature_names=["h", "g"])
shapley_values = te(X)
shap.plots.scatter(shap_values=shapley_values)
```

[shap.Explainer — SHAP latest documentation](https://shap.readthedocs.io/en/latest/generated/shap.Explainer.html)
[【Python可解释机器学习库SHAP】：Python的可解释机器学习库SHAP - 知乎 (zhihu.com)](https://zhuanlan.zhihu.com/p/483622352?utm_id=0)

```Python
# 包括了几乎有意义的Shap尝试
/opt/workspace/researcher/labTest/AllKindsTest.ipynb
```

### Load模型后预估

```Python
# DataFrame中的每一行计算众数（最常出现的数）
all_predictions = list(prediction_df.mode(axis=1)[0].astype(int))
print(all_predictions[0])
labels_unique[all_predictions[0]]

test_dataset = Leaf_Dataset(test, transform = transforms_test,test = True)
test_loader = DataLoader(dataset=test_dataset, batch_size=32)
device="cuda:0"
model_path = '/opt/workspace/app/Awesome-Backbones/data2/best_epoch_3366.pth'
model = torchvision.models.resnet50(weights = torchvision.models.ResNet50_Weights.IMAGENET1K_V1)
model = model.to(device)
in_features = model.fc.in_features
print(in_features)
model.fc = nn.Linear(in_features, 176)
model.load_state_dict(torch.load(model_path))
model = model.to(device)  # 确保模型在相同的设备上
model.eval()
predictions = []
for x in test_loader:
    with torch.no_grad():
        y_pred = model(x.to(device))
        predictions.extend(y_pred.argmax(dim=-1).cpu().numpy().tolist())
```

### **Symbolic Regression 符号回归**

```LaTeX
gplearn and PySR.
https://arxiv.org/pdf/2305.01582.pdf
https://astroautomata.com/PySR/operators/
https://julialang.org/downloads/
from gplearn.genetic import SymbolicRegressor

sr = SymbolicRegressor(
    function_set=(
        "add",
        "sub",
        "mul",
        "div",
        "sqrt",
        "log",
        "abs",
        "neg",
    ),
    random_state=0,
)
sr.fit(X_train, y_train)
==================================================
curl -fsSL https://install.julialang.org > julia.sh
sudo ./julia.sh
rsync -avl .juliaup/ /home/<USER>/.juliaup/
rsync -avl .julia/ /home/<USER>/.julia/
update .bashrc 
# >>> juliaup initialize >>>
# !! Contents within this block are managed by juliaup !!
case ":$PATH:" in
    *:/home/<USER>/.juliaup/bin:*)
        ;;
    *)
        export PATH=/home/<USER>/.juliaup/bin${PATH:+:${PATH}}
        ;;
esac
# <<< juliaup initialize <<<
and change symbolic blew
(mlearn) [raysheng@MONSTER:~/.juliaup/bin]$ ls -lrt
total 17332
-rwxr-xr-x 1 <USER> <GROUP> 12621464 Feb  3 07:41 juliaup
-rwxr-xr-x 1 <USER> <GROUP>  5123496 Feb  3 07:41 julialauncher
lrwxrwxrwx 1 raysheng raysheng       41 Feb 12 14:27 julia -> /home/<USER>/.juliaup/bin/julialauncher
(
conda activate mlearn
python3 -m pysr install
quit and reenter user
==================================================
import pysr
import pandas as pd
from pysr import PySRRegressor, julia_helpers
# 初始化 Julia 实例：
julia_helpers.init_julia()
import torch
# 读取数据
data = pd.read_csv("free_fall.csv")
X = data[["h", "g"]].values
y = data["t"].values
# 创建 PySRRegressor 对象
model = PySRRegressor(
    niterations=5,  # Number of iterations of the algorithm to run
    binary_operators=["*", "+", "-", "/", "^"],  # Mathematical operations to use
    unary_operators=[
        "square", "log", "sqrt","cos", "exp", "sin"  # Unary mathematical operations to use
    ],
)
# 拟合模型
model.fit(X, y)
# 打印发现的公式
print(model)
```

### **KAN Model**

```Python
from kan import *
# 选择设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# create a KAN: 2D inputs, 1D output, and 5 hidden neurons. cubic spline (k=3), 5 grid intervals (grid=5).
model = KAN(width=[2,5,1], grid=5, k=3, seed=0, device=device)
KAN(
  (biases): ModuleList(
    (0): Linear(in_features=5, out_features=1, bias=False)
    (1): Linear(in_features=1, out_features=1, bias=False)
  )
  (act_fun): ModuleList(
    (0-1): 2 x KANLayer(
      (base_fun): SiLU()
    )
  )
  (base_fun): SiLU()
  (symbolic_fun): ModuleList(
    (0-1): 2 x Symbolic_KANLayer()
  )
)
# create dataset f(x,y) = exp(sin(pi*x)+y^2)
f = lambda x: torch.exp(torch.sin(torch.pi*x[:,[0]]) + x[:,[1]]**2)
dataset = create_dataset(f, n_var=2)
train_input, train_label = dataset['train_input'].to(device), dataset['train_label'].to(device)
# plot KAN at initialization
model(train_input)
model.plot(beta=100)
# 将训练数据集移到 GPU
dataset_gpu = {
    'train_input': dataset['train_input'].to(device),
    'train_label': dataset['train_label'].to(device),
    'test_input': dataset['test_input'].to(device),
    'test_label': dataset['test_label'].to(device)
}
# train the model
model.train(dataset_gpu, opt="LBFGS", steps=20, lamb=0.01, lamb_entropy=10., device=device)
model.plot()
model.prune()
model.plot(mask=True)
model = model.prune()
model(dataset_gpu['train_input'])
model.plot()
model.train(dataset_gpu, opt="LBFGS", steps=50,device=device)
model.plot()
mode = "auto" # "manual"
if mode == "manual":
    # manual mode
    model.fix_symbolic(0,0,0,'sin');
    model.fix_symbolic(0,1,0,'x^2');
    model.fix_symbolic(1,0,0,'exp');
elif mode == "auto":
    # automatic mode
    lib = ['x','x^2','x^3','x^4','exp','log','sqrt','tanh','sin','abs']
    model.auto_symbolic(lib=lib)
model.train(dataset, opt="LBFGS", steps=50, device=device)
model.symbolic_formula()[0][0]
```

> [!info] A Simplified Explanation Of The New Kolmogorov-Arnold Network (KAN) from MIT
> Exploring the Next Frontier in AI: The Kolmogorov-Arnold Network (KAN)
> [https://medium.com/@isaakmwangi2018/a-simplified-explanation-of-the-new-kolmogorov-arnold-network-kan-from-mit-cbb59793a040](https://medium.com/@isaakmwangi2018/a-simplified-explanation-of-the-new-kolmogorov-arnold-network-kan-from-mit-cbb59793a040)

### Logistic 回归

```JavaScript
import numpy as np
class LogisticRegression:
    def __init__(self, learning_rate=0.01, epochs=10000):
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.W = None
        self.b = None
    def sigmoid(self, z):
        return 1 / (1 + np.exp(-z))
    def compute_cost(self, Y, Y_pred):
        m = len(Y)
        return -1/m * np.sum(Y * np.log(Y_pred) + (1 - Y) * np.log(1 - Y_pred))
    def fit(self, X, Y):
        m, n = X.shape
        self.W = np.zeros(n)
        self.b = 0
        for i in range(self.epochs):
            Y_pred = self.sigmoid(np.dot(X, self.W) + self.b)
            gradient = np.dot(X.T, (Y_pred - Y)) / m
            self.W -= self.learning_rate * gradient
            self.b -= self.learning_rate * np.sum(Y_pred - Y) / m
            if i % 1000 == 0:
                cost = self.compute_cost(Y, Y_pred)
                print(f"Iteration {i}: Cost = {cost:.4f}")
    def predict(self, X):
        Y_pred = self.sigmoid(np.dot(X, self.W) + self.b)
        return (Y_pred > 0.5).astype(int)
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
# Create dataset
X, y = make_classification(n_samples=1000, n_features=2, n_informative=2, n_redundant=0)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)
# Initialize and train the model
model = LogisticRegression(learning_rate=0.01, epochs=5000)
model.fit(X_train, y_train)
# Predict
y_pred = model.predict(X_test)
# Show some of the predictions
print(f"First 10 predictions: {y_pred[:10]}")
print(f"First 10 actual labels: {y_test[:10]}")
```

### SOM算法分类

```Python
/opt/workspace/researcher/minisom
import numpy as np
import matplotlib.pyplot as plt
%matplotlib inline
from minisom import MiniSom
from sklearn.datasets import fetch_20newsgroups
from sklearn.feature_extraction.text import TfidfVectorizer
dataset = fetch_20newsgroups(shuffle=True, random_state=1,
                             remove=('headers', 'footers', 'quotes'))
documents = dataset.data
no_features = 1000
tfidf_vectorizer = TfidfVectorizer(max_df=0.95, min_df=2,
                                   max_features=no_features,
                                   stop_words='english')
tfidf = tfidf_vectorizer.fit_transform(documents)
tfidf_feature_names = tfidf_vectorizer.get_feature_names_out()
D = tfidf.todense().tolist()
n_neurons = 2
m_neurons = 4
som = MiniSom(n_neurons, m_neurons, no_features)
som.random_weights_init(D)
som.train(D, 5000, random_order=False, verbose=True)
top_keywords = 10
weights = som.get_weights()
cnt = 1
for i in range(n_neurons):
    for j in range(m_neurons):
        keywords_idx = np.argsort(weights[i,j,:])[-top_keywords:]
        keywords = ' '.join([tfidf_feature_names[k] for k in keywords_idx])
        print('Topic', cnt, ':', keywords)
        cnt += 1
```

```Python
1. 经验法则
一般情况下，网络的尺寸应满足以下条件:
- 足侈大：使每个神经元不至于代表太多的数据点。
- 足侈小: 避免网络过大而导致难以训练或过拟合。
常用的经验公式:
```

$\begin{equation}m \approx 5 \cdot \sqrt{n}\end{equation}$

```Python
其中:
- m 是网络中的神经元总数。
- n 是数据点的数量。
在代码中，'20newsgroups $\cdot$数据集的样本总数约为 11314 个。根据经验公式:
```

$\begin{equation}m \approx 5 \cdot \sqrt{11314} \approx 53\end{equation}$

```Python
这意味着我们应该选择包含约 53 个神经元的网络。
2. 确定行数程例数
- 设定矩形网络的尺寸 (行数、列数) 以接近总神经元数。
- 例如:
- 如果常望设置 53 个神经元，可以设置 4 行 13 列或 5 行 11 列。
3. 小新模安验
在实际应用中，经常会先从一个较小的网络开始尝试，然后根据实际效果调整大小。在此例中，作者设置了:
- 行数 (n_neurons): 2
- 列数 (m_neurons): 4
```

### Feature Selection

/opt/workspace/researcher/labTest/AllKindsTest.ipynb

```Python
data = pd.concat([fed_funds_rate_select, bond_yield_20y_select, djia_select, nikkei225_select, usdjpy_select,
                  oil_price_select, gold_select ,bitcoin_select], axis=1)
data.columns = ['Fed Funds Rate', 'Bond Yield 20Y', 'DJIA', 'Nikkei 225', 'JPY/USD','Oil Price', 'Gold','BITCOIN']
# 去除包含缺失值的行
data_final = data.dropna()
feature_names = ['Fed Funds Rate', 'Bond Yield 20Y', 'Nikkei 225', 'Oil Price', 'Gold','JPY/USD','BITCOIN', 'DJIA']
X = data_final[feature_names].values
X = scale(X)
size = 15
som = MiniSom(size, size, len(X[0]),
              neighborhood_function='gaussian', sigma=1.5,
              random_seed=1)
som.pca_weights_init(X)
som.train_random(X, 5000, verbose=True)
W = som.get_weights()
feature_names = ['Fed Funds Rate', 'Bond Yield 20Y', 'Nikkei 225', 'Oil Price', 'Gold','JPY/USD','BITCOIN', 'DJIA']
plt.figure(figsize=(10, 10))
for i, f in enumerate(feature_names):
    plt.subplot(3, 3, i+1)
    plt.title(f)
    plt.pcolor(W[:,:,i].T, cmap='coolwarm')
    plt.xticks(np.arange(size+1))
    plt.yticks(np.arange(size+1))
plt.tight_layout()
plt.show()
def som_feature_selection(W, labels, target_index = 0, a = 0.04):
    """ Performs feature selection based on a self organised map trained with the desired variables
    INPUTS: W = numpy array, the weights of the map (X*Y*N) where X = map's rows, Y = map's columns, N = number of variables
            labels = list, holds the names of the variables in same order as in W
            target_index = int, the position of the target variable in W and labels
            a = float, an arbitary parameter in which the selection depends, values between 0.03 and 0.06 work well
    OUTPUTS: selected_labels = list of strings, holds the names of the selected features in order of selection
             target_name = string, the name of the target variable so that user is sure he gave the correct input
    """

    W_2d = np.reshape(W, (W.shape[0]*W.shape[1], W.shape[2])) \#reshapes W into MxN assuming M neurons and N features
    target_name = labels[target_index]

    Rand_feat = np.random.uniform(low=0, high=1, size=(W_2d.shape[0], W_2d.shape[1] - 1)) # create N -1 random features
    W_with_rand = np.concatenate((W_2d,Rand_feat), axis=1) # add them to the N regular ones
    W_normed = (W_with_rand - W_with_rand.min(0)) / W_with_rand.ptp(0) # normalize each feature between 0 and 1
    Target_feat = W_normed[:,target_index] # column of target feature
    # Two conditions to check against a
    Check_matrix1 = abs(np.vstack(Target_feat) - W_normed)
    Check_matrix2 = abs(np.vstack(Target_feat) + W_normed - 1)
    S = np.logical_or(Check_matrix1 <= a, Check_matrix2 <= a).astype(int) # applie "or" element-wise in two matrices
    S[:,target_index] = 0 \#ignore the target feature so that it is not picked
    selected_labels = []
    while True:
        S2 = np.sum(S, axis=0) # add all rows for each column (feature)
        if not np.any(S2 > 0): # if all features add to 0 kill
            break
        selected_feature_index = np.argmax(S2) # feature with the highest sum gets selected first
        if selected_feature_index > (S.shape[1] - (Rand_feat.shape[1] + 1)): # if random feature is selected kill
            break

        selected_labels.append(labels[selected_feature_index])
        # delete all rows where selected feature evaluates to 1, thus avoid selecting complementary features
        rows_to_delete = np.where(S[:,selected_feature_index] == 1)
        S[rows_to_delete, :] = 0
#     selected_labels = [label for i, label in enumerate(labels) if i in feature_indeces]
    return selected_labels, target_name
selected_features, target_name = som_feature_selection(W, feature_names, 5, 0.04)
print("Target variable: {}\nSelected features {}".format(target_name, selected_features))
============= Output =====================
Target variable: JPY/USD
Selected features ['Bond Yield 20Y', 'BITCOIN']
            
```

```Python
import xgboost as xgb
import shap
import matplotlib.pyplot as plt
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
# Load Iris dataset
iris = load_iris()
X, y = iris.data, iris.target
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# Convert to DMatrix for XGBoost
dtrain = xgb.DMatrix(X_train, label=y_train)
dtest = xgb.DMatrix(X_test, label=y_test)
# Set up parameters for XGBoost
params = {
    'max_depth': 3,
    'eta': 0.3,
    'objective': 'multi:softprob',
    'num_class': 3
}
num_rounds = 100
# Train the model
bst = xgb.train(params, dtrain, num_rounds)
# Compute SHAP values
explainer = shap.TreeExplainer(bst)
shap_values = explainer.shap_values(X_test)
# Plot Summary Plot
shap.summary_plot(shap_values, X_test, feature_names=iris.feature_names)
# Force Plot for a single prediction
shap.initjs()
display(shap.force_plot(explainer.expected_value[0], shap_values[0][0], X_test[0], feature_names=iris.feature_names))
# Dependence Plot
shap.dependence_plot("petal length (cm)", shap_values[1], X_test, feature_names=iris.feature_names, interaction_index="sepal width (cm)")
```

## 自然语言模型

```Python
import torch
from transformers import AutoTokenizer
from transformers import AutoModelForCausalLM
checkpoint = "/opt/aibase/CodeLlama-7B-Python-fp16"
tokenizer = AutoTokenizer.from_pretrained(checkpoint, trust_remote_code=True, use_fast=False)
model = AutoModelForCausalLM.from_pretrained(
    checkpoint,
    load_in_8bit=True,
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True,
)
prompt = "give me a program to train a LSTM model."
# 推論の実行
input_ids = tokenizer.encode(prompt, add_special_tokens=False, return_tensors="pt")
tokens = model.generate(
    input_ids.to(device=model.device),
    max_length=30,
    temperature=1.0,
    do_sample=True,
    pad_token_id=tokenizer.pad_token_id,
)
output = tokenizer.decode(tokens[0])
print(output)
```

### AI模型線上Play

```JavaScript
AI Model Online Play 
https://chat.lmsys.org/
https://huggingface.co/chat/
https://medium.com/gitconnected/how-i-built-my-own-language-model-in-15-minutes-and-its-totally-free-f2ba54ac52c7
```

### Youtube 上微調教學

```JavaScript
\#Youtube
Understanding 4bit Quantization: QLoRA explained (w/ Colab)
https://www.youtube.com/watch?v=TPcXVJ1VSRI&t=3s
Fine-tuning LLMs with PEFT and LoRA
https://www.youtube.com/watch?v=Us5ZFp16PaU
Boost Fine-Tuning Performance of LLM: Optimal Architecture w/ PEFT LoRA Adapter-Tuning on Your GPU
https://www.youtube.com/watch?v=A-a-l_sFtYM
Run LLAMA-v2 chat locally
https://www.youtube.com/watch?v=Kg588OVYTiw
Llama 2 Fine-Tune with QLoRA 
https://www.youtube.com/watch?v=eeM6V5aPjhk&t=8s

How To Create Datasets for Finetuning From Multiple Sources! Improving Finetunes With Embeddings.
https://www.youtube.com/watch?v=fYyZiRi6yNE

LoRA: Low-Rank Adaptation of Large Language Models - Explained visually + PyTorch code from scratch
https://www.youtube.com/watch?v=PXWYUTMt-AU
How to Fine-Tune the Alpaca Model For Any Language | ChatGPT Alternative
https://www.youtube.com/watch?v=yTROqe8T_eA
Fine-Tuning from deeplearning.ai
https://learn.deeplearning.ai/evaluating-debugging-generative-ai/lesson/6/finetuning-a-language-model
The EASIEST way to finetune LLAMA-v2 on local machine!
https://www.youtube.com/watch?v=3fsn19OI_C8
LLaMA explained: KV-Cache, Rotary Positional Embedding, RMS Norm, Grouped Multi-Query Attention
https://www.youtube.com/watch?v=Mn_9W1nCFLo
```

### 微調的一些鏈接和數據集

```JavaScript
-- run llama in light device
https://replicate.com/blog/run-llama-locally
-- mlc
https://zhuanlan.zhihu.com/p/626381058
https://zhuanlan.zhihu.com/p/634763785
-- LLM 4 位量化库（CPU、GPU）说明
https://zenn.dev/syoyo/articles/3bde98e9972dea
-- Some od code related to lama training
https://www.notion.so/netcaster/LLaMA-related-facebookresearch-llama-Inference-code-for-LLaMA-models-39e86ed6eb4d4b81a6ecf574128c03d5?pvs=4
--- memo after test!!!!
https://www.notion.so/netcaster/Chatbots-with-Llama-2-FastAPI-Celery-Redis-Docker-Towards-Data-Science-42868f59bc604259b928ece0cf339ba1?pvs=4
-- opensource chines llm and training dataset
https://github.com/HqWu-HITCS/Awesome-Chinese-LLM
-- ChatGLM funetuning
https://github.com/hiyouga/ChatGLM-Efficient-Tuning
https://aitechtogether.com/python/90699.html
-- 大语言模型LLaMA, ChatGLM, BLOOM 的高效参数微调实践
https://zhuanlan.zhihu.com/p/636491955?utm_id=0 
-- xTuring
https://github.com/stochasticai/xturing/blob/main/examples/int4_finetuning/README.md
-- Fine-Tuning a GPT - LoRA
https://dataman-ai.medium.com/fine-tune-a-gpt-lora-e9b72ad4ad3
https://dataman-ai.medium.com/fine-tune-a-gpt-prefix-tuning-13c263e73141
-- QLoRa: Fine-Tune a Large Language Model on Your GPU
https://towardsdatascience.com/qlora-fine-tune-a-large-language-model-on-your-gpu-27bed5a03e2b
-- Private GPT: Fine-Tune LLM on Enterprise Data
https://towardsdatascience.com/private-gpt-fine-tune-llm-on-enterprise-data-7e663d808e6a
\#Predibase provide AI training platform. 参考价值不大如果不用此系统
https://predibase.com/blog/how-to-fine-tune-llama-2-on-your-data-with-scalable-llm-infrastructure
End to End ML with GPT-3.5
https://towardsdatascience.com/end-to-end-ml-with-gpt-3-5-8334db3d78e2

Fine-Tune Your Own Llama 2 Model in a Colab Notebook
https://towardsdatascience.com/fine-tune-your-own-llama-2-model-in-a-colab-notebook-df9823a04a32
\#AI infra
https://medium.com/cowboy-ventures/the-new-infra-stack-for-generative-ai-9db8f294dc3f
\#AI Tuning and Generative
https://learn.deeplearning.ai/evaluating-debugging-generative-ai/lesson/1/introduction
\#LLaMA2-Accessory: An Open-source Toolkit for LLM Development
https://github.com/Alpha-VLLM/LLaMA2-Accessory
#中文儿童情感陪伴大模型“巧板”
https://github.com/HIT-SCIR-SC/QiaoBan
\#Trl training package in HF!!
https://huggingface.co/docs/trl/index
-- Fine-tune Llama 2 with DPO
https://huggingface.co/blog/dpo-trl
https://huggingface.co/blog/trl-peft
Japanese Training dataset
https://github.com/masa3141/japanese-alpaca-lora
https://github.com/llm-jp/awesome-japanese-llm
https://tech.yellowback.net/posts/transformers-japanese-models
https://dev.classmethod.jp/articles/huggingface-jp-text-classification/
https://wandb.ai/authors/enriching-words-with-subwords/reports/W-B-HuggingFace-Transformer---Vmlldzo1MDc3NzA
Japanese LLM/embedding benchmark
https://note.com/npaka/n/ndec10f78fe2f
\#Japanese NLP model
https://huggingface.co/HachiML/Llama-2-13b-hf-qlora-dolly-ja-2ep

Data set 书生
https://opendatalab.org.cn/home

Baby Llama2
https://tech.ifeng.com/c/8RgHVm6Uoqd
```

### 微調環境搭建和sample

```JavaScript
#建立finetuning环境
1. install llama
2. install xTuring
3. install others
pip install pytest
pip install Requests==2.31.0
pip install tiktoken==0.3.3
https://github.com/karpathy/llama2.c
(llama) [raysheng@MONSTER:/opt/workspace/llama/llama2.c]$ python export_meta_llama_bin.py /opt/aibase/Llama-2-7b llama2_7b.bin
{'dim': 4096, 'multiple_of': 256, 'n_heads': 32, 'n_layers': 32, 'norm_eps': 1e-05, 'vocab_size': -1}
writing tok_embeddings...
writing tok_embeddings.weight...
import os
import psutil
import timeit
from datasets import load_dataset
mem_before = psutil.Process(os.getpid()).memory_info().rss / (1024 * 1024)
wiki = load_dataset("wikipedia", "20220301.en", split="train")
mem_after = psutil.Process(os.getpid()).memory_info().rss / (1024 * 1024)
print(f"RAM memory used: {(mem_after - mem_before)} MB")

from transformers import BertTokenizer, BertForSequenceClassification
model = BertForSequenceClassification.from_pretrained("bert-base-uncased")
tokenizer = BertTokenizer.from_pretrained("bert-base-uncased")
trainings_args = TrainingArguments(
    output_dir="./results",
    overwrite_output_dir=True,
    do_train=True,
    do_eval=True,
    per_device_train_batch_size=8,
    per_device_eval_batch_size=8,
    learning_rate=1e-5,
    num_train_epochs=3,
    logging_steps=10,
    logging_dir="./logs",
    save_steps=10,
    eval_steps=10,
    evaluation_strategy="steps",
    load_best_model_at_end=True,
    metric_for_best_model="accuracy",
    greater_is_better=True,
    warmup_steps=10,
    weight_decay=0.01,
)
trainer = Trainer(
    model=model,
    args=trainings_args,
    train_dataset=dataset.load_dataset("summarization", "xsum", split="train"),
    eval_dataset=dataset.load_dataset("summarization", "xsum", split="validation"),
    tokenizer=tokenizer,
)
trainer.train()
"""Convert a dataset to the huggingface dataset format."""
from zenml.logger import get_logger
from zenml.steps import step
from datasets import Dataset
logger = get_logger(__name__)
@step
def convert_to_hg_dataset(data: dict) -> Dataset:
    """Convert a dataset to the huggingface dataset format."""
    texts, summaries = [], []
    for _, v in data.items():
        texts.append(v["original_text"])
        summaries.append(v["reference_summary"])
    df = pd.DataFrame({"text": texts, "summary": summaries})
    dataset = Dataset.from_pandas(df)
    return dataset
###########################################################################
```

### Code copilot微調

```JavaScript
# Code copilot
https://github.com/THUDM/CodeGeeX2
git clone https://github.com/THUDM/CodeGeeX2.git
https://marketplace.visualstudio.com/items?itemName=aminer.codegeex
from transformers import AutoTokenizer, AutoModel
tokenizer = AutoTokenizer.from_pretrained("THUDM/codegeex2-6b", trust_remote_code=True)
model = AutoModel.from_pretrained("THUDM/codegeex2-6b", trust_remote_code=True, device='cuda')
model = model.eval()
# remember adding a language tag for better performance
prompt = "# language: python\n# write a bubble sort function\n"
inputs = tokenizer.encode(prompt, return_tensors="pt").to(model.device)
outputs = model.generate(inputs, max_length=256, top_k=1)
response = tokenizer.decode(outputs[0])

python ./demo/run_demo_local.py
from transformers import get_linear_schedule_with_warmup
# model
# optimizer and lr scheduler
optimizer = torch.optim.AdamW(model.parameters(), lr=lr)
lr_scheduler = get_linear_schedule_with_warmup(
    optimizer=optimizer,
    num_warmup_steps=0,
    num_training_steps=(len(train_dataloader) * num_epochs),
)
```

### 分词器 embedding

```JavaScript
###########################################################################
分词器
GPT2-Chinese 中文版 GPT2 训练代码，使用 BERT 分词器
https://houbb.github.io/2020/01/20/nlp-gpt2-chinese
文言文分词器
https://github.com/jiaeyan/Jiayan
https://zhuanlan.zhihu.com/p/638929185

!!! Embedding leaderboard
https://huggingface.co/spaces/mteb/leaderboard
\#Embedding overview
https://www.youtube.com/watch?v=QdDoFfkVkcw
\#Embedding learning site
https://learn.deeplearning.ai/large-language-models-semantic-search/lesson/3/embeddings
demo plot to say 2dim graph
-- for small one
import umap
import altair as alt
from utils import umap_plot
-- sentense is list 
-- emb is embedding model
chart = umap_plot(sentences, emb)
chart.interactive()
-- for big one
import pandas as pd
wiki_articles = pd.read_pickle('wikipedia.pkl')
wiki_articles
import numpy as np
from utils import umap_plot_big
articles = wiki_articles[['title', 'text']]
embeds = np.array([d for d in wiki_articles['emb']])
chart = umap_plot_big(articles, embeds)
chart.interactive()
# Clean up to remove empty spaces and new lines
texts = np.array([t.strip(' \n') for t in texts])
```

### 減少模型

```JavaScript
\#Decrease model size
https://zenn.dev/syoyo/articles/3bde98e9972dea
https://mlc.ai/mlc-llm/
```

### Self-instruct

```JavaScript
#####################################################################
Self-instruct
https://github.com/jianzhnie/awesome-instruction-datasets
https://github.com/yizhongw/self-instruct
https://github.com/stochasticai/xTuring/blob/main/examples/datasets/preparing_your_dataset.py
不用api。直接调用web
https://github.com/SupritYoung/free-self-instruct

@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
InstructionDataset raw instr data => 必须是复合 dataframe.save 后的,有这三个文件，data-0000-of-001.arrow/dataset_info.json/state.json
-- Load raw instruction data into dataset which can be used for finetuning
from xturing.datasets import InstructionDataset
instruction_dataset = InstructionDataset("/opt/workspace/llama/xTuring/examples/llama/alpaca_data")
model.finetune(dataset=instruction_dataset, logger=wandb_logger)

--从text pdf转成 InstructionDataset raw instruct
from xturing.datasets import InstructionDataset
from xturing.model_apis.openai import ChatGPT
engine = ChatGPT("your-api-key")
dataset = InstructionDataset.generate_dataset_from_dir(engine=engine, path="./sample_finance_data")
--从 seed task 生成 InstructionDataset raw instruct
from xturing.datasets import InstructionDataset
from xturing.model_apis.openai import Davinci
engine = Davinci("your-api-key")
dataset = InstructionDataset.generate_dataset(engine=engine, path="./seed_tasks.jsonl")
-- Save
dataset.save("./output_dataset")
-- Tuning
from xturing.models import GPT2Lora
model = GPT2Lora()
Or
from xturing.models import BaseModel
model = BaseModel.create("gpt2_lora")
# Finetune the model on generated dataset
model.finetune(dataset=dataset)

csv
instruction, input, outputs
sss,sss,ss
sss,,sss
import pandas as pd
df = pd.read_csv("file.csv")
print(df.loc[0])
convert into train.csv format
### Instruction:
sss
### Response:
sss
```

### Embedding Vector DB

```JavaScript
###########################################################################
\#Embedding Vector DB with postgresql
installation and test Steps
https://juejin.cn/post/7236541021121871928
Step1 install postgresql
https://technixleo.com/install-postgresql-on-centos-alma-rhel-9/
sudo dnf install -y https://download.postgresql.org/pub/repos/yum/reporpms/EL-9-x86_64/pgdg-redhat-repo-latest.noarch.rpm
sudo dnf install -y postgresql14-server
dnf install perl-CPAN
perl -MCPAN -e 'install IPC::Run'
dnf --enablerepo=crb install perl-IPC-Run -y
sudo yum install postgresql14-devel.x86_64
binary /usr/pgsql-14/
PGDATA=/var/lib/pgsql/14/data
sudo /usr/pgsql-14/bin/postgresql-14-setup initdb
sudo systemctl enable postgresql-14
sudo systemctl start postgresql-14
sudo vi /var/lib/pgsql/14/data/postgresql.conf
listen_addresses = '*'
pg_hba.conf
host      all      all      0.0.0.0/0       md5
Step2 install pgvector
https://github.com/pgvector/pgvector
git clone --branch v0.4.4 https://github.com/pgvector/pgvector.git
cd pgvector
export PG_CONFIG=/usr/pgsql-14/bin/pg_config
sudo dnf install ccache
make
sudo --preserve-env=PG_CONFIG make install

CREATE EXTENSION vector;
SELECT * FROM pg_extension;
CREATE TABLE public.tfacerecord (
	f_id varchar NULL,
	f_uuid varchar NULL,
	f_feature vector NULL
);
SET ivfflat.probes = 10;
SET max_parallel_workers_per_gather = 4;
CREATE INDEX ON items USING ivfflat (embedding vector_l2_ops) WITH (lists = 1000);
-- check index progress
SELECT phase, tuples_done, tuples_total FROM pg_stat_progress_create_index;
ipython
!pip install text2vec
from text2vec import SentenceModel
model = SentenceModel('shibing624/text2vec-base-chinese')
sentence = '这里是你想编码的文本输入'
vec = model.encode(sentence)
CREATE TABLE sentences
(
  id    BIGINT PRIMARY KEY,  -- 标识
  txt   TEXT NOT NULL,       -- 文本
  vec   VECTOR(768) NOT NULL -- 向量
);
# !/usr/bin/env python3
from text2vec import SentenceModel
from psycopg2 import connect
model = SentenceModel('shibing624/text2vec-base-chinese')
def query(question, limit=64):
    vec = model.encode(question)  # 生成一个一次性的编码向量，默认查找最接近的64条记录
    item = 'ARRAY[' + ','.join([str(f) for f in vec.tolist()]) + ']::VECTOR(768)'
    cursor = connect('postgres:///').cursor()
    cursor.execute("""SELECT id, txt, vec <-> %s AS d FROM sentences ORDER BY 3 LIMIT %s;""" % (item, limit))
    for id, txt, distance in cursor.fetchall():
        print("%-6d [%.3f]\t%s" % (id, distance, txt))
DROP TABLE IF EXISTS vtest; CREATE TABLE vtest ( id BIGINT, v  VECTOR(1536) ); TRUNCATE vtest;
INSERT INTO vtest SELECT i, random_array(1536)::vector(1536) FROM generate_series(1, 1000000) AS i;
CREATE INDEX ON vtest USING ivfflat (v vector_cosine_ops) WITH(lists = 1000);
WITH probe AS (SELECT random_array(1536)::VECTOR(1536) AS v) SELECT id FROM vtest ORDER BY v <=> (SELECT v FROM probe) limit 1;

CREATE TABLE public.tfacerecord (
	f_id varchar NULL,
	f_uuid varchar NULL,
	f_feature vector NULL
);
f_feature为特征值字段
#创建索引
L2 distance(欧拉距离)
CREATE INDEX ON items USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
Inner product(点积距离)
CREATE INDEX ON items USING ivfflat (embedding vector_ip_ops) WITH (lists = 100);
Cosine distance(余弦距离)
CREATE INDEX ON items USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

#####################################################################
\#How to use it
https://supabase.com/blog/openai-embeddings-postgres-vector
https://www.modb.pro/db/627954
https://qiita.com/yoshioterada/items/fddbc738cca9f24dac8b
langchain with pgvector
https://python.langchain.com/docs/integrations/vectorstores/pgvector
loader = TextLoader("../../../state_of_the_union.txt")
documents = loader.load()
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
docs = text_splitter.split_documents(documents)
embeddings = OpenAIEmbeddings()
# PGVector needs the connection string to the database.
CONNECTION_STRING = "postgresql+psycopg2://harrisonchase@localhost:5432/test3"
# # Alternatively, you can create it from enviornment variables.
# import os
# CONNECTION_STRING = PGVector.connection_string_from_db_params(
#     driver=os.environ.get("PGVECTOR_DRIVER", "psycopg2"),
#     host=os.environ.get("PGVECTOR_HOST", "localhost"),
#     port=int(os.environ.get("PGVECTOR_PORT", "5432")),
#     database=os.environ.get("PGVECTOR_DATABASE", "postgres"),
#     user=os.environ.get("PGVECTOR_USER", "postgres"),
#     password=os.environ.get("PGVECTOR_PASSWORD", "postgres"),
# )
COLLECTION_NAME = "state_of_the_union_test"
db = PGVector.from_documents(
    embedding=embeddings,
    documents=docs,
    collection_name=COLLECTION_NAME,
    connection_string=CONNECTION_STRING,
)
query = "What did the president say about Ketanji Brown Jackson"
docs_with_score = db.similarity_search_with_score(query)
for doc, score in docs_with_score:
    print("-" * 80)
    print("Score: ", score)
    print(doc.page_content)
    print("-" * 80)
\#Vector DB index and Training
CREATE EXTENSION embedding;
CREATE TABLE documents(id integer PRIMARY KEY, embedding real[]);
SELECT id FROM documents ORDER BY embedding <-> ARRAY[1.1, 2.2, 3.3] LIMIT 1;

#对于Vector的优化，pg_embedding
git clone https://github.com/neondatabase/pg_embedding.git
cd pg_embedding
export PG_CONFIG=/usr/pgsql-14/bin/pg_config
sudo dnf install ccache
make
sudo --preserve-env=PG_CONFIG make install
git log --oneline
git checkout <commit=hash>
git checkout 710f44d
make
https://segmentfault.com/a/1190000044018189
https://github.com/neondatabase/pg_embedding
https://python.langchain.com/docs/integrations/vectorstores/pgembedding
postgres=# create database raydb;
CREATE DATABASE
postgres=# create role rayrole;
CREATE ROLE
postgres=# grant connect on database raydb to rayrole;
GRANT
postgres=# create user ray password 'test123';
CREATE ROLE
postgres=# grant rayrole to ray;
GRANT ROLE
grant all privileges on database raydb to rayrole;
GRANT ALL ON ALL TABLES in schema public to rayrole;
GRANT ALL ON ALL sequences in schema public to rayrole;
GRANT ALL ON ALL functions in schema public to rayrole;
\c raydb
CREATE EXTENSION embedding;
SELECT * FROM pg_extension;
CREATE TABLE documents(id INTEGER, embedding REAL[]);
INSERT INTO documents(id, embedding) VALUES (1, '{1.1, 2.2, 3.3}'),(2, '{4.4, 5.5, 6.6}');
select count(1) from langchain_pg_embedding where collection_id = '37289563-30d1-4ed0-b989-5ba8f59b9f36';
delete from langchain_pg_collection where uuid = '37289563-30d1-4ed0-b989-5ba8f59b9f36';

SELECT id FROM documents ORDER BY embedding <-> array[1.1, 2.2, 3.3] LIMIT 1;
CREATE INDEX ON documents USING hnsw(embedding) WITH (maxelements=1000, dims=3, m=8);
CREATE INDEX ON items USING hnsw (embedding) WITH (maxelements = 1000000, dims=1536, m=32);
# CREATE INDEX paperwithcode_idx on paperwithcode using hnsw (embedding) with (maxelements = 1000000, dims=768, m = 8, ef_construction = 32, ef_search = 32);
CREATE INDEX paperwithcode_idx on paperwithcode using hnsw (embedding) with (dims=768, m=8);
CREATE INDEX paperwithcode_t_idx on paperwithcode_t using hnsw (embedding) with (dims=768, m=8);
-----
CREATE INDEX paperwithcode_t_eidx ON paperwithcode_t USING hnsw(embedding) WITH (maxelements=200000, dims=768, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_t_cidx ON paperwithcode_t USING hnsw(embedding ann_cos_ops) WITH (maxelements=200000, dims=768, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_t_midx ON paperwithcode_t USING hnsw(embedding ann_manhattan_ops) WITH (maxelements=200000, dims=768, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_t_bge_eidx ON paperwithcode_t_bge USING hnsw(embedding) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_t_bge_cidx ON paperwithcode_t_bge USING hnsw(embedding ann_cos_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_t_bge_midx ON paperwithcode_t_bge USING hnsw(embedding ann_manhattan_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_bge_eidx ON paperwithcode_bge USING hnsw(embedding) WITH (maxelements=400000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_bge_cidx ON paperwithcode_bge USING hnsw(embedding ann_cos_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_bge_midx ON paperwithcode_bge USING hnsw(embedding ann_manhattan_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);

CREATE TABLE documents(id INTEGER, embedding REAL[]);
INSERT INTO documents(id, embedding) VALUES (1, '{1.1, 2.2, 3.3}'),(2, '{4.4, 5.5, 6.6}');
Euclidean (L2) distance:
SELECT id FROM documents ORDER BY embedding <-> array[3,3,3] LIMIT 1;
Cosine distance:
SELECT id FROM documents ORDER BY embedding <=> array[3,3,3] LIMIT 1;
Manhattan distance:
SELECT id FROM documents ORDER BY embedding <~> array[3,3,3] LIMIT 1;
from langchain.vectorstores import PGEmbedding
db = PGEmbedding.from_documents(
    embedding=embeddings,
    documents=docs,
    collection_name="state_of_the_union",
    connection_string=CONNECTION_STRING,
)
db.create_hnsw_index(max_elements= 10000, dims = 1536, m = 8, ef_construction = 16, ef_search = 16)
or
CREATE INDEX ON vectors USING hnsw(vec) WITH (maxelements=10000, dims=1536, m=3, efconstruction=16, efsearch=16);
-- 为了提高效率，HNSW 索引存储在内存中。为了进行可比较的测试，我们使用 pg_prewarm 扩展将 IVFFlat 索引也存储在内存中：
SELECT pg_prewarm('3448836', mode => 'buffer');
db = PGEmbedding.from_documents(
    embedding=embeddings,
    documents=new_docs,
    collection_name=COLLECTION_NAME,
    connection_string=CONNECTION_STRING,
    pre_delete_collection=False,   <===========!!! It will apend but not recreate collection
)
###########################################################################
# Some of local embedding as well as Chinese embedding model
https://zhuanlan.zhihu.com/p/622017658
# Alibaba BGE Rank1!
https://huggingface.co/BAAI/bge-large-zh
# Japanese local embedding model
https://tech.yellowback.net/posts/transformers-japanese-models
```

### GPT Cache

```JavaScript
\#GPT Cache
https://mem.ai/p/rxoNKaObckMPKkD89QR4
https://medium.com/@simon_attard/building-a-memory-layer-for-gpt-using-function-calling-da17d66920d0
```

### FastChat 用法

```JavaScript
#####################################################################
git clone https://github.com/lm-sys/FastChat.git
cd FastChat
conda create -n fastchat
conda install python==3.10.12
pip install --upgrade pip
pip3 install -e .
pip install transformers

-- start controller
python3 -m fastchat.serve.controller --host 0.0.0.0
-- start model worker
python3 -m fastchat.serve.model_worker --model-path /opt/aibase/chatglm2-6b
python3 -m fastchat.serve.model_worker --model-path /opt/aibase/Chinese-Llama-2-7b
python3 -m fastchat.serve.model_worker --model-path /opt/aibase/codegeex2-6b
python3 -m fastchat.serve.model_worker --model-path /opt/aibase/LLaVA-Lightning-MPT-7B-preview --load-bit
python3 -m fastchat.serve.model_worker --model-path /opt/workspace/aibase/CodeLlama-7b-hf-flash
python3 -m fastchat.serve.model_worker --model-path /opt/aibase/CodeLlama-7b-hf-flash
python3 -m fastchat.serve.model_worker --model-path /opt/aibase/CodeLlama-7B-Python-fp16
python3 -m fastchat.serve.multi_model_worker --model-path /opt/aibase/CodeLlama-7b-hf-flash
python3 -m fastchat.serve.multi_model_worker --model-path /opt/aibase/CodeLlama-7B-Python-fp16
python3 -m fastchat.serve.multi_model_worker --model-path /opt/aibase/educhat-sft-002-7b
LLaVA-Lightning-MPT-7B-preview
-- test message
python3 -m fastchat.serve.test_message --model-name Chinese-Llama-2-7b
-- launch gradio web server
python3 -m fastchat.serve.gradio_web_server --host 0.0.0.0
python3 -m fastchat.serve.gradio_web_server --host 0.0.0.0 --controller-url ************:21001
python3 -m fastchat.serve.gradio_web_server --host 0.0.0.0 --controller-url ***********:21001
```

### Code Llama

```JavaScript
code llama 
https://ai.meta.com/blog/code-llama-large-language-model-coding/
https://github.com/facebookresearch/codellama
git clone https://huggingface.co/TheBloke/CodeLlama-7B-Python-fp16
git clone https://huggingface.co/NousResearch/CodeLlama-7b-hf-flash
-- to run flash attention2 you need to install below
pip install flash-attn --no-build-isolation
# pip install rotary-embedding-torch
pip install git+https://github.com/HazyResearch/flash-attention.git\#subdirectory=csrc/rotary
\#download cuda
https://developer.nvidia.com/cuda-11-7-0-download-archive?target_os=Linux&target_arch=x86_64&Distribution=RHEL&target_version=8&target_type=rpm_local
wget https://developer.download.nvidia.com/compute/cuda/11.7.0/local_installers/cuda-repo-rhel8-11-7-local-11.7.0_515.43.04-1.x86_64.rpm
sudo rpm -i cuda-repo-rhel8-11-7-local-11.7.0_515.43.04-1.x86_64.rpm
(llama) [raysheng@MONSTER:/etc/yum.repos.d]$ cat cuda-rhel8-11-7-local.repo
[cuda-rhel8-11-7-local]
name=cuda-rhel8-11-7-local
baseurl=file:///var/cuda-repo-rhel8-11-7-local
enabled=1
gpgcheck=1
gpgkey=file:///var/cuda-repo-rhel8-11-7-local/D1FBD088.pub
obsoletes=0
sudo dnf install cuda-11-7
\#sudo dnf clean all
\#sudo dnf -y module install nvidia-driver:latest-dkms
\#sudo dnf -y install cuda
sudo update-alternatives --config cuda 

https://blog.csdn.net/weixin_44491772/article/details/131205174
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer
model_path = "/opt/aibase/CodeLlama-7b-hf-flash"

tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
model = AutoModelForCausalLM.from_pretrained(model_path,trust_remote_code=True).half().cuda()
streamer = TextStreamer(tokenizer, skip_prompt=True, skip_special_tokens=True)
prompt = "give me code for sorting"
generate_ids = model.generate(tokenizer(prompt, return_tensors='pt').input_ids.cuda(), max_new_tokens=4096, streamer=streamer)
```

### ChatGLM 微調

```JavaScript
#####################################################################
ChatGLM FineTuning
https://www.heywhale.com/mw/project/64984a7b72ebe240516ae79c   wechat/mobile/h5bi9pmf
https://github.com/THUDM/ChatGLM2-6B/tree/main/ptuning
https://aitechtogether.com/python/90699.html
sudo su - raysheng
conda update -n base conda
pip install --upgrade pip
conda create -n ChatGLM
conda install python=3.10.12
conda activate ChatGLM
cd /opt/workspace/ChatGLM-6B/
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
\#install cuda toolkit
wget https://developer.download.nvidia.com/compute/cuda/12.2.0/local_installers/cuda-repo-rhel9-12-2-local-12.2.0_535.54.03-1.x86_64.rpm
rpm -i cuda-repo-rhel9-12-2-local-12.2.0_535.54.03-1.x86_64.rpm
dnf clean all
sudo dnf -y module install nvidia-driver:latest-dkms
dnf -y install cuda
-- will install into /usr/local/cuda
(base) [root@MONSTER:/etc/profile.d]# cat cuda.sh
export CUDA_HOME=/usr/local/cuda
export PATH=$PATH:/usr/local/cuda/bin
pip install fastapi
pip install SwissArmyTransformer>=0.3.6
pip install icetk (it will remove protobuf=4.23.4)
pip install protobuf==4.23.4
pip install peft
pip install ipywidgets
pip install wandb
pip install scikit-learn
pip install evaluate
/opt/workspace/raylab/FTPlay.ipynb
#伤寒论训练
/opt/workspace/raylab/FTPlayWithoutEval.py
from transformers import AutoTokenizer, AutoModel
checkpoint = "/opt/aibase/chatglm-6b"
model = AutoModel.from_pretrained(checkpoint,  trust_remote_code=True).half().cuda()
tokenizer = AutoTokenizer.from_pretrained(checkpoint,  trust_remote_code=True)
from peft import LoraConfig, get_peft_model, TaskType
def load_lora_config(model):
	config = LoraConfig(
	    task_type=TaskType.CAUSAL_LM,
	    inference_mode=False,
	    r=8,
	    lora_alpha=32,
	    lora_dropout=0.1,
	    target_modules=["query_key_value"]
	)
	return get_peft_model(model, config)
model = load_lora_config(model)
model.print_trainable_parameters()
trainable params: 3,670,016 || all params: 6,176,956,416 || trainable%: 0.05941463324063059
sum(p.numel() for p in model.parameters())
\#how to use evaluate dataset and train dataset
https://lewtun.github.io/blog/til/nlp/huggingface/transformers/2021/01/01/til-data-collator.html
https://qiita.com/m__k/items/2c4e476d7ac81a3a44af
https://stackoverflow.com/questions/67457480/how-to-get-the-accuracy-per-epoch-or-step-for-the-huggingface-transformers-train
https://github.com/huggingface/evaluate
https://discuss.huggingface.co/t/use-evaluate-to-add-compute-metrics-to-custom-trainer/38445
！！ official doc
https://huggingface.co/docs/evaluate/base_evaluator
!! sample
https://qiita.com/thmd9726/items/e9bc7cdd7e041c4adb97
!! Chinese article
https://blog.csdn.net/muyao987/article/details/125411129
https://blog.51cto.com/u_15942590/6506368
!! fine tuning bbc transformer
https://ithelp.ithome.com.tw/articles/10306026
https://tt-tsukumochi.com/archives/5553
```

### Azure 用法

```JavaScript
###########################################################################
-- DeepLearning AI Finetuning Large Language Models Course
###########################################################################
Azure OpenAI Service
\#Price
https://azure.microsoft.com/en-us/pricing/details/cognitive-services/openai-service/
\#AI engineer learning path
https://learn.microsoft.com/zh-cn/training/browse/?roles=ai-engineer&resource_type=learning%20path
\#Chinese doc container registry
https://learn.microsoft.com/zh-CN/azure/container-registry/container-registry-health-error-reference
\#Azure OpenAI
https://learn.microsoft.com/zh-cn/azure/ai-services/openai/overview
https://learn.microsoft.com/zh-CN/azure/ai-services/openai/how-to/function-calling
\#Azure 机器学习文档
https://learn.microsoft.com/zh-cn/azure/machine-learning/?view=azureml-api-2
https://learn.microsoft.com/ja-jp/azure/ai-services/openai/how-to/create-resource?pivots=web-portal
https://www.bing.com/videos/search?q=create+python+virtual+environment+in+azure&docid=603484738103611812&mid=1CEB7A7EBCAD4CB744D11CEB7A7EBCAD4CB744D1&view=detail&FORM=VIRE
-- create virtual machine
https://www.bing.com/videos/search?&q=create+python+virtual+environment+in+azure&docid=603484738103611812&mid=E115DCF5A32CF9E41EF3E115DCF5A32CF9E41EF3&view=detail&FORM=VDRVRV&ajaxhist=0
-- azure rest api doc
https://learn.microsoft.com/en-us/rest/api/cognitiveservices/azureopenaistable/models/list?tabs=HTTP
https://learn.microsoft.com/en-us/azure/ai-services/openai/reference

azure chatbot service
https://powervirtualagents.microsoft.com/zh-cn/
https://qiita.com/taiki_yoshida/items/d48d6286ce5b8f3aa2a8
Another azure chatbot github
https://github.com/linjungz/azure-chatgpt-ui/tree/main
\#improved one
https://github.com/dotneet/smart-chatbot-ui

-- Getting Started with Microsoft Azure in Python !!!!
https://www.bing.com/videos/search?&q=create+python+virtual+environment+in+azure&docid=603484738103611812&mid=744BFF38B84DBB4EAB22744BFF38B84DBB4EAB22&view=detail&FORM=VDRVRV&ajaxhist=0
https://www.youtube.com/watch?v=DPBspKl2epk
1. create azure account
2. download and install azure function core tools
https://learn.microsoft.com/en-us/azure/azure-functions/functions-run-local?tabs=windows%2Cportal%2Cv2%2Cbash&pivots=programming-language-python\#install-the-azure-functions-core-tools
install azure cli
https://learn.microsoft.com/ja-jp/cli/azure/install-azure-cli-windows?tabs=azure-cli
if you didn't install docker desktop in windows, pls install 
https://docs.docker.com/desktop/install/windows-install/
Start docker service in windows
wsl -l -v
docker info
docker version
Or in AlmaLinux
https://linux.how2shout.com/how-to-install-docker-on-almalinux-9-linux/
dnf remove podman-docker
sudo dnf config-manager --add-repo=https://download.docker.com/linux/centos/docker-ce.repo
sudo dnf install docker-ce docker-ce-cli containerd.io docker-compose-plugin
sudo systemctl enable --now docker
sudo systemctl status docker
sudo usermod -aG docker $USER
newgrp docker
3. open vcs and install extension. python/docker/azure app services
4. Same as usual, setup git and conda
5. sign into azure
ctl+shift+p ->azure sign in
!! when run azure-cli in docker failed
G:\ResearchDirection\AI\azure>docker run -it --name azure-cli azuresdk/azure-cli-python
Unable to find image 'azuresdk/azure-cli-python:latest' locally
docker: Error response from daemon: pull access denied for azuresdk/azure-cli-python, repository does not exist or may require 'docker login': denied: requested access to the resource is denied.
See 'docker run --help'.
--- Solution. as azuresdk/azure-cli-python is depreted
docker run -it mcr.microsoft.com/azure-cli
6. create service and get access key
****************************************************
OR 
create container registry via cli
az acr create --resource-group poctest --name rayacrtest --sku Basic
  "loginServer": "rayacrtest.azurecr.io",
  "name": "rayacrtest",
registry name: crsbichatpoc
login server: crsbichatpoc.azurecr.io
username: crsbichatpoc
password: ****************************************************
7. from vcs terminal
docker login rayacrtest.azurecr.io
docker login crsbichatpoc.azurecr.io
if failed, run health check
az acr check-health --name rayacrtest --ignore-errors --yes
az acr check-health --name crsbichatpoc --ignore-errors --yes
8. build docker image
type docker image build 
set tag as rayacrtest.azurecr.io/flask-webapp-quickstart:latest
docker scout quickview

(azure) F:\ResearchDirection\AI\azure>docker image list
REPOSITORY                                      TAG       IMAGE ID       CREATED              SIZE
rayacrtest.azurecr.io/flask-webapp-quickstart   latest    00314f7ae0a2   About a minute ago   189MB
ambassador/telepresence-docker-extension        1.0.9     829a500cb0ad   7 weeks ago          531MB
ambassador/telepresence-docker-runtime          1.0.9     3a0d378b0ab9   7 weeks ago          21.3MB
mcr.microsoft.com/mcr/hello-world               latest    fce289e99eb9   4 years ago          1.84kB
9. push image to azure
Before do that, you need to register resource providers to your subscription. otherwise
error:   The subscription is not registered to use namespace 'Microsoft.Web'.
https://stackoverflow.com/questions/37682546/subscription-not-registered-to-use-namespace-microsoft-network

10. goto docker extensio -> registeries -> find crsbichatpoc -> find ur docker image -> right click -> 
deploy image to azure app service
After deploy, access this web
use azure resource tabs -》 app service -> flask-rayacr -》 WEBSITES_PORT=8000
restart app service
https://flask-rayacr.azurewebsites.net/
Adjust application setting
https://github.com/mckaywrigley/chatbot-ui
https://github.com/mckaywrigley/chatbot-ui/blob/main/utils/app/const.ts
==============================
# WEBSITES_PORT=3000 <= looks new version is not required in azure
OPENAI_API_TYPE = "azure"
OPENAI_API_HOST = https://sbiopenaipoc.openai.azure.com/
OPENAI_API_VERSION = 2023-03-15-preview   <== Maybe we can't add
OPENAI_CHAT_API_VERSION = 2023-07-01-preview
OPENAI_API_KEY = "********************************"
AZURE_DEPLOYMENT_ID = "DP_SBIChatPoC"
NEXT_PUBLIC_DEFAULT_SYSTEM_PROMPT = "You are AI support from SBI Holdings, Please always speak Japanaese and provide answer to users"
WEBSITES_PORT=3000
Final Entrypoint
https://sbiopenaipoc.openai.azure.com/openai/deployments/DP_SBIChatPoC/chat/completions?api-version=2023-03-15-preview
==============================
For code change
only need to reimage build in local and push to azure
restart app service and done!
Steps:
1. ctl+shift+p ->azure sign in
2. docker login crsbichatpoc.azurecr.io
registry name: crsbichatpoc
login server: crsbichatpoc.azurecr.io
username: crsbichatpoc
password: ****************************************************
3. ctl+shift+p ->  docker image build -> select dockfile -> input tag
4. goto vcs 资源管理器 -> docker images -> find new built image there -> push
//it will automatically put to currently login azure docker registry
5. goto docker extetsion -> registeries -> find crsbichatpoc -> find ur docker image -> check it updated
6. goto azure extention -> resource -> azure -> app service -> restart the service
//check application settings if need to change.


-- openAI service Video
Getting started with generative AI using Azure OpenAI Service | BRK214H
https://www.youtube.com/watch?v=o5uhn4GSpQU
Azure OpenAI embeddings + search tutorial
https://www.youtube.com/watch?v=PSLO-yM6eFY
New easy way to add your data to Azure OpenAI Service
https://www.youtube.com/watch?v=6SNfeVop4zM

-- Azure Machine Learning Studio
# doc
https://learn.microsoft.com/en-us/azure/machine-learning/?view=azureml-api-2
# Azure dashboard
https://ml.azure.com/home?tid=0b2b6ffa-4f15-4679-b8d3-723103f44b4a
-- ML Service video
Meta LLama 2 Foundational Model with Prompt Flow
https://www.youtube.com/watch?v=F9r4nE53j4A
Model deployment and inferencing with Azure Machine Learning | Machine Learning Essentials
https://www.youtube.com/watch?v=WZ7vS10KPAw
Build and maintain your company Copilot with Azure ML and GPT-4 | BRK211H
https://www.youtube.com/watch?v=2meEvuWAyXs
Learn Live - Azure ML Fundamentals
https://www.youtube.com/watch?v=B3MTKu989ks
Next generation AI for developers with the Microsoft Cloud | KEY03H
!!! MS Prompt Flow
https://www.youtube.com/watch?v=KMOV1Zy8YeM
!!! Google Visual Blocks
https://visualblocks.withgoogle.com/#/

-- Azure AI Fundamation Certification
Azure AI Fundamentals Certification (AI-900) - Full Course to PASS the Exam
https://www.youtube.com/watch?v=OwZHNH8EfSU
-- Azure OpenAI Chatbot
https://dev.classmethod.jp/articles/azure-openai-chatbot/
https://github.com/mckaywrigley/chatbot-ui
https://techcommunity.microsoft.com/t5/azure-architecture-blog/building-a-private-chatgpt-interface-with-azure-openai/ba-p/3869522
\#Latest API call information
https://github.com/Azure/azure-rest-api-specs/blob/main/specification/cognitiveservices/data-plane/AzureOpenAI/inference/preview/2024-02-15-preview/inference.json
https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference
>>model list
https://learn.microsoft.com/en-us/rest/api/azureopenai/models/list?view=rest-azureopenai-2023-05-15&tabs=HTTP\#code-try-0
>> playground
https://oai.azure.com/portal/e54d243cb19f4e4db292587cecf57bc8/chat?tenantid=0b2b6ffa-4f15-4679-b8d3-723103f44b4a
-- some fix
F:\ResearchDirection\AI\azure\chatbot-ui\pages\api\models.ts
      url = `${OPENAI_API_HOST}/openai/deployments?api-version=${OPENAI_API_VERSION}`;
Fix:
\#line 18:
      url = `${OPENAI_API_HOST}/openai/models?api-version=${OPENAI_API_VERSION}`;
Pls refer to https://learn.microsoft.com/en-us/rest/api/cognitiveservices/azureopenaistable/models/list?tabs=HTTP
# F:\ResearchDirection\AI\azure\chatbot-ui\utils\server\index.ts
#    url = `${OPENAI_API_HOST}/openai/deployments/${AZURE_DEPLOYMENT_ID}/chat/completions?api-version=${OPENAI_API_VERSION}`;
F:\ResearchDirection\AI\azure\chatbot-ui\utils\app\const.ts
export const DEFAULT_SYSTEM_PROMPT =
  process.env.NEXT_PUBLIC_DEFAULT_SYSTEM_PROMPT ||
  "You are AI support from SBI Holdings, Please always speak Japanaese and provide answer to users.";
F:\ResearchDirection\AI\azure\chatbot-ui\components\Chat\Chat.tsx
Backup to Chat.bkup
F:\ResearchDirection\AI\azure\chatbot-ui\pages\api\home\home.tsx
\#ln 363
			        <title>SBI GEN AI</title>
		
\#debug next js (chatai)
https://www.codermen.com/next-is-not-recognized-as-an-internal-or-external-command-solved/?expand_article=1
\#npm install next
\#npm run dev
https://stackoverflow.com/questions/31169259/how-to-debug-typescript-files-in-visual-studio-code
Or
git clone https://github.com/mckaywrigley/chatbot-ui.git
npm -i
\#setup key
npm run dev

OR
https://www.youtube.com/watch?v=f3i9b3uK00U
npm install --save-dev ts-node tsconfig-paths
in vcs , click debug/run icon, create launch.json -> Node.js
{
    "version": "0.2.0",
    "configurations": [
  
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Typescript",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "program": "${file}",
            "cwd": "${workspaceFolder}/chatbot-ui",
            "runtimeArgs": [
                "--nolazy",
                "-r",
                "ts-node/register",
                "-r",
                "tsconfig-paths/register"
            ],
            "console": "integratedTerminal",
            "outFiles": [
                "${workspaceFolder}/chatbot-ui/out/**/*.js"
            ]
        }
    ]
}
!!! Looks work
https://qiita.com/yuzukaki/items/68ca1aed777b296145d5
after setup done
nodejs -> build -> start 
i.headers["Symbol(headers list)"]["Symbol(headers map)"]
{"api-deprecated-versions" => "2022-03-01-preview,2022-06-01-preview"}
{"api-supported-versions" => "2022-12-01,2023-03-15-preview,2023-05-15,2023-06-01-preview,2023-07-01-preview"}
{
  "error": {
    "code": "NoRegisteredProviderFound",
    "message": "未找到位置“japaneast”的已注册资源提供程序和类型“accounts”的 api-version“2023-03-15-preview”。
	支持的 api-version 为“2016-02-01-preview, 2017-04-18, 2021-04-30, 2021-10-01, 2022-03-01, 2022-10-01, 2022-12-01, 2023-05-01, 2023-06-01-preview”。
	支持的位置为“global, australiaeast, brazilsouth, westus, westus2, westeurope, northeurope, southeastasia, eastasia, westcentralus, southcentralus, 
	eastus, eastus2, canadacentral, japaneast, centralindia, uksouth, japanwest, koreacentral, francecentral, northcentralus, centralus, 
	southafricanorth, uaenorth, swedencentral, switzerlandnorth, switzerlandwest, germanywestcentral, norwayeast, westus3, jioindiawest, 
	qatarcentral, canadaeast”。"
  }
}
{
  "data": [
    {
      "scale_settings": {
        "scale_type": "standard"
      },
      "model": "gpt-35-turbo",
      "owner": "organization-owner",
      "id": "DP_SBIChatPoC",
      "status": "succeeded",
      "created_at": **********,
      "updated_at": **********,
      "object": "deployment"
    }
  ],
  "object": "list"
}
@@@@@@@ azure restapi的不兼容性，让程序很难，3-15的版本和6-1版本和7-1版本取得model列表都不同。
https://management.azure.com/subscriptions/72c6de5d-6db7-4780-89e1-3cd938a56c1f/resourceGroups/rg_sbipoc/providers/Microsoft.CognitiveServices/accounts/SBIOpenAIPoC/deployments/DP_SBIChatPoC?api-version=2023-06-01-preview
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
How to access azure storage
https://stackoverflow.com/questions/********/access-a-blob-file-via-uri-over-a-web-browser-using-new-aad-based-access-control
https://learn.microsoft.com/en-us/azure/postgresql/single-server/concepts-data-access-and-security-vnet

Code is available at github.com/TsinghuaDatabaseGroup/DB-GPT
```

```LaTeX
https://github.com/thivy/azure-openai-js-stream.git
AZURE_OPEN_AI_KEY = '********************************'
AZURE_OPEN_AI_BASE = 'https://sbiopenaipoc.openai.azure.com/openai/deployments/DP_SBIChatPoC'
AZURE_OPEN_AI_CHAT_VERSION = '2024-02-15-preview'
\#prepare a dockerfile
# ---- Base Node ----
    FROM node:19-alpine AS base
    WORKDIR /app
    COPY package*.json ./
  
    # ---- Dependencies ----
    FROM base AS dependencies
    COPY package-lock.json ./
    RUN npm ci
  
    # ---- Build ----
    FROM dependencies AS build
    COPY . .
    RUN npm run build
  
    # ---- Production ----
    FROM node:19-alpine AS production
    WORKDIR /app
    COPY --from=dependencies /app/node_modules ./node_modules
    COPY --from=build /app/.next ./.next
    COPY --from=build /app/public ./public
    COPY --from=build /app/package*.json ./
    COPY --from=build /app/next.config.js ./
    COPY --from=build /app/.env.local ./
    COPY --from=build /app/next-env.d.ts ./
    COPY --from=build /app/postcss.config.js ./
    COPY --from=build /app/tailwind.config.js ./
    COPY --from=build /app/tsconfig.json ./
    COPY --from=build /app/type.ts ./
  
    # Expose the port the app will run on
    EXPOSE 3000
  
    # Start the application
    CMD ["npm", "start"]
```

```JavaScript
\#Example
G:\ResearchDirection\AI\langchain\openai_azure.py
openai.api_type = "azure"
openai.api_base = "https://rayopenai.openai.azure.com/"
openai.api_version = "2023-07-01-preview"
openai.api_key = "********************************"
    response = openai.ChatCompletion.create(
        engine="gpt3poc",
        messages=messages,
        functions = function_descriptions,
        function_call="auto"  
        # temperature=1,
        # max_tokens=100,
        # top_p=0.5,
        # frequency_penalty=0,
        # presence_penalty=0,
        # stop=None,
    )
\#How to access via langchain
https://python.langchain.com/docs/integrations/llms/azure_openai
===============================================================
https://sbihd-genai-dev-openai.openai.azure.com/
https://sbihd-genai-dev-openai.openai.azure.com/openai/deployments/SBIHD-GenAI-dev-OpenAI-gpt4/chat/completions?api-version=2023-07-01-preview
japaneast
4ec3e35c7b0c4e0f80da6aadbc849adf
API_TYPE: "aoai" 
OPENAI_API_BASE: "YOUR_ENDPOINT" # The AOAI API address. Format: https://{your-resource-name}.openai.azure.com/openai/deployments/{deployment-id}/completions?api-version={api-version}
OPENAI_API_KEY: "YOUR_API_KEY"  # Set the value to the openai key for the llm model
OPENAI_API_MODEL: "GPTV_MODEL_NAME"  # The only OpenAI model by now that accepts visual input
import os
import requests
import base64
# Configuration
GPT4V_KEY = "YOUR_API_KEY"
IMAGE_PATH = "YOUR_IMAGE_PATH"
encoded_image = base64.b64encode(open(IMAGE_PATH, 'rb').read()).decode('ascii')
headers = {
    "Content-Type": "application/json",
    "api-key": GPT4V_KEY,
}
# Payload for the request
payload = {
  "messages": [
    {
      "role": "system",
      "content": [
        {
          "type": "text",
          "text": "You are an AI assistant that helps people find information."
        }
      ]
    }
  ],
  "temperature": 0.7,
  "top_p": 0.95,
  "max_tokens": 800
}
GPT4V_ENDPOINT = "https://sbihd-genai-dev-openai.openai.azure.com/openai/deployments/SBIHD-GenAI-dev-OpenAI-gpt4/chat/completions?api-version=2023-07-01-preview"
# Send request
try:
    response = requests.post(GPT4V_ENDPOINT, headers=headers, json=payload)
    response.raise_for_status()  # Will raise an HTTPError if the HTTP request returned an unsuccessful status code
except requests.RequestException as e:
    raise SystemExit(f"Failed to make the request. Error: {e}")
# Handle the response as needed (e.g., print or process)
print(response.json())
```

### Yarn-Llama

```JavaScript
###########################################################################
Yarn-Llama-2-13b-128k 128K
Yarn-Llama-2-13b-128k 128K
https://github.com/jquesnelle/yarn
https://huggingface.co/conceptofmind/Yarn-Llama-2-13b-128k
git clone https://huggingface.co/conceptofmind/Yarn-Llama-2-7b-128k
pip install flash-attn --no-build-isolation
https://github.com/Dao-AILab/flash-attention/issues/449
pip install git+https://github.com/HazyResearch/flash-attention.git\#subdirectory=csrc/rotary
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer
model_path = "/opt/workspace/aibase/Yarn-Llama-2-13b-128k"
tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
print(tokenizer)
model = AutoModelForCausalLM.from_pretrained(model_path,trust_remote_code=True).half().cuda()
streamer = TextStreamer(tokenizer, skip_prompt=True, skip_special_tokens=True)
prompt = "give me code for sorting"
generate_ids = model.generate(tokenizer(prompt, return_tensors='pt').input_ids.cuda(), max_new_tokens=4096, streamer=streamer)
response = tokenizer.decode(generate_ids[0])
print(f"**Model Response:**\n```diff\n{response}\n```")
RuntimeError: CUDA error: no kernel image is available for execution on the device
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1.
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
```

### Open interpreter

```JavaScript
#######################################################################################
https://github.com/KillianLucas/open-interpreter/tree/main/interpreter
https://colab.research.google.com/drive/1WKmRXZgsErej2xUriKzxrEAXdxMSgWbb?usp=sharing
```

### 使用卷积做文本情感分析

```Python
- 'vocab_size $=100$ ：词汇表大小为 100 。
- embed_size $=10$ ：嵌入向量的维度为 10 。个连续词元。
- 'num_channels $=[100,100,100] ：$ 每种大小的卷积核都有 100 个通道。
接下来，假设输入的批量大小 (batch size) 为 2 ，每个样本包含 7 个词元 (即句子长度为 7 ) 。因此，输入张量“'inputs'的形状为 2,7 ，其中 2 代表批量大小， 7 代表每个样本的词元数量。
现在，让我们逐步分析$\cdot$'forward’方法:
1. 嵌入层: “inputs"通过两个嵌入层 ('self.embedding'和 'self.constant_embedding') 转换为嵌入向量。每个嵌入层都将'inputs'转换为形状为 $2,7,10$ 的张量。然后将这两个张量沿着最后一个维度拼接，得到形状为 $2,7,20$的张量。
2. 重排维度: 将拼接后的张量' embeddings'的维度重排，以符合卷积层的输入要求。重排后的张量形状变为 $2,20,7$ 。
3. 立用卷积层: 每个卷积层在 'embeddings ${ }^{-}$上运算。假设我们有三个卷积层，每个卷积核的大小分别为 $3 、 4$ 和 5 。每个卷积层的输出形状将取决于卷积核的大小。以大小为 3 的卷积核为例，它的输出形状将是 $2,100,5$ ，其中 100 是通道数， 5 是由于在长度为 7 的序列上应用大小为 3 的卷积核得到的结果。
4. 激活函数和池化: 应用ReLU激活函数后，使用自适应池化层 ('self.pool') 将每个卷积层的输出压缩为长度为啲向量。这将每个卷积层的输出形状转换为 $2,100,1$ 。
5. 讲接卷积层输出: 将所有卷积层的输出沿通道维度拼接，得到形状为 2,300 的张量 (因为每个卷积层有 100 个通道，总共有 3 个卷积层) 。
6. 全连接层: 最后，通过应用dropout和全连接层（'self.decoder')，将拼接后的张量映射到最终的输出类别上。
以下是使用LaTeX格式表示的一些关键步骤的张量变化:
1. 荻入层輸出:
$$
\text { 嵌入层输出形状 }=[2,7,20]
$$
2. 重排维度后:
重排维度后形状 $=[2,20,7]$
3. 卷积层输出 (以卷积核大小为 3 为例)：
卷积层输出形状 $=[2,100,5]$
4. 池化后的输出:
$$
\text { 池化后输出形状 }=[2,100,1]
$$
5. 讲接后的形状:
拼接后形状 $=[2,300]$
```

```JavaScript
class TextCNN(nn.Module):
		def __init__(self, vocab_size, embed_size, kernel_sizes, num_channels,
		**kwargs):
					super(TextCNN, self).__init__(**kwargs)
					self.embedding = nn.Embedding(vocab_size, embed_size)
					# 这个嵌⼊层不需要训练
					self.constant_embedding = nn.Embedding(vocab_size, embed_size)
					self.dropout = nn.Dropout(0.5)
					self.decoder = nn.Linear(sum(num_channels), 2)
					# 最⼤时间汇聚层没有参数，因此可以共享此实例
					self.pool = nn.AdaptiveAvgPool1d(1)
					self.relu = nn.ReLU()
					# 创建多个⼀维卷积层
					self.convs = nn.ModuleList()
					for c, k in zip(num_channels, kernel_sizes):
					self.convs.append(nn.Conv1d(2 * embed_size, c, k))
		def forward(self, inputs):
					# 沿着向量维度将两个嵌⼊层连结起来，
					# 每个嵌⼊层的输出形状都是（批量⼤⼩，词元数量，词元向量维度）连结起来
					embeddings = torch.cat((
					self.embedding(inputs), self.constant_embedding(inputs)), dim=2)
					# 根据⼀维卷积层的输⼊格式，重新排列张量，以便通道作为第2维
					embeddings = embeddings.permute(0, 2, 1)
					# 每个⼀维卷积层在最⼤时间汇聚层合并后，获得的张量形状是（批量⼤⼩，通道数，1）
					# 删除最后⼀个维度并沿通道维度连结
					encoding = torch.cat([
					torch.squeeze(self.relu(self.pool(conv(embeddings))), dim=-1)
					for conv in self.convs], dim=1)
					outputs = self.decoder(self.dropout(encoding))
					return outputs
```

```Python
对下面的解释：		
encoding = torch.cat([
torch.squeeze(self.relu(self.pool(conv(embeddings))), dim=-1)
    for conv in self.convs], dim=1)
```

$$
% 卷积操作
\text{conv}(embeddings) \text{是一个卷积层的操作。输入参数`embeddings`}\\
\text{的形状是} [batch\_size, embed\_size, sequence\_length], \text{例如} [2, 20, 7]. \text{表示一个批量的}\\
\text{嵌入表示——一个批次的数据，其中嵌入层产生维度为`embed\_size`的嵌入表示。表示为100个}\\
\text{词汇的序列，每个词汇有20个特征表示。输出形状可能是} [2, 100, 5].

% ReLU操作
\text{self.relu(...)} \text{是ReLU激活函数的应用。}

% 池化操作
\text{self.pool(...)} \text{是池化层的操作。该层的目的是减少数据的空间维度，以减少参数数量} [2, 100, 1].

% 去除维度
\text{torch.squeeze(..., dim=-1)} \text{用于去除张量中长度为1的维度，使形状由} [2, 100] \text{变为} [2, 100].

% 卷积操作
\text{接着, 对于} \text{self.conv} \text{的每个卷积层，上述过程重复进行，典型的卷积神经网络包含多个（卷积+激活）操作。}\\
\text{最终层是一个卷积层，最终的} \text{encoding} \text{张量形状可能是} [2, 300].

% 使用LaTeX方程式来表示这些步骤的数学逻辑：

% 卷积层的输出 (以嵌入表示作为输入的例子)：
\text{卷积层输出形状} = [2,100,5]

% ReLU层的输出：
\text{ReLU层输出形状} = [2,100,1]

% 去除维度的操作：
\text{去除维度后形状} = [2,100]

% 卷积层后续操作的输出：
\text{卷积层后续操作形状} = [2,300]
$$

### 中文停用词表

[CharyHong/Stopwords: 常用中文停用词表：包含百度停用词表、哈工大停用词表和四川大学机器智能实验室停用词表。还有整理过的英文停用词表以及其他语言的停用词表 (github.com)](https://github.com/CharyHong/Stopwords)

```Python
def read_text_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
  
    paragraphs = []
    for line in lines:
        line = line.replace('“', '').replace('”', '').replace(',', '').replace('，', '').replace('？', '')
        # 先按句号分割
        parts = line.strip().split('。')
        # 过滤掉空的部分
        parts = [part for part in parts if part.strip()]
  
        if parts:
            paragraphs.append(parts)
    # random.shuffle(paragraphs)
    return paragraphs
class SeqDataset(Dataset):
    def __init__(self, data, block_size, batch_size):
        self.data = data
        self.block_size = block_size
        self.batch_size = batch_size
        self.adjusted_length = len(data) - (len(data) % (block_size * batch_size))
    def __len__(self):
        return self.adjusted_length // self.block_size
    def __getitem__(self, idx):
        start = idx * self.block_size
        end = start + self.block_size
        x = self.data[start:end]
        y = self.data[start + 1:end + 1]
        return torch.tensor(x, dtype=torch.long), torch.tensor(y, dtype=torch.long)

def create_data_loaders(file_path, tokenizer, block_size, batch_size, stopwords_path='stopwords_file.txt', ratio=0.8):
    # 读取文本文件
    paragraphs = read_text_file(file_path)
    input_texts = [token for paragraph in paragraphs for token in paragraph]
    # 使用 tokenizer
    batch_dict_tmp = tokenizer(input_texts, max_length=512, padding=True, truncation=True, return_tensors='pt', add_special_tokens=False)
    data = batch_dict_tmp['input_ids'].numpy()
    # 从文本文件中读取停用词列表，并转换为 token ids
    with open(stopwords_path, 'r') as f:
        stopwords = f.read().splitlines()
    remove_token_ids = [tokenizer.convert_tokens_to_ids(token) for token in stopwords]
    # 移除不需要的 token，并使用 '。' 作为分隔符
    # end_token_id = tokenizer.convert_tokens_to_ids('。')
    merged_data = []
    for seq in data:
        seq = [token for token in seq if token not in remove_token_ids]
        merged_data.extend(seq)
        # merged_data.append(end_token_id)
    # 分割为训练集和验证集
    merged_data = np.array(merged_data)
    split_idx = int(len(merged_data) * ratio)
    train_data, val_data = merged_data[:split_idx], merged_data[split_idx:]
    # 创建数据集和 DataLoader
    train_dataset = SeqDataset(train_data, block_size, batch_size)
    val_dataset = SeqDataset(val_data, block_size, batch_size)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    return train_loader, val_loader
```

### Mixture of Experts 技术

```Python
# 单个模型
https://huggingface.co/mistralai/Mistral-7B-v0.1
#论文
https://arxiv.org/abs/2211.15841
https://ar5iv.labs.arxiv.org/html/2211.15841?_immersive_translate_auto_translate=1
#代码
https://github.com/mistralai/megablocks-public
#下载
magnet:?xt=urn:btih:5546272da9065eddeb6fcd7ffddeef5b75be79a7&dn=mixtral-8x7b-32kseqlen&tr=udp%3A%2F%http://2Fopentracker.i2p.rocks%3A6969%2Fannounce&tr=http%3A%2F%http://2Ftracker.openbittorrent.com%3A80%2Fannounce
```

![[Untitled 50.png]]

### 采用不同的模型（RNN、CNN，Transformer）文本分类

```Python
# source 
/opt/workspace/researcher/THUCNewsProject/Pytorch_Text_Classification_Demo/TextClassification.ipynb
\#video
https://www.bilibili.com/video/BV17G411a7EL/?spm_id_from=autoNext&vd_source=0650603b46ebcb66f5e69177839272c0
该例子里有对于不同模型的分类测试，对于理解模型性能很有好处，另外里面用到了wandb，tensorboard和日志来记录loss 和accurate
是非常好的例子
# 权重初始化，默认xavier
def init_network(model, method='xavier', exclude='embedding', seed=123):
    torch.manual_seed(seed)
    print(model.named_parameters())
    is_transformer = model.__class__.__name__.lower() == "transformer"
    for name, w in model.named_parameters():
        if exclude not in name:
            if 'weight' in name:
                if is_transformer:
                    num_layers = len(model.encoders) + 1
                    # 仅当模型为Transformer时使用这种初始化方式
                    torch.nn.init.normal_(w, mean=0.0, std=0.02 / math.sqrt(2 * num_layers))
                elif method == 'xavier':
                    nn.init.xavier_normal_(w)
                elif method == 'kaiming':
                    nn.init.kaiming_normal_(w)
                else:
                    nn.init.normal_(w)
            elif 'bias' in name:
                nn.init.constant_(w, 0)
            else:
                pass
      
def train(model,  # 需要训练的模型
          train_iter,  # 训练数据迭代器）
          dev_iter=None,  # 验证数据迭代器
          epochs=20,  # 训练周期
          stop_patience=3,  # 提前终止周期（当验证集的loss值持续stop_patience个周期未提升时，提前终止训练）
          device=None,
          log=None):  # 把数据写到日志（写入到public的log文件夹中）
    # # 初始化 wandb
    # wandb.init(project="文本分类", entity="jbsheng")
    # Initialize TensorBoard SummaryWriter
    writer = SummaryWriter()
    if device is None:  # 初始化设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    # 模型基本处理
    init_network(model)
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.0005)
    model.to(device)
    loss = torch.nn.CrossEntropyLoss()
    # 记录当前最小损失值（以便于提前终止训练）
    min_loss_epoch = (None, None)  # 两个参数的元组 (loss, epoch)
    stop_flag = False   # 终止训练的标志
    model_name = model._get_name()  # 获取模型名称
    tip_str = f"\n{model_name} 开始训练....."
    print(tip_str)
    if log:  # 写入日志
        log.info(tip_str)
    for epoch in range(epochs):
        loss_value_list = []  # 将损失值记录在数组里，以便于每个周期计算平均损失值
        total_iter = len(train_iter)
        for i, (x_batch, y_batch) in enumerate(train_iter):
            # 得到x的shape（batch_size, seq_len）不全之处用0补全  （128, 38）
            # 得到要y的shape（batch_size），为实际分类的类别 （128）
            x_batch = torch.LongTensor(x_batch).to(device)
            y_batch = torch.LongTensor(y_batch).to(device)
            # 模型训练
            outputs = model(x_batch)
            optimizer.zero_grad()
            loss_value = loss(outputs, y_batch)
            loss_value.backward()
            optimizer.step()
            # 记录损失值
            loss_value_list.append(loss_value.cpu().data.numpy())
            # 记录到 wandb
            # wandb.log({"train_loss": loss_value.cpu().data.numpy()})      
            # Logging to TensorBoard
            writer.add_scalar('Train/Loss', loss_value.item(), epoch * len(train_iter) + i)
            # 刷新训练信息以便于实时查看
            str_ = f"{model_name} 周期:{epoch + 1}/{epochs} 步数:{i + 1}/{total_iter} mean_loss:{np.mean(loss_value_list): .4f}"
            sys.stdout.write('\r' + str_)
            sys.stdout.flush()
            # 在每一个训练周期训练完后，进行验证操作
            if (i + 1) == total_iter and dev_iter is not None:
                acc_, loss_ = eval(model, dev_iter, device)
                str_ = f" 验证集 loss:{loss_:.4f}  acc:{acc_:.4f}"
                # sys.stdout.flush()
                # sys.stdout.close()
                sys.stdout.write(str_)
                sys.stdout.flush()
                print()
                # 记录到 wandb
                # wandb.log({"dev_loss": loss_, "dev_acc": acc_})
                writer.add_scalar('Validation/Loss', loss_, epoch)
                writer.add_scalar('Validation/Accuracy', acc_, epoch)
          
                if log:
                    tip_str = f"训练周期:{epoch + 1}/{epochs} 训练集 loss:{np.mean(loss_value_list):.4f} 验证集 loss:{loss_:.4f} acc:{acc_:.4f}"
                    log.info(tip_str)
                model.train()  # 由于验证的时候，将模型切换到了验证模式，在训练的时候需要再切换回来
                # 判断是否需要终止训练
                if (min_loss_epoch[0] is None) or (min_loss_epoch[0] > loss_):
                    min_loss_epoch = (loss_, epoch)
                else:
                    if (epoch - min_loss_epoch[1]) >= stop_patience:
                        stop_flag = True
                        break
        # 终止训练
        if stop_flag is True:
            tip_str = f"训练损失值持续 {stop_patience} 个周期没有提高，停止训练"
            # print(tip_str)
            if log:
                log.info(tip_str)
            break
    # 结束 wandb 记录
    # wandb.finish()
    # Close the TensorBoard SummaryWriter
    writer.close()

def eval(model, data_iter, device):
    """  模型验证
    :param model: 模型
    :param data_iter: 验证数据集迭代器
    :param device: 设备
    :return: 验证集的平均 准确率、损失值
    """
    model.eval()
    with torch.no_grad():
        acc_list = []
        loss_list = []
        # 之所以验证的时候也是分周期，是因为在一旦模型很大同时验证集也很大的时候，可能会造成内存不够的情况
        for x, y in data_iter:
            dev_x_ = torch.LongTensor(x).to(device)
            dev_y_ = torch.LongTensor(y).to(device)
            outputs = model(dev_x_)
            p_ = torch.max(outputs.data, 1)[1].cpu().numpy()
            acc_ = metrics.accuracy_score(y, p_)
            loss_ = torch.nn.CrossEntropyLoss()(outputs, dev_y_)
            acc_list.append(acc_)
            loss_list.append(loss_.cpu().data.numpy())
        return np.mean(acc_list), np.mean(loss_list)

def create_log(path, stream=False):
    """
    获取日志对象
    :param path: 日志文件路径
    :param stream: 是否输出控制台
                False: 不输出到控制台
                True: 输出控制台，默认为输出到控制台
    :return:日志对象
    """
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    fmt = logging.Formatter('[%(asctime)s] [%(levelname)s] %(message)s', '%Y-%m-%d %H:%M:%S')
    if stream:
        # 设置CMD日志
        sh = logging.StreamHandler()
        sh.setFormatter(fmt)
        sh.setLevel(logging.DEBUG)
        logger.addHandler(sh)
    # 设置文件日志
    fh = logging.FileHandler(path, encoding='utf-8')
    fh.setFormatter(fmt)
    fh.setLevel(logging.DEBUG)
    logger.addHandler(fh)
    return logger
# 本项目的是基于字的，没有基于词
c2i = get_vocab()   # 获取词典
class_ = get_classs()  # 获取数据集的类别
max_len_ = 38
n_class_ = len(class_)  # 类别的数量
vocab_size_ = len(c2i)  # 词典大小
epochs = 10  # 训练周期
stop_patience = 1   # 提前终止周期（当验证集损失函数连续 stop_patience 个周期没有减少小于最小值时终止训练）
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# 获取数据集，同时创建迭代器
train_samples = load_dataset('train', max_len=max_len_)
dev_samples = load_dataset('dev', max_len=max_len_)
train_iter = DataIter(train_samples, batch_size=128)
dev_iter = DataIter(dev_samples, batch_size=128)
models = get_models(vocab_size=vocab_size_,  # 词典大小
                    n_class=n_class_,  # 类别个数
                    seq_len=max_len_,  # 句子长度
                    device=device)
# 创建日志
log_path = os.path.join(log_dir, f"{time.strftime('%Y-%m-%d_%H_%M')}.log")
log = create_log(path=log_path)  # 获取日志文件

# 训练所有的样本
for model in models:
    train(model,  # 需要训练的模型
          train_iter,  # 训练数据迭代器）
          dev_iter=dev_iter,  # 验证数据迭代器
          epochs=epochs,  # 训练周期
          stop_patience=stop_patience,  # 提前终止周期
          device=device,
          log=log)  # 记录训练数据

def test(config, model, test_iter, model_name):
    # test
    model.load_state_dict(torch.load(config.save_path + model_name + '.pt'))
    model.eval()
    start_time = time.time()
    test_acc, test_loss, test_report, test_confusion = evaluate(config, model, test_iter, test=True)
    msg = 'Test Loss: {0:>5.2},  Test Acc: {1:>6.2%}'
    print(msg.format(test_loss, test_acc))
    print("Precision, Recall and F1-Score...")
    print(test_report)
    print("Confusion Matrix...")
    print(test_confusion)
    time_dif = get_time_dif(start_time)
    print("Time usage:", time_dif)
```

### Tensorboard 用法

```Python
# Play tensorboard
cd /opt/workspace/researcher/THUCNewsProject/Pytorch_Text_Classification_Demo
tensorboard --logdir=runs
# Confusion Matrix
# Add confusion matrix image to tensorboard
https://discuss.pytorch.org/t/save-confusion-matrix-image-to-tensorboard/86529
https://neptune.ai/blog/tensorboard-tutorial
https://stackoverflow.com/questions/65498782/how-to-dump-confusion-matrix-using-tensorboard-logger-in-pytorch-lightning
def log_confusion_matrix(epoch, logs):
    predictions = model.predict(X_test)
    predictions = np.argmax(predictions, axis=1)
    cm = metrics.confusion_matrix(y_test, predictions)
    figure = plot_confusion_matrix(cm, class_names=class_names)
    cm_image = plot_to_image(figure)
    with file_writer_cm.as_default():
        tf.summary.image("Confusion Matrix", cm_image, step=epoch)
```

### Prompt 优化

```JavaScript
# TEXTGRAD
https://github.com/zou-group/textgrad
https://medium.com/@jelkhoury880/textgrad-vs-dspy-revolutionizing-ai-system-optimization-through-automatic-text-based-58f8ee776447
# DSPy
/opt/workspace/researcher/labTest/DSPy-BaseTest.ipynb
https://www.unite.ai/optimize-llm-with-dspy-a-step-by-step-guide-to-build-optimize-and-evaluate-ai-systems/
```

## 图像模型

### pre_trained models以及设定每层的学习率

```Python
Tensorflow Hub: https://www.kaggle.com/models?tfhub-redirect=true
TIMM： https://github.com/huggingface/pytorch-image-models
import timm
from torch import nn
import torch.optim as optim
# 创建模型
model = timm.create_model('resnet18', pretrained=True)
n_classes = 10  # 假设有10个类别
model.fc = nn.Linear(model.fc.in_features, n_classes)
# 准备不同的参数组
fc_params = model.fc.parameters()
base_params = filter(lambda p: id(p) not in list(map(id, model.fc.parameters())), model.parameters())
# 创建优化器，为不同的参数组设置不同的学习率
optimizer = optim.Adam([
    {'params': base_params, 'lr': 0},  # 除了fc层以外的层的学习率设置为0
    {'params': fc_params, 'lr': 0.001}  # fc层的学习率
])
```

```Python
\#Where to find pre_trained models
Training Image classfication
\#Autogluon
https://auto.gluon.ai/stable/tutorials/multimodal/image_prediction/beginner_image_cls.html
export LD_PRELOAD=$LD_PRELOAD:/opt/workspace/miniconda3/envs/mlearn/lib/python3.8/site-packages/scikit_learn.libs/libgomp-d22c30c5.so.1.0.0
\#Awespme-Backbones
https://github.com/Fafa-DL/Awesome-Backbones/blob/main/datas/docs/How_to_train.md
https://github.com/Fafa-DL/Awesome-Backbones/blob/main/datas/docs/Data_preparing.md
1, 运行做早期处理
/opt/workspace/app/Awesome-Backbones/autogluonTest.ipynb  生成annotations.txt and train.csv
/opt/workspace/app/Awesome-Backbones/data2/dataproc1.py  把image文件copy到对应的分类目录
2. split data into train/test 打包nand gnerate train.txt and test.txt
/opt/workspace/app/Awesome-Backbones/tools/split_data.py
/opt/workspace/app/Awesome-Backbones/tools/get_annotation.py
3.start train
python tools/train.py models/seresnext/seresnext101.py

# Self Made !!! It is the best
/opt/workspace/app/Awesome-Backbones/TestCNNTrain.ipynb
import torch
import torchvision
from torch import nn
from d2l import torch as d2l
def apply(img, aug, num_rows=2, num_cols=4, scale=1.5):
    Y = [aug(img) for _ in range(num_rows * num_cols)]
    d2l.show_images(Y, num_rows, num_cols, scale=scale)
shape_aug = torchvision.transforms.RandomResizedCrop(
    (200, 200), scale=(0.1, 1), ratio=(0.5, 2))
color_aug = torchvision.transforms.ColorJitter(
    brightness=0.5, contrast=0.5, saturation=0.5, hue=0.5)
augs = torchvision.transforms.Compose([
    torchvision.transforms.RandomHorizontalFlip(), color_aug, shape_aug])
all_images = torchvision.datasets.CIFAR10(train=True, root="../data",
                                          download=True)
train_augs = torchvision.transforms.Compose([
     torchvision.transforms.RandomHorizontalFlip(),
     torchvision.transforms.ToTensor()])
test_augs = torchvision.transforms.Compose([
     torchvision.transforms.ToTensor()])
def load_cifar10(is_train, augs, batch_size):
    dataset = torchvision.datasets.CIFAR10(root="../data", train=is_train,
                                           transform=augs, download=True)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size,
                    shuffle=is_train, num_workers=d2l.get_dataloader_workers())
    return dataloader
#@save
def train_batch_ch13(net, X, y, loss, trainer, devices):
    """用多GPU进行小批量训练"""
    if isinstance(X, list):
        # 微调BERT中所需
        X = [x.to(devices[0]) for x in X]
    else:
        X = X.to(devices[0])
    y = y.to(devices[0])
    net.train()
    trainer.zero_grad()
    pred = net(X)
    l = loss(pred, y)
    l.sum().backward()
    trainer.step()
    train_loss_sum = l.sum()
    train_acc_sum = d2l.accuracy(pred, y)
    return train_loss_sum, train_acc_sum
#@save
def train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs,
               devices=d2l.try_all_gpus()):
    """用多GPU进行模型训练"""
    timer, num_batches = d2l.Timer(), len(train_iter)
    animator = d2l.Animator(xlabel='epoch', xlim=[1, num_epochs], ylim=[0, 1],
                            legend=['train loss', 'train acc', 'test acc'])
    net = nn.DataParallel(net, device_ids=devices).to(devices[0])
    for epoch in range(num_epochs):
        # 4个维度：储存训练损失，训练准确度，实例数，特点数
        metric = d2l.Accumulator(4)
        for i, (features, labels) in enumerate(train_iter):
            timer.start()
            l, acc = train_batch_ch13(
                net, features, labels, loss, trainer, devices)
            metric.add(l, acc, labels.shape[0], labels.numel())
            timer.stop()
            if (i + 1) % (num_batches // 5) == 0 or i == num_batches - 1:
                animator.add(epoch + (i + 1) / num_batches,
                             (metric[0] / metric[2], metric[1] / metric[3],
                              None))
        test_acc = d2l.evaluate_accuracy_gpu(net, test_iter)
        animator.add(epoch + 1, (None, None, test_acc))
    print(f'loss {metric[0] / metric[2]:.3f}, train acc '
          f'{metric[1] / metric[3]:.3f}, test acc {test_acc:.3f}')
    print(f'{metric[2] * num_epochs / timer.sum():.1f} examples/sec on '
          f'{str(devices)}')
batch_size, devices, net = 256, d2l.try_all_gpus(), d2l.resnet18(10, 3)
def init_weights(m):
    if type(m) in [nn.Linear, nn.Conv2d]:
        nn.init.xavier_uniform_(m.weight)
net.apply(init_weights)
def train_with_data_aug(train_augs, test_augs, net, lr=0.001):
    train_iter = load_cifar10(True, train_augs, batch_size)
    test_iter = load_cifar10(False, test_augs, batch_size)
    loss = nn.CrossEntropyLoss(reduction="none")
    trainer = torch.optim.Adam(net.parameters(), lr=lr)
    train_ch13(net, train_iter, test_iter, loss, trainer, 10, devices)
train_with_data_aug(train_augs, test_augs, net)
```

### 微调

```Python
/opt/workspace/app/d2l-zh/pytorch/chapter_computer-vision/fine-tuning.ipynb
#替换最后一层，然后对最后一层调整学习率，准确率会较其他高，但有可能毁掉pretrain的记忆
finetune_net = torchvision.models.resnet18(pretrained=True)
finetune_net.fc = nn.Linear(finetune_net.fc.in_features, 2)
nn.init.xavier_uniform_(finetune_net.fc.weight);
finetune_net.to(d2l.try_gpu());
def train_fine_tuning(net, learning_rate, batch_size=128, num_epochs=5,
                      param_group=True):
    train_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(
        os.path.join(data_dir, 'train'), transform=train_augs),
        batch_size=batch_size, shuffle=True)
    test_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(
        os.path.join(data_dir, 'test'), transform=test_augs),
        batch_size=batch_size)
    devices = d2l.try_all_gpus()
    loss = nn.CrossEntropyLoss(reduction="none")
    if param_group:
        params_1x = [param for name, param in net.named_parameters()
             if name not in ["fc.weight", "fc.bias"]]
        trainer = torch.optim.SGD([{'params': params_1x},
                                   {'params': net.fc.parameters(),
                                    'lr': learning_rate * 10}],
                                lr=learning_rate, weight_decay=0.001)
    else:
        trainer = torch.optim.SGD(net.parameters(), lr=learning_rate,
                                  weight_decay=0.001)
    d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs,
                   devices)
#完全不碰pretrain的模型，freeze梯度，之后加上新的层，因为用了BN，可以调大学习率
finetune_net = torchvision.models.resnet18(pretrained=True)
# Freeze all the layers in pretrained_net
for param in finetune_net.parameters():
    param.requires_grad = False
# for param in finetune_net.fc.parameters():
#     print(param.requires_grad)
out_features = finetune_net.fc.out_features
# 创建一个新的全连接层，输入特征数与预训练模型最后一层的输出特征数相同，输出特征数为 2
new_fc = nn.Linear(out_features, 2)
# 创建一个新的 Sequential 模型，包括预训练模型和新添加的全连接层
new_model = nn.Sequential(
    finetune_net,
    BatchNorm(1000,num_dims=2),
    nn.ReLU(),
    new_fc
)
# 将新添加的全连接层设置为可训练
new_model[3].requires_grad = True
nn.init.xavier_uniform_(new_model[3].weight);
new_model.to(d2l.try_gpu());
train_fine_tuning(new_model, 5e-5*5, param_group=False, num_epochs=10)
#这种情况特殊，已知类别在pretrain上的权重，可以赋予新的输出层
weight = pretrained_net.fc.weight
hotdog_w = torch.split(weight.data, 1, dim=0)[934]
hotdog_w.shape
finetune_net = torchvision.models.resnet18(pretrained=True)
# Freeze all the layers in pretrained_net
for param in finetune_net.parameters():
    param.requires_grad = False
# Replace Fc layer with your own
finetune_net.fc = nn.Linear(finetune_net.fc.in_features, 2)
# 假设您希望使用这个权重初始化第一个输出节点
# print(finetune_net.fc.weight[0])
finetune_net.fc.weight[0].data = hotdog_w.data

# nn.init.xavier_uniform_(finetune_net.fc.weight);
for param in finetune_net.fc.parameters():
    print(param)
finetune_net.to(d2l.try_gpu());
summary(finetune_net, (3, 244, 244))
train_fine_tuning(finetune_net, 5e-5, param_group=False, num_epochs=10)
```

```Python
https://www.kaggle.com/code/sachinpatil1280/cifar-10-image-classification-cnn-89
plt.figure(figsize=(20,20)) # specifying the overall grid size
plt.subplots_adjust(hspace=0.4)
labels = ['airplane', 'automobile', 'bird', 'cat', 'deer', 
          'dog', 'frog', 'horse', 'ship', 'truck']
for i in range(100):
  
    plt.subplot(10,10,i+1)    # the number of images in the grid is 5*5 (25)
    plt.imshow(X_train[i])
    plt.title(labels[int(y_train[i])],fontsize=12)
    plt.axis('off')
plt.show()
# count for Training dataset
label,count = np.unique(y_train,return_counts=True)
uni = pd.DataFrame(data=count,index=labels,columns=['Count'])
plt.figure(figsize=(14,4),dpi=200)
sns.barplot(data=uni,x=uni.index,y='Count',palette='icefire',width=0.5).set_title('Class distribution in training set',fontsize=15)
plt.show()
tf.keras.utils.plot_model(model,to_file='model.png',show_shapes=True)
https://www.kaggle.com/code/sachinpatil1280/cifar-10-image-classification-cnn-89
/opt/workspace/app/cifar10/TestCNNTrain.ipynb
```

```JavaScript
https://www.kaggle.com/competitions/dog-breed-identification/overview
这个题目特点是输出分类要你求概率分布
当输出分类很多时候, 有可能top几的分类其实是一个东西.所以我们这里求了概率
preds = []
for data, label in test_iter:
    output = torch.nn.functional.softmax(net(data.to(devices[0])), dim=1)
    preds.extend(output.cpu().detach().numpy())
```

### 图像数据集

```JavaScript
\#coco 目标检测
https://cocodataset.org/\#overview
\#DeepFashion2数据集
https://zhuanlan.zhihu.com/p/285576127
https://www.kaggle.com/datasets/agrigorev/clothing-dataset-full/code
https://medium.com/data-science-insider/clothing-dataset-5b72cd7c3f1f
https://www.kaggle.com/code/parthoece/clothing-outfit-detection
\#imagenet
\#cifar10
```

### Yolo的使用和自己标注

```JavaScript
\#Yolo example
/opt/workspace/app/raylab/kaggle-cowboy.ipynb
\#Yolo theory
https://mmyolo.readthedocs.io/zh_CN/latest/recommended_topics/algorithm_descriptions/yolov5_description.html
\#How to label by yourself and do it in Windows
https://zhuanlan.zhihu.com/p/501798155
https://tech.aru-zakki.com/how-to-use-labelimg/
Make sure you are using python less than 3.10!! otherwise
Traceback (most recent call last):
  File "G:\ResearchDirection\AI\miniconda3\envs\mlearn\Lib\site-packages\libs\canvas.py", line 530, in paintEvent
    p.drawLine(self.prev_point.x(), 0, self.prev_point.x(), self.pixmap.height())
TypeError: arguments did not match any overloaded call:
  drawLine(self, l: QLineF): argument 1 has unexpected type 'float'
  drawLine(self, line: QLine): argument 1 has unexpected type 'float'
  drawLine(self, x1: int, y1: int, x2: int, y2: int): argument 1 has unexpected type 'float'
  drawLine(self, p1: QPoint, p2: QPoint): argument 1 has unexpected type 'float'
  drawLine(self, p1: Union[QPointF, QPoint], p2: Union[QPointF, QPoint]): argument 1 has unexpected type 'float'
In my env, mlagents is python 3.9 version
conda activate mlagents
pip3 install labelImg
<<before run, setup DISPLAY and xauth probably>>
labelImg
(mlearn) [raysheng@MONSTER:/opt/workspace/raylab/parrot]$ tree
.
├── dataset
│   ├── images
│   │   ├── train
│   │   └── val
│   └── labels
│       ├── train
│       └── val
├── images <- generated by labelImg
└── labels <- generated by labelImg
copy all images to images subfolder
<< setup View -> Autosave and Display Label>>
<<下面jetson程序会对上面进行处理，准备yolo训练>>
/opt/workspace/app/raylab/parrot/family_identify.ipynb
\#from WSL
python detect.py --source 'rtsp://ray:r122105@************:554/h264
python detect.py --weights /opt/workspace/monster/motioneye/best.pt --source /opt/workspace/monster/motioneye/netcaster_NHK_Fee_collector__real.png
python detect.py --weights /opt/workspace/raylab/parrot/parrot.pt --source /opt/workspace/VisualGLM-6B/motion/2023-10-22/10-26-15.mp4

1。在Windows上
conda activate mlagents
labelImg
2. Copy 标注好的数据集到Jetson
然后参考，训练
/opt/workspace/app/raylab/parrot/family_identify.ipynb
3. Copy best.pt 到WSL环境，然后inference
python detect.py --weights /opt/workspace/raylab/parrot/parrot.pt --source /opt/workspace/VisualGLM-6B/motion/2023-10-22/10-26-15.mp4
```

[[Raspberry PiにmotionEyeOSをインストール 令和2年(2020)5月版]]

## 声音模型

### Good  article

```
https://towardsdatascience.com/fine-tune-the-audio-spectrogram-transformer-with-transformers-73333c9ef717

```

### 对声音产生Token

[[WavTokenizer SOTA discrete acoustic codec models with 40 tokens per second for audio language modeling]]

```LaTeX
#数据集
http://wham.whisper.ai/
https://catalog.ldc.upenn.edu/LDC93S6A
\#Tool
https://www.merl.com/research/highlights/deep-clustering
```

```JavaScript
\#librosa音频处理教程
https://zhuanlan.zhihu.com/p/509839164
https://www.cnblogs.com/xingshansi/p/6816308.html
pip install librosa scikit-learn numpy
import librosa
import numpy as np
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
# 1. 声音文件预处理
def preprocess_audio(file_path):
    audio, sr = librosa.load(file_path, sr=None)
    return audio, sr
# 2. 声音分段（此处省略，直接用整段声音）
# 常用的声音分段方法包括使用VAD（Voice Activity Detection）进行静音检测与去除。
# 3. 特征提取
def extract_features(audio, sr):
    mfccs = librosa.feature.mfcc(audio, sr, n_mfcc=13)
    return mfccs
# 4. 特征向量生成（省略，使用原始MFCC特征）
# 通常在这里进行特征选择、归一化等操作。
# 5. 模式匹配和识别
def train_and_classify(train_data, train_labels, test_data):
    # 特征标准化
    scaler = StandardScaler()
    train_data = scaler.fit_transform(train_data)
    test_data = scaler.transform(test_data)
  
    # 使用SVM进行训练
    classifier = SVC()
    classifier.fit(train_data, train_labels)
  
    # 预测
    predictions = classifier.predict(test_data)
    return predictions
# 主函数
if __name__ == '__main__':
    # 训练数据（这里使用同一个文件作为简单示例，实际应用中应该使用不同的训练和测试数据）
    train_audio, sr = preprocess_audio("your_train_audio_file.wav")
    train_features = extract_features(train_audio, sr)
  
    # 假设有两个标签，0和1
    train_labels = np.array([0, 1])
  
    # 测试数据
    test_audio, sr = preprocess_audio("your_test_audio_file.wav")
    test_features = extract_features(test_audio, sr)
  
    # 模型训练和预测
    predictions = train_and_classify(train_features.T, train_labels, test_features.T)
  
    print("预测标签：", predictions)
pip install fastdtw
import librosa
from fastdtw import fastdtw
from scipy.spatial.distance import euclidean
import numpy as np
# 加载第一个音频文件并提取MFCC特征
audio_path1 = 'audio1.wav'
x1, sr1 = librosa.load(audio_path1)
mfcc1 = librosa.feature.mfcc(y=x1, sr=sr1, n_mfcc=13)
# 加载第二个音频文件并提取MFCC特征
audio_path2 = 'audio2.wav'
x2, sr2 = librosa.load(audio_path2)
mfcc2 = librosa.feature.mfcc(y=x2, sr=sr2, n_mfcc=13)
# 使用fastdtw进行动态时间规整
distance, path = fastdtw(mfcc1.T, mfcc2.T, dist=euclidean)
# 打印DTW距离和路径
print(f"DTW distance: {distance}")
print(f"DTW path: {path}")
# 通过DTW距离进行相似度判断（这个阈值可以根据实际需求调整）
threshold = 10000
if distance < threshold:
    print("The audio files are similar.")
else:
    print("The audio files are not similar.")
\#DeepLearning in sound files
基于CNN+MFCC的语音情感识别
https://zhuanlan.zhihu.com/p/148355978
如何在音频分析中使用机器学习和深度学习
https://blog.csdn.net/calvinpaean/article/details/103204271
语音情感识别数据集及特征提取
https://zhuanlan.zhihu.com/p/497299680
只需5秒音源，这个网络就能实时“克隆”你的声音
https://blog.csdn.net/dQCFKyQDXYm3F8rB0/article/details/103059937
https://github.com/CorentinJ/Real-Time-Voice-Cloning
https://blog.csdn.net/zzfive/article/details/125379373?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-8-125379373-blog-103059937.235^v38^pc_relevant_sort_base1&spm=1001.2101.3001.4242.5&utm_relevant_index=11
#理解Mel频谱图
https://cloud.tencent.com/developer/article/1689856
# 特征提取
https://blog.csdn.net/zzc15806/article/details/84974458
https://github.com/JasonZhang156/Sound-Recognition-Tutorial
```

### 降噪的处理

/opt/workspace/GPT-SoVITS/SimplebirdTest.ipynb
[https://blog.csdn.net/u013250861/article/details/111290319](https://blog.csdn.net/u013250861/article/details/111290319)
List of audio-denosing in github
https://github.com/topics/audio-denoising

Looks good
[https://github.com/jose-solorzano/audio-denoiser](https://github.com/jose-solorzano/audio-denoiser)
[https://github.com/jose-solorzano/audio-denoiser](https://github.com/jose-solorzano/audio-denoiser)

### 数据增强 (Augmentation)

[https://www.kaggle.com/code/hidehisaarai1213/rfcx-audio-data-augmentation-japanese-english](https://www.kaggle.com/code/hidehisaarai1213/rfcx-audio-data-augmentation-japanese-english)

````Python
使用Autoencoder进行音频降噪是一种现代且有效的方法。下面是一个简单的示例，展示了如何使用Keras构建和训练一个用于音频降噪的Autoencoder模型。
### 1. 准备数据
首先，我们需要准备干净和带噪音的音频数据。这里我们假设已经有两个数据集：一个是干净的音频数据，另一个是带噪音的音频数据。
```python
import numpy as np
import librosa
import os
# 加载音频数据
def load_audio_files(directory, sample_rate=16000):
    audio_files = []
    for filename in os.listdir(directory):
        if filename.endswith('.wav'):
            file_path = os.path.join(directory, filename)
            audio, sr = librosa.load(file_path, sr=sample_rate)
            audio_files.append(audio)
    return audio_files
clean_data_dir = 'path_to_clean_audio_files'
noisy_data_dir = 'path_to_noisy_audio_files'
clean_audio = load_audio_files(clean_data_dir)
noisy_audio = load_audio_files(noisy_data_dir)
# 确保干净和带噪音的数据对齐
min_length = min(len(clean_audio), len(noisy_audio))
clean_audio = clean_audio[:min_length]
noisy_audio = noisy_audio[:min_length]
# 将数据转换为numpy数组并裁剪到相同的长度
clean_audio = [a[:min(map(len, clean_audio))] for a in clean_audio]
noisy_audio = [a[:min(map(len, noisy_audio))] for a in noisy_audio]
clean_audio = np.array(clean_audio)
noisy_audio = np.array(noisy_audio)
```
### 2. 构建Autoencoder模型
使用Keras构建一个简单的Autoencoder模型：
```python
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, Dense, Conv1D, MaxPooling1D, UpSampling1D
# 构建Autoencoder模型
input_dim = clean_audio.shape[1]
input_audio = Input(shape=(input_dim, 1))
# Encoder
x = Conv1D(16, 3, activation='relu', padding='same')(input_audio)
x = MaxPooling1D(2, padding='same')(x)
x = Conv1D(8, 3, activation='relu', padding='same')(x)
encoded = MaxPooling1D(2, padding='same')(x)
# Decoder
x = Conv1D(8, 3, activation='relu', padding='same')(encoded)
x = UpSampling1D(2)(x)
x = Conv1D(16, 3, activation='relu', padding='same')(x)
x = UpSampling1D(2)(x)
decoded = Conv1D(1, 3, activation='sigmoid', padding='same')(x)
autoencoder = Model(input_audio, decoded)
autoencoder.compile(optimizer='adam', loss='binary_crossentropy')
```
### 3. 训练Autoencoder模型
将音频数据转换为合适的形状并训练模型：
```python
# 数据预处理
clean_audio = clean_audio.reshape(clean_audio.shape[0], clean_audio.shape[1], 1)
noisy_audio = noisy_audio.reshape(noisy_audio.shape[0], noisy_audio.shape[1], 1)
# 训练模型
autoencoder.fit(noisy_audio, clean_audio, epochs=50, batch_size=16, shuffle=True, validation_split=0.2)
```
### 4. 使用训练好的模型进行降噪
使用训练好的模型对带噪音的音频数据进行降噪处理：
```python
# 对带噪音的音频数据进行降噪处理
denoised_audio = autoencoder.predict(noisy_audio)
# 保存降噪后的音频数据
output_file_path = 'path_to_save_denoised_audio.wav'
write(output_file_path, sample_rate, denoised_audio.reshape(-1).astype(np.float32))
```
### 总结
这个示例展示了如何使用Autoencoder模型进行音频降噪处理。首先准备干净和带噪音的音频数据，然后构建并训练Autoencoder模型，最后使用训练好的模型对带噪音的音频数据进行降噪处理。根据具体需求，可以对模型结构和超参数进行调整，以达到更好的降噪效果。
````

### Text转语音

```python
import requests
import json
import os

# FAST AND CHEAP AND GOOD TTS BY NEETS

# https://neets.ai/
# get your free API key here: https://neets.ai/

response = requests.request(
  method="POST",
  url="https://api.neets.ai/v1/tts",
  headers={
    "Content-Type": "application/json",
    "X-API-Key": os.getenv("NEETS_API_KEY")
  },
  json={
    "text": "Marcus Aurelius was the son of the praetor Marcus Annius Verus and his wife, Domitia Calvilla. He was related through marriage to the emperors Trajan and Hadrian. Marcus was three when his father died, and was raised by his mother and paternal grandfather. After Hadrian's adoptive son, Aelius Caesar, died in 138, Hadrian adopted Marcus's uncle Antoninus Pius as his new heir. In turn, Antoninus adopted Marcus and Lucius, the son of Aelius. Hadrian died that year, and Antoninus became emperor. Now heir to the throne, Marcus studied Greek and Latin under tutors such as Herodes Atticus and Marcus Cornelius Fronto. He married Antoninus's daughter Faustina in 145.",
    "voice_id": "us-male-5",
    "params": {
      "model": "style-diff-500" # also try ar-diff-50k
    }
  }
)
print(response.content)

with open("neets_demo.mp3", "wb") as f:
  f.write(response.content)
```

## 通用模型

@ To fix d2l bug , you need to add below contents
![[d2lpart.txt]]
[d2l-en/d2l at master · d2l-ai/d2l-en · GitHub](https://github.com/d2l-ai/d2l-en/tree/master/d2l)

```JavaScript
\#new version like
# you can refer to /databank/workspace/miniconda3/envs/mlearn/lib/python3.8/site-packages/d2l/torch.py
dropout1, dropout2 = 0.2, 0.5
num_epochs, lr, batch_size = 10, 0.5, 256
net = nn.Sequential(nn.Flatten(),
        nn.Linear(784, 256),
        nn.ReLU(),
        # 在第一个全连接层之后添加一个dropout层
        nn.Dropout(dropout1),
        nn.Linear(256, 256),
        nn.ReLU(),
        # 在第二个全连接层之后添加一个dropout层
        nn.Dropout(dropout2),
        nn.Linear(256, 10)).to('cuda')
train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size)
d2l.train_ch6(net, train_iter, test_iter,  num_epochs, lr, "cuda")
```

## GAN model

```JavaScript
Reference
https://pytorch-geometric.readthedocs.io/en/latest/notes/installation.html

Installation 
(1)Download dependency package
https://data.pyg.org/whl/
check torch and cuda version and enter subfolder
check python version
pip install pyg_lib torch_scatter torch_sparse torch_cluster torch_spline_conv -f https://data.pyg.org/whl/torch-2.0.0+cu118.html
pip install torch_geometric
(2)Install package with this sequence
pip install pyg_lib-0.2.0+pt20cu118-cp310-cp310-linux_x86_64.whl
pip install torch_scatter-2.1.1+pt20cu118-cp310-cp310-linux_x86_64.whl
pip install torch_sparse-0.6.17+pt20cu118-cp310-cp310-linux_x86_64.whl
pip install torch_cluster-1.6.1+pt20cu118-cp310-cp310-linux_x86_64.whl
pip install torch_spline_conv-1.2.2+pt20cu118-cp310-cp310-linux_x86_64.whl
pip install torch-geometric
\#got error
OSError: /home/<USER>/miniconda3/envs/mlearn/lib/python3.10/site-packages/torch_cluster/_version_cuda.so: undefined symbol: _ZN5torch3jit17parseSchemaOrNameERKSs
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda-repo-rhel9-11-8-local-11.8.0_520.61.05-1.x86_64.rpm
sudo rpm -i cuda-repo-rhel9-11-8-local-11.8.0_520.61.05-1.x86_64.rpm
sudo dnf clean all
sudo dnf -y module install nvidia-driver:latest-dkms
sudo dnf -y install cuda
Nvidia driver download
https://www.nvidia.com/Download/index.aspx?lang=en-us
https://docs.nvidia.com/cuda/wsl-user-guide/index.html\#getting-started-with-cuda-on-wsl
https://learn.microsoft.com/en-us/windows/ai/directml/gpu-tensorflow-wsl
```

## 打包后计划输出

```Python
train = pd.read_csv("/opt/workspace/app/Awesome-Backbones/data2/train.csv")
labels = list(pd.read_csv("/opt/workspace/app/Awesome-Backbones/data2/train.csv")['label'])
labels_unique = list(set(list(labels))) \#list index--labels
indexed_labels = enumerate(labels_unique)
with open("annotations.txt", "w") as f:
    for index, label in indexed_labels:
        f.write(f"{label} {index}\n")
label_nums = []
for i in range(len(labels)):
    label_nums.append(labels_unique.index(labels[i]))
train['number'] = label_nums
train.to_csv("./train_num_label.csv", sep=' ', columns=['image', 'number'], index=False, header=False) #记录对应关系
preds = predictor.predict(test_data.drop(columns=[id]))
submission = pd.DataFrame({id:test_data[id], lable:preds})
submission.to_csv('submission.csv',index=False)
# method 1
submission = pd.read_csv("/opt/workspace/app/Awesome-Backbones/data2/test.csv")
submission["label"] = pd.Series(predictions_labels)
submission.to_csv(saveFileName, index=False)
\#method 2
saveFileName = '/opt/workspace/app/Awesome-Backbones/data2/submission.csv'
test_path = "/opt/workspace/app/Awesome-Backbones/data2/test.csv"  
test_data = pd.read_csv(test_path)
test_data['label'] = pd.Series(predictions_labels)
submission = pd.concat([test_data['image'], test_data['label']], axis=1)
submission.to_csv(saveFileName, index=False)
```

## 分类问题指标

```JavaScript
# 【深度学习】——性能指标（ROC、MAP、AUC等）
https://blog.csdn.net/qq_45769063/article/details/120008815
# python实现二分类和多分类的ROC曲线
https://zhuanlan.zhihu.com/p/266386193
# 深度学习分类类别不平衡_利用类权重来改善类别不平衡
https://blog.csdn.net/weixin_29086203/article/details/112395318
#数据类别不平衡时的模型评估和解决办法
https://zhuanlan.zhihu.com/p/165435239
# 用神经网络解决一个简单的二类分类问题
https://blog.csdn.net/ronaldo_hu/article/details/91651594/?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1--blog-104332809.235^v38^pc_relevant_sort_base1&spm=1001.2101.3001.4242.2&utm_relevant_index=4
preds = []
for data, label in test_iter:
    output = torch.nn.functional.softmax(net(data.to(devices[0])), dim=1)
    preds.extend(output.cpu().detach().numpy())
# y_test is the true labels
# scores is the classifier's probability output
fpr, tpr, thresholds  =  roc_curve(y_test, scores)
```

## Kaggle 技巧

### 提高训练效果方法

```JavaScript
# 发现提高训练效果方法
1. 适当的数据片大小 比如64比256要好
2. 不要太大的epoch 100 better than 500， 配合上Kfolder 会收到好的效果，同时val loss和train loss都递减

#######################################################################################
def shopee_dataset(
    download_dir: str,
    is_bytearray=False,
):
    """
    Download Shopee dataset for demo.
    Parameters
    ----------
    download_dir
        Path to save the dataset locally.
    Returns
    -------
    train and test set of Shopee dataset in pandas DataFrame format.
    """
    zip_file = "https://automl-mm-bench.s3.amazonaws.com/vision_datasets/shopee.zip"
    from autogluon.core.utils.loaders import load_zip
    load_zip.unzip(zip_file, unzip_dir=download_dir)
    dataset_path = os.path.join(download_dir, "shopee")
    train_data = pd.read_csv(f"{dataset_path}/train.csv")
    test_data = pd.read_csv(f"{dataset_path}/test.csv")
    expander = path_to_bytearray_expander if is_bytearray else path_expander
    train_data["image"] = train_data["image"].apply(lambda ele: expander(ele, base_folder=dataset_path))
    test_data["image"] = test_data["image"].apply(lambda ele: expander(ele, base_folder=dataset_path))
    return train_data, test_data

#######################################################################################
preds = predictor.predict(test_data.drop(columns=[id]))
submission = pd.DataFrame({id:test_data[id], lable:preds})
submission.to_csv('submission.csv',index=False)
# 将其重新格式化以导出到Kaggle
test_data['SalePrice'] = pd.Series(preds.cpu().squeeze().tolist())
# test_data['SalePrice'] = pd.Series(preds.reshape(1, -1)[0])
submission = pd.concat([test_data['Id'], test_data['SalePrice']], axis=1)
submission.to_csv('submission.csv', index=False)
#######################################################################################
train_data,eval_data = train_test_split(train,test_size=0.2,stratify=train['number'])

from sklearn.model_selection import StratifiedKFold
skf = StratifiedKFold(n_splits=5,shuffle=True,random_state=2023)
prediction_df = pd.DataFrame()
for fold_n, (trn_idx, val_idx) in enumerate(skf.split(train, train['number'])):
    print(f'fold {fold_n} training...')
    train_data = train.iloc[trn_idx]
    eval_data = train.iloc[val_idx]
    trainset = Leaf_Dataset(train_data, transform = transforms_train)
    evalset = Leaf_Dataset(eval_data, transform = transforms_test)
    train_loader = torch.utils.data.DataLoader(trainset, batch_size=32, shuffle=True, drop_last=False)
    eval_loader = torch.utils.data.DataLoader(evalset, batch_size=32, shuffle=False, drop_last=False)
    predictions = train_model(train_loader, eval_loader)
    prediction_df[f'fold_{fold_n}'] = predictions

# DataFrame中的每一行计算众数（最常出现的数）
all_predictions = list(prediction_df.mode(axis=1)[0].astype(int))
print(all_predictions[0])
labels_unique[all_predictions[0]]
model = model.to(device)  # 确保模型在相同的设备上
model.eval()
predictions = []
for x in test_loader:
    with torch.no_grad():
        y_pred = model(x.to(device))
        predictions.extend(y_pred.argmax(dim=-1).cpu().numpy().tolist())

all_features = pd.get_dummies(all_features, dummy_na=True)
n_train = train_data.shape[0]
all_features = all_features.astype('float64')
print(all_features[:n_train].values.dtype)
from autogluon.tabular import TabularDataset, TabularPredictor
predictor = TabularPredictor(label=label).fit(
  train_data.drop(columns=[Id]),
	hyperparameters='multimodal',
	# ag_args_fit={'num_gpus': 1},
	num_stack_levels=1, num_bag_folds=5)

predictor.fit_summary(show_plot=True)
preds = predictor.predict(test_data.drop(columns=[id]))
preds.head()
new_data = pd.concat([test_data[:], preds], axis=1)
new_data.head()
leaderboard = predictor.leaderboard(new_data)

https://auto.gluon.ai/stable/tutorials/multimodal/advanced_topics/continuous_training.html
test_score = predictor.evaluate(test_data)
print(test_score)
predictor_2 = MultiModalPredictor.load(model_path)  # you can also use the `predictor` we assigned above
train_data_2 = train_data.drop(train_data_1.index).sample(n=subsample_size, random_state=0)
predictor_2.fit(train_data_2, time_limit=60)
max_tokens
_IMPROVE_CODE_CONFIG = {
    "prompt": """Analyze the code in the following files and return a list of suggestions for improvement{followup}, to achieve the objective of '{objective}'.
{code}
""",
    "model": DEFAULT_MODEL,
    "request_timeout": 900,


}

use_cache
max_consecutive_auto_reply

        # Ask questions and get answers in a loop
        while True:
            query = console.input("Query: ")
            if query.lower() in ["/q", "quit", "exit"]:
                console.print("Exiting the chat.", style="green")
                exit()
          
            elif query.lower() in ["/reset", "/r"]:  # 检查是否需要重置
                console.print("Resetting the document selection.", style="blue")
                break
            elif query.lower() in ["/ai"]:  # AI will ask questions
                console.print("AI Thinking a question...", style="blue")
                query = journalist.step("Start the conversation")
                console.print("AI: "+ query, style="yellow")
	
	
            answer = chat.ask_question(query)
            console.print(f"Answer: {answer}", style="green")

AI Researcher (to Ray):
The current status of the Japanese currency is that it has been experiencing a decline in value, with the USD/JPY exchange rate reaching 152.26 by the end of this quarter, according to Trading Economics global macro models and analysts expectations. This decline is expected to continue, with USD/JPY forecasted to trade at 139.185 by the end of this quarter and 146.882 in 12 months' time, according to TradingEconomics. The Japanese Yen is also expected to trade at 160.33 in 12 months time, according to the same source.
As for the future trend of the Japanese currency, it is expected to remain weak against the US Dollar, with Societe Generale forecasting that the USD/JPY pair could gradually trend lower over the coming years. Additionally, the Bank of Japan has vowed to maintain a dovish stance and tweak its yield cap, which is expected to further weaken the Yen.
AI Researcher (to Ray):
The current status of the Japanese currency is that it has been experiencing a decline in value, with the USD/JPY exchange rate reaching 152.26 by the end of this quarter, according to Trading Economics global macro models and analysts expectations. This decline is expected to continue, with USD/JPY forecasted to trade at 139.185 by the end of this quarter and 146.882 in 12 months' time, according to the same source. Additionally, the Bank of Japan has vowed to maintain a dovish stance and tweak its yield cap, which is expected to further weaken the Yen. The yen will probably weaken to 152 against the dollar this year, and 155 in 2024, according to Tohru Sasaki, JPMorgan Chase & Co.'s head of Japan markets. The Japanese Yen's strength from the last quarter of 2022 has found an interim bottom at the beginning of the new year. 2023 is set to be a year of weakness for the Yen, with the USD/JPY pair expected to trend lower over the coming years. According to the USD/JPY forecast, the Japanese Yen's strength from the last quarter of 2022 has found an interim bottom at the beginning of the new year. 2023 is set to be a year of weakness for the Yen, with the USD/JPY pair expected to trend lower over the coming years. The Japanese Yen is expected to trade at 160.33 in 12 months time. The Japanese Yen has fallen 3.9% against the U.S. dollar over the last month—briefly reaching a 32-year low—and by 23.6% year to date. The yen to USD exchange rate is expected to trade at 240.*********** (61.768%) after a year according to our prediction system. The yen will probably weaken to 152 against the dollar this year, and 155 in 2024. The yen to USD exchange rate is expected to trade at 151.27 by the end of this quarter, according to Trading Economics global macro models and analysts expectations. Looking forward, we estimate it to trade
--------------------------------------------------------------------------------
AI Researcher (to Ray):
The Japanese Yen has been experiencing a decline in value in recent years, with the USD/JPY exchange rate reaching 152.26 by the end of this quarter, according to Trading Economics global macro models and analysts expectations. This decline is expected to continue, with USD/JPY forecasted to trade at 139.185 by the end of this quarter and 146.882 in 12 months' time, according to the same source. Additionally, the Bank of Japan has vowed to maintain a dovish stance and tweak its yield cap, which is expected to further weaken the Yen. The yen will probably weaken to 152 against the dollar this year, and 155 in 2024, according to Tohru Sasaki, JPMorgan Chase & Co.'s head of Japan markets. The Japanese Yen's strength from the last quarter of 2022 has found an interim bottom at the beginning of the new year. 2023 is set to be a year of weakness for the Yen, with the USD/JPY pair expected to trend lower over the coming years. The Japanese Yen is expected to trade at 160.33 in 12 months time. The Japanese Yen has fallen 3.9% against the U.S. dollar over the last month—briefly reaching a 32-year low—and by 23.6% year to date. The yen to USD exchange rate is expected to trade at 240.*********** (61.768%) after a year according to our prediction system. The yen will probably weaken to 152 against the dollar this year, and 155 in 2024. The yen to USD exchange rate is expected to trade at 151.27 by the end of this quarter, according to Trading Economics global macro models and analysts expectations. Looking forward, we estimate it to trade at 160.33 in 12 months time. The yen to USD exchange rate is expected to trade at 151.27 by the end of this quarter, according to Trading Economics global macro models and analysts expectations. Looking forward, we estimate it to trade at 16
```

### WANDB 使用技巧

```Python
《Resume Wandb》
https://docs.wandb.ai/guides/runs/resuming
wandb website -> find your project -> Overview 
 <entity>/<project>/<run_id>.
jbsheng/bird-sound-classification/h2v4eirl
run = wandb.init(entity="<entity>", \ 
        project="<project>", id="<run ID>", resume="allow")

os.environ['WANDB_NOTEBOOK_NAME'] = 'SimplebirdTest.ipynb'
wandb.login()
wandb.init(entity='jbsheng',project='bird-sound-classification', id='h2v4eirl', resume='auto')
```

### 动态显示Loss或者accuracy without wandb

```Python
from IPython import display
from matplotlib import pyplot as plt
from matplotlib_inline import backend_inline
def gpu(i=0):
    """Get a GPU device.
    Defined in :numref:`sec_use_gpu`"""
    return torch.device(f'cuda:{i}')
def num_gpus():
    """Get the number of available GPUs.
    Defined in :numref:`sec_use_gpu`"""
    return torch.cuda.device_count()
def try_all_gpus():
    """Return all available GPUs, or [cpu(),] if no GPU exists.
    Defined in :numref:`sec_use_gpu`"""
    return [gpu(i) for i in range(num_gpus())]
def use_svg_display():
    """Use the svg format to display a plot in Jupyter.
    Defined in :numref:`sec_calculus`"""
    backend_inline.set_matplotlib_formats('svg')
def set_axes(axes, xlabel, ylabel, xlim, ylim, xscale, yscale, legend):
    """Set the axes for matplotlib.
    Defined in :numref:`sec_calculus`"""
    axes.set_xlabel(xlabel), axes.set_ylabel(ylabel)
    axes.set_xscale(xscale), axes.set_yscale(yscale)
    axes.set_xlim(xlim),     axes.set_ylim(ylim)
    if legend:
        axes.legend(legend)
    axes.grid()
def set_figsize(figsize=(3.5, 2.5)):
    """Set the figure size for matplotlib.
    Defined in :numref:`sec_calculus`"""
    use_svg_display()
    plt.rcParams['figure.figsize'] = figsize
class Animator:
    """For plotting data in animation."""
    def __init__(self, xlabel=None, ylabel=None, legend=None, xlim=None,
                 ylim=None, xscale='linear', yscale='linear',
                 fmts=('-', 'm--', 'g-.', 'r:'), nrows=1, ncols=1,
                 figsize=(3.5, 2.5)):
        """Defined in :numref:`sec_utils`"""
        # Incrementally plot multiple lines
        if legend is None:
            legend = []
        use_svg_display()
        self.fig, self.axes = plt.subplots(nrows, ncols, figsize=figsize)
        if nrows * ncols == 1:
            self.axes = [self.axes, ]
        # Use a lambda function to capture arguments
        self.config_axes = lambda: set_axes(
            self.axes[0], xlabel, ylabel, xlim, ylim, xscale, yscale, legend)
        self.X, self.Y, self.fmts = None, None, fmts
    def add(self, x, y):
        # Add multiple data points into the figure
        if not hasattr(y, "__len__"):
            y = [y]
        n = len(y)
        if not hasattr(x, "__len__"):
            x = [x] * n
        if not self.X:
            self.X = [[] for _ in range(n)]
        if not self.Y:
            self.Y = [[] for _ in range(n)]
        for i, (a, b) in enumerate(zip(x, y)):
            if a is not None and b is not None:
                self.X[i].append(a)
                self.Y[i].append(b)
        self.axes[0].cla()
        for x, y, fmt in zip(self.X, self.Y, self.fmts):
            self.axes[0].plot(x, y, fmt)
        self.config_axes()
        display.display(self.fig)
        display.clear_output(wait=True)
class Accumulator:
    """For accumulating sums over `n` variables."""
    def __init__(self, n):
        """Defined in :numref:`sec_utils`"""
        self.data = [0.0] * n
    def add(self, *args):
        self.data = [a + float(b) for a, b in zip(self.data, args)]
    def reset(self):
        self.data = [0.0] * len(self.data)
    def __getitem__(self, idx):
        return self.data[idx]

# 创建模型、优化器和一些模拟数据
model = SimpleBinaryModel()
optimizer = optim.Adam(model.parameters(), lr=0.001)
legend = ['loss']
animator = Animator(xlabel='epoch', xlim=[1, 100], legend=legend)
# 模拟一些数据
x = torch.randn(100, 10)  # 100个样本，每个样本10个特征
y = torch.randint(0, 2, (100,))  # 100个二分类标签

# 训练循环
for epoch in range(100):
    metric = Accumulator(1)
    optimizer.zero_grad()
    outputs = model(x).squeeze()
    loss = pairwise_ranking_loss(outputs, y)
    loss.backward()
    optimizer.step()
    metric.add(loss, y.shape[0])
    animator.add(epoch + 1, (metric[0], ))
```

### Bagging (Bootstrap AGGrgratING)

```Python
#相同model框架，然后训练不同的数据，取结果平均
class Bagging:
    def __init__(self, base_learner, n_learners):
        self.learners = [clone(base_learner) for _ in range(n_learners)]
    def fit(self, X, y):
        for learner in self.learners:
            examples = np.random.choice(
                np.arange(len(X)), int(len(X)), replace=True)
            learner.fit(X.iloc[examples, :], y.iloc[examples])
    def predict(self, X):
        preds = [learner.predict(X) for learner in self.learners]
        return np.array(preds).mean(axis=0)
```

### Boosting (GBDT XGBoost）

```Python
/opt/workspace/app/d2l-zh/Stanford/ideaConfirm.ipynb
\#article
https://biostat.jhsph.edu/~mmccall/articles/friedman_1999.pdf
https://qiita.com/ground0state/items/89ab13435e29f0fede56
AdaBoost、Gradient Boosting
H_t+1(x) = H_t-1(x) + n * H_t(x)
# 导入所需库
from sklearn.base import clone
from sklearn.tree import DecisionTreeRegressor
import numpy as np
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
from sklearn.exceptions import NotFittedError
# 梯度提升类
class GradientBoosting:
    def __init__(self, base_learner, n_learners, learning_rate):
        self.learners = [clone(base_learner) for _ in range(n_learners)]
        self.lr = learning_rate
        self.is_fitted = False  # 添加了is_fitted属性  
    def fit(self, X, y, X_valid=None, y_valid=None):
        residual = y.copy()
        self.train_losses = []
        self.valid_losses = []
        for i, learner in enumerate(self.learners):
            learner.fit(X, residual)
            predictions = learner.predict(X)
            residual -= self.lr * predictions
            # 计算累积预测值以获取当前的训练和验证损失
            train_predictions = sum((self.lr * l.predict(X) for l in self.learners[:i+1]), 0)
            self.train_losses.append(mean_squared_error(y, train_predictions))
      
            if X_valid is not None and y_valid is not None:
                valid_predictions = sum((self.lr * l.predict(X_valid) for l in self.learners[:i+1]), 0)
                self.valid_losses.append(mean_squared_error(y_valid, valid_predictions))
        self.is_fitted = True
    def predict(self, X, training=False):
        # 如果是训练过程中的预测，允许使用未完全训练的模型
        if not self.is_fitted and not training:
            raise NotFittedError("This GradientBoosting instance is not fitted yet.")
  
        preds = np.zeros(X.shape[0])
        for learner in self.learners:
            # 如果learner已经fit过，则使用它来做预测
            if hasattr(learner, 'predict'):
                preds += self.lr * learner.predict(X)
        return preds
# 创造一些数据
X, y = make_regression(n_samples=100, n_features=1, noise=10, random_state=42)
# 数据集分割
X_train, X_valid, y_train, y_valid = train_test_split(X, y, test_size=0.2, random_state=42)
# 初始化模型
base_learner = DecisionTreeRegressor(max_depth=3)
model = GradientBoosting(base_learner=base_learner, n_learners=50, learning_rate=0.1)
# 模型初始化和训练
model.fit(X_train, y_train, X_valid, y_valid)  # 注意这里传入 y_valid 而不是 X_valid
# 绘图
plt.plot(range(len(model.train_losses)), model.train_losses
         , label='train')
plt.plot(range(len(model.valid_losses)), model.valid_losses, label='valid')
plt.xlabel('n_learners')
plt.ylabel('Loss')
plt.legend()
plt.show()
# 进行预测
predictions = model.predict(X)
# 打印预测结果（仅展示前5个预测值）
print(predictions[:5])
```

### Stacking

单层stacking，输出dense一下

```Mermaid
graph TD
Inputs("Inputs") --> |"输入数据"| RF(Random Forest)
Inputs --> |"输入数据"| GBDT(GBDT)
Inputs --> |"输入数据"| MLP(MLP)
RF --> |"随机森林预测结果"| Concat
GBDT --> |"GBDT预测结果"| Concat
MLP --> |"MLP预测结果"| Concat
Concat --> |"拼接特征向量"| Dense("Dense Layer")
Dense --> |"最终预测结果"| Output("Output")
```

多层stacking
多层容易出现过拟合

```Mermaid
graph TD
    Inputs("Inputs") --> |"输入数据"| L1_RF(Random Forest L1)
    Inputs --> |"输入数据"| L1_GBDT(GBDT L1)
    Inputs --> |"输入数据"| L1_MLP(MLP L1)
  
    L1_RF --> |"随机森林L1预测结果"| L1_Concat("Concat L1")
    L1_GBDT --> |"GBDT L1预测结果"| L1_Concat
    L1_MLP --> |"MLP L1预测结果"| L1_Concat
  
    Inputs --> |"输入数据"| L1_Concat
  
    L1_Concat --> |"拼接特征向量L1和输入数据"| L2_RF(Random Forest L2)
    L1_Concat --> |"拼接特征向量L1和输入数据"| L2_GBDT(GBDT L2)
    L1_Concat --> |"拼接特征向量L1和输入数据"| L2_MLP(MLP L2)
  
    L2_RF --> |"随机森林L2预测结果"| L2_Concat("Concat L2")
    L2_GBDT --> |"GBDT L2预测结果"| L2_Concat
    L2_MLP --> |"MLP L2预测结果"| L2_Concat
  
    L2_Concat --> |"拼接特征向量L2"| L3_Dense("Dense Layer L3")
  
    L3_Dense --> |"最终预测结果"| Output("Output")
```

```Python
from autogluon.tabular import TabularPredictor
#隐含层1，bagging fold 5
predictor = TabularPredictor(label=label).fit(
    train, num_stack_levels=1, num_bag_folds=5
)
```

### LazyPredict

```Python
/opt/workspace/researcher/labTest/Lazypredict.ipynb
from lazypredict.Supervised import LazyRegressor
from sklearn import datasets
from sklearn.utils import shuffle
import numpy as np
import pandas as pd
  
data_url = "http://lib.stat.cmu.edu/datasets/boston"
raw_df = pd.read_csv(data_url, sep="\s+", skiprows=22, header=None)
data = np.hstack([raw_df.values[::2, :], raw_df.values[1::2, :2]])
target = raw_df.values[1::2, 2]
  
# boston = datasets.load_boston()
X, y = shuffle(data, target, random_state=13)
X = X.astype(np.float32)
offset = int(X.shape[0] * 0.9)
X_train, y_train = X[:offset], y[:offset]
X_test, y_test = X[offset:], y[offset:]
reg = LazyRegressor(verbose=0, ignore_warnings=False, custom_metric=None)
models, predictions = reg.fit(X_train, X_test, y_train, y_test)
print(models)
```

### 对模型调参

$\begin{equation}$

### 超参数优化和NAS

```Python
SH or Hyperband
多种参数尝试，减半继续尝试同时延长ephon，idea！【是否可用ChatGPT生成参数json文件，然后读入训练模型】
自动化
1. 数据处理
2. 参数选择
3. 模型选择
```

优化算法

```JavaScript
深度学习优化算法经验 SGD -> SGDM -> NAG ->AdaGrad -> AdaDelta -> Adam -> Nadam
https://www.tulingxueyuan.cn/tlzx/jsp/2226.html
```

ML with Optuna
[https://github.com/cristianleoo/models-from-scratch-python/tree/main/ML Optimization With Optuna?source=post_page-----57593d700e52--------------------------------](https://github.com/cristianleoo/models-from-scratch-python/tree/main/ML%20Optimization%20With%20Optuna?source=post_page-----57593d700e52--------------------------------)
/opt/workspace/GPT-SoVITS/SimplebirdTest.ipynb

### KL散度

![](https://www.tutorialexample.com/wp-content/uploads/2019/05/kl-divergence-equation-1.png)

```LaTeX
import scipy.stats
# 假设p和q是两个离散概率分布，且它们的长度相同
p = [0.1, 0.2, 0.7]
q = [0.2, 0.3, 0.5]
# 计算KL散度
kl_divergence = scipy.stats.entropy(p, q)
```

### DPO数学解释

![[Untitled 51.png]]

```LaTeX
import torch
import torch.nn.functional as F
def dpo_loss(logits, actions, beta, pi_ref, pi_theta, pi_theta_ref):
    # 将logits转换为概率
    probs = F.softmax(logits, dim=1)
  
    # 获取实际采取的动作的对数概率
    log_probs = F.log_softmax(logits, dim=1)
    log_probs_actions = log_probs.gather(1, actions.unsqueeze(-1)).squeeze(-1)
  
    # 计算pi_theta和pi_theta_ref的对数概率
    log_pi_theta = pi_theta.log()
    log_pi_theta_ref = pi_theta_ref.log()
  
    # 计算DPO损失
    dpo = log_probs_actions * (beta * (log_pi_theta - log_pi_theta_ref) - F.logsigmoid(log_probs_actions))
    loss = -dpo.mean()  # 取平均损失
  
    return loss
```

### SPD

```LaTeX
class space_to_depth(nn.Module):
   def __init__(self, dimension=1):
       super().__init__()
       self.d = dimension
   def forward(self, x):
       return torch.cat([x[..., ::2, ::2], x[..., 1::2, ::2], x[..., ::2, 1::2], x[..., 1::2, 1::2]], 1)
```

### DeformableAttention

```LaTeX
https://github.com/lucidrains/deformable-attention
https://arxiv.org/pdf/2201.00520.pdf
```

### 自适应参数学习

```Python
import torch
from torch import nn
from torch.nn import functional as F
from torch.nn.parameter import Parameter
def gem(x, p=3, eps=1e-6):
    return F.avg_pool2d(x.clamp(min=eps).pow(p), (x.size(-2), x.size(-1))).pow(1./p)
class GeM(nn.Module):
    def __init__(self, p=3, eps=1e-6):
        super(GeM, self).__init__()
        self.p = Parameter(torch.ones(1)*p)
        self.eps = eps
  
    def forward(self, x):
        return gem(x, p=self.p, eps=self.eps)
  
    def __repr__(self):
        return self.__class__.__name__ + '(' + 'p=' + '{:.4f}'.format(self.p.data.tolist()[0]) + ', ' + 'eps=' + str(self.eps) + ')'

class MyLinear(nn.Module):
    def __init__(self, in_units, units):
        super().__init__()
        self.weight = nn.Parameter(torch.randn(in_units, units))
        self.bias = nn.Parameter(torch.randn(units,))
    def forward(self, X):
        linear = torch.matmul(X, self.weight.data) + self.bias.data
        return F.relu(linear)
```

---

# 对于量化的详细解释

```Python
\#video for detail explanation
https://learn.deeplearning.ai/courses/quantization-in-depth/lesson/11/replace-pytorch-layers-with-quantized-layers
\#code start from below
import torch
import torch.nn as nn
import torch.nn.functional as F
```

## 替换类

```Python
register_buffer是PyTorch中nn.Module类的一个方法，用于在模型中注册一个缓冲区（buffer）。缓冲区是一种特殊的张量，
它不是模型的参数，但在模型的前向传播过程中会被使用到。
通过register_buffer注册的缓冲区不会被视为模型的参数，不会被优化器更新
#替换类
def replace_linear_with_target(module, 
                               target_class, module_name_to_exclude):
    for name, child in module.named_children():
        if isinstance(child, nn.Linear) and not \
          any([x == name for x in module_name_to_exclude]):
            old_bias = child.bias
            new_module = target_class(child.in_features, 
                                      child.out_features, 
                                      old_bias is not None, 
                                      child.weight.dtype)
            setattr(module, name, new_module)
            if old_bias is not None:
              getattr(module, name).bias = old_bias
        else:
            # Recursively call the function for nested modules
            replace_linear_with_target(
                child, target_class, module_name_to_exclude)
def replace_linear_with_target_and_quantize(module, 
                               target_class, module_name_to_exclude):
    for name, child in module.named_children():
        if isinstance(child, nn.Linear) and not \
        any([x == name for x in module_name_to_exclude]):
            old_bias = child.bias
            old_weight = child.weight
            new_module = target_class(child.in_features, 
                                      child.out_features, 
                                      old_bias is not None, 
                                      child.weight.dtype)
            setattr(module, name, new_module)
            getattr(module, name).quantize(old_weight)
      
            if old_bias is not None:
              getattr(module, name).bias = old_bias
        else:
            # Recursively call the function for nested modules
            replace_linear_with_target_and_quantize(child, 
                     target_class, module_name_to_exclude)   

def w8_a16_forward(weight, input, scales, bias=None):
  
    casted_weights = weight.to(input.dtype)
    output = F.linear(input, casted_weights) * scales
  
    if bias is not None:
        output = output + bias
  
    return output
  
class W8A16LinearLayer(nn.Module):
    def __init__(self, in_features, out_features, 
                 bias=True, dtype=torch.float32):
        super().__init__()
  
  
        self.register_buffer(
            "int8_weights",
            torch.randint(
                -128, 127, (out_features, in_features), dtype=torch.int8
            )
        )
  
        self.register_buffer("scales", 
                             torch.randn((out_features), dtype=dtype))
  
        if bias:
            self.register_buffer("bias", 
                                 torch.randn((1, out_features), 
                                             dtype=dtype))
  
        else:
            self.bias = None
    def quantize(self, weights):
        w_fp32 = weights.clone().to(torch.float32)
        scales = w_fp32.abs().max(dim=-1).values / 127
        scales = scales.to(weights.dtype)
        int8_weights = torch.round(weights
                        /scales.unsqueeze(1)).to(torch.int8)
        self.int8_weights = int8_weights
        self.scales = scales
  
    def forward(self, input):
        return w8_a16_forward(self.int8_weights, 
                              input, self.scales, self.bias)   
                        
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
model_id = "./models/Salesforce/codegen-350M-mono"
model = AutoModelForCausalLM.from_pretrained(model_id, 
                                    torch_dtype=torch.bfloat16, 
                                             low_cpu_mem_usage=True)
tokenizer = AutoTokenizer.from_pretrained(model_id)
                                                   
pipe = pipeline("text-generation", model=model, tokenizer=tokenizer)
replace_linear_with_target_and_quantize(model, W8A16LinearLayer, ["lm_head"])
print(pipe("def hello_world():", max_new_tokens=20, 
           do_sample=False)[0]["generated_text"])     
     
```

## **保存权重并且推送到haggingface**

```Python
# save weight     
quantized_state_dict = model.state_dict()
torch.save(quantized_state_dict, "quantized_state_dict.pth")                                           
# push to huggingface
from huggingface_hub import HfApi, create_repo
YOUR_HF_USERNAME = ""
your_repo_id = f"{YOUR_HF_USERNAME}/opt-125m-quantized-dlai"
api = HfApi()
# create_repo(your_repo_id)
api.upload_file(
 path_or_fileobj="quantized_state_dict.pth",
 path_in_repo="quantized_state_dict.pth",
 repo_id=your_repo_id
)
```

## 加载量化模型

```Python
# load model but no weights
from transformers import OPTForCausalLM, AutoTokenizer, AutoConfig
model_id = "./models/facebook/opt-125m"
config = AutoConfig.from_pretrained(model_id)
with torch.device("meta"):
  model = OPTForCausalLM(config)
tokenizer = AutoTokenizer.from_pretrained(model_id)
for param in model.parameters():
  print(param)
replace_linear_with_target(model, W8A16LinearLayer, ["lm_head"])
\#download weight
from huggingface_hub import hf_hub_download
state_dict_cache_path = hf_hub_download(
    "ybelkada/opt-125m-quantized-dlai",
    "quantized_state_dict.pth"
)  
\#assign weight to model
state_dict = torch.load(state_dict_cache_path)
model.load_state_dict(state_dict, strict=True, assign=True)  
  
\#Test
from transformers import pipeline
pipe = pipeline("text-generation", model=model, tokenizer=tokenizer)
pipe("Hello today I am", max_new_tokens=40)from transformers import pipeline
```

## Packing Weights mode 2bit/4bits

```Python
def pack_weights(uint8tensor, bits):
    if uint8tensor.shape[0] * bits % 8 != 0:
        raise ValueError(f"The input shape needs to be a mutiple \
        of {8 / bits} - got {uint8tensor.shape[0]}")
    num_values = uint8tensor.shape[0] * bits // 8
    num_steps = 8 // bits
    unpacked_idx = 0
    packed_tensor = torch.zeros((num_values), dtype=torch.uint8)
    # 1 0 3 2 - 01 00 11 10
    # [0000 0000] -> 0000 0001
    # 0000 0001
    # 0000 0000 - 0000 0000
    # 0000 0011 - 0011 0000 - 0011 0001
    # 1011 0001
  
    for i in range(num_values):
        for j in range(num_steps):
            packed_tensor[i] |= uint8tensor[unpacked_idx] << (bits * j)
            unpacked_idx += 1
    return packed_tensor
unpacked_tensor = torch.tensor([1, 0, 3, 2], 
                               dtype=torch.uint8)
                         
pack_weights(unpacked_tensor, 2)   
```

## Unpack Weights

```Python
def unpack_weights(uint8tensor, bits):
    num_values = uint8tensor.shape[0] * 8 // bits
    num_steps = 8 // bits
    unpacked_tensor = torch.zeros((num_values), dtype=torch.uint8)
    unpacked_idx = 0
    # 1 0 3 2 - 01 00 11 10
    # [00000000 00000000 00000000 00000000]
    # [10110001 00101100 00001011 00000010]
    # [00000001 00000000 00000011 00000010]
    # 10110001
    # 00000011
  
    # 00000001
    # 1: [10110001]
    # 2: [00101100]
    # 3: [00001011]
    mask = 2 ** bits - 1
    for i in range(uint8tensor.shape[0]):
        for j in range(num_steps):
            unpacked_tensor[unpacked_idx] |= uint8tensor[i] >> (bits * j)
            unpacked_idx += 1
    unpacked_tensor &= mask
    return unpacked_tensor
  
unpacked_tensor = torch.tensor([177, 255], 
                               dtype=torch.uint8)
# Answer should be: torch.tensor([1, 0, 3, 2, 3, 3, 3, 3]
unpack_weights(unpacked_tensor, 2)  
```

## Bert Model Post Train and merge

/opt/workspace/mom/model/ProfanityDetect.py
/opt/workspace/mom/model/merge_prune.py
https://github.com/arcee-ai/mergekit

```Plain
# log in to huggingface with an access token (must have write permission)
huggingface-cli login
# upload your model
huggingface-cli upload your_hf_username/my-cool-model ./output-model-directory .
```

```Python
import json
import subprocess
import time
import pandas as pd
import random
import os
import multiprocessing as mp
import numpy as np
import nltk
from nltk.corpus import words as nltk_words
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.metrics import classification_report
from sklearn.metrics import roc_auc_score
import joblib
# 导入所需的库
from typing import Tuple, List
from functools import partial
import torch
import torch.nn as nn
from torch.optim import AdamW
from torch.utils.data import Dataset, DataLoader, RandomSampler
from torch.nn.utils.rnn import pad_sequence
from transformers import BertJapaneseTokenizer, BertTokenizer, BertModel, get_linear_schedule_with_warmup, BertPreTrainedModel
from tqdm import tqdm
import wandb
def load_word_lists():
    # 步骤1: 读取脏话列表
    def read_wordlist(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            return [line.strip() for line in file if line.strip()]
    dirty_words = read_wordlist('data/wordlist.txt')
    # 步骤2: 获取英语正常词汇
    nltk.download('words')
    english_words = set(nltk_words.words())
    # 步骤3: 获取日语正常词汇
    def read_japanese_words(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            return [line.strip() for line in file if line.strip()]
    japanese_words = read_japanese_words('data/japanese_words.txt')
    # 合并英语和日语词汇
    normal_words = list(english_words) + japanese_words
    return dirty_words, english_words, japanese_words, normal_words
def generate_data_chunk(chunk_id, n_samples, english_words, japanese_words, dirty_words):
    sentences = []
    labels = []
    for _ in range(n_samples):
        if random.random() < 0.5:  # 50% 概率生成包含脏话的句子
            sentences.append(generate_sentence(english_words, japanese_words,dirty_words, include_dirty=True))
            labels.append(1)
        else:
            sentences.append(generate_sentence(english_words, japanese_words, dirty_words, include_dirty=False))
            labels.append(0)
  
    df = pd.DataFrame({'sentence': sentences, 'label': labels})
    temp_file = f'data/temp_sentences_{chunk_id}.csv'
    df.to_csv(temp_file, index=False)
    return temp_file
# 生成句子的函数
def generate_sentence(english_words, japanese_words, dirty_words,include_dirty=False):
    language = random.choice(['english', 'japanese'])
    sentence_length = random.randint(5, 15)
  
    if language == 'english':
        sentence = random.sample(list(english_words), sentence_length)
        if include_dirty:
            dirty_word = random.choice(dirty_words)
            insert_position = random.randint(0, len(sentence))
            sentence.insert(insert_position, dirty_word)
        return ' '.join(sentence)
    else:
        sentence = random.sample(japanese_words, sentence_length)
        if include_dirty:
            dirty_word = random.choice(dirty_words)
            insert_position = random.randint(0, len(sentence))
            sentence.insert(insert_position, dirty_word)
        return ''.join(sentence)  # 日语句子不使用空格
def generate_synthetic_data():
    # 设置总样本数和进程数
    total_samples = 400
    num_processes = int(mp.cpu_count() / 2)
    samples_per_process = total_samples // num_processes
    # 创建进程池
    pool = mp.Pool(processes=num_processes)
    # 加载词汇列表
    dirty_words, english_words, japanese_words, normal_words = load_word_lists()
  
    # 并行生成数据
    temp_files = pool.starmap(generate_data_chunk, 
                              [(i, samples_per_process, english_words, japanese_words, dirty_words) 
                               for i in range(num_processes)])
    # 关闭进程池
    pool.close()
    pool.join()
    # 合并所有临时文件
    df_list = [pd.read_csv(file) for file in temp_files]
    final_df = pd.concat(df_list, ignore_index=True)
    # 保存最终的CSV文件
    final_df.to_csv('data/data_sentences.csv', index=False)
    # 删除临时文件
    for file in temp_files:
        os.remove(file)
    print(f"数据生成完成并保存到 data_sentences.csv，共生成 {len(final_df)} 条数据")
def NB_train_and_evaluate_model():
    # 读取生成的数据文件
    df = pd.read_csv('data/merged_sentences.csv')
  
    # 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(df['sentence'], df['label'], test_size=0.2, random_state=42)
  
    # 创建特征
    vectorizer = CountVectorizer()
    X_train_vectorized = vectorizer.fit_transform(X_train)
    X_test_vectorized = vectorizer.transform(X_test)
  
    # 检查模型文件是否存在
    model_path = 'model/profanity_model.joblib'
    vectorizer_path = 'model/vectorizer.joblib'
    if os.path.exists(model_path) and os.path.exists(vectorizer_path):
        # 如果模型文件存在，直接加载模型
        print("加载已有模型...")
        model = joblib.load(model_path)
        vectorizer = joblib.load(vectorizer_path)
    else:
        # 如果模型文件不存在，训练新模型
        print("训练新模型...")
        model = MultinomialNB()
        model.fit(X_train_vectorized, y_train)
  
        # 评估模型
        y_pred = model.predict(X_test_vectorized)
        print(classification_report(y_test, y_pred))
  
        # 保存模型和向量器
        os.makedirs('model', exist_ok=True)
        joblib.dump(model, model_path)
        joblib.dump(vectorizer, vectorizer_path)
        print("模型已保存到", model_path)
        print("向量器已保存到", vectorizer_path)
    # 使用模型进行预测
    def check_profanity(sentence):
        vectorized_sentence = vectorizer.transform([sentence])
        return model.predict(vectorized_sentence)[0]
  
    # 示例使用
    print("预测结果（0表示正常，1表示含有不当内容）：")
    print("普通英文句子:", check_profanity("This is a normal sentence."))
    print("含有脏话的英文句子:", check_profanity("This sentence contains a dirty word like fuck."))
    print("普通日文句子:", check_profanity("これは普通の文章です。"))
    print("含有脏话的日文句子:", check_profanity("これはkusoというばか下品な言葉を含む文章です。"))
# 创建Dataset类
class ToxicDataset(Dataset):
    def __init__(self, tokenizer: BertJapaneseTokenizer, dataframe: pd.DataFrame, lazy: bool = False):
        self.tokenizer = tokenizer
        self.pad_idx = tokenizer.pad_token_id
        self.lazy = lazy
        if not self.lazy:
            self.X = []
            self.Y = []
            for i, (row) in tqdm(dataframe.iterrows()):
                x, y = self.row_to_tensor(self.tokenizer, row)
                self.X.append(x)
                self.Y.append(y)
        else:
            self.df = dataframe
              
    @staticmethod
    def row_to_tensor(tokenizer: BertJapaneseTokenizer, row: pd.Series) -> Tuple[torch.LongTensor, torch.LongTensor]:
        tokens = tokenizer.encode(row["sentence"], add_special_tokens=True)
        if len(tokens) > 120:
            tokens = tokens[:119] + [tokens[-1]]
        x = torch.LongTensor(tokens)
        y = torch.FloatTensor([row["label"]])
        return x, y
  
    def __len__(self):
        if self.lazy:
            return len(self.df)
        else:
            return len(self.X)
  
    def __getitem__(self, index: int) -> Tuple[torch.LongTensor, torch.LongTensor]:
        if not self.lazy:
            return self.X[index], self.Y[index]
        else:
            return self.row_to_tensor(self.tokenizer, self.df.iloc[index])
class BertClassifier(nn.Module):
    def __init__(self, bert: BertModel, num_classes: int):
        super().__init__()
        self.bert = bert
        self.classifier = nn.Linear(bert.config.hidden_size, num_classes)
    def forward(self, input_ids, attention_mask=None, token_type_ids=None, position_ids=None, head_mask=None,
            labels=None):
        outputs = self.bert(input_ids,
                               attention_mask=attention_mask,
                               token_type_ids=token_type_ids,
                               position_ids=position_ids,
                               head_mask=head_mask)
        cls_output = outputs[1] # batch, hidden
        cls_output = self.classifier(cls_output) # batch, 6
        cls_output = torch.sigmoid(cls_output)
        criterion = nn.BCELoss()
        loss = 0
        if labels is not None:
            loss = criterion(cls_output, labels)
        return loss, cls_output
# 定义collate函数
def collate_fn(batch: List[Tuple[torch.LongTensor, torch.LongTensor]], device: torch.device) \
        -> Tuple[torch.LongTensor, torch.LongTensor]:
    x, y = list(zip(*batch))
    x = pad_sequence(x, batch_first=True, padding_value=0)
    y = torch.stack(y)
    return x.to(device), y.to(device)
def get_gpu_temp():
    try:
        # Call gpustat command and get output in JSON format
        output = subprocess.check_output(["/home/<USER>/miniconda3/envs/research/bin/gpustat", 
                                          "--no-color", "--no-header", "--id", "0", "--json"], encoding='utf-8')
        data = json.loads(output)  # Parse the JSON data
        # Get the temperature from the parsed JSON
        temp = data['gpus'][0]['temperature.gpu']
        return temp
    except subprocess.CalledProcessError as e:
        print("Failed to get GPU temperature: ", e)
        return None
    except KeyError as e:
        print("Error parsing GPU temperature: ", e)
        return None
  
def train(model, iterator, optimizer, scheduler, epoch):
    model.train()
    total_loss = 0
    for batch_idx, (x, y) in enumerate(tqdm(iterator)):
  
        # 检查GPU温度
        temperature = get_gpu_temp()
        if temperature is None or temperature > 90:
            print(f"GPU temperature is {temperature}, waiting for 6 minutes.")
            time.sleep(360) # 等待10分钟
                          
        optimizer.zero_grad()
        mask = (x != 0).float()
        loss, outputs = model(x, attention_mask=mask, labels=y)
        total_loss += loss.item()
        loss.backward()
        optimizer.step()
        scheduler.step()
  
        # Log batch loss
        wandb.log({"batch": epoch * len(iterator) + batch_idx, "batch_loss": loss.item()})
  
    avg_loss = total_loss / len(iterator)
    print(f"Train loss {avg_loss}")
    return avg_loss
def evaluate(model, iterator):
    model.eval()
    pred = []
    true = []
    with torch.no_grad():
        total_loss = 0
        for x, y in tqdm(iterator):
            mask = (x != 0).float()
      
            # 检查GPU温度
            temperature = get_gpu_temp()
            if temperature is None or temperature > 90:
                print(f"GPU temperature is {temperature}, waiting for 6 minutes.")
                time.sleep(360) # 等待5分钟
      
            loss, outputs = model(x, attention_mask=mask, labels=y)
            total_loss += loss
            true += y.cpu().numpy().tolist()
            pred += outputs.cpu().numpy().tolist()
    true = np.array(true)
    pred = np.array(pred)
    roc_auc = roc_auc_score(true, pred)
    avg_loss = total_loss / len(iterator)
    print(f"Evaluate loss {avg_loss}")
    print(f"ROC AUC Score: {roc_auc}")
    return avg_loss, roc_auc
def save_checkpoint(model, optimizer, scheduler, epoch, best_loss, best_roc_auc, save_path):
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'best_loss': best_loss,
        'best_roc_auc': best_roc_auc,
    }, save_path)
    print(f"Checkpoint saved to {save_path}")
def load_checkpoint(model, optimizer, scheduler, load_path):
    checkpoint = torch.load(load_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    start_epoch = checkpoint['epoch']
    best_loss = checkpoint['best_loss']
    best_roc_auc = checkpoint['best_roc_auc']
    print(f"Checkpoint loaded from {load_path}")
    return start_epoch, best_loss, best_roc_auc
# https://www.kaggle.com/code/hawkeoni/pytorch-simple-bert
if __name__ == '__main__':
    # 生成数据
    # generate_synthetic_data()
    # NB_train_and_evaluate_model()
    # exit()
  
    # 初始化wandb
    wandb.init(project="profanity-detection", name="bert-classifier")
    # 设置路径和模型参数
    path = "data/"
    # bert_model_name = 'bert-base-cased'
    bert_model_name = '/opt/aibase/bert-base-japanese-whole-word-masking'
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
  
    # 加载tokenizer
    tokenizer = BertJapaneseTokenizer.from_pretrained(bert_model_name)
    # assert tokenizer.pad_token_id == 0, "Padding值在masks中设置为零，请在所有地方更改它"
  
    # 加载数据集 data/merged_sentences.csv
    train_df = pd.read_csv(os.path.join(path, 'merged_sentences.csv'))
    # train_df = train_df.sample(frac=0.33)
    train_df, val_df = train_test_split(train_df, test_size=0.05)
   
    # 创建数据集和迭代器
    train_dataset = ToxicDataset(tokenizer, train_df, lazy=True)
    dev_dataset = ToxicDataset(tokenizer, val_df, lazy=True)
    collate_fn = partial(collate_fn, device=device)
    BATCH_SIZE = 32
    train_sampler = RandomSampler(train_dataset)
    dev_sampler = RandomSampler(dev_dataset)
    train_iterator = DataLoader(train_dataset, batch_size=BATCH_SIZE, sampler=train_sampler, collate_fn=collate_fn)
    dev_iterator = DataLoader(dev_dataset, batch_size=BATCH_SIZE, sampler=dev_sampler, collate_fn=collate_fn)
  
    # Initialize model
    model = BertClassifier(BertModel.from_pretrained(bert_model_name), 1).to(device)
  
    # Set up optimizer and scheduler
    # Define parameters that should not have weight decay applied
    no_decay = ['bias', 'LayerNorm.weight']
  
    # Group model parameters for optimization:
    # 1. Apply weight decay to all parameters except those in 'no_decay'
    # 2. Don't apply weight decay to parameters in 'no_decay'
    optimizer_grouped_parameters = [
        {'params': [p for n, p in model.named_parameters() if not any(nd in n for nd in no_decay)], 'weight_decay': 0.01},
        {'params': [p for n, p in model.named_parameters() if any(nd in n for nd in no_decay)], 'weight_decay': 0.0}
    ]
  
    # This grouping allows for differential treatment of parameters during optimization,
    # which can lead to better model performance and generalization.
    # Weight decay is not applied to bias terms and LayerNorm weights, as is common practice.
    EPOCH_NUM = 3
    # triangular learning rate, linearly grows untill half of first epoch, then linearly decays 
    warmup_steps = 10 ** 3
    total_steps = len(train_iterator) * EPOCH_NUM - warmup_steps
    optimizer = AdamW(optimizer_grouped_parameters, lr=2e-5, eps=1e-8)
    scheduler = get_linear_schedule_with_warmup(optimizer, warmup_steps, total_steps)
    # scheduler = WarmupLinearSchedule(optimizer, warmup_steps=warmup_steps, t_total=total_steps)  
    # Check for existing checkpoint
    checkpoint_path = 'model/bert_classifier_checkpoint.pt'
    start_epoch = 0
    best_loss = float('inf')
    best_roc_auc = 0
    if os.path.exists(checkpoint_path):
        start_epoch, best_loss, best_roc_auc = load_checkpoint(model, optimizer, scheduler, checkpoint_path)
        print(f"Resuming training from epoch {start_epoch+1}")
  
    # Training loop
    for epoch in range(start_epoch, EPOCH_NUM):
        print('=' * 50, f"EPOCH {epoch+1}/{EPOCH_NUM}", '=' * 50)
        train_loss = train(model, train_iterator, optimizer, scheduler, epoch)
        val_loss, roc_auc = evaluate(model, dev_iterator)
  
        # Log metrics
        wandb.log({
            "epoch": epoch+1,
            "train_loss": train_loss,
            "val_loss": val_loss,
            "roc_auc": roc_auc
        })
  
        # Save best model
        if val_loss < best_loss:
            best_loss = val_loss
            best_roc_auc = roc_auc
            save_checkpoint(model, optimizer, scheduler, epoch, best_loss, best_roc_auc, checkpoint_path)
            print(f"New best model saved with validation loss: {best_loss:.4f} and ROC AUC: {best_roc_auc:.4f}")
  
    print("Training completed.")
    wandb.finish()
  
```

```python
def merge_and_save_model(bert_model_name, checkpoint_path, merged_model_path, if_quantized=False):
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
  
    # Load pretrained model and tokenizer
    tokenizer = BertJapaneseTokenizer.from_pretrained(bert_model_name)
    pretrained_bert = BertModel.from_pretrained(bert_model_name).to(device)

    # Create BertClassifier with pretrained BERT
    config = pretrained_bert.config
    config.num_labels = 1  # Assuming binary classification and looks num_labels already in pretrained model
    model = BertClassifier(config).to(device)

    # Load pretrained BERT weights
    model.bert.load_state_dict(pretrained_bert.state_dict())

    # Load fine-tuned checkpoint
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'], strict=False)

    # ############## Prune model ##############
    # # Print initial sparsity
    # print_sparsity(model)
    # # Apply pruning
    # pruned_model = prune_bert_model(model, amount=0.3)
    # # Print sparsity after pruning
    # print_sparsity(pruned_model)
    # model = pruned_model
    # ############## Prune model ##############
  
    # Create directory for the merged model
    os.makedirs(merged_model_path, exist_ok=True)

    # Copy necessary files from the pretrained model
    for file in ['config.json', 'tokenizer_config.json', 'vocab.txt']:
        shutil.copy(os.path.join(bert_model_name, file), merged_model_path)

    if if_quantized:
        ############## Quantize model ##############
        replace_linear_with_target_and_quantize(model, W8A16LinearLayer, ["lm_head"])
        # quantized_model = torch.quantization.quantize_dynamic(
        #     model, {torch.nn.Linear}, dtype=torch.qint8
        # )
        ############## Quantize model ##############
 
    # Save the merged model
    model.eval()  # Set to evaluation mode
    model.save_pretrained(merged_model_path)
    model.config.save_pretrained(merged_model_path)  
    tokenizer.save_pretrained(merged_model_path)  

    print(f"Merged model saved to {merged_model_path}")

def load_quantized_model(model_path,if_quantized):
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')  
    if if_quantized:
        config = BertConfig.from_pretrained(model_path)  
        # Create an instance of your model with the loaded config
        quantized_model = BertClassifier(config).to(device)
        replace_linear_with_target_and_quantize(quantized_model, 
                             W8A16LinearLayer, 
                                   ["lm_head"])
        # Load the safetensors file
        checkpoint_path = model_path + '/model.safetensors'
        state_dict = load_file(checkpoint_path)
        # Load the state dict into your model
        quantized_model.load_state_dict(state_dict)
    else:
        quantized_model = AutoModelForSequenceClassification.from_pretrained(model_path)
   
    return quantized_model

def test_model_inference(if_quantized):
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    model_path = 'model/profanity_classifier'
  
    tokenizer = AutoTokenizer.from_pretrained(model_path)  
    model = load_quantized_model(model_path,if_quantized)
    model.to(device).eval()
    config = model.config # to check the number of labels

    def predict_profanity(text):
        encoded = tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=120,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
  
        input_ids = encoded['input_ids'].to(device)
        attention_mask = encoded['attention_mask'].to(device)
  
        with torch.no_grad():
            if if_quantized:
                _, output = model(input_ids, attention_mask=attention_mask)
            else:
                outputs = model(input_ids, attention_mask=attention_mask)

        if if_quantized:
            probability = output.item()      
        else:
            logits = outputs.logits
            probability = torch.sigmoid(logits).item()
        return probability

    def test_sentences():
        sentences = [
            "This is a normal sentence.",
            "You are an idiot!",
            "I love this beautiful day.",
            "Go to hell, you scum!",
            "The quick brown fox jumps over the lazy dog.",
            "You p1ec3 of sHit.",
            "That's bullshit!",
            "おまえはぼけ。",
            "あなたはダサいです。",
            "変態人！",
            "バカヤローおまえ！",
            "これから選手厚さ庭兄セーター受け取るまだ文章お腹暖かい",
            "お腹暖かい",
            "淫売心配集まるうまい踊る天気",
            "ちくしょう、パンクだ。"
        ]
  
        for sentence in sentences:
            prob = predict_profanity(sentence)
            print(f"Sentence: '{sentence}'")
            print(f"Probability of containing profanity: {prob:.4f}")
            print("Classification:", "Profane" if prob > 0.7 else "Clean")
            print()

    test_sentences()

```

[[🦜🦜🦜Building a Better Profanity Detection Library with scikit-learn]]

## Directly use 3rd party package to quantize model
```
pip install llmcompressor
```
```python
from llmcompressor.modifiers.quantization import QuantizationModifier
from llmcompressor.transformers import SparseAutoModelForCausalLM, oneshot

# Load model
MODEL_ID = "meta-llama/Meta-Llama-3.1-70B-Instruct"
model = SparseAutoModelForCausalLM.from_pretrained(MODEL_ID, device_map="auto", torch_dtype="auto")

# Configure the quantization scheme. In this case, we:
#   * Quantize the weights to FP8 with static per-channel scales
#   * Quantize the activations to FP8 with dynamic per-token scales
recipe = QuantizationModifier(scheme="FP8_DYNAMIC", targets="Linear", ignore=["lm_head"])

# Apply quantization
SAVE_DIR = MODEL_ID.split("/")[1] + "-FP8-Dynamic"
oneshot(model=model, recipe=recipe, save_compressed=True, output_dir=SAVE_DIR)
```
``

# llama.cpp 格式转换

[[llama.cpp一种在本地CPU上部署的量化模型（超低配推理llama）_llama cpp-CSDN博客]]

# 推荐系统

```Python
/opt/workspace/researcher/AICourse/DeepFM/mainTest.ipynb
import numpy as np
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.data import sampler
from model.DeepFM import DeepFM
from data.dataset import CriteoDataset
# 900000 items for training, 10000 items for valid, of all 1000000 items
Num_train = 9000
rootpath = '/opt/workspace/researcher/AICourse/DeepFM/data'
# load data
train_data = CriteoDataset(rootpath, train=True)
loader_train = DataLoader(train_data, batch_size=16,
                          sampler=sampler.SubsetRandomSampler(range(Num_train)))
val_data = CriteoDataset(rootpath, train=True)
loader_val = DataLoader(val_data, batch_size=16,
                        sampler=sampler.SubsetRandomSampler(range(Num_train, 10000)))
feature_sizes = np.loadtxt(rootpath + '/feature_sizes.txt', delimiter=',')
feature_sizes = [int(x) for x in feature_sizes]
print(feature_sizes)
model = DeepFM(feature_sizes, use_cuda=True)
optimizer = optim.Adam(model.parameters(), lr=1e-4, weight_decay=0.0)
model.fit(loader_train, loader_val, optimizer, epochs=5, verbose=True)
torch.save(model.state_dict(), 'trained_model.pt')
------------------------------------------
model = DeepFM(feature_sizes, use_cuda=True)
model.load_state_dict(torch.load('trained_model.pt')) 
model.eval()  # 将模型设为评估模式
with torch.no_grad():
    Xi, Xv = item_features  # 物品特征
    y_pred = model(Xi, Xv)
    ctr_pred = torch.sigmoid(y_pred).item()  # 将输出转为0-1之间的ctr预估值
item_preds = []
with torch.no_grad():
    for item_feature in candidate_items_features:
        Xi, Xv = item_feature
        y_pred = model(Xi, Xv) 
        item_preds.append((item_id, torch.sigmoid(y_pred).item()))
  
item_preds.sort(key=lambda x: x[1], reverse=True)  # 按ctr从高到低排序
recommended_items = [item[0] for item in item_preds[:n]]  # 取前n个作为推荐结果   
```

## **DeepCTR-Torch**

```Python
/opt/workspace/researcher/AICourse/DeepFM/mainTest.ipynb
https://deepctr-torch.readthedocs.io/en/latest/Quick-Start.html
https://deepctr-torch.readthedocs.io/en/latest/Examples.html\#classification-criteo
https://deepctr-torch.readthedocs.io/en/latest/FAQ.html
https://lacusrinz.github.io/2020/02/09/ctr/#%E8%AE%AD%E7%BB%83
pip install -U deepctr-torch

import pandas as pd
import torch
from sklearn.metrics import log_loss, roc_auc_score
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from deepctr_torch.inputs import SparseFeat, DenseFeat, get_feature_names
from deepctr_torch.models import *
data = pd.read_csv('/opt/workspace/researcher/AICourse/DeepFM/data/train.txt')
sparse_features = ['C' + str(i) for i in range(1, 27)]
dense_features = ['I' + str(i) for i in range(1, 14)]
column_names =  dense_features + sparse_features + ['label']
data.columns = column_names
data[sparse_features] = data[sparse_features].fillna('-1', )
data[dense_features] = data[dense_features].fillna(0, )
target = ['label']
# 1.Label Encoding for sparse features,and do simple Transformation for dense features
for feat in sparse_features:
    lbe = LabelEncoder()
    data[feat] = lbe.fit_transform(data[feat])
mms = MinMaxScaler(feature_range=(0, 1))
data[dense_features] = mms.fit_transform(data[dense_features])
fixlen_feature_columns = [SparseFeat(feat, data[feat].nunique())
                          for feat in sparse_features] + [DenseFeat(feat, 1, )
                                                          for feat in dense_features]
dnn_feature_columns = fixlen_feature_columns
linear_feature_columns = fixlen_feature_columns
feature_names = get_feature_names(
    linear_feature_columns + dnn_feature_columns)
  
train, test = train_test_split(data, test_size=0.2)
train_model_input = {name: train[name] for name in feature_names}
test_model_input = {name: test[name] for name in feature_names}
  
device = 'gpu'
use_cuda = True
if use_cuda and torch.cuda.is_available():
    print('cuda ready...')
    device = 'cuda:0'
model = DeepFM(linear_feature_columns=linear_feature_columns, dnn_feature_columns=dnn_feature_columns,
               task='binary',
               l2_reg_embedding=1e-5, device=device)
model.compile("adagrad", "binary_crossentropy",
              metrics=["binary_crossentropy", "auc"], )
model.fit(train_model_input,train[target].values,batch_size=32,epochs=10,verbose=2,validation_split=0.0)
pred_ans = model.predict(test_model_input, 256)
print("")
print("test LogLoss", round(log_loss(test[target].values, pred_ans), 4))
print("test AUC", round(roc_auc_score(test[target].values, pred_ans), 4))
```

### issue

```Python
在一个训练批次里的Y只有一个类别
~/miniconda3/envs/research/lib/python3.10/site-packages/deepctr_torch/models/basemodel.py:268
捕获 log_loss 异常并跳过当前批次：
在 BaseModel 的 fit 方法中捕获 log_loss 抛出的 ValueError 异常，并跳过当前批次。
跳过异常批次：
当捕获到异常时，记录错误信息并跳过当前批次。
                        if verbose > 0:
                            for name, metric_fun in self.metrics.items():
                                if name not in train_result:
                                    train_result[name] = []
                                try:
                                    train_result[name].append(metric_fun(
                                        y.cpu().data.numpy(), y_pred.cpu().data.numpy().astype("float64")))
                                except ValueError as e:
                                    if ("y_true contains only one label" in str(e)) or ("Only one class present in y_true" in str(e)):
                                        print(f"Skipping batch due to ValueError: {e}")
                                        continue
                                    else:
                                        raise                              
                                # train_result[name].append(metric_fun(
                                #     y.cpu().data.numpy(), y_pred.cpu().data.numpy().astype("float64")))
```

### 推荐给相似用户的程序

/opt/workspace/researcher/AICourse/DeepFM/mainTest.ipynb
[https://chatgpt.com/share/df8b70d0-c69b-4803-9a29-ef214c568ef8](https://chatgpt.com/share/df8b70d0-c69b-4803-9a29-ef214c568ef8)

```Python
import numpy as np
import pandas as pd
import random
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split
from deepctr_torch.inputs import SparseFeat, DenseFeat, get_feature_names
from deepctr_torch.models import DeepFM
from sklearn.decomposition import TruncatedSVD
from sklearn.metrics.pairwise import cosine_similarity
import torch
# 示例数据集
data = pd.DataFrame({
    'User_ID': [1, 2, 1, 3, 2, 1, 3, 2],
    'Item_ID': [101, 102, 103, 104, 105, 106, 107, 108],
    'User_Age': [25, 30, 25, 22, 30, 25, 22, 30],
    'User_Gender': ['M', 'F', 'M', 'F', 'F', 'M', 'F', 'F'],
    'Item_Category': ['Electronics', 'Clothing', 'Books', 'Electronics', 'Books', 'Clothing', 'Books', 'Electronics'],
    'Context_Time': ['Morning', 'Afternoon', 'Evening', 'Night', 'Morning', 'Afternoon', 'Evening', 'Night'],
    'Label': [1, 0, 1, 0, 1, 0, 0, 1]
})

sparse_features = ['User_ID', 'Item_ID', 'User_Gender', 'Item_Category', 'Context_Time']
dense_features = ['User_Age']
target = ['Label']
# 对稀疏特征进行编码
for feat in sparse_features:
    lbe = LabelEncoder()
    data[feat] = lbe.fit_transform(data[feat])
# 对密集特征进行归一化
mms = MinMaxScaler(feature_range=(0, 1))
data[dense_features] = mms.fit_transform(data[dense_features])
# 定义特征列
fixlen_feature_columns = [SparseFeat(feat, data[feat].nunique()) for feat in sparse_features] + [DenseFeat(feat, 1,) for feat in dense_features]
dnn_feature_columns = fixlen_feature_columns
linear_feature_columns = fixlen_feature_columns
feature_names = get_feature_names(linear_feature_columns + dnn_feature_columns)
# 训练数据
train, test = train_test_split(data, test_size=0.2, random_state=42)
train_model_input = {name: train[name] for name in feature_names}
test_model_input = {name: test[name] for name in feature_names}
# 训练模型
device = 'cuda' if torch.cuda.is_available() else 'cpu'
model = DeepFM(linear_feature_columns=linear_feature_columns, dnn_feature_columns=dnn_feature_columns, task='binary', device=device)
model.compile("adagrad", "binary_crossentropy", metrics=["binary_crossentropy", "auc"])
model.fit(train_model_input, train[target].values, batch_size=32, epochs=100, verbose=2, validation_split=0.0)
# 预测
all_user_item_pairs = pd.DataFrame([(user, item) for user in data['User_ID'].unique() for item in data['Item_ID'].unique()], columns=['User_ID', 'Item_ID'])
# 添加其他特征
all_user_item_pairs['User_Age'] = all_user_item_pairs['User_ID'].map(dict(zip(data['User_ID'], data['User_Age'])))
all_user_item_pairs['User_Gender'] = all_user_item_pairs['User_ID'].map(dict(zip(data['User_ID'], data['User_Gender'])))
all_user_item_pairs['Item_Category'] = all_user_item_pairs['Item_ID'].map(dict(zip(data['Item_ID'], data['Item_Category'])))
all_user_item_pairs['Context_Time'] = random.choices(data['Context_Time'].unique(), k=len(all_user_item_pairs))
all_user_item_pairs
# 对稀疏特征进行编码
for feat in sparse_features:
    all_user_item_pairs[feat] = lbe.fit_transform(all_user_item_pairs[feat].astype(str))
# 对密集特征进行归一化
all_user_item_pairs[dense_features] = mms.transform(all_user_item_pairs[dense_features])
# 准备模型输入
predict_model_input = {name: all_user_item_pairs[name] for name in feature_names}
# 预测点击率
pred_ans = model.predict(predict_model_input, batch_size=256)
all_user_item_pairs['pred_score'] = pred_ans
# 找出用户1最感兴趣的项目
user_1_recommendations = all_user_item_pairs[all_user_item_pairs['User_ID'] == 1].sort_values(by='pred_score', ascending=False).head(5)
print("Recommendations for User 1:")
display(user_1_recommendations)
# 使用SVD进行降维
user_features = data[['User_ID', 'User_Age', 'User_Gender']]
user_features = user_features.drop_duplicates().reset_index(drop=True)
user_features['User_Gender'] = lbe.fit_transform(user_features['User_Gender'].astype(str))
svd = TruncatedSVD(n_components=2, random_state=42)
user_features_reduced = svd.fit_transform(user_features[['User_Age', 'User_Gender']])
# 计算用户之间的相似度
similarities = cosine_similarity(user_features_reduced)
# 找出与用户1特征相似的用户
user_1_index = user_features[user_features['User_ID'] == 1].index[0]
similar_users_indices = similarities[user_1_index].argsort()[::-1][1:2]  # 排除自己。就找1个用户
similar_users = user_features.iloc[similar_users_indices]
print("Users similar to User 1 based on Age and Gender:")
display(similar_users)
# 将用户1感兴趣的项目推荐给其他相似用户
recommended_items = user_1_recommendations['Item_ID'].tolist()
recommended_items

print("Recommended items for similar users:")
for item in recommended_items:
    print(f"Item ID: {item}")
    similar_users_recommendations = all_user_item_pairs[(all_user_item_pairs['Item_ID'] == item) & (all_user_item_pairs['User_ID'].isin(similar_users['User_ID']))]
    similar_users_recommendations = similar_users_recommendations.sort_values(by='pred_score', ascending=False).head(5)
    display(similar_users_recommendations)
```

## 自注意句子embedding

[[Self-attentive sentence embedding for the recommendation system]]
[[A Structured Self-attentive Sentence Embedding]]

# python special feature

### 推荐系统SVD提取特征

```Python
/opt/workspace/researcher/AICourse/Python实现音乐推荐系统/推荐系统.ipynb
import numpy as np
from scipy.sparse.linalg import svds
# 构建用户-物品评分矩阵
def build_rating_matrix(ratings, n_users, n_items):
    A = np.zeros((n_users, n_items))
    for user, item, rating in ratings:
        A[user][item] = rating
    return A
# SVD算法提取特征
def svd_features(A, k):
    U, s, Vt = svds(A, k=k)
    S = np.diag(s)
    user_features = np.dot(U, np.sqrt(S))
    item_features = np.dot(np.sqrt(S), Vt)
    return user_features, item_features
# 预测用户对物品的评分
def predict_rating(user_id, item_id, user_features, item_features):
    return np.dot(user_features[user_id], item_features[:, item_id])
# 主程序
if __name__ == '__main__':
    # 用户-物品评分数据
    ratings = [
        (0, 0, 5), (0, 1, 3), (0, 3, 1),
        (1, 0, 4), (1, 3, 1), 
        (2, 1, 3), (2, 2, 4), (2, 3, 5),
        (3, 0, 1), (3, 1, 2), (3, 2, 4)
    ]
    n_users = 4
    n_items = 4
    n_features = 2
    # 构建评分矩阵
    A = build_rating_matrix(ratings, n_users, n_items)
    print("原始评分矩阵:")
    print(A)
    # SVD提取特征
    user_features, item_features = svd_features(A, n_features)
    print("\nUser特征矩阵:")
    print(user_features)
    print("\nItem特征矩阵:")
    print(item_features)
    # 预测评分示例
    user_id = 0
    item_id = 2
    pred_rating = predict_rating(user_id, item_id, user_features, item_features)
    print(f"\nUser {user_id} 对 Item {item_id} 的预测评分: {pred_rating:.2f}")
  
  
  
    ==================================================
import numpy as np
from scipy.sparse import csr_matrix
from sklearn.decomposition import TruncatedSVD
from sklearn.metrics import mean_squared_error
from math import sqrt
# 创建用户-项目评分矩阵
ratings = [
    [0, 0, 0, 5, 4, 0],
    [0, 5, 0, 4, 0, 0],
    [5, 4, 0, 0, 0, 0],
    [0, 0, 5, 0, 0, 4],
    [0, 0, 4, 0, 5, 0]
]
# 将评分矩阵转换为稀疏矩阵
ratings = csr_matrix(ratings)
# 使用TruncatedSVD进行降维
svd = TruncatedSVD(n_components=2, random_state=42)
reduced_matrix = svd.fit_transform(ratings)
# 使用降维后的矩阵重建评分矩阵
reconstructed_matrix = np.dot(reduced_matrix, svd.components_)
# 预测结果
print("Predicted Ratings Matrix:")
print(np.round(reconstructed_matrix, 2))
# 定义预测函数，返回某个用户的推荐项目
def recommend(user_id, num_recommendations):
    user_ratings = reconstructed_matrix[user_id, :]
    # 筛选出用户未评分的项目
    unrated_items = np.where(ratings[user_id, :].toarray() == 0)[1]
    recommendations = sorted(unrated_items, key=lambda x: user_ratings[x], reverse=True)
    return recommendations[:num_recommendations]
# 给用户0推荐2个项目
user_id = 0
num_recommendations = 2
recommended_items = recommend(user_id, num_recommendations)
print(f"Recommended Items for User {user_id}: {recommended_items}")
# 计算RMSE评估误差
def rmse(actual, predicted):
    actual = actual.toarray().flatten()
    predicted = predicted.flatten()
    mask = actual != 0
    return sqrt(mean_squared_error(actual[mask], predicted[mask]))
rmse_score = rmse(ratings, reconstructed_matrix)
print(f"Root Mean Square Error (RMSE): {rmse_score:.4f}")
```

### 向量操作

```JavaScript
\#python special feature
a = torch.randn(3, 4)
b = torch.randn(4, 5)
# 使用einsum函数执行矩阵乘法
c = torch.einsum('ij,jk->ik', a, b)
def cross_entropy(y_hat, y):
    return - torch.log(y_hat[range(len(y_hat)), y])
query_results = [
            QueryResult(PaperWithCode=result.PaperWithCode, distance=result.distance)
            for result in results
        ]
random_tensor = torch.rand((2, 3, 4))
softmax_result = torch.nn.functional.softmax(random_tensor, dim=-1)
shape = softmax_result.shape
flattened_result = softmax_result.flatten()
noise_coefficient = 1e-4
gamma_noise = torch.tensor([math.gamma(x.item()) * noise_coefficient for x in flattened_result])
gamma_noise = gamma_noise.reshape(shape)
result_with_noise = softmax_result + gamma_noise
import numpy as np
import torch
import torch.nn.functional as F
# 任务1：创建一个形状为(3, 1, 5)的 NumPy 多维数组
numpy_array = np.random.rand(3, 1, 5)
# 任务2：将该 NumPy 数组转换为 Torch Tensor
torch_tensor = torch.from_numpy(numpy_array).float()
# 任务3：将 Tensor 重塑为(3, 5)
reshaped_tensor = torch_tensor.view(3, 5)
# 任务4：对第二维求 Softmax
softmax_tensor = F.softmax(reshaped_tensor, dim=1)
# 任务5：将 Tensor 重塑回(3, 1, 5)
reshaped_back_tensor = softmax_tensor.view(3, 1, 5)
# 任务6：将最后的 Tensor 转换回 NumPy 数组
final_numpy_array = reshaped_back_tensor.detach().numpy()
如果原来的张量形状是 [a, b]，使用 .unsqueeze(1) 后，它将变为 [a, 1, b]。
张量中的所有元素除以256。这通常用于归一化
torch.tensor(targets).unsqueeze(1) / 256
img = img[0].to(rgb_std.device)
# img.permute(1, 2, 0): 改变张量的维度顺序。通常，PyTorch张量的形状是 [channels, height, width]，这里我们将其更改为 [height, width, channels]。
# * rgb_std + rgb_mean: 这是标准化的逆操作。假设在训练时，我们使用了 (img - rgb_mean) / rgb_std 这样的标准化，现在我们将其逆转回原始的尺度和均值。
# torch.clamp(..., 0, 1): 为了确保得到的值在 [0,1] 范围内，我们使用了 clamp 函数。这可以防止得到的像素值超出这个范围，导致颜色失真。
img = torch.clamp(img.permute(1, 2, 0) * rgb_std + rgb_mean, 0, 1)
# 将张量转换为PIL图片格式
return torchvision.transforms.ToPILImage()(img.permute(2, 0, 1))
conv(x)：应用卷积操作。卷积操作通常改变输入张量的通道数，并可能改变其高度和宽度，取决于卷积核的大小、步长和填充。
F.relu()：应用ReLU激活函数。ReLU函数是非线性的，用于增加网络的非线性特性并帮助解决梯度消失问题。激活函数不改变张量的形状。
.squeeze(3)：这个方法移除维度中大小为1的维度。这里指定的维度是索引3，意味着如果第四维（从0开始计数）的大小是1，它将被移除。这通常是在卷积之后用于移除单个通道维度。
F.max_pool1d()：应用一维最大池化。这里使用 x.size(2) 作为池化的窗口大小，通常这会减少张量的一个维度的大小，具体取决于窗口大小。在这种情况下，它使用第三维度（即高度）作为池化的大小。
.squeeze(2)：和之前一样，移除第三维度（从0开始计数），如果它的大小是1。
x = F.relu(conv(x)).squeeze(3)  [128,1,32,300] -> [128,256,32] 
x = F.max_pool1d(x, x.size(2)).squeeze(2)  [128,256,32] -> [128,256]
out = torch.cat([self.conv_and_pool(out, conv) for conv in self.convs], 1)  [128,256] -> [128,768] 
out = self.dropout(out) [128,768] 
out = self.fc(out)  [128,768] -> [128,10] 
############################################################
single_sentences_list = re.split(r'(?<=[.?!])\s+', essay)
正则表达式：(?<=\$)\d+
解释：这个表达式将匹配任何跟在美元符号$后的数字序列。(?<=\$)是一个正向回顾后发断言，指定匹配必须出现在$符号之后，但不包括$本身。\d+匹配一个或多个数字。
例子 2：匹配被某单词跟随的单词
假设我们想要找到所有紧接着单词Python的单词。
正则表达式：(?<=Python\s)\w+
解释：这个表达式会匹配任何紧随“Python”（和一个空格）之后的单词。(?<=Python\s)是正向回顾后发断言，\w+匹配一个或多个字母数字字符组成的单词。
例子 3：匹配特定字符后的整个单词
假设我们要找到所有在叹号!之后的单词。
正则表达式：(?<=\!)\s\w+
解释：这个表达式会匹配紧跟在叹号!之后的单词。(?<=\!)是正向回顾后发断言，\s匹配空格，\w+匹配一个或多个字母数字字符组成的单词。

########################################################################################################
>>> import polars as pl
>>> from datasets import load_dataset
>>>>>> ds = load_dataset("DIBT/10k_prompts_ranked", split="train")
>>> ds.to_polars() \
...     .group_by("topic") \
...     .agg(pl.len(), pl.first()) \
...     .sort("len", descending=True) \
...     .rename({"len": "count", "prompt": "example_prompt"})
```

### einops usage

```python
import numpy as np
import einops

# Create a batch of 32 RGB images, each 64x64 pixels
images = np.random.rand(32, 64, 64, 3)

# Traditional way to flatten the images
flattened_traditional = images.reshape(32, -1)

# Einops way to flatten the images
flattened_einops = einops.rearrange(images, 'b h w c -> b (h w c)')

# Traditional way to switch color channel position
color_last_traditional = images.transpose(0, 3, 1, 2)

# Einops way to switch color channel position
color_last_einops = einops.rearrange(images, 'b h w c -> b c h w')

print(f"Original shape: {images.shape}")
print(f"Flattened shape (traditional): {flattened_traditional.shape}")
print(f"Flattened shape (einops): {flattened_einops.shape}")
print(f"Color channel last shape (traditional): {color_last_traditional.shape}")
print(f"Color channel last shape (einops): {color_last_einops.shape}")

```

### dataframe regex

```Python
2df = df[(df['filename'].str.contains("_aug", regex=False, na=False)) | (df['filename'].str.contains(r"/\d", regex=True, na=False))]
```

### **  向量squeeze and reshape  **

```Python
如何移除形状为([1, 2, 192])张量的第一维度：
# 创建一个形状为([1, 2, 192])的张量
original_tensor = torch.randn(1, 2, 192)
# 使用squeeze移除第一维度
squeezed_tensor = original_tensor.squeeze(0)
# 使用reshape指定新的形状
reshaped_tensor = original_tensor.reshape(2, 192)
# 打印变形后的形状
squeezed_tensor_shape = squeezed_tensor.shape
reshaped_tensor_shape = reshaped_tensor.shape
squeezed_tensor_shape, reshaped_tensor_shape
Result
(torch.Size([2, 192]), torch.Size([2, 192]))
reshape(-1, 192)意味着你想要保持最后一个维度为192，而第一个维度的具体大小则由张量中元素的总数和最后一个维度的大小共同决定，以保证变形前后张量元素的总数不发生变化。
对于一个初始形状为([1, 2, 192])的张量，它总共有1*2*192 = 384个元素。当你使用reshape(-1, 192)时，PyTorch会自动计算第一个维度的大小，以保持元素总数为384。因为每个“行”（这里的行是一个比喻，实际上是高维数据）应该有192个元素，所以384个元素可以被整齐地组织成2行，每行192个元素。因此，reshape(-1, 192)会将张量变形为形状为[2, 192]的张量。
假设我们有以下三个张量:
tensor1 = torch.randn(1, 1, 192)
tensor2 = torch.randn(1, 192)
tensor3 = torch.randn(2, 1, 192)
对这些张量应用reshape(1, -1)和squeeze(0),看看会发生什么

{'reshape1_shape': torch.Size([1, 192]),
 'reshape2_shape': torch.Size([1, 192]),
 'reshape3_shape': torch.Size([1, 384]),
 'squeeze1_shape': torch.Size([1, 192]),
 'squeeze2_shape': torch.Size([192]),
 'squeeze3_shape': torch.Size([2, 1, 192]) #因为squeeze(0)在这里不会改变形状，由于第一维度不是单一维度。
 } 
```

```Python
#显示所有值不省略
import pandas as pd
# 显示所有列
pd.set_option('display.max_columns', None)
# 显示所有行
pd.set_option('display.max_rows', None)
# 设置value的显示长度为100，默认为50
pd.set_option('max_colwidth',100)
print(train_data.iloc[0, :])
train_data.columns
```

### 替换默认print函数

```JavaScript
日志生成，替换默认print函数
import logging
import os
import datetime
import builtins
# Get today's date
today_date = datetime.datetime.now().strftime('%Y-%m-%d')
# 获取当前脚本的目录
current_path = os.path.dirname(os.path.abspath(__file__))
logs_dir = os.path.join(current_path, "logs")
if not os.path.exists(logs_dir):
    os.mkdir(logs_dir)
filename = os.path.join(logs_dir, f"logfile_{today_date}.log")
# Set up logging configuration
logging.basicConfig(filename=filename, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
def custom_print(*args, **kwargs):
    # 将信息记录到日志文件
    logging.info(' '.join(map(str, args)))
    # 保持原始print函数的功能
    original_print(*args, **kwargs)
original_print = builtins.print
builtins.print = custom_print
```

### Remove background

```JavaScript
pip install rembg
```

### Debug code with icecream tool

```Python
https://medium.com/datadriveninvestor/introducing-icecream-never-use-print-to-debug-your-python-code-again-fb0c1f1f2972
pip install icecream
from icecream import ic
def func(input_num):
    if input_num == 1:
        ic()
        ...
    else:
        ic()
        ...
func(2)
## main_file.py
from icecream import install
install()
from help_file import func
func(2)

ic.disable()
```

### 提高Pandas库在进行数据追加操作时性能的方法

```JavaScript
将DataFrame转换为另一种数据结构，比如字典或NumPy数组。
在这种数据结构中执行追加操作。
完成追加后，再将数据结构转换回Pandas的DataFrame。
请注意，这种方法主要针对追加新行，添加新列通常不是问题，因为它不会影响到其他列。
下面是一个Python示例，演示如何使用这种方法来提高性能：
import pandas as pd
import numpy as np
# 假设我们有一个DataFrame
df = pd.DataFrame({
    'A': [1, 2],
    'B': [3, 4]
})
# 将DataFrame转换为字典
data_dict = df.to_dict(orient='records')
# 假设我们有要追加的数据
new_data = [{'A': 5, 'B': 6}, {'A': 7, 'B': 8}]
# 将新数据追加到字典中
data_dict.extend(new_data)
# 将字典转换回DataFrame
new_df = pd.DataFrame(data_dict)
print(new_df)
在这个例子中，我们首先创建了一个包含两行数据的DataFrame。然后，我们将DataFrame转换为字典格式，以便可以轻松地追加新行。接着，我们创建了一个包含两行新数据的列表，并将其追加到字典中。最后，我们将更新后的字典转换回DataFrame。
请注意，这种方法适用于追加大量数据行的情况，可以显著提高性能。然而，如果只是偶尔追加少量数据，直接使用Pandas的append方法可能更为方便。
```

Improve matplot output

```Python
from matplotlib_inline.backend_inline import set_matplotlib_formats
set_matplotlib_formats('svg')
```

# Mojo

```JavaScript
#######################################################################################
Install Mojo
To use the Mojo SDK, you need a system that meets these specifications:
Ubuntu 20.04/22.04 LTS
x86-64 CPU (with SSE4.2 or newer) and a minimum of 8 GiB memory
Python 3.8 - 3.10
g++ or clang++ C++ compiler
https://developer.modular.com/download
curl https://get.modular.com | \
  MODULAR_AUTH=mut_6e637632606a498c8a374238b4f4bff5 \
  sh -
conda install -c conda-forge ncurses
echo 'export MODULAR_HOME="$HOME/.modular"' >> ~/.bashrc
echo 'export PATH="$MODULAR_HOME/pkg/packages.modular.com_mojo/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
modular install mojo
install mojo vcs extention
```

# AutoML

```Python
AUTOGLUON
https://github.com/autogluon/autogluon
https://auto.gluon.ai/stable/tutorials/tabular/advanced/tabular-gpu.html
00000000000
https://blog.csdn.net/fyfugoyfa/article/details/125442923
https://aitechtogether.com/article/38383.html
https://wenku.csdn.net/answer/b8fbb114a223483ab9346bc809c6914c
https://blog.csdn.net/xzpdxz/article/details/122601996 !!! detail program and useful
00000000000
you need reinstall jetson torch as it will remove original version
pip install bokeh==2.0.1
pip install vowpalwabbit==9.4
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
pip install mxnet
https://github.com/autogluon/autogluon/issues/612
conda install -n base mamba -c conda-forge
mamba create -n ag autogluon python -c conda-forge
conda install autogluon -c conda-forge
conda activate autogluon
cd 下载/
pip install torch-1.10.2+cu102-cp39-cp39-linux_x86_64.whl
pip install mxnet_cu102 //安装CUDA10.2对应的mxnet_gpu
pip install autogluon

OSError: libmkl_intel_lp64.so.2: cannot open shared object file: No such file or directory
(mlearn) [raysheng@MONSTER:~/miniconda3/envs/mlearn/lib/python3.10/site-packages/torch/lib]$ locate  libmkl_intel_lp64
/home/<USER>/miniconda3/envs/causal/lib/libmkl_intel_lp64.so
/home/<USER>/miniconda3/envs/causal/lib/libmkl_intel_lp64.so.2
/home/<USER>/miniconda3/pkgs/mkl-2023.1.0-h213fc3f_46343/lib/libmkl_intel_lp64.so
/home/<USER>/miniconda3/pkgs/mkl-2023.1.0-h213fc3f_46343/lib/libmkl_intel_lp64.so.2
@Fix  it is related to torch-xla distribut
pip install mkl
find this librabrry 
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/path/to/library/
[LightGBM] [Fatal] GPU Tree Learner was not enabled in this build.
xgboost.core.XGBoostError: [22:00:45] /croot/xgboost-split_1675457761144/work/src/data/../common/common.h:239: XGBoost version not compiled with GPU support.
@fix
https://xgboost.readthedocs.io/en/latest/build.html
git clone --recursive https://github.com/dmlc/xgboost
mkdir build
cd build
cmake .. -DUSE_CUDA=ON
make -j4
pip install -v . --config-settings use_cuda=True
pip install -e .

https://lightgbm.readthedocs.io/en/latest/Python-Intro.html
dnf install boost boost-devel
git clone --recursive https://github.com/microsoft/LightGBM
cd LightGBM
mkdir build
cd build
cmake -DUSE_GPU=1 -DOpenCL_LIBRARY=/usr/local/cuda/lib64/libOpenCL.so -DOpenCL_INCLUDE_DIR=/usr/local/cuda/include/ ..
cmake -DUSE_GPU=1 -DUSE_CUDA=1 -DOpenCL_LIBRARY=/usr/local/cuda/lib64/libOpenCL.so -DOpenCL_INCLUDE_DIR=/usr/local/cuda/include/ ..
cmake -DUSE_GPU=1 -DUSE_CUDA=1 -DOpenCL_LIBRARY=/home/<USER>/miniconda3/envs/causal/lib/libOpenCL.so.1 -DOpenCL_INCLUDE_DIR=/home/<USER>/miniconda3/envs/causal/include/ ..
make -j4
make install
cd ..
sh ./build-python.sh install --precompile
\#confirm working after installation
https://qiita.com/so1_tsuda/items/83a58c93f15afdc34d00
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/home/<USER>/miniconda3/envs/mlearn/lib/
../../lightgbm config=train.conf data=binary.train valid=binary.test device=gpu
https://lightgbm.readthedocs.io/en/latest/GPU-Tutorial.html\#build-lightgbm
https://www.kaggle.com/code/kirankunapuli/ieee-fraud-lightgbm-with-gpu/notebook

AttributeError: 'Booster' object has no attribute 'best_ntree_limit'
/home/<USER>/miniconda3/envs/mlearn/lib/python3.10/site-packages/autogluon/tabular/models/xgboost/xgboost_model.py
https://stackoverflow.com/questions/51955256/xgboost-best-iteration
144: self.params_trained["n_estimators"] = bst.best_iteration

https://auto.gluon.ai/stable/cheatsheet.html
https://qiita.com/kirikei/items/f879eb2cfbaf3d37ee0f
https://github.com/Kaggle/kaggle-api
pip install kaggle
\#setup kaggle key
Ensure kaggle.json is in the location ~/.kaggle/kaggle.json to use the API.
kaggle c download titanic
kaggle c list
kaggle c leaderboard stanford-ribonanza-rna-folding --show
kaggle c files stanford-ribonanza-rna-folding
kaggle competitions list --category gettingStarted
kaggle competitions list -s health
kaggle competitions submit favorita-grocery-sales-forecasting -f sample_submission_favorita.csv.7z -m "My submission message"
kaggle competitions submissions favorita-grocery-sales-forecasting
kaggle datasets list -s demographics
kaggle competitions submit -c house-prices-advanced-regression-techniques -f submission.csv -m "Here is a test"
\#EXAMPLE
https://www.kaggle.com/code/gusthema/house-prices-prediction-using-tfdf
from autogluon.tabular import TabularDataset, TabularPredictor
train_data = TabularDataset('https://autogluon.s3.amazonaws.com/datasets/Inc/train.csv')
test_data = TabularDataset('https://autogluon.s3.amazonaws.com/datasets/Inc/test.csv')
predictor = TabularPredictor(label='class').fit(train_data, time_limit=120)  # Fit models for 120s
leaderboard = predictor.leaderboard(test_data)
# leaderboard = predictor.leaderboard(new_data, extra_metrics=['accuracy','log_loss'])
id, label = 'Id', 'Sold Price'
large_val_cols = ['Lot', 'Total interior livable area', 'Tax assessed value', 'Annual tax amount', 'Listed Price', 'Last Sold Price']
for c in large_val_cols+[label]:
		train_data[c] = np.log(train_data[c]+1)
predictor = TabularPredictor(label=label).fit(
  train_data.drop(columns=[id]),
	hyperparameters='multimodal',
	num_stack_levels=1, num_bag_folds=5)
preds = predictor.predict(test_data.drop(columns=[id]))
submission = pd.DataFrame({id:test_data[id], lable:preds})
submission.to_csv('submission.csv',index=False)
train_data = train_data.drop(columns=['PassengerID'])
verbosity=3
eval_metric='roc_auc'
time_limit=400
presets='best_quality'
tuning_data=val_data
hyperparameters='very_light'
exclude_model_types=['KNN','NN_TORCH']
predictor.fit_summary(show_plot=True)
models=predictor.get_model_names()
predictor.predict(test_data,model=models[1])
\#load/save model
predictor=TabularPredictor.load('{model path}')
predictor.presist_models()
presets=['good_quality','optimize_for_deployment']
students=predictor.distill()
predictor.evaluate(test_data, model=students[2])
preds = net(test_features)
# 将其重新格式化以导出到Kaggle
test_data['SalePrice'] = pd.Series(preds.cpu().squeeze().tolist())
# test_data['SalePrice'] = pd.Series(preds.reshape(1, -1)[0])
submission = pd.concat([test_data['Id'], test_data['SalePrice']], axis=1)
submission.to_csv('submission.csv', index=False)
```

---

# 对算法benchmark

```Python
#@save
class Benchmark:
    """用于测量运行时间"""
    def __init__(self, description='Done'):
        self.description = description
    def __enter__(self):
        self.timer = d2l.Timer()
        return self
    def __exit__(self, *args):
        print(f'{self.description}: {self.timer.stop():.4f} sec')
net = get_net()
with Benchmark('无torchscript'):
    for i in range(1000): net(x)
net = torch.jit.script(net)
with Benchmark('有torchscript'):
    for i in range(1000): net(x)
```

---

[Improving RAG (Retrieval Augmented Generation) Answer Quality with Re-ranker | by Shivam Solanki | Towards Generative AI | Aug, 2023 | Medium --- 使用重新排序器提高 RAG（检索增强生成）答案质量 |作者：Shivam Solanki |迈向生成式人工智能 | 2023 年 8 月 |中等的](https://medium.com/towards-generative-ai/improving-rag-retrieval-augmented-generation-answer-quality-with-re-ranker-55a19931325)

```JavaScript
https://github.com/ibm-ecosystem-engineering/SuperKnowa/blob/main/3.%20Re-ranker/Watson%20Discovery/Re-ranker.ipynb
https://primeqa.github.io/primeqa/tutorials/03_ReRank_search_results.html
Purpose: 我们需要重新排名，因为第一阶段的检索器可能有缺陷。它可能会将一些不相关的文档排名较高，而某些相关文档可能会获得较低的分数。因此，并非所有 top-k 文档都是相关的，
也不是所有相关文档都在 top-k 中。重新排名器会完善这些结果并提出最相关的答案。
from primeqa.components.reranker.colbert_reranker import ColBERTReranker
def process_discovery_retriever(question):
    global string_unicode
    projects = discovery.list_projects().get_result()
    for project in projects['projects']:
        if (project['name'] == project_name):
            project_id = project['project_id']
    collections = discovery.list_collections(project_id = project_id).get_result()
    for collection in collections['collections']:
        # print(collection)
        if (collection['name'] == collection_name):
            collection_id = collection['collection_id']

    query_result = discovery.query(project_id=project_id, query=question).get_result()
    return query_result
# Run ColBERT Reranker
from primeqa.components.reranker.colbert_reranker import ColBERTReranker
model_name_or_path = "DrDecr.dnn"
max_reranked_documents = 2
reranker = ColBERTReranker(model=model_name_or_path)
reranker.load()
reranked_results = reranker.predict(queries= [question], documents = [results], max_num_documents=max_reranked_documents)
print(reranked_results)
reranked_results_to_display = [result['document'] for result in reranked_results[0]]
df = pd.DataFrame.from_records(reranked_results_to_display, columns=['rank','document_id','title','text'])
print('======================================================================')
print(f'QUERY: {question}')
display( HTML(df.to_html()) )
```

# 录屏程序

```JavaScript
import cv2
import numpy as np
import pyautogui
# Set the screen resolution to record
screen_width, screen_height = pyautogui.size()
resolution = (screen_width, screen_height)
# Set the output video filename
output_filename = "screen_recording.mp4"
# Set the frames per second (FPS) for the recording
fps = 30.0
# Define the codec and create VideoWriter object
fourcc = cv2.VideoWriter_fourcc(*"mp4v")
out = cv2.VideoWriter(output_filename, fourcc, fps, resolution)
# Define the recording duration in seconds
recording_duration = 5  # Change this to the desired recording duration
# Start the screen recording
for _ in range(int(fps * recording_duration)):
    # Capture the screen
    screen = pyautogui.screenshot()
    # Convert the screenshot to a numpy array and BGR format for OpenCV
    frame = np.array(screen)
    frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
    # Write the frame to the output video
    out.write(frame)
# Release the VideoWriter
out.release()
\#clCoding.com
```

# L40S and A4000 MultiGPUs

```Python
1. Install L40S hardware
a. 2 8bin to 16pin power cable is enough
   if worry about power.  open cmd as admin
	 nvidia-smi -L
	 nvidia-smi -q
   nvidia-smi -pl 150-350
   nvidia-smi  --query-gpu=memory.total --format=csv
b. make sure power supply 1200W
2. Setup A4000 and install windows nvidia latest driver 
3. Install cuda toolkit 12.3
https://developer.nvidia.com/cuda-downloads?target_os=Linux&target_arch=x86_64&Distribution=RHEL&target_version=9&target_type=rpm_local
4. Enable L40S to WDDM mode (UNOFFICIAL WAY) (Can't find offical way, if we install grid software, blackscreen)
https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/
https://developer.nvidia.com/nvidia-display-mode-selector-tool-home
5. Use MultiGPU
https://github.com/huggingface/transformers/issues/22595
https://keras.io/examples/nlp/data_parallel_training_with_keras_nlp/
https://www.tensorflow.org/guide/keras/distributed_training
https://www.tensorflow.org/guide/migrate/mirrored_strategy?hl=zh-cn
https://zhuanlan.zhihu.com/p/639850033

\#how to use gpu (memory/dedicated gpu)
https://www.tensorflow.org/guide/gpu?hl=zh-cn
(mom) [raysheng@MONSTER:/opt/workspace/mom]$ cat /etc/profile.d/cuda.sh
export CUDA_HOME=/usr/local/cuda
export PATH=$PATH:/usr/local/cuda/bin
export CUDA_VISIBLE_DEVICES=0

Reference
l40s manual: https://resources.nvidia.com/en-us-l40s/nvidia-l40s-product
nvidia-smi cmd:  https://qiita.com/miyamotok0105/items/1b34e548f96ef7d40370
https://zhuanlan.zhihu.com/p/166161217
相当全面!!!: https://zhuanlan.zhihu.com/p/254738836
model = transformers.LlamaForCausalLM.from_pretrained(
    "path/to/converted/llama-65B",
    load_in_8bit=True,
    device_map="auto",
    max_memory={0: "10GB", 1: "10GB", 2: "48GB", 3: "48GB"}
)
\#----------------------------------------------------------------------------------------------------
from transformers import LlamaConfig,LlamaForCausalLM,LlamaTokenizer
from accelerate import init_empty_weights,infer_auto_device_map,load_checkpoint_in_model,dispatch_model
import torch
cuda_list = '6,7'.split(',')
memory = '35GiB'
model_path = 'xxx'
no_split_module_classes = LlamaForCausalLM._no_split_modules
max_memory = {int(cuda):memory for cuda in cuda_list}
config = LlamaConfig.from_pretrained(model_path)
with init_empty_weights():
    model = LlamaForCausalLM._from_config(config, torch_dtype=torch.float16) #加载到meta设备中，不需要耗时，不需要消耗内存和显存
device_map = infer_auto_device_map(model, max_memory=max_memory,no_split_module_classes=no_split_module_classes) #自动划分每个层的设备
load_checkpoint_in_model(model,model_path,device_map=device_map) #加载权重
model = dispatch_model(model,device_map=device_map) #并分配到具体的设备上
tokenizer = LlamaTokenizer.from_pretrained(model_path)
torch.set_grad_enabled(False)
model.eval()
sents=['你是谁']
ids = tokenizer(sents,max_length=1800,padding=True,truncation=True,return_tensors="pt")
ids = ids.to(model.device) 
outputs = model.generate(**ids, do_sample=False)

#训练
def train(net, num_gpus, batch_size, lr):
    train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size)
    devices = [d2l.try_gpu(i) for i in range(num_gpus)]
    def init_weights(m):
        if type(m) in [nn.Linear, nn.Conv2d]:
            nn.init.normal_(m.weight, std=0.01)
    net.apply(init_weights)
    # 在多个GPU上设置模型
    net = nn.DataParallel(net, device_ids=devices)
    trainer = torch.optim.SGD(net.parameters(), lr)
    loss = nn.CrossEntropyLoss()
    timer, num_epochs = d2l.Timer(), 10
    animator = d2l.Animator('epoch', 'test acc', xlim=[1, num_epochs])
    for epoch in range(num_epochs):
        net.train()
        timer.start()
        for X, y in train_iter:
            trainer.zero_grad()
            X, y = X.to(devices[0]), y.to(devices[0])
            l = loss(net(X), y)
            l.backward()
            trainer.step()
        timer.stop()
        animator.add(epoch + 1, (d2l.evaluate_accuracy_gpu(net, test_iter),))
    print(f'测试精度：{animator.Y[0][-1]:.2f}，{timer.avg():.1f}秒/轮，'
          f'在{str(devices)}')
train(num_gpus=2, batch_size=256, lr=0.2)
```

## 控制GPU的最大输出功率

```JavaScript
#!/bin/bash
# 从nvidia-smi命令中获取Power Limit的数值
power_limit=$(nvidia-smi -g 0 -q -d POWER | grep "Power Limit" | grep -v "Default\|Enforced\|Min\|Max" | awk '{print $4}')
# 去除末尾的 'W'
power_limit=${power_limit%W}
# 检查Power Limit是否为150
if [ "$power_limit" != "150" ]; then
    # 如果不是150，设置Power Limit为150
    nvidia-smi -g 0 -pl 150
fi
================================================================
@echo off
setlocal enabledelayedexpansion
:: 获取 Power Limit 的数值
for /f "tokens=5 delims= " %%a in ('nvidia-smi -g 0 -q -d POWER ^| findstr /C:"Current Power Limit" ^| findstr /v "N/A"') do (
    set power_limit=%%a
)
:: 去除 'W' 字符
set power_limit=!power_limit:W=!
:: 输出当前 Power Limit 的值进行调试
echo 当前 Power Limit 的值为: !power_limit!
:: 检查 Power Limit 是否为 150
if not "!power_limit!"=="150.00" (
    :: 如果不是 150，设置 Power Limit 为 150
    nvidia-smi -g 0 -pl 150
) else (
    :: 如果已经是 150，输出一个消息
    echo Power Limit 已经设置为 150 瓦。
)
endlocal
```

```LaTeX
# 控制温度到一定程度发msg 报警
https://console.twilio.com/
https://login.twilio.com/u/signup?state=hKFo2SA5U0R1Y2dJNU9aZjlBVVVOSm5SSnRlb1czbTg3bi03S6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGNtVGRCR0t1dVl3aTZTUVVzVVJ2UVpGYlAzYWU4VmFOo2NpZNkgTW05M1lTTDVSclpmNzdobUlKZFI3QktZYjZPOXV1cks
<EMAIL>：kZ<MYP%1JJ/vmy<6
Recovery code: 6G2BJFX5FG19SU29ACX87YTL
Twilio phone number: +***********
Account SID: **********************************
Auth Token: bfb22282040140787e69f7812f8fc980
pip install twilio
添加Incoming Webhooks功能：
在你的Slack应用的设置页面中，找到“Incoming Webhooks”功能。
点击“Activate Incoming Webhooks”开关以启用它。
＜　Add New Webhook to Workspace＞
*********************************************************************************
\#enable crontab in wsl almalinux
sudo dnf install cronie
systemctl enable crond
systemctl start crond
systemctl -a|grep cron
\#code program
import subprocess
import requests
import json
# 获取GPU温度
def get_gpu_temp():
    try:
        output = subprocess.check_output(["nvidia-smi", "-i", "0", "--query-gpu=temperature.gpu", "--format=csv,noheader"], encoding='utf-8')
        return int(output.strip())
    except subprocess.CalledProcessError as e:
        print("无法获取GPU温度: ", e)
        return None
# 发送Slack通知
def send_slack_notification(temp):
    webhook_url = '*********************************************************************************'  # 替换为你的Slack Webhook URL
    slack_data = {'text': f'警告：GPU温度过高！当前温度：{temp}度'}
    try:
        response = requests.post(
            webhook_url, data=json.dumps(slack_data),
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code != 200:
            raise ValueError(f'请求到Slack返回了错误状态码：{response.status_code}, 响应体：{response.text}')
    except Exception as e:
        print("发送Slack通知失败: ", e)
temp_threshold = 80  # 设定温度阈值为80度
temp = get_gpu_temp()
if temp and temp > temp_threshold:
    # send_sms(temp)
    send_slack_notification(temp)
### Setup crontab job
(base) [raysheng@MONSTER:/opt/workspace/researcher]$ crontab -l
*/5 * * * * /home/<USER>/miniconda3/envs/joyrl/bin/python /opt/workspace/researcher/monitorgputemp.py
```

# 书刊以及网站

```JavaScript
All in One (Save into My Jetson server）
https://github.com/Mikoto10032/DeepLearning.git
清华大学推荐的人工智能书单
https://juejin.cn/post/7073749620387479589
《pytorch深度学习实战》中英文PDF+代码+伊莱
https://www.douban.com/note/836391762/?_i=8744121UyKXL_8
《机器学习》（南瓜书）公式详解
https://github.com/datawhalechina/pumpkin-book
《机器学习》（西瓜书）
https://jingyuexing.github.io/Ebook/Machine_Learning/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0_%E5%91%A8%E5%BF%97%E5%8D%8E.pdf
<<蘑菇书EasyRL>> 
github: https://github.com/datawhalechina/easy-rl
online book:  https://datawhalechina.github.io/easy-rl/
pdf download:  https://github.com/datawhalechina/easy-rl/releases
source: https://github.com/datawhalechina/easy-rl/tree/master/notebooks
<<精品教材-《Grokking深度学习》分享>>
https://github.com/iamtrask/Grokking-Deep-Learning
https://cdn.ttgtmedia.com/rms/pdf/grokking_deep_learning.pdf
# online reading book
https://livebook.manning.com/book/grokking-deep-learning/brief-contents/v-12/1
<<课程>>
- 学院
咕泡科技 https://ai.gupaoedu.cn/ 
- 课程
https://lexuecode.com/7029.html   <EMAIL>:zaq12wsxW
https://www.itpromise.cloud/list/gupao.html
咕泡学院 人工智能P5第5期2022年 https://shikey.com/2022/08/03/artificial-intelligence-p5-issue-5.html/comment-page-2
https://58xueke.com/gupao_all_2149
============================================
蘑菇书 -> 作者本人
https://www.bilibili.com/video/BV1HZ4y1v7eX
"G:\ResearchDirection\AI\Reference\01494 - 人工智能深度学习系统班6期"
南瓜书 -> 作者本人
https://www.bilibili.com/video/BV1Mh411e7VU
西瓜书 -> 作者本人
https://www.bilibili.com/video/BV1gG411f7zX 
机器学习入门到精通  -> 其实就是西瓜书讲解
https://www.bilibili.com/video/BV1PN4y1V7d9

Informer 
-> 迪哥
【吹爆！2024最容易出论文创新点的两个时间序列模型【LSTM】+【Informer】原理详解
+项目实战+源码复现！草履虫看了都能直接提出论文创新点！】
https://www.bilibili.com/video/BV11e411m7rx
-> 作者本人
【【顶会精读】绝对是B站最佳论文 | Informer：比Transformer更有效的长时间序列预测方法！草覆虫都能学会！】
https://www.bilibili.com/video/BV1nN4y1W7We
```

# 数据集取得

```Python
常用数据集
• MNIST: digits written by employees of the US Census Bureau
• ImageNet: millions of images from image search engines
• AudioSet: YouTube sound clips for sound classification 
• LibriSpeech: 1000 hours of English speech from audiobook 
• Kinetics: YouTube videos clips for human actions classification 
• KITTI:  traffic scenarios recorded by cameras and other sensors
• Amazon Review: customer reviews and from Amazon online shopping 
• SQuAD: question-answer pairs derived from Wikipedia 
More at https://en.wikipedia.org/wiki/List_of_datasets_for_machine-learning_research
```

```JavaScript
https://en.wikipedia.org/wiki/List_of_datasets_for_machine-learning_research
\#network site
1. PaperWithCodes
https://paperswithcode.com/datasets
2. Kaggle
https://www.kaggle.com/datasets
3. Google dataset search
https://datasetsearch.research.google.com/
4. huggingface
https://huggingface.co/docs/datasets/index
5. Azure
https://huggingface.co/docs/datasets/index
6. scale AI
https://dashboard.scale.com/nucleus/?datasets_list_display=public
```

# 有待尝试

## Simple Transformer

![[Untitled 52.png]]

### 实现

```JavaScript
1. transformer use bge token set
2. simple transform test
3. make 
#类似llama
/opt/workspace/raylab/myTransformer/karpathy/nanoGPT/model.py
import math
import torch
import torch.nn as nn
from torch.nn import functional as F
class LayerNorm(nn.Module):
    """ LayerNorm but with an optional bias. PyTorch doesn't support simply bias=False """
    def __init__(self, ndim, bias):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(ndim))
        self.bias = nn.Parameter(torch.zeros(ndim)) if bias else None
    def forward(self, input):
        return F.layer_norm(input, self.weight.shape, self.weight, self.bias, 1e-5)
class MLP(nn.Module):
    def __init__(self, embedding_size,dropout):
        super().__init__()
        self.c_fc    = nn.Linear(embedding_size, 4 * embedding_size, bias=False)
        self.gelu    = nn.GELU()
        self.c_proj  = nn.Linear(4 * embedding_size, embedding_size, bias=False)
        self.dropout = nn.Dropout(dropout)
    def forward(self, x):
        x = self.c_fc(x)
        x = self.gelu(x)
        x = self.c_proj(x)
        x = self.dropout(x)
        return x
class MultiHeadAttention(nn.Module):
    """多头注意力"""
    def __init__(self, block_size, num_hiddens,
                 num_heads, dropout, bias=False, **kwargs):
        super(MultiHeadAttention, self).__init__(**kwargs)
        self.c_attn = nn.Linear(num_hiddens, 2 * num_hiddens, bias=bias)
        # regularization
        self.attn_dropout = nn.Dropout(dropout)
        # self.resid_dropout = nn.Dropout(dropout)   
        self.n_head = num_heads
        self.block_size = block_size
        self.n_embd = num_hiddens
        self.dropout = dropout
  
    def forward(self, x):
        B, T, C = x.size() # batch size, sequence length, embedding dimensionality (n_embd)
        # calculate query, key, values for all heads in batch and move head forward to be the batch dim
        q, k  = self.c_attn(x).split(self.n_embd, dim=2)
        k = k.view(B, T, self.n_head, C // self.n_head).transpose(1, 2) # (B, nh, T, hs)
        q = q.view(B, T, self.n_head, C // self.n_head).transpose(1, 2) # (B, nh, T, hs)
        att = (q @ k.transpose(-2, -1)) * (1.0 / math.sqrt(k.size(-1)))
        # att = att.masked_fill(self.bias[:,:,:T,:T] == 0, float('-inf'))
        att = F.softmax(att, dim=-1)
        att = self.attn_dropout(att)
        y = att.transpose(1, 2).contiguous().view(B, T, C) # re-assemble all head outputs side by side
        return y

class Block(nn.Module):
    def __init__(self, block_size, num_hiddens, num_heads, dropout):
        super().__init__()
        self.attn = MultiHeadAttention(block_size, num_hiddens, num_heads, dropout)
    def forward(self, x):
        x = x * self.attn(x)
        return x
      
class SimpleTransformer(nn.Module):
    def __init__(self, vocab_size, embedding_size, block_size,
                    num_hiddens, ffn_num_input, ffn_num_hiddens,
                    num_heads, num_layers, dropout, **kwargs):
            super(SimpleTransformer, self).__init__(**kwargs)
      
            # 使用nn.ModuleDict来组织模块
            self.transformer = nn.ModuleDict({
                'wte': nn.Embedding(vocab_size, embedding_size),
                'wpe': nn.Embedding(block_size, embedding_size),
                'drop': nn.Dropout(dropout),
                'blocks': nn.ModuleList([Block(block_size, num_hiddens, num_heads, dropout) for _ in range(num_layers)]),
                'mlp_in': MLP(embedding_size, dropout),
                'ln_f': LayerNorm(embedding_size, bias=False),
                'mlp_out': MLP(embedding_size, dropout)
            })
                            
            # self.lm_head = nn.Linear(num_hiddens, vocab_size)
            # self.embedding.weight = self.lm_head.weight
            # # init all weights
            # self.apply(self._init_weights)
            # # apply special scaled init to the residual projections, per GPT-2 paper
            # for pn, p in self.named_parameters():
            #     if pn.endswith('c_proj.weight'):
            #         torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * num_layers))
    # def _init_weights(self, module):
    #     if isinstance(module, nn.Linear):
    #         torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
    #         if module.bias is not None:
    #             torch.nn.init.zeros_(module.bias)
    #     elif isinstance(module, nn.Embedding):
    #         torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
      
    # def forward(self, idx, targets=None):
    #     device = idx.device
    #     b, t = idx.size()
    #     pos = torch.arange(0, t, dtype=torch.long, device=device)  # shape (t)
  
    #     # forward the GPT model itself
    #     tok_emb = self.transformer['wte'](idx)  # token embeddings of shape (b, t, n_embd)
    #     pos_emb = self.transformer['wpe'](pos)  # position embeddings of shape (t, n_embd)
    #     x0 = self.transformer['drop'](tok_emb + pos_emb)
    #     x = x0
  
    #     # 使用blocks中的每个Block
    #     for block in self.transformer['blocks']:
    #         x = block(x)
  
    #     y = self.transformer['mlp_in'](x)
    #     y = self.transformer['ln_f'](y)
    #     y = self.transformer['mlp_out'](y)
  
    #     return torch.cat((x, y), dim=1)
  
    def forward(self, idx, targets=None):
        device = idx.device
        b, t = idx.size()
        pos = torch.arange(0, t, dtype=torch.long, device=device)  # shape (t)
  
        # forward the GPT model itself
        tok_emb = self.transformer['wte'](idx)  # token embeddings of shape (b, t, n_embd)
        pos_emb = self.transformer['wpe'](pos)  # position embeddings of shape (t, n_embd)
        x = self.transformer['drop'](tok_emb + pos_emb)
  
        # 并行执行blocks
        x_futures = [torch.jit.fork(block, x) for block in self.transformer['blocks']]
  
        # 并行执行MLP
        y = self.transformer['mlp_in'](x)
        y = self.transformer['ln_f'](y)
        y_future = torch.jit.fork(self.transformer['mlp_out'], y)
  
        # 等待blocks执行完成并聚合结果
        for future in x_futures:
            x = x + torch.jit.wait(future)
  
        # 等待MLP执行完成
        y = torch.jit.wait(y_future)
  
        return torch.cat((x, y), dim=1)
        # if targets is not None:
        #     # if we are given some desired targets also calculate the loss
        #     logits = self.lm_head(x)
        #     loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)
        # else:
        #     # inference-time mini-optimization: only forward the lm_head on the very last position
        #     logits = self.lm_head(x[:, [-1], :]) # note: using list [-1] to preserve the time dim
        #     loss = None
        # return logits, loss

# 假设vocab_size是您的词汇表大小
vocab_size = 250002
\#num_hiddens 应该是 num_heads 的整数倍
num_hiddens, num_layers, dropout = 2048, 8, 0.0
num_heads = 8
device = "cuda"  # 获取设备
block_size = 128
ffn_num_input, ffn_num_hiddens, num_heads = num_hiddens, num_hiddens, num_heads
key_size  = num_hiddens
norm_shape = [num_hiddens]
model = SimpleTransformer(
    vocab_size, key_size, block_size, num_hiddens,
    ffn_num_input, ffn_num_hiddens, num_heads,
    num_layers, dropout).to(device)
model
SimpleTransformer(
  (transformer): ModuleDict(
    (wte): Embedding(250002, 2048)
    (wpe): Embedding(128, 2048)
    (drop): Dropout(p=0.0, inplace=False)
    (blocks): ModuleList(
      (0-7): 8 x Block(
        (attn): MultiHeadAttention(
          (c_attn): Linear(in_features=2048, out_features=4096, bias=False)
          (attn_dropout): Dropout(p=0.0, inplace=False)
        )
      )
    )
    (mlp_in): MLP(
      (c_fc): Linear(in_features=2048, out_features=8192, bias=False)
      (gelu): GELU(approximate='none')
      (c_proj): Linear(in_features=8192, out_features=2048, bias=False)
      (dropout): Dropout(p=0.0, inplace=False)
    )
    (ln_f): LayerNorm()
    (mlp_out): MLP(
      (c_fc): Linear(in_features=2048, out_features=8192, bias=False)
      (gelu): GELU(approximate='none')
      (c_proj): Linear(in_features=8192, out_features=2048, bias=False)
      (dropout): Dropout(p=0.0, inplace=False)
    )
  )
)
```

## 人工智能学习班

```JavaScript
1、盐城数据赛事学习
详细教程及学习者后续放出，欢迎进群学习
2、共读《人工智能（第3版）》
开源内容：https://github.com/siyuxin/AI-3rd-edition-notes
3、Leetcode：面试篇（上）
开源内容：https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch04/index.md
电子网站：https://datawhalechina.github.io/leetcode-notes/#/ch04/index.md
4、深度强化学习基础
开源内容：
https://github.com/datawhalechina/joyrl-book
https://github.com/datawhalechina/joyrl/tree/offline
https://datawhalechina.github.io/easy-rl/
5、吃瓜教程——西瓜书+南瓜书
开源内容：https://linklearner.com/learn/detail/10
B 站视频教程：https://www.bilibili.com/video/BV1Mh411e7VU
6、大模型理论基础（So-Large-LLM）
开源内容：https://github.com/datawhalechina/so-large-lm/tree/main
7、faster-Git：Git入门教程
开源内容：https://github.com/datawhalechina/faster-git
8、AI+量化：whale-quant
开源内容：https://github.com/datawhalechina/whale-quant
/opt/workspace/researcher/whale-quant
```
