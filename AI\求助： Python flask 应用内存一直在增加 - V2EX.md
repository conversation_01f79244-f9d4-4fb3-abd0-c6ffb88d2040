---
Updated: 2023-09-27T09:01
tags:
  - AI->-Programming
Created: 2023-09-27T08:58
---
2 个版本，都没有解决内存持续增加，由于内存一直增加，最后因为小鸡内存不足而被杀掉。
这是一个加载训练后的模型，通过网络传入预测参数，然后返回预测 json 结果。
## 可能同时发起（并发）几十个请求。
求大佬帮忙看看问题出在哪里，谢谢。
这是 chatgpt 4.0 给的版本
```Plain
from flask import Flask, request, jsonify
import pickle
import os
import psutil
import pandas as pd
app = Flask(__name__)
class SingletonModel:
    _instance = None
    def __new__(cls):
        if cls._instance is None:
            print("Creating Singleton Instance")
            cls._instance = super(SingletonModel, cls).__new__(cls)
            modelName = "xgboost_model-k.pkl"
            with open(modelName, "rb") as pkl_file:
                loaded_data = pickle.load(pkl_file)
            cls._instance.model = loaded_data['model']
            cls._instance.scaler = loaded_data['scaler']
            cls._instance.label_encoder = loaded_data['label_encoder']
            cls._instance.feature_names = ['shortAvg','longAvg','volatility','diff']
        return cls._instance

resources = SingletonModel()
model = resources.model
scaler = resources.scaler
label_encoder = resources.label_encoder
@app.route('/predict', methods=['POST'])
def predict():
    global model, scaler, label_encoder
    data = request.json['input']
    df = pd.DataFrame([data], columns=resources.feature_names)
    scaled_data = scaler.transform(df)
    prediction = model.predict(scaled_data)
    label_prediction = label_encoder.inverse_transform(prediction)
    return jsonify([label_prediction[0]])
if __name__ == '__main__':
    app.run(port=6601,debug=True)
```
这是 Claude 给的版本
```Plain
import asyncio
from flask import Flask, request, jsonify
import pickle
import pandas as pd
app = Flask(__name__)
# 模型相关全局变量
model = None
scaler = None
label_encoder = None
async def load_model():
  global model, scaler, label_encoder,feature_names
  if not model:
    with open('xgboost_model-k.pkl', 'rb') as f:
      loaded_data = pickle.load(f)
      model = loaded_data['model']
      scaler = loaded_data['scaler']
      label_encoder = loaded_data['label_encoder']
      feature_names = ['shortAvg','longAvg','volatility','diff']
async def predict(data):
  await load_model()
  df = pd.DataFrame([data], columns=feature_names)
  scaled_data = scaler.transform(df)
  prediction = model.predict(scaled_data)
  label_prediction = label_encoder.inverse_transform(prediction)
  return label_prediction[0]

@app.route('/predict', methods=['POST'])
async def predict_handler():
  data = request.json['input']
  result = await asyncio.gather(predict(data))
  return jsonify(result)
if __name__ == '__main__':
    app.run(port=6601,debug=False)
```
第 1 条附言 · 21 小时 8 分钟前
使用常规uWSGI也无法解决内存持续增加问题，但是，
## 配置一个参数可以完美的解决。
## -max-worker-lifetime 参数，它允许你设置 worker 进程的最大生命周期（以秒为单位）, 到达这个时间限制后，worker 进程将被优雅地重启。
## -reload-on-rss 如果一个 worker 使用超过限定 的内存，它将被重启。
![[**********-1041142x.png]]