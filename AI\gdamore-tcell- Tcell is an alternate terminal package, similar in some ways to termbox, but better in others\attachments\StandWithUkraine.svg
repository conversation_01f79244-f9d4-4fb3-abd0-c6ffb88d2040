<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="155" height="20" role="img" aria-label="#StandWithUkraine: 🇺🇦"><title>#StandWithUkraine: 🇺🇦</title><linearGradient id="s" x2="0" y2="100%"><stop offset="0" stop-color="#bbb" stop-opacity=".1"/><stop offset="1" stop-opacity=".1"/></linearGradient><clipPath id="r"><rect width="155" height="20" rx="3" fill="#fff" /></clipPath><g clip-path="url(#r)"><rect width="119" height="20" fill="#555" /><rect x="119" width="50" height="10" fill="#005eb8"/><rect x="119" y="10" width="50" height="10" fill="#ffd101"/><rect width="155" height="20" fill="url(#s)"/></g><g fill="#fff" text-anchor="middle" font-family="Verdana,Geneva,DejaVu Sans,sans-serif" text-rendering="geometricPrecision" font-size="110"><text aria-hidden="true" x="605" y="150" fill="#010101" fill-opacity=".3" transform="scale(.1)" textLength="1090">#StandWithUkraine</text><text x="605" y="140" transform="scale(.1)" fill="#fff" textLength="1090">#StandWithUkraine</text></g></svg>
