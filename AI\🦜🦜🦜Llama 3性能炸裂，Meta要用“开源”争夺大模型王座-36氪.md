---
DocFlag:
  - Reference
  - Tested
Updated: 2024-04-21T14:38
tags:
  - AI->-Model
URL: https://m.36kr.com/p/2671935169809665
Created: 2024-04-19T03:36
---
Download location
```Python
https://huggingface.co/meta-llama/Meta-Llama-3-8B-Instruct
https://huggingface.co/meta-llama/Meta-Llama-Guard-2-8B
\#clone
<NAME_EMAIL>:meta-llama/Meta-Llama-3-8B-Instruct
\#refresh
git remote set-<NAME_EMAIL>:<repo_path>
git pull
/opt/workspace/researcher/labTest/LLama3Test.py
/opt/workspace/llama/finetuning/fastLLMCheck.ipynb
```
```Python
LlamaForCausalLM(
  (model): LlamaModel(
    (embed_tokens): Embedding(128256, 4096)
    (layers): ModuleList(
      (0-31): 32 x LlamaDecoderLayer(
        (self_attn): LlamaAttention(
          (q_proj): Linear(in_features=4096, out_features=4096, bias=False)
          (k_proj): Linear(in_features=4096, out_features=1024, bias=False)
          (v_proj): Linear(in_features=4096, out_features=1024, bias=False)
          (o_proj): Linear(in_features=4096, out_features=4096, bias=False)
          (rotary_emb): LlamaRotaryEmbedding()
        )
        (mlp): LlamaMLP(
          (gate_proj): Linear(in_features=4096, out_features=14336, bias=False)
          (up_proj): Linear(in_features=4096, out_features=14336, bias=False)
          (down_proj): Linear(in_features=14336, out_features=4096, bias=False)
          (act_fn): SiLU()
        )
        (input_layernorm): LlamaRMSNorm()
        (post_attention_layernorm): LlamaRMSNorm()
      )
    )
    (norm): LlamaRMSNorm()
  )
  (lm_head): Linear(in_features=4096, out_features=128256, bias=False)
)
```