---
DocFlag:
  - Tested
  - Testing
Updated: 2024-03-18T11:04
tags:
  - AI->-Fine-Tuning
  - AI->-Model
  - AI->-llama
Created: 2023-07-19T23:23
---
[![](https://opengraph.githubassets.com/38395ba1bab5f9b02e6560045860e2d640c9d33e3d68abeb10f1ac73db7b12d9/facebookresearch/llama)](https://opengraph.githubassets.com/38395ba1bab5f9b02e6560045860e2d640c9d33e3d68abeb10f1ac73db7b12d9/facebookresearch/llama)
---
![[llama]]
# Llama 2
We are unlocking the power of large language models. Our latest version of Llama is now accessible to individuals, creators, researchers and businesses of all sizes so that they can experiment, innovate and scale their ideas responsibly.
This release includes model weights and starting code for pretrained and fine-tuned Llama language models — ranging from 7B to 70B parameters.
This repository is intended as a minimal example to load [Llama 2](https://ai.meta.com/research/publications/llama-2-open-foundation-and-fine-tuned-chat-models/) models and run inference. For more detailed examples leveraging HuggingFace, see [llama-recipes](https://github.com/facebookresearch/llama-recipes/).
## Download
![[26a0.png]]
**7/18: We're aware of people encountering a number of download issues today. Anyone still encountering issues should remove all local files, re-clone the repository, and** [**request a new download link**](https://ai.meta.com/resources/models-and-libraries/llama-downloads/)**. It's critical to do all of these in case you have local corrupt files. When you receive the email, copy** _**only**_ **the link text - it should begin with** [**https://download.llamameta.net**](https://download.llamameta.net/) **and not with** [**https://l.facebook.com**](https://l.facebook.com/)**, which will give errors.**
In order to download the model weights and tokenizer, please visit the [Meta AI website](https://ai.meta.com/resources/models-and-libraries/llama-downloads/) and accept our License.
Once your request is approved, you will receive a signed URL over email. Then run the download.sh script, passing the URL provided when prompted to start the download. Make sure that you copy the URL text itself, **do not use the 'Copy link address' option** when you right click the URL. If the copied URL text starts with: [https://download.llamameta.net](https://download.llamameta.net/), you copied it correctly. If the copied URL text starts with: [https://l.facebook.com](https://l.facebook.com/), you copied it the wrong way.
Pre-requisites: make sure you have `wget` and `md5sum` installed. Then to run the script: `./download.sh`.
Keep in mind that the links expire after 24 hours and a certain amount of downloads. If you start seeing errors such as `403: Forbidden`, you can always re-request a link.
### Access on Hugging Face
We are also providing downloads on [Hugging Face](https://huggingface.co/meta-llama). You must first request a download from the Meta AI website using the same email address as your Hugging Face account. After doing so, you can request access to any of the models on Hugging Face and within 1-2 days your account will be granted access to all versions.
## Setup
In a conda env with PyTorch / CUDA available, clone the repo and run in the top-level directory:
```Plain
pip install -e .
```
## Inference
Different models require different model-parallel (MP) values:
|   |   |
|---|---|
|Model|MP|
|7B|1|
|13B|2|
|70B|8|
All models support sequence length up to 4096 tokens, but we pre-allocate the cache according to `max_seq_len` and `max_batch_size` values. So set those according to your hardware.
### Pretrained Models
These models are not finetuned for chat or Q&A. They should be prompted so that the expected answer is the natural continuation of the prompt.
See `example_text_completion.py` for some examples. To illustrate, see command below to run it with the llama-2-7b model (`nproc_per_node` needs to be set to the `MP` value):
```Plain
torchrun --nproc_per_node 1 example_text_completion.py \
 --ckpt_dir llama-2-7b/ \
 --tokenizer_path tokenizer.model \
 --max_seq_len 128 --max_batch_size 4
```
### Fine-tuned Chat Models
The fine-tuned models were trained for dialogue applications. To get the expected features and performance for them, a specific formatting defined in [`chat_completion`](https://github.com/facebookresearch/llama/blob/main/llama/generation.py#L212) needs to be followed, including the `INST` and `<<SYS>>` tags, `BOS` and `EOS` tokens, and the whitespaces and breaklines in between (we recommend calling `strip()` on inputs to avoid double-spaces).
You can also deploy additional classifiers for filtering out inputs and outputs that are deemed unsafe. See the llama-recipes repo for [an example](https://github.com/facebookresearch/llama-recipes/blob/main/inference/inference.py) of how to add a safety checker to the inputs and outputs of your inference code.
Examples using llama-2-7b-chat:
```Plain
torchrun --nproc_per_node 1 example_chat_completion.py \
 --ckpt_dir llama-2-7b-chat/ \
 --tokenizer_path tokenizer.model \
 --max_seq_len 512 --max_batch_size 4
```
Llama 2 is a new technology that carries potential risks with use. Testing conducted to date has not — and could not — cover all scenarios. In order to help developers address these risks, we have created the [Responsible Use Guide](https://github.com/facebookresearch/llama/blob/main/Responsible-Use-Guide.pdf). More details can be found in our research paper as well.
## Issues
Please report any software “bug,” or other problems with the models through one of the following means:
- Reporting issues with the model: [github.com/facebookresearch/llama](http://github.com/facebookresearch/llama)
- Reporting risky content generated by the model: [developers.facebook.com/llama_output_feedback](http://developers.facebook.com/llama_output_feedback)
- Reporting bugs and security concerns: [facebook.com/whitehat/info](http://facebook.com/whitehat/info)
## Model Card
See [MODEL_CARD.md](https://github.com/facebookresearch/llama/blob/main/MODEL_CARD.md).
## License
Our model and weights are licensed for both researchers and commercial entities, upholding the principles of openness. Our mission is to empower individuals, and industry through this opportunity, while fostering an environment of discovery and ethical AI advancements.
See the [LICENSE](https://github.com/facebookresearch/llama/blob/main/LICENSE) file, as well as our accompanying [Acceptable Use Policy](https://github.com/facebookresearch/llama/blob/main/USE_POLICY.md)
## References
1. [Research Paper](https://ai.meta.com/research/publications/llama-2-open-foundation-and-fine-tuned-chat-models/)
2. [Llama 2 technical overview](https://ai.meta.com/resources/models-and-libraries/llama)
3. [Open Innovation AI Research Community](https://ai.meta.com/llama/open-innovation-ai-research-community/)
## Original LLaMA
The repo for the original llama release is in the [`llama_v1`](https://github.com/facebookresearch/llama/tree/llama_v1) branch.
  
  
```JavaScript
# LLaMA related resource
https://www.philschmid.de/llama-2
-- 下载link
https://huggingface.co/meta-llama
https://huggingface.co/blog/llama2
# get HF hub API Key
https://huggingface.co/docs/api-inference/quicktour\#get-your-api-token
langchain
*************************************
\#find model and usage
https://huggingface.co/tasks
\#autotrain
https://huggingface.co/docs/autotrain/getting_started
\#To use .env  loadenv file
pip install python-dotenv
#直接下载HF格式权重文件
https://huggingface.co/meta-llama/Llama-2-7b-chat-hf/tree/main
#转换成HF格式
python src/transformers/models/llama/convert_llama_weights_to_hf.py \
    --input_dir /path/to/downloaded/llama/weights --model_size 7B --output_dir /output/path
#转换后可以加载
from transformers import LlamaForCausalLM, LlamaTokenizer
tokenizer = LlamaTokenizer.from_pretrained("/output/path")
model = LlamaForCausalLM.from_pretrained("/output/path")

\#Inference
pip install transformers
huggingface-cli login
from transformers import AutoTokenizer
import transformers
import torch
model = "meta-llama/Llama-2-7b-chat-hf"
tokenizer = AutoTokenizer.from_pretrained(model)
pipeline = transformers.pipeline(
    "text-generation",
    model=model,
    torch_dtype=torch.float16,
    device_map="auto",
)
sequences = pipeline(
    'I liked "Breaking Bad" and "Band of Brothers". Do you have any recommendations of other shows I might like?\n',
    do_sample=True,
    top_k=10,
    num_return_sequences=1,
    eos_token_id=tokenizer.eos_token_id,
    max_length=200,
)
for seq in sequences:
    print(f"Result: {seq['generated_text']}")
\#Fine-tuning with PEFT
pip install trl
git clone https://github.com/lvwerra/trl
python trl/examples/scripts/sft_trainer.py \
    --model_name meta-llama/Llama-2-7b-hf \
    --dataset_name timdettmers/openassistant-guanaco \
    --load_in_4bit \
    --use_peft \
    --batch_size 4 \
    --gradient_accumulation_steps 2

\#test command
torchrun --nproc_per_node 1 example_chat_completion.py  --ckpt_dir Llama-2-7b-chat/  --tokenizer_path tokenizer.model  --max_seq_len 512 --max_batch_size 4
```
```JavaScript
<< Chat UI >>
https://github.com/huggingface/chat-ui
```
  
```JavaScript
<<Finetune LLAMA-v2 on local machine>>
https://www.youtube.com/watch?v=3fsn19OI_C8
https://github.com/huggingface/autotrain-advanced
convert.py
import pandas as pd
df = pd.read_csv("data.csv")
df = df.filla("")
\#df.loc[0]
\#print(df.head())
text_col = []
for _, row in df.iterrows():
   prompt = "Below is an instruction that describes a task, paired with an input that provides further context , Write a response that appropriately completes the request. \n\n"
	 instruction = str(row["instruction"])
   input_query = str(row["input"])
   response = str(row["output"])
   if len(input_query.strip()) == 0:
		   text = prompt + "### Instruction" + instruction + "\n### Response:\n " + response
   else
		   text = prompt + "### Instruction" + instruction + "\n### input:" + input_query + "\n### Response:\n " + response
	 text_col.append(text)
df.loc[:, "text"] = text_col
print(df.head())
df.to_csv("train.csv", index = False)

\#hugging face autotrain
https://huggingface.co/docs/autotrain/getting_started
Steps:
Install autotrain-advanced using pip:
- pip install autotrain-advanced
Setup (optional, required on google colab):
- autotrain setup --update-torch
Train:
autotrain llm --train --project_name my-llm --model meta-llama/Llama-2-7b-hf --data_path . --use_peft --use_int4 --learning_rate 2e-4 --train_batch_size 12 --num_train_epochs 3 --trainer sft
```
  
```JavaScript
# coding=utf-8
# Copyright 2023 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import os
from dataclasses import dataclass, field
from typing import Optional
import torch
from datasets import load_dataset
from peft import LoraConfig
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
    HfArgumentParser,
    AutoTokenizer,
    TrainingArguments,
)
from trl import SFTTrainer
# This example fine-tunes Llama v2 model on Guanace dataset
# using QLoRA. At the end of the script we perform merging the weights
# Use it by correctly passing --model_name argument when running the
# script. 
#
# Versions used:
# accelerate == 0.21.0
# peft == 0.4.0
# bitsandbytes == 0.40.2
# transformers == 4.31.0
# trl == 0.4.7
# For models that have `config.pretraining_tp > 1` install:
# pip install git+https://github.com/huggingface/transformers.git
@dataclass
class ScriptArguments:
    """
    These arguments vary depending on how many GPUs you have, what their capacity and features are, and what size model you want to train.
    """
    local_rank: Optional[int] = field(default=-1, metadata={"help": "Used for multi-gpu"})
    per_device_train_batch_size: Optional[int] = field(default=4)
    per_device_eval_batch_size: Optional[int] = field(default=1)
    gradient_accumulation_steps: Optional[int] = field(default=4)
    learning_rate: Optional[float] = field(default=2e-4)
    max_grad_norm: Optional[float] = field(default=0.3)
    weight_decay: Optional[int] = field(default=0.001)
    lora_alpha: Optional[int] = field(default=16)
    lora_dropout: Optional[float] = field(default=0.1)
    lora_r: Optional[int] = field(default=64)
    max_seq_length: Optional[int] = field(default=512)
    model_name: Optional[str] = field(
        default="meta-llama/Llama-2-7b-hf",
        metadata={
            "help": "The model that you want to train from the Hugging Face hub. E.g. gpt2, gpt2-xl, bert, etc."
        }
    )
    dataset_name: Optional[str] = field(
        default="timdettmers/openassistant-guanaco",
        metadata={"help": "The preference dataset to use."},
    )
    use_4bit: Optional[bool] = field(
        default=True,
        metadata={"help": "Activate 4bit precision base model loading"},
    )
    use_nested_quant: Optional[bool] = field(
        default=False,
        metadata={"help": "Activate nested quantization for 4bit base models"},
    )
    bnb_4bit_compute_dtype: Optional[str] = field(
        default="float16",
        metadata={"help": "Compute dtype for 4bit base models"},
    )
    bnb_4bit_quant_type: Optional[str] = field(
        default="nf4",
        metadata={"help": "Quantization type fp4 or nf4"},
    )
    num_train_epochs: Optional[int] = field(
        default=1,
        metadata={"help": "The number of training epochs for the reward model."},
    )
    fp16: Optional[bool] = field(
        default=False,
        metadata={"help": "Enables fp16 training."},
    )
    bf16: Optional[bool] = field(
        default=False,
        metadata={"help": "Enables bf16 training."},
    )
    packing: Optional[bool] = field(
        default=False,
        metadata={"help": "Use packing dataset creating."},
    )
    gradient_checkpointing: Optional[bool] = field(
        default=True,
        metadata={"help": "Enables gradient checkpointing."},
    )
    optim: Optional[str] = field(
        default="paged_adamw_32bit",
        metadata={"help": "The optimizer to use."},
    )
    lr_scheduler_type: str = field(
        default="constant",
        metadata={"help": "Learning rate schedule. Constant a bit better than cosine, and has advantage for analysis"},
    )
    max_steps: int = field(default=10000, metadata={"help": "How many optimizer update steps to take"})
    warmup_ratio: float = field(default=0.03, metadata={"help": "Fraction of steps to do a warmup for"})
    group_by_length: bool = field(
        default=True,
        metadata={
            "help": "Group sequences into batches with same length. Saves memory and speeds up training considerably."
        },
    )
    save_steps: int = field(default=10, metadata={"help": "Save checkpoint every X updates steps."})
    logging_steps: int = field(default=10, metadata={"help": "Log every X updates steps."})
    merge_and_push: Optional[bool] = field(
        default=False,
        metadata={"help": "Merge and push weights after training"},
    )
    output_dir: str = field(
        default="./results",
        metadata={"help": "The output directory where the model predictions and checkpoints will be written."},
    )

parser = HfArgumentParser(ScriptArguments)
script_args = parser.parse_args_into_dataclasses()[0]

def create_and_prepare_model(args):
    compute_dtype = getattr(torch, args.bnb_4bit_compute_dtype)
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=args.use_4bit,
        bnb_4bit_quant_type=args.bnb_4bit_quant_type,
        bnb_4bit_compute_dtype=compute_dtype,
        bnb_4bit_use_double_quant=args.use_nested_quant,
    )
    if compute_dtype == torch.float16 and args.use_4bit:
        major, _ = torch.cuda.get_device_capability()
        if major >= 8:
            print("=" * 80)
            print("Your GPU supports bfloat16, you can accelerate training with the argument --bf16")
            print("=" * 80)
    # Load the entire model on the GPU 0
    # switch to `device_map = "auto"` for multi-GPU
    device_map = {"": 0}
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name, 
        quantization_config=bnb_config, 
        device_map=device_map, 
        use_auth_token=True
    )
    
    # check: https://github.com/huggingface/transformers/pull/24906
    model.config.pretraining_tp = 1 
    peft_config = LoraConfig(
        lora_alpha=script_args.lora_alpha,
        lora_dropout=script_args.lora_dropout,
        r=script_args.lora_r,
        bias="none",
        task_type="CAUSAL_LM", 
    )
    tokenizer = AutoTokenizer.from_pretrained(script_args.model_name, trust_remote_code=True)
    tokenizer.pad_token = tokenizer.eos_token
    return model, peft_config, tokenizer

training_arguments = TrainingArguments(
    output_dir=script_args.output_dir,
    per_device_train_batch_size=script_args.per_device_train_batch_size,
    gradient_accumulation_steps=script_args.gradient_accumulation_steps,
    optim=script_args.optim,
    save_steps=script_args.save_steps,
    logging_steps=script_args.logging_steps,
    learning_rate=script_args.learning_rate,
    fp16=script_args.fp16,
    bf16=script_args.bf16,
    max_grad_norm=script_args.max_grad_norm,
    max_steps=script_args.max_steps,
    warmup_ratio=script_args.warmup_ratio,
    group_by_length=script_args.group_by_length,
    lr_scheduler_type=script_args.lr_scheduler_type,
)
model, peft_config, tokenizer = create_and_prepare_model(script_args)
model.config.use_cache = False
dataset = load_dataset(script_args.dataset_name, split="train")
# Fix weird overflow issue with fp16 training
tokenizer.padding_side = "right"
trainer = SFTTrainer(
    model=model,
    train_dataset=dataset,
    peft_config=peft_config,
    dataset_text_field="text",
    max_seq_length=script_args.max_seq_length,
    tokenizer=tokenizer,
    args=training_arguments,
    packing=script_args.packing,
)
trainer.train()
if script_args.merge_and_push:
    output_dir = os.path.join(script_args.output_dir, "final_checkpoints")
    trainer.model.save_pretrained(output_dir)
    # Free memory for merging weights
    del model
    torch.cuda.empty_cache()
    from peft import AutoPeftModelForCausalLM
    model = AutoPeftModelForCausalLM.from_pretrained(output_dir, device_map="auto", torch_dtype=torch.bfloat16)
    model = model.merge_and_unload()
    output_merged_dir = os.path.join(script_args.output_dir, "final_merged_checkpoint")
    model.save_pretrained(output_merged_dir, safe_serialization=True)
```
  
```JavaScript
Chinese Llama
https://huggingface.co/LinkSoul/Chinese-Llama-2-7b
https://github.com/LinkSoul-AI/Chinese-Llama-2-7b
DataSet
https://huggingface.co/datasets/LinkSoul/instruction_merge_set
\#Chinese instruction datasets for LLM
https://github.com/basicv8vc/chinese-instruction-datasets-for-llms
\#All related to Chinese LLM train
https://github.com/HqWu-HITCS/Awesome-Chinese-LLM
from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer
model_path = "LinkSoul/Chinese-Llama-2-7b"
tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
model = AutoModelForCausalLM.from_pretrained(model_path).half().cuda()
streamer = TextStreamer(tokenizer, skip_prompt=True, skip_special_tokens=True)
instruction = """[INST] <<SYS>>\nYou are a helpful, respectful and honest assistant. Always answer as helpfully as possible, while being safe.  Your answers should not include any harmful, unethical, racist, sexist, toxic, dangerous, or illegal content. Please ensure that your responses are socially unbiased and positive in nature.
            If a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct. If you don't know the answer to a question, please don't share false information.\n<</SYS>>\n\n{} [/INST]"""
prompt = instruction.format("用中文回答，When is the best time to visit Beijing, and do you have any suggestions for me?")
generate_ids = model.generate(tokenizer(prompt, return_tensors='pt').input_ids.cuda(), max_new_tokens=4096, streamer=streamer)

\#Train
DATASET="LinkSoul/instruction_merge_set"
DATA_CACHE_PATH="hf_datasets_cache"
MODEL_PATH="/PATH/TO/TRANSFORMERS/VERSION/LLAMA2"
output_dir="./checkpoints_llama2"
torchrun --nnodes=1 --node_rank=0 --nproc_per_node=8 \
    --master_port=25003 \
        train.py \
        --model_name_or_path ${MODEL_PATH} \
        --data_path ${DATASET} \
        --data_cache_path ${DATA_CACHE_PATH} \
        --bf16 True \
        --output_dir ${output_dir} \
        --num_train_epochs 1 \
        --per_device_train_batch_size 4 \
        --per_device_eval_batch_size 4 \
        --gradient_accumulation_steps 1 \
        --evaluation_strategy 'no' \
        --save_strategy 'steps' \
        --save_steps 1200 \
        --save_total_limit 5 \
        --learning_rate 2e-5 \
        --weight_decay 0. \
        --warmup_ratio 0.03 \
        --lr_scheduler_type cosine \
        --logging_steps 1 \
        --fsdp 'full_shard auto_wrap' \
        --fsdp_transformer_layer_cls_to_wrap 'LlamaDecoderLayer' \
        --tf32 True \
        --model_max_length 4096 \
        --gradient_checkpointing True
```
  
  
```JavaScript
<< xTuring >>
https://github.com/stochasticai/xTuring/tree/main/examples

\#bug 1
/opt/workspace/llama/xTuring/src/xturing/trainers/lightning_trainer.py
elif self.optimizer_name == "adam": \#change from adam to Adam
            optimizer = torch.optim.Adam(
                self.pytorch_model.parameters(), lr=self.learning_rate
            )
\#bug2
AttributeError: /home/<USER>/miniconda3/envs/llama/lib/python3.10/site-packages/bitsandbytes/libbitsandbytes_cpu.so: undefined symbol: cget_col_row_stats
cd /home/<USER>/miniconda3/envs/llama/lib/python3.10/site-packages/bitsandbytes/
cp -rp  libbitsandbytes_cpu.so libbitsandbytes_cpu.so.orig
--check version and cope related version to cpu version
cp -rp  libbitsandbytes_cuda117.so libbitsandbytes_cpu.so
Or
Haven't tried maybe work , uninstall and reinstall "pip install -i https://test.pypi.org/simple/ bitsandbytes"

\#To add new model, must change below files
/opt/workspace/llama/xTuring/examples/llama/preparing_your_dataset.py
--------------------------------------------------------------
LLama2Engine
LLama2Int8Engine
LLama2LoraEngine
LLama2LoraInt8Engine
LLama2LoraKbitEngine

/opt/workspace/llama/xTuring/src/xturing/config/finetuning_config.yaml
llama2:
  learning_rate: 5e-5
  weight_decay: 0.01
  num_train_epochs: 3
  optimizer_name: adam
/opt/workspace/llama/xTuring/src/xturing/config/generation_config.yaml
llama2:
  penalty_alpha: 0.6
  top_k: 4
  max_new_tokens: 256
  do_sample: false
/opt/workspace/llama/xTuring/src/xturing/engines/__init__.py
from xturing.engines.llama2_engine import (
    LLama2Engine,
    LLama2Int8Engine,
    LLama2LoraEngine,
    LLama2LoraInt8Engine,
    LLama2LoraKbitEngine,    
)
BaseEngine.add_to_registry(LLama2Engine.config_name, LLama2Engine)
BaseEngine.add_to_registry(LLama2Int8Engine.config_name, LLama2Int8Engine)
BaseEngine.add_to_registry(LLama2LoraEngine.config_name, LLama2LoraEngine)
BaseEngine.add_to_registry(LLama2LoraInt8Engine.config_name, LLama2LoraInt8Engine)
BaseEngine.add_to_registry(LLama2LoraKbitEngine.config_name, LLama2LoraKbitEngine)

/opt/workspace/llama/xTuring/src/xturing/engines/llama2_engine.py
\#define engine class
/opt/workspace/llama/xTuring/src/xturing/models/__init__.py
from xturing.models.llama2 import (
    Llama2,
    Llama2Int8,
    Llama2Lora,
    Llama2LoraInt8,
    Llama2LoraKbit,
)
BaseModel.add_to_registry(Llama2.config_name, Llama2)
BaseModel.add_to_registry(Llama2Int8.config_name, Llama2Int8)
BaseModel.add_to_registry(Llama2Lora.config_name, Llama2Lora)
BaseModel.add_to_registry(Llama2LoraInt8.config_name, Llama2LoraInt8)
BaseModel.add_to_registry(Llama2LoraKbit.config_name, Llama2LoraKbit)
/opt/workspace/llama/xTuring/src/xturing/models/llama2.py
from xturing.engines.llama2_engine import (
    LLama2Engine,
    LLama2Int8Engine,
    LLama2LoraEngine,
    LLama2LoraInt8Engine,
    LLama2LoraKbitEngine,
)

#_------------------------------------------------------------
# do thing
from xturing.models.llama2 import Llama2Int8
from xturing.datasets import InstructionDataset
from pytorch_lightning.loggers import WandbLogger
from xturing.models import BaseModel
from xturing.ui.playground import Playground

# Initialize the model
# model = BaseModel.create("llama_lora")
model = Llama2Int8(weights_path="/opt/aibase/Llama-2-7b-chat-hf")
instruction_dataset = InstructionDataset("/opt/workspace/llama/xTuring/examples/llama/alpaca_data")
# Initializes WandB integration 
wandb_logger = WandbLogger()
model.finetune(dataset=instruction_dataset, logger=wandb_logger)
# model.finetune(dataset=instruction_dataset)
model.save("llama_lora_finetuned")
# del model
# gc.collect()
# model = BaseModel.load("./llama_weights")

# Playground().launch() ## launches localhost UI
# # Perform inference
# output = model.generate(texts=["Why LLM models are becoming so important?"])
# print("Generated output by the model: {}".format(output))
# xturing chat -m "<path-to-model-folder>"
```
  
```JavaScript
from transformers import AutoTokenizer
from datasets import load_dataset
from transformers import AutoModelForCausalLM
from transformers import Trainer, TrainingArguments
import transformers
transformers.set_seed(42)
import wandb
wandb.init(
       # set the wandb project where this run will be logged
       project="FinBot",
    )
model_checkpoint = "roneneldan/TinyStories-33M"
ds = load_dataset('MohamedRashad/characters_backstories')
# Let's take a look at one example
ds["train"][400]
# As this dataset has no validation split, we will create one
ds = ds["train"].train_test_split(test_size=0.2, seed=42)
# We'll create a tokenizer from model checkpoint
tokenizer = AutoTokenizer.from_pretrained(model_checkpoint, use_fast=False)
# We'll need padding to have same length sequences in a batch
tokenizer.pad_token = tokenizer.eos_token
# Define a tokenization function that first concatenates text and target
def tokenize_function(example):
    merged = example["text"] + " " + example["target"]
    batch = tokenizer(merged, padding='max_length', truncation=True, max_length=128)
    batch["labels"] = batch["input_ids"].copy()
    return batch
# Apply it on our dataset, and remove the text columns
tokenized_datasets = ds.map(tokenize_function, remove_columns=["text", "target"])
# Let's check out one prepared example
print(tokenizer.decode(tokenized_datasets["train"][900]['input_ids']))
# We will train a causal (autoregressive) language model from a pretrained checkpoint
model = AutoModelForCausalLM.from_pretrained(model_checkpoint);
# Start a new wandb run
run = wandb.init(project='dlai_lm_tuning', job_type="training", anonymous="allow")
# Define training arguments
model_name = model_checkpoint.split("/")[-1]
training_args = TrainingArguments(
    f"{model_name}-finetuned-characters-backstories",
    report_to="wandb", # we need one line to track experiments in wandb
    num_train_epochs=1,
    logging_steps=1,
    evaluation_strategy = "epoch",
    learning_rate=1e-4,
    weight_decay=0.01,
    no_cuda=True, # force cpu use, will be renamed `use_cpu`
)
# We'll use HF Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_datasets["train"],
    eval_dataset=tokenized_datasets["test"],
)
# Let's train!
trainer.train()
transformers.logging.set_verbosity_error() # suppress tokenizer warnings
prefix = "Generate Backstory based on following information Character Name: "
prompts = [
    "Frogger Character Race: Aarakocra Character Class: Ranger Output: ",
    "Smarty Character Race: Aasimar Character Class: Cleric Output: ",
    "Volcano Character Race: Android Character Class: Paladin Output: ",
]
table = wandb.Table(columns=["prompt", "generation"])
for prompt in prompts:
    input_ids = tokenizer.encode(prefix + prompt, return_tensors="pt")
    output = model.generate(input_ids, do_sample=True, max_new_tokens=50, top_p=0.3)
    output_text = tokenizer.decode(output[0], skip_special_tokens=True)
    table.add_data(prefix + prompt, output_text)
    
wandb.log({'tiny_generations': table})
wandb.finish()
```
  
  
```JavaScript
MLC
https://hamel.dev/notes/llm/inference/03_inference.html\#mlc
python3 -m mlc_llm.build \
--hf-path meta-llama/Llama-2-7b-chat-hf \
--target cuda --quantization q4f16_1
from mlc_chat import ChatModule, ChatConfig
cfg = ChatConfig(max_gen_len=200)
cm = ChatModule(model="Llama-2-7b-chat-hf-q4f16_1", chat_config=cfg)
output = cm.generate(prompt=prompt)
```
Made One End-2-End Test
1. Generate training/test data
2. Training
3. Test
4. Merge checkpoint and basemodel to one model files
5. verification
    
    gpt_oracle_trainer.html
    
## LLAMA无法中断的问题
```JavaScript
/opt/workspace/app/LLMfastCheck.ipynb
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer
model_path = "/opt/workspace/aibase/CodeLlama-7B-Python-fp16"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(model_path,trust_remote_code=True).half().cuda()
streamer = TextStreamer(tokenizer, skip_prompt=True, skip_special_tokens=True)
import transformers
class _SentinelTokenStoppingCriteria(transformers.StoppingCriteria):
    def __init__(self, sentinel_token_ids: list, starting_idx: int):
        transformers.StoppingCriteria.__init__(self)
        self.sentinel_token_ids = sentinel_token_ids
        self.starting_idx = starting_idx
        self.shortest = min([x.shape[-1] for x in sentinel_token_ids])
    def __call__(self, input_ids: torch.LongTensor, _scores: torch.FloatTensor) -> bool:
        for sample in input_ids:
            trimmed_sample = sample[self.starting_idx:]
            trimmed_len = trimmed_sample.shape[-1]
            if trimmed_len < self.shortest:
                continue
            for sentinel in self.sentinel_token_ids:
                sentinel_len = sentinel.shape[-1]
                if trimmed_len < sentinel_len:
                    continue
                window = trimmed_sample[-sentinel_len:]
                if torch.all(torch.eq(sentinel, window)):
                    return True
        return False
stop_words = ["</s>",  "\n###", "\n### Human:", "\nComment: @AkshayKumar"]
sentinel_token_ids = []
for string in stop_words:
    if string.startswith("\n"):
        sentinel_token_ids.append(
            tokenizer.encode(
                string, return_tensors="pt", add_special_tokens=False
            )[:, 1:].cuda()
        )
    else:
        sentinel_token_ids.append(
            tokenizer.encode(
                string, return_tensors="pt", add_special_tokens=False
            ).cuda()
        )

stopping_criteria_list = transformers.StoppingCriteriaList()

prompt = "give me code for sorting with python"
# generate_ids = model.generate(tokenizer(prompt, return_tensors='pt').input_ids.cuda(), max_new_tokens=4096, streamer=streamer)
# generate_ids = model.generate(tokenizer(prompt, return_tensors='pt').input_ids.cuda(), max_new_tokens=16000)
input_ids = tokenizer(prompt, return_tensors='pt').input_ids.cuda()
stopping_criteria_list.append(
            _SentinelTokenStoppingCriteria(
                sentinel_token_ids=sentinel_token_ids, starting_idx=len(input_ids[0])
            )
        )
generate_ids = model.generate(input_ids, max_new_tokens=16000, streamer=streamer, stopping_criteria=stopping_criteria_list)
response = tokenizer.decode(generate_ids[0])
print(f"**Model Response:**\n```diff\n{response}\n```")
```