---
Updated: 2024-02-02T12:04
tags:
  - AI->-CloudEnv
  - AI->-DevPlatform
  - AI->-Programming
URL: https://www.zhihu.com/question/641968681?utm_id=0
Created: 2024-02-01T23:34
---
![[v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg]]
没想到这么多同学关注。
作为参与者之一，简要说一下我个人的想法
## 1. [学件](https://www.zhihu.com/search?q=%E5%AD%A6%E4%BB%B6&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3382016881%7D)/[北冥坞](https://www.zhihu.com/search?q=%E5%8C%97%E5%86%A5%E5%9D%9E&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3382016881%7D)能做什么
学件的目标是改变我们使用机器学习的方式。
当我们手上有一批数据 data 需要进行预测，传统建立机器学习模型的方法是在data上训练，训练就要涉及到算法、模型结构、参数等的选择和调试。
学件试图让这个过程更加简单：
1）用下面语句从数据中构造出能够识别任务的信息（但同时又不包含任何的原始数据，以免泄漏数据）：
```Plain
rkme = generate_stat_spec ( type =" table ", X = data )
user_info = BaseUserInfo ( stat_info ={ rkme . type : rkme })
```
2） 根据任务的信息，从北冥坞中搜索相关模型，并下载下来：
```Plain
learnware_ids = client . search_learnware ( user_info ) [" multiple "][" learnware_ids "]
learnware_list = client . load_learnware ( learnware_id = learnware_ids , runnable_option =" docker ")
```
3）把搜索到的模型聚合成针对data的模型（这里用最简单的方法），就可以用来预测了：
```Plain
y_pred = AveragingReuser ( learnware_list , mode =" vote_by_label ") . predict ( data )
```
更详细的信息请见题主给出的论文和系统链接。
## **2. 北冥坞跟 Hugging Face 有什么区别**
HuggingFace公司从2020年开始做的 [huggingface.co](https://link.zhihu.com/?target=http%3A//huggingface.co/) 这个网站，是一个模型/算法/数据仓库。得益于大模型社区的发展，[huggingface.cn](https://link.zhihu.com/?target=http%3A//huggingface.cn/) 网站上存放了数十万的模型，提供文字搜索和社交功能。
北冥坞是周志华老师2016年构想的学件概念的一个开源实现，面向科研，为学件技术的探索提供一个完整的系统

> Zhi-Hua Zhou. Learnware
> 
> _Frontiers_
**在使用上的不同：**
- huggingface的用户需要关注网站上有哪些模型/算法/数据，可自行下载使用。
- 北冥坞的用户仅需要关心自己手上的学习任务，系统会自动寻找相关的模型，（未来会）自动用最适合的方式组合这些模型，得到针对用户任务的最佳模型
北冥坞的用户不需要关注网站上有哪些模型，都可以尝试去用来解决手上的任务。如果解决得好，那就有不错的收益；如果北冥坞上的模型不够，解决得不好，也不会耗费多少时间和资源。
可见北冥坞跟huggingface是完全不同的系统，两者仅仅是在“有很多模型”方面相似。
## 3. 学件/北冥坞是否是一个成熟的系统
当然不是，学件的构想中还有很多问题有待解决，其中有不少非常有挑战，例如：
1）如何描述学习任务

> Model Reuse With Reduced Kernel Mean Embedding Specification. TKDE 2023
RKME只是第一种方法，期待更加通用、准确、高效的描述方法。
2）如何查找有用的学件

> Identifying Helpful Learnwares Without Examining the Whole Market. ECAI 2023 Identifying Useful Learnwares for Heterogeneous Label Spaces. ICML 2023
当有大量的学件积累下来后，如何快速找到相关学件，如何找到异构、跨模态的有用模型是关键
3）如何复用学件

> Pre-Trained Model Reusability Evaluation for Small-Data Transfer Learning. NeurIPS 2022
还有许多其他方面，例如如何联合不同空间的学件等

> Handling Learnwares Developed from Heterogeneous Feature Spaces without Auxiliary Data. IJCAI 2023
学件的实现才刚起步，而北冥坞更是一个非常初步的系统
## 3. 关于起名字
学件 / Learnware 这一名字，体现的是机器学习的“软件化”，能够像软件（software）系统，通过拼搭复用来解决问题。
学件中的一个核心是 规约 / Specification ，规约 是软件工程中的标准名词，在软件中是软件库/需求的完整描述，这里指模型的完整描述。
北冥坞 是一个实现学件的系统的名字，开发者对系统自然有起名权。当然如我这样的土鳖，自然也起不出“抱抱脸”这么清新脱俗、精致高雅、余音绕梁的名字，请海涵。
## **4. 关于商业化？**
在北冥坞之前，其实组里已经与几个大厂联合开发了企业内部的学件系统，进行了一定的验证，但同时也发现企业内部的系统对于科研来说较难让更多的研究者和同学参与。
因此北冥坞是完全开源的系统，包括网站前后端都全部开源了，任何人都可以自己部署，仅有唯一目的，就是希望有更多的研究者参与，而**没有任何商业化的目的**。也希望有兴趣的同学能对北冥坞系统和算法做出改进。
还有很多同学讨论生态，确实生态的形成是很难的，看着还有很多不看内容的评论就知道了。不过我也看到了另一种可能，大量模型的产生和使用并不一定是人参与的，也有可能是机器产生的，也许会形成另一种生态。
另外声明一下，没有花一分钱去推广或者买什么热搜