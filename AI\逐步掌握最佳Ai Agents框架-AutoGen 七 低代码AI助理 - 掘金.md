---
Updated: 2024-08-10T22:11
tags:
  - AI->-Tools
  - AI->Automation
Created: 2024-08-10T22:11
---
==从LangChain使用Agent的懵懵懂懂，到AutoGen的"放肆"Agents,这周的学习和写作实在太幸福了。假如您是第一次接触AugoGen,请先要查看==[==AutoGen官方文档==](https://link.juejin.cn/?target=https%3A%2F%2Fmicrosoft.github.io%2Fautogen%2F)==，或翻阅AutoGen系列的前几篇。==
[==逐步掌握最佳Ai Agents框架-AutoGen 一==](https://juejin.cn/post/7300779071389663286)
[==逐步掌握最佳Ai Agents框架-AutoGen 二==](https://juejin.cn/post/7300855272914337801)
[==逐步掌握最佳Ai Agents框架-AutoGen 三 写新闻稿新方式==](https://llm.juejin.cn/post/7301574930869420083)
==[==[==逐步掌握最佳Ai Agents框架-AutoGen 四 多代理群聊实例==](https://juejin.cn/post/7301948815752790054)==]==
[==逐步掌握最佳Ai Agents框架-AutoGen 五 与LangChain手拉手 - 掘金 (juejin.cn)==](https://juejin.cn/post/7302357332913717258)
[==逐步掌握最佳Ai Agents框架-AutoGen 六 语音AI代理 - 掘金 (juejin.cn)==](https://juejin.cn/post/7302712860806807579)
## ==前言==
  ==在上两篇文章中，我们围绕Uniswap专家知识库打造AI助理。今天我们将以零代码的方式来实现，这要归功于Flowise。我们将使用Flowise构建的工作流，来实现一个同样的AI助理应用。==
## ==AI生态==
  ==随着AIGC的火爆，有很多非常优秀的框架、工具和服务，构建起来了良好的AI开发生态。Flowise 也是其中之一， 在介绍它之前，我们先来聊聊我们熟悉AI生态里的工具或服务，欢迎大家在评论区补充，好让我也快点学起来， 用起来。如何将这些优秀的AI生态成员工具接我们的项目，这会是我下一步写作的方向。==   ==上图来自==[==Flowise==](https://link.juejin.cn/?target=https%3A%2F%2Fflowiseai.com%2F)==首页==
[![](https://p9-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/b6dcd67f7a514820a0dbbe58919121d5~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=2230&h=1118&s=839912&e=png&b=5f37af)](https://p9-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/b6dcd67f7a514820a0dbbe58919121d5~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=2230&h=1118&s=839912&e=png&b=5f37af)
- ==LangChain==
  ==`LangChain`====提供python和js两个语言版本，====`Flowise`====基于LangChainJS库，零代码完成LLMs应用非常方便。==
- ==Chroma==
  ==`Chroma`====是====`LangChain`====生态里的向量数据库。将用户查询和资料，使用LLM进行====`embedding`====后，计算cosin向量值，就可以快速完成自然语言相似性处理。Chroma封装了这一流程。==
- ==OpenAI==
  ==最为常用的AI大模型，我们也可以使用其它代替。==
- ==HuggingFace==
 ==&emsp全球最大LLMs 社区, 有着丰富的开源大模型、数据集和类似github page 一样的在线大模型服务。==
## ==Flowise==
  ==Flowise是使用非常广泛的AI零代码工具。不需要任何编码，就可以通过拖拽的方式，轻松实现一个chat bot，并支持一键布署。==
[![](https://p1-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/cc862c276c804bd6aa18dd5ece477591~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=2558&h=1420&s=4920556&e=gif&f=445&b=fcfbfe)](https://p1-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/cc862c276c804bd6aa18dd5ece477591~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=2558&h=1420&s=4920556&e=gif&f=445&b=fcfbfe)
- ==安装Floise==
  ==在==[==FlowiseAI/Flowise: Drag & drop UI to build your customized LLM flow (github.com)==](https://link.juejin.cn/?target=https%3A%2F%2Fgithub.com%2FFlowiseAI%2FFlowise)==中有两种使用方式。在这里我们使用基于node的第一种方案，如果大家对docker比较熟悉，也可以使用第二种。==
1. ==Install Flowise==
    
    ```plain
    npm install -g flowise
    ```
    
2. ==Start Flowise==
    
    ```plain
    npx flowise start
    ```
    
    ==With username & password==
    
    ```plain
    npx flowise start --FLOWISE_USERNAME=user --FLOWISE_PASSWORD=1234
    ```
    
- ==AI助理要使用到的工作流组件==
  ==假定大家已经安装好flowise, 并在相关端口本地成功启动flowise。==
[![](https://p1-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/5c5c02b46b2744ecbea97c6642b3079a~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=299&h=498&s=67335&e=png&b=f6f6f6)](https://p1-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/5c5c02b46b2744ecbea97c6642b3079a~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=299&h=498&s=67335&e=png&b=f6f6f6)
  ==通过左上角的加号，我们添加了OPENAI 大模型模块。==
[![](https://p9-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/1d1b16ba80fa4a08b992d0a227abab57~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=992&h=1010&s=400734&e=png&b=fafafa)](https://p9-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/1d1b16ba80fa4a08b992d0a227abab57~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=992&h=1010&s=400734&e=png&b=fafafa)
  ==PdfFile、Recursive Character Text Spliter、OpenAI Embeddings、和 In-Momory Vector Store 四个模块的引入，分别解决了Uniswap 白皮书(pdf格式)的加载、1000字符一片的切片、切片的embedding计算和向量存储==
[![](https://p6-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/3cb318317c5546bea1a7ca30f9ec71a4~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=432&h=526&s=99019&e=png&b=fbfbfb)](https://p6-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/3cb318317c5546bea1a7ca30f9ec71a4~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=432&h=526&s=99019&e=png&b=fbfbfb)
  ==`LangChain`====的====`Conversational Retrieval QA Chain`====模块的加入，集合OPENAI、向量数据库、数据缓存一起，实现了chat boot。 保存并运行，我们就可以和AI聊天机器人QA对话了。==
[![](https://p9-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/9ba1a27d28824590ac16860b1a30bf8e~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=891&h=1252&s=441714&e=png&b=f6f6f6)](https://p9-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/9ba1a27d28824590ac16860b1a30bf8e~tplv-k3u1fbpfcp-jj-mark:3024:0:0:0:q75.awebp#?w=891&h=1252&s=441714&e=png&b=f6f6f6)
  ==当我们了解了AI知识文档聊天机器人的各个功能模块和LangChain的工作方式后，使用Flowise可以快速搭建工作流，非常惊艳。==
- ==与AutoGen交流==
  ==点击右上角第二个按钮，会出现如上图的操作。我们选择python，打开之前的python note book, 交由AutoGen调用。==
  ==大家可以打开==[==LangChain-Advanced/Integrations/AutoGen/autogen_flowise_ai_agent.ipynb at main · sugarforever/LangChain-Advanced (github.com)==](https://link.juejin.cn/?target=https%3A%2F%2Fgithub.com%2Fsugarforever%2FLangChain-Advanced%2Fblob%2Fmain%2FIntegrations%2FAutoGen%2Fautogen_flowise_ai_agent.ipynb)==来看代码。前几篇文章中讲过的代码我这里就不再累述，这里只讲flowise_ai的增量部分==
1. ==拷贝flowise 提供的代码， 放入ipynb文件。==
    
    ==import requests==
    
    ==API_URL = "http://localhost:4000/api/v1/prediction/433ed37e-9546-4e73-a688-7352b78bf852"==
    
    ==def query(payload):  
    response = requests.post(API_URL, json=payload)  
    return response.json()  
    ==
    
    ==output = query({  
    "question": "Hey, how are you?",  
    })  
    ==
    
  ==上面的代码使用requests请求库，向flowiseAI拖拽生成的AI代理应用发送了请求。并且调用并问了个问题，来测试是否引入成功。这个例子大家需要在本地运行python notebook哦，在远程colab里是运行不了的，注意看API_URL。==
1. ==定义agents with function calls 函数==
    
    ==import requests==
    
    ==API_URL = "http://localhost:4000/api/v1/prediction/433ed37e-9546-4e73-a688-7352b78bf852"==
    
    ==def answer_flowise_uniswap_question(question):  
    response = requests.post(API_URL, json={ "question": question })  
    return response.json()  
    ==
    
2. ==在llm_config和user_proxy中添加配置==
    
    ==llm_config={  
    "request_timeout": 600,  
    "seed": 42,  
    "config_list": config_list,  
    "temperature": 0,  
    "functions": [  
    {  
    "name": "answer_flowise_uniswap_question",  
    "description": "Answer any Uniswap related questions",  
    "parameters": {  
    "type": "object",  
    "properties": {  
    "question": {  
    "type": "string",  
    "description": "The question to ask in relation to Uniswap protocol",  
    }  
    },  
    "required": ["question"],  
    },  
    }  
    ],  
    }  
    ==
    
    ==user_proxy = autogen.UserProxyAgent(  
    name="user_proxy",  
    human_input_mode="NEVER",  
    max_consecutive_auto_reply=10,  
    code_execution_config={"work_dir": "."},  
    llm_config=llm_config,  
    system_message="""Reply TERMINATE if the task has been solved at full satisfaction.  
    Otherwise, reply CONTINUE, or the reason why the task is not solved yet.""",  
    function_map={"answer_flowise_uniswap_question": answer_flowise_uniswap_question}  
    )  
    ==
    
3. ==use_proxy 开始工作==
    
    ==user_proxy.initiate_chat(  
    assistant,  
    message="""  
    I'm writing a blog to introduce the version 3 of Uniswap protocol. Find the answers to the 3 questions below and write an introduction based on them.  
    ==
    
    1. ==What is Uniswap?==
    2. ==What are the main changes in Uniswap version 3?==
    3. ==How to use Uniswap?==
    
    ==Start the work now.  
    """  
    )  
    ==
    
4. ==autogen 返回==
  ==autogen工作流程和==[==逐步掌握最佳Ai Agents框架-AutoGen 五 与LangChain手拉手 - 掘金 (juejin.cn)==](https://juejin.cn/post/7302357332913717258)==文章是一样的，大家可以点开查看， 这里就不粘代码了。==
## ==总结==
  ==本文继上篇之后，使用Flowise 低代码的方式，同样实现了基于Uniswap的专家知识库AI助理项目。有以下收获：==
- ==agents with function calls 这一autogen的语法，是autogen与其它AI工具或服务连接的利器，这一想法再次加固==
- ==Flowise AI工具，非常便捷和快速的帮助我们搭建了产品思路和实现，在AI原型阶段非常有必要==
## ==参考资料==
- [==AutoGen | AutoGen (microsoft.github.io)==](https://link.juejin.cn/?target=https%3A%2F%2Fmicrosoft.github.io%2Fautogen%2F)
- [==AutoGen + Flowise = 零代码平台上的超级AI助理_哔哩哔哩_bilibili==](https://link.juejin.cn/?target=https%3A%2F%2Fwww.bilibili.com%2Fvideo%2FBV1gC4y137kr%2F%3Fspm_id_from%3D333.999.0.0%26vd_source%3D3d50341f547faf8df242a214b04f2d86)
- ==python notebook== [==LangChain-Advanced/Integrations/AutoGen/autogen_flowise_ai_agent.ipynb at main · sugarforever/LangChain-Advanced (github.com)==](https://link.juejin.cn/?target=https%3A%2F%2Fgithub.com%2Fsugarforever%2FLangChain-Advanced%2Fblob%2Fmain%2FIntegrations%2FAutoGen%2Fautogen_flowise_ai_agent.ipynb)