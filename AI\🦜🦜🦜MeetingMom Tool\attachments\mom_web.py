# =========== Copyright 2023 @ SBI Holdings. All Rights Reserved. ===========
# Licensed under the Apache License, Version 2.0 (the “License”);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an “AS IS” BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# =========== Copyright 2023 @ SBI Holdings. All Rights Reserved. ===========
#  Ray Sheng 2021/09/20
# ===========================================================================
import openai
import json
import os
from dotenv import load_dotenv
import requests
import argparse
import io
import speech_recognition as sr
import whisper
import torch
import hashlib
from tqdm import tqdm

from datetime import datetime, timedelta
from queue import Queue
from tempfile import NamedTemporaryFile
from time import sleep
# from sys import platform

# from langchain.text_splitter import CharacterTextSplitter
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain import LLMChain, OpenAI
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate
from dataclasses import dataclass
from typing import List, Dict

from rich import print
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.progress import Progress 

import pyairtable


load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")
airtable_api_key = os.getenv("AIRTABLE_API_KEY")
airtable_base_key = os.getenv("AIRTABLE_BASE_KEY")


airAPI = pyairtable.Api(airtable_api_key)
MeetingMasterTable = airAPI.table(airtable_base_key, "tblBBOqUElDTb1YtS")
MeetingTableInDetail = airAPI.table( airtable_base_key, "tbldTKn8qSn3UzQLu")

@dataclass
class Document:
    page_content: str
    metadata: Dict[str, str]


# ask_function_calling(user_query)

def get_device():
    if torch.cuda.is_available():
        return 'cuda'
    else:
        return 'cpu'


def extract_meetingTopic(content: str):
    llm = ChatOpenAI(temperature=0, model="gpt-3.5-turbo-0613")
    template = """
    You are good for extracting meeting topic from the content below:

    {content}

    Above is the content; please try to summarize content within 5 words as topic and return the topic.
    """

    prompt = PromptTemplate(
        input_variables=["content"],
        template=template,
    )

    chain = LLMChain(llm=llm, prompt=prompt)

    results = chain.run(content=content)

    return results

default_data_points = """{
    "Sentiment": "the content sentiment if it is positive or negative",
    "Translate": "The content translated into English",
}"""

def judege_line(content: str):
    llm = ChatOpenAI(temperature=0, model="gpt-3.5-turbo-0613")
    template = """
    You are an expert who will judge people sentiment while people talking and 
    also help to tranlate content into english if it is not in english.:

    {content}

    Above is the content; please try to extract all data points from the content above 
    and export in a JSON array format:
    {data_points}

    Now please extract details from the content  and export in a JSON array format, 
    return ONLY the JSON array:
    """

    prompt = PromptTemplate(
        input_variables=["content", "data_points"],
        template=template,
    )

    chain = LLMChain(llm=llm, prompt=prompt)

    results = chain.run(content=content, data_points=default_data_points)

    return results


def add_meetingmastertable(memid, memtp, memsum, memdate):
    MeetingMasterTable.create({"MeetingID":memid, "MeetingTopic": memtp, "MeetingSummary":memsum, "MeetingDateTime":memdate})

def add_meetingtableindetail(memid, Line, Sentiment):
    MeetingTableInDetail.create({"MeetingID":memid, "Line": Line , "Sentiment": Sentiment})

def generate_meeting_id(summarized_topic):
    current_time = datetime.now().strftime("%Y%m%d%H%M%S")
    raw_str = summarized_topic + current_time
    hash_object = hashlib.sha256(raw_str.encode())
    return hash_object.hexdigest()

def summarise(data, query, meeting_id):
    
    text_splitter = RecursiveCharacterTextSplitter(
        separators=["\n\n", "\n"], chunk_size=3000, chunk_overlap=200)
    
    # text_splitter = CharacterTextSplitter(separator="\n", chunk_size=3000, chunk_overlap=200, length_function=len)
    text = text_splitter.split_documents(data)
    
    llm = OpenAI(model_name="gpt-3.5-turbo", temperature=.7) # type: ignore
    # template = """
    # {text}
    # You are a meeting clerk and after meeting finish, you will try to summarise the above text in order to create meeting of memo for the meeting topic {query}.
    # Please follow all of the following rules:
    # 1/ Make sure the content is engaging, informative with good data
    # 2/ Make sure the content is not too long, it should be around 300 words
    # 3/ The content should address the {query} topic very well
    # 5/ The content needs to be written in a way that is easy to understand and read
    # 6/ The content needs to give audience actionable advice and insights too
    # 7/ summarize in Japanese
    
    # SUMMARY:
    # """
    template = """
    ------------------------------
    {text}
    ------------------------------
    You are a meeting clerk and after meeting finish, you will try to summarise the above text in order to create meeting of memo.
    Please follow all of the following rules:
    1/ Make sure the content is not too long, it should be less than 100-200 words
    2/ The content needs to be written in a way that is easy to understand and read
    3/ Dont make up information and dont explain any thing except return the summary
    4/ summarize in Japanese
    
    SUMMARY:
    """
    
    prompt_template = PromptTemplate(input_variables=["text", "query"], template=template)
    summarise_chain = LLMChain(llm=llm, prompt=prompt_template, verbose=True)
    
    summaries = []
    
    for chunk in tqdm(enumerate(text)):
        summary = summarise_chain.predict(text=chunk[1].page_content, query=query)        
        add_meetingtableindetail(meeting_id, chunk[1].page_content, chunk[1].metadata['Sentiment'])
        summaries.append(summary)
        
    print("summaries: ", summaries)
     
    return summaries   

def summarise2(data, meeting_id):
    
    text_splitter = RecursiveCharacterTextSplitter(
        separators=["\n\n", "\n"], chunk_size=3000, chunk_overlap=200)
    
    # text_splitter = CharacterTextSplitter(separator="\n", chunk_size=3000, chunk_overlap=200, length_function=len)
    text = text_splitter.split_documents(data)
    
    llm = OpenAI(model_name="text-davinci-003", temperature=0) # type: ignore
    # template = """
    # {text}
    # You are a meeting clerk and after meeting finish, you will try to summarise the above text in order to create meeting of memo for the meeting topic {query}.
    # Please follow all of the following rules:
    # 1/ Make sure the content is engaging, informative with good data
    # 2/ Make sure the content is not too long, it should be around 300 words
    # 3/ The content should address the {query} topic very well
    # 5/ The content needs to be written in a way that is easy to understand and read
    # 6/ The content needs to give audience actionable advice and insights too
    # 7/ summarize in Japanese
    
    # SUMMARY:
    # """
    template = """
    ------------------------------
    {text}
    ------------------------------
    You are a meeting clerk and you will summarise meeting mom based on the above text.
    Please follow all of the following rules:
    1/ Make sure the content is not too long, it should be as short as possible
    2/ The content needs to be written in a way that is easy to understand and read
    3/ Dont make up information and dont explain any thing except return the summary
    4/ summarize in Japanese
    
    SUMMARY:
    """
    
    prompt_template = PromptTemplate(input_variables=["text"], template=template)
    summarise_chain = LLMChain(llm=llm, prompt=prompt_template, verbose=False)
    
    summaries = ''
    
    with Progress() as progress:
        task1 = progress.add_task("[green]Processing...", total=len(text))
        
        for chunk in enumerate(text):
            summaries = summaries + "\n" + chunk[1].page_content
            add_meetingtableindetail(meeting_id, chunk[1].page_content, chunk[1].metadata['Sentiment'])
            progress.update(task1, advance=1)

        
    final_summary = summarise_chain.predict(text=summaries)        

    # print("summaries: ", final_summary)
     
    return final_summary   

def split_text(text, length):
    return "\n".join([text[i:i+length] for i in range(0, len(text), length)])


def transcribe_audio_via_server(temp_file_path: str, server_url: str = "http://************:8123/transcribe/") -> str:
    """
    使用服务器进行音频转录。

    参数:
    - temp_file_path: 临时音频文件的路径
    - server_url: 服务器API的URL

    返回:
    - text: 转录文本
    """
    try:
        with open(temp_file_path, 'rb') as f:
            response = requests.post(server_url, files={"file": f})
        
        if response.status_code == 200:
            result = response.json()
            text = result.get('text', '').strip()
            return text
        else:
            print("Error:", response.status_code, response.text)
            return ""
    except Exception as e:
        print(f"An error occurred: {e}")
        return ""
    
    
console = Console()

if __name__ == "__main__":
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--energy_threshold", default=1000,
                        help="Energy level for mic to detect.", type=int)
    parser.add_argument("--record_timeout", default=60,
                        help="How real time the recording is in seconds.", type=float)
    parser.add_argument("--phrase_timeout", default=5,
                        help="How much empty space between recordings before we "
                             "consider it a new line in the transcription.", type=float)  
    args = parser.parse_args()
    
    # The last time a recording was retreived from the queue.
    phrase_time = datetime.utcnow()
    # Current raw audio bytes.
    last_sample = bytes()
    # Thread safe Queue for passing data from the threaded recording callback.
    data_queue = Queue()
    # We use SpeechRecognizer to record our audio because it has a nice feauture where it can detect when speech ends.
    recorder = sr.Recognizer()
    recorder.energy_threshold = args.energy_threshold
    # Definitely do this, dynamic energy compensation lowers the energy threshold dramtically to a point where the SpeechRecognizer never stops recording.
    recorder.dynamic_energy_threshold = False
    
    # Prevents permanent application hang and crash by using the wrong Microphone
    source = sr.Microphone(sample_rate=16000)
        
    # Load / Download model
    # audio_model = whisper.load_model("G:\ResearchDirection\AI\mom\model\medium.pt", device=get_device())

    record_timeout = args.record_timeout
    phrase_timeout = args.phrase_timeout

    temp_file = NamedTemporaryFile().name
    transcription = ['']
    mom_docs = []
    
    with source:
        recorder.adjust_for_ambient_noise(source,duration=5)

    def record_callback(_, audio:sr.AudioData) -> None:
        """
        Threaded callback function to recieve audio data when recordings finish.
        audio: An AudioData containing the recorded bytes.
        """
        # Grab the raw bytes and push it into the thread safe queue.
        data = audio.get_raw_data()
        data_queue.put(data)

    # Create a background thread that will pass us raw audio bytes.
    # We could do this manually but SpeechRecognizer provides a nice helper.
    recorder.listen_in_background(source, record_callback, phrase_time_limit=record_timeout)

    # Cue the user that we're ready to go.
    print("Model loaded... We are ready to start meeting recording\n")

    # query = console.input("Please input meeting topic so that I can generate meeting ID for record purpose: ")    
    # meeting_topic = extract_meetingTopic(query)
    meeting_topic= "AI Meeting"
    meeting_id = generate_meeting_id(meeting_topic)  
    meeting_table = Table(title=meeting_topic,  show_header=True, header_style="bold magenta", expand=True)
    meeting_table.add_column("Original", justify="left", style="bright_green", width=80, no_wrap=False, overflow="fold") #, overflow="fold"
    meeting_table.add_column("Sentiment", justify="center", style="bright_yellow", no_wrap=False)
    meeting_table.add_column("English", justify="left", style="bright_blue", no_wrap=False)
    console.print(meeting_table)
    
    while True:
        try:
            # with Live(meeting_table, auto_refresh=False, refresh_per_second=4, transient=True, vertical_overflow="visable") as live:
                now = datetime.utcnow()
                # Pull raw recorded audio from the queue.
                if not data_queue.empty():
                    phrase_complete = False
                    # If enough time has passed between recordings, consider the phrase complete.
                    # Clear the current working audio buffer to start over with the new data.
                    if phrase_time and now - phrase_time > timedelta(seconds=phrase_timeout):
                        last_sample = bytes()
                        phrase_complete = True
                    # This is the last time we received new audio data from the queue.
                    phrase_time = now

                    # Concatenate our current audio data with the latest audio data.
                    while not data_queue.empty():
                        data = data_queue.get()
                        last_sample += data

                    # Use AudioData to convert the raw data to wav data.
                    audio_data = sr.AudioData(last_sample, source.SAMPLE_RATE, source.SAMPLE_WIDTH)
                    wav_data = io.BytesIO(audio_data.get_wav_data())

                    # Write wav data to the temporary file as bytes.
                    with open(temp_file, 'w+b') as f:
                        f.write(wav_data.read())

                    # Read the transcription.
                    text = transcribe_audio_via_server(temp_file)
                    # text = result['text'].strip()

                    # Search if there is 'Thanks for watching' in the text, if yes, then skip and loop again
                    text = text.replace("\n", "")
                    if text.find("Thank you for watching") != -1:
                        continue
                    if text.find("Thanks for watching") != -1:
                        continue
                    if text.find("ご視聴ありがとうございました") != -1:
                        continue
                    if text == "":
                        continue
                    
                    # If we detected a pause between recordings, add a new item to our transcripion.
                    # Otherwise edit the existing one.
                    if phrase_complete:
                        transcription.append(text)
                        
                        data= judege_line(text)
                        json_data = json.loads(data)
                        if isinstance(json_data, list):
                            sentimentstr = json_data[0].get("Sentiment")
                            englishstr = json_data[0].get("Translate")
                        else:
                            sentimentstr = json_data.get("Sentiment")
                            englishstr = json_data.get("Translate")
                        
                        text = split_text(text, 40)
                        meeting_table.add_row(text, sentimentstr,englishstr )
                        mom_doc = Document(page_content=text, metadata={'Sentiment':sentimentstr})
                        mom_docs.append(mom_doc)
                        # console.clear()    
                        console.print(meeting_table)
                        # live.update(meeting_table)
                    else:
                        transcription[-1] = text
                        # live.console.print(text)

                    #
                    # Clear the console to reprint the updated transcription.
                    # os.system('cls' if os.name=='nt' else 'clear')
                    # for line in transcription:
                    #     print(line)                    
                    # Flush stdout.
                    # print('', end='', flush=True)

                    
                    # Infinite loops are bad for processors, must sleep.
                    sleep(0.25)
        except KeyboardInterrupt:
            break

    print("\n\nSaving Meeting Mom and Summarization into database :")    
                        
    sumstr = summarise2(mom_docs, meeting_id)
    sum_table = Table(title="Meeting Summary", show_header=True, header_style="red")
    sum_table.add_column("Summary", justify="center", style="bright_green")    
    sumstr=split_text(sumstr, 100)
    sum_table.add_row(sumstr)
    
    add_meetingmastertable(meeting_id, meeting_topic, sumstr, str(datetime.now()))
    console.print(sum_table)
    print("\n\nEnd of Today Meeting!")

