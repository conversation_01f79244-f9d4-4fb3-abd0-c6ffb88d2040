---
DocFlag:
  - Testing
Updated: 2023-04-25T18:35
tags:
  - AI->-LangChain
  - AI->-ToDO
Created: 2023-04-25T18:35
---
最近正在用nltk 对中文网络商品评论进行褒贬情感分类，计算评论的[信息熵](https://www.zhihu.com/search?q=%E4%BF%A1%E6%81%AF%E7%86%B5&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)（entropy）、互信息（point mutual information）和困惑值（perplexity）等（不过这些概念我其实也还理解不深...只是nltk 提供了相应方法）。
我感觉用nltk 处理中文是完全可用的。其重点在于[中文分词](https://www.zhihu.com/search?q=%E4%B8%AD%E6%96%87%E5%88%86%E8%AF%8D&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)和文本表达的形式。
中文和英文主要的不同之处是中文需要分词。因为nltk 的处理粒度一般是词，所以必须要先对文本进行分词然后再用nltk 来处理（不需要用nltk 来做分词，直接用[分词包](https://www.zhihu.com/search?q=%E5%88%86%E8%AF%8D%E5%8C%85&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)就可以了。严重推荐结巴分词，非常好用）。
中文分词之后，文本就是一个由每个词组成的[长数组](https://www.zhihu.com/search?q=%E9%95%BF%E6%95%B0%E7%BB%84&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)：[word1, word2, word3…… wordn]。之后就可以使用nltk 里面的各种方法来处理这个文本了。比如用FreqDist [统计文本](https://www.zhihu.com/search?q=%E7%BB%9F%E8%AE%A1%E6%96%87%E6%9C%AC&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)词频，用bigrams 把文本变成双词组的形式：[(word1, word2), (word2, word3), (word3, word4)……([wordn-1](https://www.zhihu.com/search?q=wordn-1&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D), wordn)]。
再之后就可以用这些来计算文本词语的信息熵、[互信息](https://www.zhihu.com/search?q=%E4%BA%92%E4%BF%A1%E6%81%AF&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)等。
再之后可以用这些来选择机器学习的特征，构建[分类器](https://www.zhihu.com/search?q=%E5%88%86%E7%B1%BB%E5%99%A8&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)，对文本进行分类（商品评论是由多个独立评论组成的[多维数组](https://www.zhihu.com/search?q=%E5%A4%9A%E7%BB%B4%E6%95%B0%E7%BB%84&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)，网上有很多情感分类的实现例子用的就是nltk 中的商品评论[语料库](https://www.zhihu.com/search?q=%E8%AF%AD%E6%96%99%E5%BA%93&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)，不过是英文的。但整个思想是可以一致的）。
另外还有一个困扰很多人的Python 中文编码问题。多次失败后我总结出一些经验。
Python 解决中文编码问题基本可以用以下逻辑：
[utf8](https://www.zhihu.com/search?q=utf8&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)（输入） ——> unicode（处理） ——> （输出）utf8
Python 里面处理的字符都是都是[unicode 编码](https://www.zhihu.com/search?q=unicode%20%E7%BC%96%E7%A0%81&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)，因此解决编码问题的方法是把输入的文本（无论是什么编码）解码为（decode）[unicode编码](https://www.zhihu.com/search?q=unicode%E7%BC%96%E7%A0%81&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D)，然后输出时再编码（encode）成所需编码。
由于处理的一般为txt 文档，所以最简单的方法，是把txt 文档另存为[utf-8](https://www.zhihu.com/search?q=utf-8&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A19975309%7D) 编码，然后使用Python 处理的时候解码为unicode（sometexts.decode('utf8')），输出结果回txt 的时候再编码成utf8（直接用str() 函数就可以了）。
另外这篇文章也有很详细的讲到nltk 的中文应用，很值得参考：
[http://blog.csdn.net/huyoo/article/details/12188573](https://link.zhihu.com/?target=http%3A//m.blog.csdn.net/blog/huyoo/12188573)
![[Notion/AI/如何用 Python 中的 NLTK 对中文进行分析和处理？ - 知乎/attachments/image|image]]