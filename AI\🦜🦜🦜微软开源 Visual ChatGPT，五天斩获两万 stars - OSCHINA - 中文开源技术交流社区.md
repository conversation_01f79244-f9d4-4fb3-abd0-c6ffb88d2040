---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-18T11:13
tags:
  - AI->-Chatbot
  - AI->-LangChain
  - AI->-Model
  - AI->-OpenAI
  - AI->-Programming
  - AI->-ToDO
Created: 2023-03-15T23:51
---
除了大力投资 Open AI ，微软还亲自下场大搞 AI 。5 天前，微软开源了 [Visual ChatGPT](https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fmicrosoft%2Fvisual-chatgpt) ，这个软件可以连接 ChatGPT 和一系列视觉模型，以实现在 ChatGPT 的聊天过程中**发送和接收图像**。
众所周知，尽管 ChatGPT 的功能非常强大，甚至可以用来写小说写论文，但目前也仅限于文字交流。但表情包早已成为日常文本聊天不可或缺的功能。
Visual ChatGPT 的出现，就像在以文字交流的 APP 中首次添加了表情包功能，而且还是根据用户输入的文本自动生成的 “定制化表情包”，大大提升了 ChatGPT 的趣味性和应用领域。
![[085021_VCas_5430600.gif]]
一方面，ChatGPT（或 LLM）充当通用界面，提供对图像的理解和用户的交互功能。另一方面，基础图像模型通过提供特定领域的深入知识来充当背后的技术专家。
仓库中列出了技术架构及原理图：
![[085457_X2g6_5430600.jpg]]
Demo 中共进行了三种不同类型的对话，分别是 Visual ChatGPT **接收**用户的图像、Visual ChatGPT 根据用户的文本**修改图像并发送**给用户，以及 Visual ChatGPT **识别图片**，并回答用户的提问。 Visual ChatGPT 会根据用户的输入，判断是否需要使用 VFM （Visual Foundation Model，视觉基础模型）来处理该问题。
仓库中还给出了 Visual ChatGPT 所使用的图像模型和显存使用情况：
![[085402_2ADX_5430600.png]]
更详细的内容可以阅读 Visual ChatGPT 的 arxiv 论文：[Visual ChatGPT: Talking, Drawing and Editing with Visual Foundation Models](https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Farxiv.org%2Fabs%2F2303.04671)
Visual ChatGPT 在 3 月 10 日发布，截至 3 月 15 日早 9 点，该项目已暂获 19547 个 Stars ，可谓是火箭式上涨。
![[090731_xJ43_5430600.png]]
---
---
---
```Go
Location:   F:\ResearchDirection\AI\visual-chatgpt
\#Web
https://www.oschina.net/news/232627/microsoft-visual-chatgpt
https://github.com/microsoft/visual-chatgpt
# clone the repo
git clone https://github.com/microsoft/visual-chatgpt.git
# Go to directory
cd visual-chatgpt
# create a new environment
conda create -n visgpt python=3.8
# activate the new environment
conda activate visgpt
#  prepare the basic environments
pip install -r requirements.txt
# prepare your private OpenAI key (for Linux)
export OPENAI_API_KEY={Your_Private_Openai_Key}
# prepare your private OpenAI key (for Windows)
set OPENAI_API_KEY=***************************************************
# Start Visual ChatGPT !
# You can specify the GPU/CPU assignment by "--load", the parameter indicates which 
# Visual Foundation Model to use and where it will be loaded to
# The model and device are separated by underline '_', the different models are separated by comma ','
# The available Visual Foundation Models can be found in the following table
# For example, if you want to load ImageCaptioning to cpu and Text2Image to cuda:0
# You can use: "ImageCaptioning_cpu,Text2Image_cuda:0"
# Advice for CPU Users
python visual_chatgpt.py --load ImageCaptioning_cpu,Text2Image_cpu
# Advice for 1 Tesla T4 15GB  (Google Colab)                       
python visual_chatgpt.py --load "ImageCaptioning_cuda:0,Text2Image_cuda:0"
                                
# Advice for 4 Tesla V100 32GB                            
python visual_chatgpt.py --load "ImageCaptioning_cuda:0,ImageEditing_cuda:0,
    Text2Image_cuda:1,Image2Canny_cpu,CannyText2Image_cuda:1,
    Image2Depth_cpu,DepthText2Image_cuda:1,VisualQuestionAnswering_cuda:2,
    InstructPix2Pix_cuda:2,Image2Scribble_cpu,ScribbleText2Image_cuda:2,
    Image2Seg_cpu,SegText2Image_cuda:2,Image2Pose_cpu,PoseText2Image_cuda:2,
    Image2Hed_cpu,HedText2Image_cuda:3,Image2Normal_cpu,
    NormalText2Image_cuda:3,Image2Line_cpu,LineText2Image_cuda:3"
python visual_chatgpt.py --load "ImageCaptioning_cuda:0,ImageEditing_cuda:0, Text2Image_cuda:0"
```
  
---
```Go
AssertionError: Torch not compiled with CUDA enabled
pip uninstall torch
pip install torch torchvision torchaudio -f https://download.pytorch.org/whl/cu111/torch_stable.html
and cuDNN library. You can find instructions on the NVIDIA website:
CUDA Toolkit: https://developer.nvidia.com/cuda-downloads
and install
cuDNN: https://developer.nvidia.com/cudnn
conda install pytorch torchvision torchaudio pytorch-cuda=11.7 -c pytorch -c nvidia
(visgpt) F:\ResearchDirection\AI\visual-chatgpt>cat testcuda.py
import torch
print(torch.cuda.is_available())
python testcuda.py
_> should return TRUE
```
---
```JavaScript
LangChain Manual
https://python.langchain.com/en/latest/index.html
ReAct的概念在LangChain（代理等）中被大量使用
https://zenn.dev/ryo1443/articles/d727b2b9a6d08c
https://tech.nri-net.com/entry/tried_langchain_to_extend_chatgpt
LangChain in Go
https://github.com/tmc/langchaingo.git

export OPENAI_API_KEY
\#change default solver
conda create -n VChatGPT
conda activate VChatGPT
\#if not working, rollback:conda config --set solver classic
conda update conda
conda install conda-libmamba-solver
conda config --set solver libmamba
conda config --set solver classic

pip3 install numpy --pre torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/nightly/cu118
https://note.com/npaka/n/n2f89fb3bf91b
```