---
DocFlag:
  - Reference
  - Tested
Updated: 2024-08-21T15:35
tags:
  - AI->-SearchEngine
  - AI->-Theory
Created: 2024-08-20T18:03
---
李宏毅深度学习教程LeeDL-Tutorial（苹果书）
纸质版
最新版PDF下载
纸质版和PDF版的区别
内容介绍
配套代码
扩展资源
贡献者
引用信息
致谢
关注我们
LICENSE
Star History
课程和视频对照
[![](https://opengraph.githubassets.com/0bdb752a8e8da06f88cdfa625396607be04b9482c108b8205fcb572f6f6f7b54/datawhalechina/leedl-tutorial)](https://opengraph.githubassets.com/0bdb752a8e8da06f88cdfa625396607be04b9482c108b8205fcb572f6f6f7b54/datawhalechina/leedl-tutorial)
[![](https://camo.githubusercontent.com/88352538d4e8a081713341a54f8a3615c5f3b40c015891b0d62ddeaf645341d1/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)](https://camo.githubusercontent.com/88352538d4e8a081713341a54f8a3615c5f3b40c015891b0d62ddeaf645341d1/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)
[![](https://camo.githubusercontent.com/e129834f68511888b1cc9c62ce5e38de9b4182d834ca63783afcdd424ec9afbc/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)](https://camo.githubusercontent.com/e129834f68511888b1cc9c62ce5e38de9b4182d834ca63783afcdd424ec9afbc/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)
[![](https://camo.githubusercontent.com/a899d85b64ef1fa7aff454e15ae68654f611cfbd5450a9b052b6347736281f58/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f666f726b732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)](https://camo.githubusercontent.com/a899d85b64ef1fa7aff454e15ae68654f611cfbd5450a9b052b6347736281f58/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f666f726b732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)
[![](https://camo.githubusercontent.com/ec4fc6e3ac8d6cc50f2db61b7b5dec0cf40fc78666aedd0d48a9ec84e2b7a539/68747470733a2f2f686974732e736565796f756661726d2e636f6d2f6170692f636f756e742f696e63722f62616467652e7376673f75726c3d68747470732533412532462532466769746875622e636f6d253246646174617768616c656368696e612532466c6565646c2d7475746f7269616c25324626636f756e745f62673d253233373943383344267469746c655f62673d2532333535353535352669636f6e3d2669636f6e5f636f6c6f723d253233453745374537267469746c653d6869747326656467655f666c61743d66616c7365)](https://camo.githubusercontent.com/ec4fc6e3ac8d6cc50f2db61b7b5dec0cf40fc78666aedd0d48a9ec84e2b7a539/68747470733a2f2f686974732e736565796f756661726d2e636f6d2f6170692f636f756e742f696e63722f62616467652e7376673f75726c3d68747470732533412532462532466769746875622e636f6d253246646174617768616c656368696e612532466c6565646c2d7475746f7269616c25324626636f756e745f62673d253233373943383344267469746c655f62673d2532333535353535352669636f6e3d2669636f6e5f636f6c6f723d253233453745374537267469746c653d6869747326656467655f666c61743d66616c7365)
[![](https://camo.githubusercontent.com/71bdd7f506acfbea23d97b44fcf515d7ad08a7c0e17327e0500660756bcccd06/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f646f776e6c6f6164732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c2f746f74616c)](https://camo.githubusercontent.com/71bdd7f506acfbea23d97b44fcf515d7ad08a7c0e17327e0500660756bcccd06/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f646f776e6c6f6164732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c2f746f74616c)
[![](https://camo.githubusercontent.com/9a588afd926871cf6caaabf8f36acf441a53ed69540c3808e77861fbd3711203/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6c6963656e73652d434325323042592d2d4e432d2d5341253230342e302d6c6967687467726579)](https://camo.githubusercontent.com/9a588afd926871cf6caaabf8f36acf441a53ed69540c3808e77861fbd3711203/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6c6963656e73652d434325323042592d2d4e432d2d5341253230342e302d6c6967687467726579)
# ==李宏毅深度学习教程LeeDL-Tutorial（苹果书）==
==李宏毅老师是台湾大学的教授，其==[==《机器学习》（2021年春）==](https://speech.ee.ntu.edu.tw/~hylee/ml/2021-spring.html)==是深度学习领域经典的中文视频之一。李老师幽默风趣的授课风格深受大家喜爱，让晦涩难懂的深度学习理论变得轻松易懂，他会通过很多动漫相关的有趣例子来讲解深度学习理论。李老师的课程内容很全面，覆盖了到深度学习必须掌握的常见理论，能让学生对于深度学习的绝大多数领域都有一定了解，从而可以进一步选择想要深入的方向进行学习，培养深度学习的直觉，对于想入门深度学习又想看中文讲解的同学是非常推荐的。==
==本教程主要内容源于==[==《机器学习》（2021年春）==](https://speech.ee.ntu.edu.tw/~hylee/ml/2021-spring.html)==，并在其基础上进行了一定的原创。比如，为了尽可能地降低阅读门槛，笔者对这门公开课的精华内容进行选取并优化，对所涉及的公式都给出详细的推导过程，对较难理解的知识点进行了重点讲解和强化，以方便读者较为轻松地入门。此外，为了丰富内容，笔者在教程中选取了==[==《机器学习》（2017年春）==](https://speech.ee.ntu.edu.tw/~hylee/ml/2017-spring.php) ==的部分内容，并补充了不少除这门公开课之外的深度学习相关知识。==
==ℹ️== [==**李宏毅老师推荐**==](https://twitter.com/HungyiLee2/status/1754042391211004235)==**：**==
[![](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/prof._lee_twitter.jpg?raw=true)](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/prof._lee_twitter.jpg?raw=true)
## ==纸质版==
[![](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/apple.png?raw=true)](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/apple.png?raw=true)
==购买链接：==[==京东==](https://u.jd.com/muCV8tI) ==|== [==当当==](https://product.dangdang.com/29766946.html)
[![](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/apple_jingdong.jpg)](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/apple_jingdong.jpg)
==京东扫码购买==
[![](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/apple_dangdang_QR.jpg)](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/apple_dangdang_QR.jpg)
==当当扫码购买==
==豆瓣评分：==[==https://book.douban.com/subject/36997460/==](https://book.douban.com/subject/36997460/)
## ==最新版PDF下载==
==地址：==[==https://github.com/datawhalechina/leedl-tutorial/releases==](https://github.com/datawhalechina/leedl-tutorial/releases)
==国内地址(推荐国内读者使用)：链接:== [==https://pan.baidu.com/s/1KUOlEMlPi5I4b5ys7aJVjw==](https://pan.baidu.com/s/1KUOlEMlPi5I4b5ys7aJVjw) ==提取码: fmuk==
## ==纸质版和PDF版的区别==
==PDF版本是全书初稿，人民邮电出版社的编辑老师们对初稿进行了反复修缮，最终诞生了纸质书籍，在此向人民邮电出版社的编辑老师的认真严谨表示衷心的感谢！（附：校对样稿）==
[![](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/yanggao.png)](https://github.com/datawhalechina/leedl-tutorial/raw/master/assets/yanggao.png)
## ==内容介绍==
- ==**引言**== ==@王琦==
- ==**深度学习**== ==@王琦==
    - ==局部最小值与鞍点==
    - ==训练技巧==
    - ==自适应学习率==
    - ==分类问题损失函数==
    - ==归一化==
- ==**卷积神经网络和自注意力机制**== ==@王琦==
    - ==卷积神经网络==
    - ==自注意力机制==
- ==**循环神经网络**== ==@王琦==
- ==**Transformer**== ==@王琦==
    - ==Transformer==
- ==**生成模型**== ==@杨毅远==
    - ==生成对抗网络基础==
    - ==生成对抗网络理论与 Wasserstein 生成对抗网络==
    - ==生成对抗网络的评估与有条件的生成对抗网络==
    - ==循环生成对抗网络==
- ==**自监督学习**== ==@王琦==
    - ==芝麻街的模型==
    - ==BERT==
    - ==GPT-3==
- ==**自动编码器概念及其应用**== ==@江季==
- ==**扩散模型**====@王琦==
- ==**对抗攻击**== ==@杨毅远==
    - ==对抗攻击基本概念==
    - ==白盒攻击vs黑盒攻击==
    - ==被动防守vs主动防守==
- ==**可解释人工智能**== ==@杨毅远==
    - ==可解释人工智能概念与案例==
    - ==可解释人工智能中的局部可解释性==
    - ==可解释人工智能中的全局可解释性==
- ==**迁移学习**== ==@王琦==
    - ==领域自适应==
    - ==领域对抗训练==
- ==**深度强化学习**== ==@王琦==
- ==**终身学习**== ==@江季==
    - ==灾难性遗忘==
    - ==缓解灾难性遗忘==
- ==**网络压缩**== ==@王琦==
    - ==剪枝与彩票假设==
    - ==知识蒸馏==
- ==**元学习**== ==@杨毅远==
    - ==元学习的概念==
    - ==元学习的实例算法==
    - ==元学习的应用==
- ==**ChatGPT**== ==@杨毅远==
    - ==对于ChatGPT的误解==
    - ==ChatGPT背后的关键技术——预训练==
    - ==ChatGPT带来的研究问题==
## ==配套代码==
[==点击==](https://github.com/datawhalechina/leedl-tutorial/tree/master/Homework)==或者网页点击====`Homework`====文件夹进入配套代码==
## ==扩展资源==
- ==对====**强化学习**====感兴趣的读者，可阅读==[==蘑菇书EasyRL==](https://github.com/datawhalechina/easy-rl)
- ==对====**视觉强化学习**====感兴趣的读者，可阅读==[==Awesome Visual RL==](https://github.com/qiwang067/awesome-visual-rl)
## ==贡献者==
==  
  
==[==Qi Wang==](https://github.com/qiwang067)
[![](https://github.com/qiwang067.png?s=40)](https://github.com/qiwang067.png?s=40)
==上海交通大学博士生====  
  
====中国科学院大学硕士==
==  
  
==[==Yiyuan Yang==](https://yyysjz1997.github.io/)
[![](https://github.com/yyysjz1997.png?s=40)](https://github.com/yyysjz1997.png?s=40)
==牛津大学博士生====  
  
====清华大学硕士==
==  
  
==[==John Jim==](https://github.com/JohnJim0816)
[![](https://github.com/JohnJim0816.png?s=40)](https://github.com/JohnJim0816.png?s=40)
==北京大学硕士==
## ==引用信息==
==@misc{wang2023leedltutorial,  
title = {李宏毅深度学习教程},  
year = {2023},  
author = {王琦，杨毅远，江季},  
url = {https://github.com/datawhalechina/leedl-tutorial}  
}  
==
==@misc{wang2023leedltutorialen,  
title = {Deep Learning Tutorial by Hung-yi Lee},  
year = {2023},  
author = {Qi Wang，Yiyuan Yang，Ji Jiang},  
url = {https://github.com/datawhalechina/leedl-tutorial}  
}  
==
==若该教程对您有所帮助，可以在页面右上角点个Star ⭐ 支持一下，谢谢 😊！==
==如果您需要转载该教程的内容，请注明出处：==[==https://github.com/datawhalechina/leedl-tutorial==](https://github.com/datawhalechina/leedl-tutorial)==。==
## ==致谢==
==特别感谢== [==@Sm1les==](https://github.com/Sm1les)==、==[==@LSGOMYP==](https://github.com/LSGOMYP)==、==[==FuWeiru==](https://github.com/FuWeiru) ==对本项目的帮助与支持。==
==另外，十分感谢大家对于LeeDL-Tutorial的关注。==
[![](https://camo.githubusercontent.com/7385445477485f2838c41fee8a9e7ecce874c694202c1594eb0948a0325d0e51/68747470733a2f2f7265706f726f737465722e636f6d2f73746172732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)](https://camo.githubusercontent.com/7385445477485f2838c41fee8a9e7ecce874c694202c1594eb0948a0325d0e51/68747470733a2f2f7265706f726f737465722e636f6d2f73746172732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)
[![](https://camo.githubusercontent.com/b2c31bb8b324436f11b0f475bca84aeca51ec11cb58ba627765067399a5468cc/68747470733a2f2f7265706f726f737465722e636f6d2f666f726b732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)](https://camo.githubusercontent.com/b2c31bb8b324436f11b0f475bca84aeca51ec11cb58ba627765067399a5468cc/68747470733a2f2f7265706f726f737465722e636f6d2f666f726b732f646174617768616c656368696e612f6c6565646c2d7475746f7269616c)
## ==关注我们==
==扫描下方二维码关注公众号：Datawhale，回复关键词“李宏毅深度学习”，即可加入“LeeDL-Tutorial读者交流群”==
[![](https://raw.githubusercontent.com/datawhalechina/easy-rl/master/docs/res/qrcode.jpeg)](https://raw.githubusercontent.com/datawhalechina/easy-rl/master/docs/res/qrcode.jpeg)
## ==LICENSE==
==  
  
====本作品采用==[==知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议==](http://creativecommons.org/licenses/by-nc-sa/4.0/)==进行许可。==
[![](https://camo.githubusercontent.com/9a588afd926871cf6caaabf8f36acf441a53ed69540c3808e77861fbd3711203/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6c6963656e73652d434325323042592d2d4e432d2d5341253230342e302d6c6967687467726579)](https://camo.githubusercontent.com/9a588afd926871cf6caaabf8f36acf441a53ed69540c3808e77861fbd3711203/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6c6963656e73652d434325323042592d2d4e432d2d5341253230342e302d6c6967687467726579)
## ==Star History==
[![](https://camo.githubusercontent.com/6f9257227da48dd3a7ca4c8c7015f2fa00a4dd764cfe17e0dae60dfe8ad7ffac/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d646174617768616c656368696e612f6c6565646c2d7475746f7269616c)](https://camo.githubusercontent.com/6f9257227da48dd3a7ca4c8c7015f2fa00a4dd764cfe17e0dae60dfe8ad7ffac/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d646174617768616c656368696e612f6c6565646c2d7475746f7269616c)
  
# 课程和视频对照
|   |   |   |   |   |   |
|---|---|---|---|---|---|
|《深度学习详解》|李宏毅《机器学习》（2021年春）课程|B站链接||||
|第1章：机器学习基础|课程介绍|[https://www.bilibili.com/video/BV1JA411c7VT/?p=2 https://www.bilibili.com/video/BV1JA411c7VT/?p=3](https://www.bilibili.com/video/BV1JA411c7VT/?p=2)||||
|第2章：实践方法论|通⽤指导|[https://www.bilibili.com/video/BV1JA411c7VT/?p=4](https://www.bilibili.com/video/BV1JA411c7VT/?p=4)||||
|第3章：深度学习基础|当梯度⽐较⼩：局最⼩值和鞍点|[https://www.bilibili.com/video/BV1JA411c7VT/?p=5](https://www.bilibili.com/video/BV1JA411c7VT/?p=5)||||
||训练技巧：批量和动量|[https://www.bilibili.com/video/BV1JA411c7VT/?p=6](https://www.bilibili.com/video/BV1JA411c7VT/?p=6)||||
||训练技巧：⾃适应学习率|[https://www.bilibili.com/video/BV1JA411c7VT/?p=7](https://www.bilibili.com/video/BV1JA411c7VT/?p=7)||||
||损失函数：分类|[https://www.bilibili.com/video/BV1JA411c7VT/?p=8](https://www.bilibili.com/video/BV1JA411c7VT/?p=8)||||
||批量归⼀化|[https://www.bilibili.com/video/BV1JA411c7VT/?p=9](https://www.bilibili.com/video/BV1JA411c7VT/?p=9)||||
|第4章：卷积神经⽹络|卷积神经⽹络|[https://www.bilibili.com/video/BV1JA411c7VT/?p=10](https://www.bilibili.com/video/BV1JA411c7VT/?p=10)||||
|第5章：循环神经网络|循环神经⽹络 （《机器学习》（2017年春））|[https://www.bilibili.com/video/BV1Ht411g7Ef/?p=36 https://www.bilibili.com/video/BV1Ht411g7Ef/?p=37](https://www.bilibili.com/video/BV1Ht411g7Ef/?p=36)||||
|第6章：自注意力机制|⾃注意⼒机制|[https://www.bilibili.com/video/BV1JA411c7VT?p=11 https://www.bilibili.com/video/BV1JA411c7VT?p=12](https://www.bilibili.com/video/BV1JA411c7VT?p=11)||||
|第7章：Transformer|Transformer|[https://www.bilibili.com/video/BV1JA411c7VT?p=13 https://www.bilibili.com/video/BV1JA411c7VT?p=14](https://www.bilibili.com/video/BV1JA411c7VT?p=13)||||
|第8章：生成模型|⽣成模型|[https://www.bilibili.com/video/BV1JA411c7VT?p=15 https://www.bilibili.com/video/BV1JA411c7VT?p=16](https://www.bilibili.com/video/BV1JA411c7VT?p=15)||||
|||[https://www.bilibili.com/video/BV1JA411c7VT?p=17 https://www.bilibili.com/video/BV1JA411c7VT?p=18](https://www.bilibili.com/video/BV1JA411c7VT?p=17)||||
|第9章：扩散模型|浅谈图像⽣成模型 （《机器学习》（2023年春））|[https://www.bilibili.com/video/BV1TD4y137mP?p=35](https://www.bilibili.com/video/BV1TD4y137mP?p=35)||||
|第10章：自监督模型|⾃监督学习：芝麻街⾥⾯的模型|[https://www.bilibili.com/video/BV1JA411c7VT?p=19](https://www.bilibili.com/video/BV1JA411c7VT?p=19)||||
||BERT介绍|[https://www.bilibili.com/video/BV1JA411c7VT?p=20](https://www.bilibili.com/video/BV1JA411c7VT?p=20)||||
||BERT有趣的事实|[https://www.bilibili.com/video/BV1JA411c7VT?p=21](https://www.bilibili.com/video/BV1JA411c7VT?p=21)||||
||GPT-3|[https://www.bilibili.com/video/BV1JA411c7VT?p=22](https://www.bilibili.com/video/BV1JA411c7VT?p=22)||||
|第11章：自编码器|⾃编码器|[https://www.bilibili.com/video/BV1JA411c7VT?p=23 https://www.bilibili.com/video/BV1JA411c7VT?p=24](https://www.bilibili.com/video/BV1JA411c7VT?p=23)||||
|第12章：对抗攻击|对抗攻击|[https://www.bilibili.com/video/BV1JA411c7VT?p=25 https://www.bilibili.com/video/BV1JA411c7VT?p=26](https://www.bilibili.com/video/BV1JA411c7VT?p=25)||||
|第13章：迁移学习|领域迁移|[https://www.bilibili.com/video/BV1JA411c7VT?p=29](https://www.bilibili.com/video/BV1JA411c7VT?p=29)||||
|第14章：强化学习|强化学习|[https://www.bilibili.com/video/BV1JA411c7VT?p=30 https://www.bilibili.com/video/BV1JA411c7VT?p=31 https://www.bilibili.com/video/BV1JA411c7VT?p=32](https://www.bilibili.com/video/BV1JA411c7VT?p=30)||||
|第15章：元学习|元学习|[https://www.bilibili.com/video/BV1JA411c7VT?p=39 https://www.bilibili.com/video/BV1JA411c7VT?p=40](https://www.bilibili.com/video/BV1JA411c7VT?p=39)||||
|第16章：终身学习|终⽣学习：灾难性遗忘|[https://www.bilibili.com/video/BV1JA411c7VT?p=35](https://www.bilibili.com/video/BV1JA411c7VT?p=35)||||
||终⽣学习：缓解灾难性遗忘|[https://www.bilibili.com/video/BV1JA411c7VT?p=36](https://www.bilibili.com/video/BV1JA411c7VT?p=36)||||
|第17章：网络压缩|压缩：剪枝和彩票假说|[https://www.bilibili.com/video/BV1JA411c7VT?p=37](https://www.bilibili.com/video/BV1JA411c7VT?p=37)||||
||压缩：知识蒸馏|[https://www.bilibili.com/video/BV1JA411c7VT?p=38](https://www.bilibili.com/video/BV1JA411c7VT?p=38)||||
|第18章：可解释性机器学习|可解释性⼈⼯智能|[https://www.bilibili.com/video/BV1JA411c7VT?p=27 https://www.bilibili.com/video/BV1JA411c7VT?p=28](https://www.bilibili.com/video/BV1JA411c7VT?p=27)||||
|第19章：ChatGPT|ChatGPT原理解析 （《机器学习》（2023年春））|[https://www.bilibili.com/video/BV1TD4y137mP?p=2 https://www.bilibili.com/video/BV1TD4y137mP?p=3 https://www.bilibili.com/video/BV1TD4y137mP?p=4](https://www.bilibili.com/video/BV1TD4y137mP?p=2)||||