{"openapi": "3.1.0", "info": {"title": "ChatPDF API for GPT", "version": "1.0.0", "description": "This GPT interacts with the ChatPDF API to extract information from PDF files and chat with them."}, "servers": [{"url": "https://api.chatpdf.com/v1"}], "components": {"schemas": {"SourceIdResponse": {"type": "object", "properties": {"sourceId": {"type": "string", "example": "src_xxxxxx"}}}, "ChatMessageRequest": {"type": "object", "properties": {"sourceId": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessage"}}}}, "ChatMessage": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}}}, "ChatResponse": {"type": "object", "properties": {"content": {"type": "string"}, "references": {"type": "array", "items": {"$ref": "#/components/schemas/PageReference"}}}}, "PageReference": {"type": "object", "properties": {"pageNumber": {"type": "integer"}}}}, "securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key"}}}, "paths": {"/sources/add-url": {"post": {"operationId": "addPdfViaUrl", "summary": "Add a PDF via URL", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "example": "https://uscode.house.gov/static/constitution.pdf"}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceIdResponse"}}}}}, "security": [{"ApiKeyAuth": []}]}}, "/chats/message": {"post": {"operationId": "chatWithPdf", "summary": "Send a chat message to a PDF", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}}}, "responses": {"200": {"description": "Chat response received", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponse"}}}}}, "security": [{"ApiKeyAuth": []}]}}, "/sources/delete": {"post": {"operationId": "deletePdf", "summary": "Delete PDF files from ChatPDF", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"sources": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "PDFs deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}}, "security": [{"ApiKeyAuth": []}]}}}}