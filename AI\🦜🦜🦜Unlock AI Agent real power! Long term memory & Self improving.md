---
DocFlag:
  - Reference
  - Tested
Updated: 2024-04-18T17:54
tags:
  - AI->-Agent
  - AI->-Programming
  - AI->-RAG
  - AI->-YouTube
  - AI/WebScrape
  - AI/YouTube
  - programming
  - AI/Agent
URL: https://youtube.com/watch?v=7LWTZqksmSg&si=mKB-dQIdfJe2LsEb
Created: 2024-04-18T08:00
---
```Python
# install teachable for long term memory, make sure you install latest version
pip install --force "pyautogen[teachable]"
https://microsoft.github.io/autogen/docs/reference/agentchat/contrib/capabilities/teachability/
from autogen import UserProxyAgent, config_list_from_json
from autogen.agentchat.contrib.capabilities.teachability import Teachability
from autogen import ConversableAgent
from autogen import register_function
from dotenv import load_dotenv
import json,os, requests, datetime
import logging
from bs4 import BeautifulSoup
from langchain.agents import initialize_agent
from langchain.chat_models import ChatOpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains.summarize import load_summarize_chain
from langchain import PromptTemplate
from langchain import LLMChain
load_dotenv()
# Get today's date
today_date = datetime.datetime.now().strftime('%Y-%m-%d')
filename = f"/opt/workspace/researcher/logfile_{today_date}.log"
# Set up logging configuration
logging.basicConfig(filename=filename, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
def summary(content):
    llm = ChatOpenAI(temperature=0, model="gpt-3.5-turbo-16k-0613")
    # openai.api_base = "https://192.168.0.76:8002/v1"
    # llm = ChatOpenAI(temperature=0, model_name="qwen")
    text_splitter = RecursiveCharacterTextSplitter(
        separators=["\n\n", "\n"], chunk_size=10000, chunk_overlap=500)
    docs = text_splitter.create_documents([content])
    map_prompt = """
    Write a detailed summary of the following text for a research purpose:
    "{text}"
    SUMMARY:
    """
    map_prompt_template = PromptTemplate(
        template=map_prompt, input_variables=["text"])
    summary_chain = load_summarize_chain(
        llm=llm,
        chain_type='map_reduce',
        map_prompt=map_prompt_template,
        combine_prompt=map_prompt_template,
        verbose=False
    )
    output = summary_chain.run(input_documents=docs,)
    return output
# Define research function
def search(query: str) -> dict:
    url = "https://google.serper.dev/search"
    payload = json.dumps({
        "q": query
    })
    headers = {
        'X-API-KEY': 'e8d813d64fd9c79f007c9c0e74c98d8d5ac1d559',
        'Content-Type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    logging.info(f"search function returned: {response.json()}")
    return response.json()
def scrape(url: str) -> str:
    # scrape website, and also will summarize the content based on objective if the content is too large
    # objective is the original objective & task that user give to the agent, url is the url of the website to be scraped
    print(f"Scraping website...{url}")
    # Define the headers for the request
    headers = {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
    }
    # Define the data to be sent in the request
    data = {
        "url": url
    }
    # Convert Python object to JSON string
    data_json = json.dumps(data)
    # Send the POST request
    response = requests.post(
        "https://chrome.browserless.io/content?token=53dea3fc-fb83-495f-9f99-7efdbe7732cf", headers=headers, data=data_json)
    # Check the response status code
    if response.status_code == 200:
        soup = BeautifulSoup(response.content, "html.parser")
        text = soup.get_text()
        # print("CONTENTTTTTT:", text)
        if len(text) > 8000:
            output = summary(text)
            # output = summary_v2(text)
            logging.info(f"scrape function returned from {url} (summarized): {output}")
            return output
        else:
            logging.info(f"scrape function returned from {url}: {text}")
            return text
    else:
        error_message = f"HTTP request failed with status code {response.status_code}"
        logging.error(error_message)       
        
# Load LLM inference endpoints from an env variable or a file
# See https://microsoft.github.io/autogen/docs/FAQs\#set-your-api-endpoints
# and OAI_CONFIG_LIST_sample
filter_dict = {
    "model": ["gpt-4-turbo-2024-04-09"]
}  # GPT-3.5 is less reliable than GPT-4 at learning from user feedback.
config_list = config_list_from_json(
    env_or_file="ai_config", filter_dict=filter_dict
)
llm_config = {"config_list": config_list, "timeout": 120}
# Start by instantiating any agent that inherits from ConversableAgent, which we use directly.
teachable_agent = ConversableAgent(
   name="teachable_agent", llm_config=llm_config,  # The name can be anything.
   system_message="""
   You are a helpful AI assistant. You can help user with their research by searching for information on the web or summarizing content from websites.
   and also you will remember the information you have learned from user.
   Return 'TERMINATE' when the task is completed or waiting for user's request.
   """   
)
# Instantiate a Teachability object. Its parameters are all optional.
teachability = Teachability(
   reset_db=False,  # Use True to force-reset the memo DB, and False to use an existing one.
   path_to_db_dir="./db/teachability_db",  # Can be any path, but teachable agents in a notebook share the same DB.
)
teachability.add_to_agent(teachable_agent)

# For this test, create a user proxy agent as usual.
# user = UserProxyAgent("user", human_input_mode="ALWAYS")
# The user proxy agent is used for interacting with the assistant agent
# and executes tool calls.
user_proxy = ConversableAgent(
    name="Ray",
    llm_config=False,
    is_termination_msg=lambda x: x.get("content", "") and x.get("content", "").rstrip().endswith("TERMINATE"),
    human_input_mode="TERMINATE",    
)

\#register function
register_function(
   search,
   caller=teachable_agent,
   executor=user_proxy,
   name="search",
   description="google search for relevant information",
)
register_function(
   scrape,
   caller=teachable_agent,
   executor=user_proxy,
   name="scrape",
   description="Scraping website content based on url",
)
# Now add teachability to the agent.

# This function will return once the user types 'exit'.
teachable_agent.initiate_chat(
   user_proxy, message="Hi, I'm a teachable user assistant! What's on your mind?"
)
# user_proxy.initiate_chat(teachable_agent, message=query, clear_history=True, silent=True)
# user_proxy.initiate_chat(teachable_agent, message="Hi, Agent")
    
```