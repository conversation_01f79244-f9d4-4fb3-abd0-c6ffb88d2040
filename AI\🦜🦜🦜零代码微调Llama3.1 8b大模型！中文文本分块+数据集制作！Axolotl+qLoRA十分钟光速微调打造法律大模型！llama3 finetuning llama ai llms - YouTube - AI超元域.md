---
DocFlag:
  - Reference
Updated: 2024-08-04T13:02
tags:
  - AI->-Fine-Tuning
  - AI->-llama
Created: 2024-08-04T10:19
---
```Python

\#base model
google-bert/bert-base-chinese
\#fine tuning framework
https://github.com/axolotl-ai-cloud/axolotl
\#setup docker
sudo usermod -aG docker $USER
sudo docker run -gpus "all" -it winglian/axolotl:main-latest
rm examples/llama-3/qlora.yml
wget -P examples/llama-3/ https://raw.githubusercontent.com/win4r/mytest/main/qlora.yml
```
qlora.yml
  
  
```YAML
base_model: NousResearch/Meta-Llama-3.1-8B
model_type: AutoModelForCausalLM
tokenizer_type: AutoTokenizer
load_in_8bit: false
load_in_4bit: true
strict: false
datasets:
  - path: leo009/lawdata
    type: alpaca
dataset_prepared_path:
val_set_size: 0
output_dir: ./outputs/qlora-out
adapter: qlora
lora_model_dir:
sequence_len: 4096
sample_packing: true
pad_to_sequence_len: true
lora_r: 32
lora_alpha: 16
lora_dropout: 0.05
lora_target_modules:
lora_target_linear: true
lora_fan_in_fan_out:
wandb_project:
wandb_entity:
wandb_watch:
wandb_name:
wandb_log_model:
gradient_accumulation_steps: 4
micro_batch_size: 2
num_epochs: 4
optimizer: paged_adamw_32bit
lr_scheduler: cosine
learning_rate: 0.0002
train_on_inputs: false
group_by_length: false
bf16: auto
fp16:
tf32: false
gradient_checkpointing: true
early_stopping_patience:
resume_from_checkpoint:
local_rank:
logging_steps: 1
xformers_attention:
flash_attention: true
warmup_steps: 10
evals_per_epoch: 4
eval_table_size:
saves_per_epoch: 1
debug:
deepspeed:
weight_decay: 0.0
fsdp:
fsdp_config:
special_tokens:
  pad_token: "<|end_of_text|>"
```
  
  
```YAML
# preprocess datasets - optional but recommended
CUDA_VISIBLE_DEVICES="" python -m axolotl.cli.preprocess examples/openllama-3b/qlora.yml
# finetune lora
accelerate launch -m axolotl.cli.train examples/llama-3/qlora.yml
# gradio
accelerate launch -m axolotl.cli.inference examples/openllama-3b/lora.yml \
    --lora_model_dir="./outputs/lora-out" --gradio
    
# merge
python3 -m axolotl.cli.merge_lora examples/openllama-3b/lora.yml --lora_model_dir="./outputs/qlora-out"
```
  
[https://www.youtube.com/watch?v=UCmPrNFWClI](https://www.youtube.com/watch?v=UCmPrNFWClI)