---
Updated: 2023-03-07T13:16
tags:
  - AI->-Programming
Created: 2023-03-06T20:40
---
  
下面是使用tcell库改写的程序：
```Plain
package main
import (
	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)
func main() {
	app := tview.NewApplication()
	ctrlShiftM := tcell.NewEventKey(tcell.KeyCtrlShift, 'M', tcell.ModNone)
	// Register a handler for the Ctrl+Shift+M key combination
	pages := tview.NewPages()
	pages.AddPage("main", tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(tview.NewBox().SetBorder(true).SetTitle("Main UI"), 0, 1, false).SetFocus(0), true, true)
	pages.AddPage("popup", tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(tview.NewBox().SetBorder(true).SetTitle("Popup Window"), 0, 1, false).SetFocus(0), true, false)
	app.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == ctrlShiftM {
			pages.SwitchToPage("popup")
			return nil
		}
		if event.Key() == tcell.KeyEscape {
			pages.SwitchToPage("main")
			return nil
		}
		return event
	})
	if err := app.SetRoot(pages, true).Run(); err != nil {
		panic(err)
	}
}
```
这个程序使用了tcell库来实现终端字符界面。使用Ctrl+Shift+M来弹出一个名为“Popup Window”的窗口，当用户关闭窗口时，程序会返回到主用户界面。