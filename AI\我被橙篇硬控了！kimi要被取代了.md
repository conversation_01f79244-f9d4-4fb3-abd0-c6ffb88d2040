---
Updated: 2024-07-22T14:55
tags:
  - AI->-DevPlatform
URL: https://mp.weixin.qq.com/s?__biz=MzIxMzUxMDg5MA==&mid=2247489830&idx=2&sn=2fe55c1a76aa4851b429397140883059&chksm=97b4e6a3a0c36fb522e5039921614d4c1f7bc36bcc1f51905047dcefdcf0f832ae70bb8f841b&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_tlfeeds&ranksessionid=1721621755_1&scene=169&subscene=200&sessionid=1721621754&ascene=56&fasttmpl_type=0&fasttmpl_fullversion=7301484-zh_CN-zip&fasttmpl_flag=0&realreporttime=1721622003063&clicktime=1721622003&enterid=1721622003&devicetype=android-34&version=28003145&nettype=plus.4g&lang=zh_CN&session_us=gh_88bf81155226&countrycode=JP&exportkey=n_ChQIAhIQElCTB6QIvRLXOuvZaSzffBLxAQIE97dBBAEAAAAAAKyZE9IBxKAAAAAOpnltbLcz9gKNyK89dVj0vvP8MJGrgvdc%2FvApfeP0ZSJaKFo6CyADxNI3xib8JIEXyFGWIYKaMcuiFahwjmazFp%2BlmmOODAsC0hQNYGdAURRA%2BxVxbeNnx%2Fjc136GNd248I0BmqNaWJv%2BEUd64LT96okJg4Bv7CHRzj%2Fcn5M1PzDP3S0BBaU7xd%2Fqohe6qslH2daFgim8T892jkH0XlLkgoWSnqU%2Bj72vYRAZRWlN3RS0ThIO%2F6wofp243NU2ye7TJldhNWW6pe2FXDxRxSuDdBJUuGXiAV3ga1Y%3D&pass_ticket=IoSoAJ4jOv1GufPn6U2E799x5XLdPkfgFithU8oOJ7prjwvdVVVwc0eIykQu8dsM&wx_header=3
Created: 2024-07-22T13:24
---
![[Notion/AI/我被橙篇硬控了！kimi要被取代了/attachments/0|0]]
说起写中文长文这事儿，我猜呀，大家第一时间想到的准是 kimi 。
之前，Kimi 可是我写长文的首选工具。后来朋友给我推荐「橙篇」，我用了用，哇塞，太好用啦！
「橙篇」是百度研发的AI工具，但「橙篇」并不是文心一言的升级版。从我使用的体验上来看，它更像是百度为写作量打造的AI工具：

> https://cp.baidu.com/#/
我是kimi的重度用户了，不过，我还是决定all in「橙篇」！
kimi很优秀，但是「橙篇」更懂用户。
下面，我详细说说，为啥我要all in 「橙篇」
# 精细化内容控制，效率翻倍
平常我们写长文的时候，可能会直接这样问AI

> 帮我写一篇长文，主题是:写篇3000字关于大模型原理的文章
用Kimi为例子，上面的问题给到Kimi之后，Kimi会输出下面的内容
kimi给出了个大纲。
那么，解析来我们要怎么做呢？
目前我的做法是：

> 1、把大纲拷贝出来
> 
> 2、把大纲的每一项提交给kimi，让kimi补全内容
> 
> 3、循环上面的步骤
我猜，其他小伙伴应该也差不多是类似的流程吧。
如果是百度「橙篇」呢？
橙篇也是先给了一个大纲，不过，不一样的是！「橙篇」可以直接修改大纲和大纲中的主要内容！
这个功能太方便了，百度的产品是懂用户的。
有了这个功能，我们就可以实现文章内容精细化控制了。
最后点击“生成长文”，只需要10分钟左右，一篇3000字的文章就生成了。
过了10分钟后，点击进入生成的文章，编辑的页面非常友好
最关键的是，我们还可以更进一步让AI帮我们优化！
相对于使用Kimi，需要反复在ctrl C 和 ctrl V之前横跳。「橙篇」的一站式写作体验真的太好了，我的写作效率至少提升了2倍。
# 专业的引用资源，写作更可靠
「橙篇」的写作体验非常好，提升了我的写作效率。但是，让我决定all in 「橙篇」并不是这个原因
让我决定all in「橙篇」，主要原因是「橙篇」的参考资源！
用过AI的都知道，AI回答的好不好，和训练的数据以及可参考的资源有关。
「橙篇」是百度出品的，据官方称，「橙篇」拥有亿级的学术资源。我们可以在「橙篇」用资料检索，找到专业的资源
当然了，我们用「橙篇」写的文章也会参考这些资源。
所以，用「橙篇」写出来的内容更加可靠。
「橙篇」的对手，kimi，写文章的时候所使用的内容都是从互联网检索得到的。
这么多年来，互联网产生的低质量内容太多了，我们平时到网上找点东西，筛选都得找好半天。
所以，用kimi生成的内容，其内容的专业性和可靠性，可想而知了。
「橙篇」还有个很好的功能--插入参考文献
这个功能，可以让我们插入生成内容的引用文献，这个功能，对写论文的小伙伴来说，真的太方便了。
要是当年我写毕业论文时有这么一个工具，我就不会天天熬夜，让近视度数高了100度了。
# 总结
总的来说，「橙篇」的表现远超我的预期，一个橙篇就顶得上Kimi+秘塔+WPS AI+…等等多个AI产品，尤其是在长文写作和资料检索上，简直是科研党的神器！
本次分享就到这儿了，希望专注于科研的小伙伴最大化利用「橙篇」，提升科研效率。
下面是我的微信，欢迎大家链接我
下面是我的免费知识星球，我会持续分享最新的AI资讯，学习AI的相关技术，探索如何使用AI打造副业，欢迎扫码免费加入