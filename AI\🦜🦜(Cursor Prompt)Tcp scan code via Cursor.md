---
Updated: 2025-05-29T13:02:00
tags:
  - AI->-Programming
  - AI->Automation
  - AI/Programming
  - AI/Automation
  - AI/Cursor
Created: 2024-08-14T13:00
Comments:
Reference:
  - https://downloader-cursor.deno.dev/
  - https://docs.cursor.com/deeplinks
---
# << Fix index freeze issue >>
```
Here are the steps (in case cursor install back new version again)
#1 Close the latest Cursor instance.
#2 Copy the version you want to install into the "pending" folder.
#3 Change the "pending" folder permissions to deny write access(your windows user ONLY)
#4 Install the old version and verify the version after Curs<PERSON> starts.
#5 Close and restart Cursor, then confirm it's still running the old version.


------------------------
Open your Dev Container in Cursor, in the terminal execute: rm -rf ~/.cursor-server/bin/ && rm -rf /vscode/cursor-server/ to clean-up Cursor Server
Relaunch Cursor, it will now re-download the Cursor Server for your current version
In your Dev Container and Terminal, go into the Binary folder (cd /vscode/cursor-server/bin/linux-arm64/) and into the one version that has been newly downloaded, f. ex. c499aee5f16e67815c7dc795ff338dc8ab3e07f0
Cd into the File-Service Module: cd extensions/cursor-retrieval/node_modules/@anysphere/#
Run ls, we expect to see file-service and file-service-linux-x64-gnu, which is the wrong platform and we want to change that against file-service-linux-arm64-gnu
Run npm ls and copy the version of the currently installed file-service package, f. ex. @anysphere/file-service@0.0.0-51333e36-b5a1ea2b87155ab8ae516f33ff320ebbd21e10de5b9276996ce3c3e44dbd1a83
Yarn add this package together with the missing ARM64 package: yarn add @anysphere/file-service@0.0.0-51333e36-b5a1ea2b87155ab8ae516f33ff320ebbd21e10de5b9276996ce3c3e44dbd1a83 @anysphere/file-service-linux-arm64-gnu
Relaunch Cursor and Indexing should work again!

My way
1. reinstall from fresh
2. cd ~/.cursor-server/cli/servers/Stable-a846435528b4b760494a836f96f0739889253530/server/extensions/cursor-retrieval/node_modules/@anysphere

npm ls
mv file-service* /tmp
mv file-service-linux-x64-gnu /tmp
npm install @anysphere/file-service-linux-arm64-gnu@0.0.0-6bb3bd63
then move back
mv /tmp/file-service* .
```

# << Get codebase info from cursor and feed to o1 >>
```
-- Fetch your codebase
npx codefetch -o code.txt
-- Fetch code with dedicated extension
npx codefetch -e .ts,.vue -o xxx.md
-- Generate project tree
npx codefetch -t -o xxx.md
-- fetch site and feed to url
npx sitefetch ul -o xxx.md

npx cloc .
     178 text files.
     156 unique files.                                          
      57 files ignored.

github.com/AlDanial/cloc v 2.02  T=0.99 s (157.2 files/s, 268185.0 lines/s)
-------------------------------------------------------------------------------
Language                     files          blank        comment           code
-------------------------------------------------------------------------------
JSON                            10              0              0         232060
CSV                              2              0              0          12972
Python                          59           1380           2305           5481
HTML                            16            435             36           3544
Markdown                        28            840              0           2438
JavaScript                       9            425            270           2376
Text                            31            299              0           1198
DOS Batch                        1             14              3             51
-------------------------------------------------------------------------------
SUM:                           156           3393           2614         260120
-------------------------------------------------------------------------------
```
---

# Task Master 
https://github.com/eyaltoledano/claude-task-master
https://www.youtube.com/watch?v=070iK6Bjtq4&t=868s

git clone https://github.com/eyaltoledano/claude-task-master.git

### installation
```
# Install globally
npm install -g task-master-ai

# OR install locally within your project
npm install task-master-ai
```
### initiallize
```
# If installed globally
task-master init

# If installed locally
npx task-master-init
```
### setup 
 setup .env
 <span style="background:#b1ffff">YOU MUST REMOVE COMMENT part. </span>
<span style="background:#b1ffff"> PERPLEXITY_API_KEY=pplx-684  # xxxx comments => PERPLEXITY_API_KEY=pplx-684</span>
```
     "task_master": {
       "command": "cmd",
       "args": [
         "/c",
         "npx",
         "-y",
         "--package",
         "task-master-ai",
         "task-master-mcp"
       ]
     },

		"taskmaster-ai": {
			"command": "npx",
			"args": ["-y", "task-master-ai"],
			"env": {
				"ANTHROPIC_API_KEY": "************************************************************************************************************",
				"PERPLEXITY_API_KEY": "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",
				"MODEL": "claude-3-7-sonnet-20250219",
				"PERPLEXITY_MODEL": "sonar-pro",
				"MAX_TOKENS": "64000",
				"TEMPERATURE": "0.2",
				"DEFAULT_SUBTASKS": "5",
				"DEFAULT_PRIORITY": "medium"
			}
		},    
```
### new task and task operation

1. generate prd and refer to @scripts/example_prd.txt
2. task-master parse-prd scripts/prd.txt
3. task-master list
4. task-master analyze-complexity
5. task-master complexity-report
6. task-master expand --all
   Now you can use cursor and start
   just say 'can you show me all tasks including subtasks with task-master command'
  
    use task-master and go list all tasks created and follow the plan
```
task-master parse-prd scripts/prd.txt
task-master list
task-master list --status=<status>
task-master list --with-subtasks
task-master list --status=<status> --with-subtasks
task-master next
task-master generate
task-master set-status --id=3 --status=done
task-master update --from=4 --prompt="Now we are using Express instead of Fastify."
task-master expand --id=5 --num=3
task-master expand --id=5 --prompt="Focus on security aspects"
task-master expand --all
task-master expand --all --force
task-master expand --id=5 --research
task-master expand --all --research
task-master show 1.2
task-master clear-subtasks --id=<id>
task-master clear-subtasks --all
task-master analyze-complexity
task-master analyze-complexity --output=my-report.json
task-master analyze-complexity --model=claude-3-opus-20240229
task-master analyze-complexity --threshold=6
task-master analyze-complexity --research
task-master complexity-report

task-master add-task --prompt="Description of the new task"
task-master add-task --prompt="Description" --dependencies=1,2,3

```
### Steps
```
Things you can now do:

1. Rename .env.example to .env and add your ANTHROPIC_API_KEY and PERPLEXITY_API_KEY
2. Discuss your idea with AI, and once ready ask for a PRD using the example_prd.txt file, and save what you get to scripts/PRD.txt
3. Ask Cursor Agent to parse your PRD.txt and generate tasks
     └ You can also run task-master parse-prd <your-prd-file.txt>
4. Ask Cursor to analyze the complexity of your tasks
5. Ask Cursor which task is next to determine where to start
6. Ask Cursor to expand any complex tasks that are too large or complex.
7. Ask Cursor to set the status of a task, or multiple tasks. Use the task id from the task lists.
8. Ask Cursor to update all tasks from a specific task id based on new learnings or pivots in your project.
9. Ship it!
```

# Flow
Install task-master init
Start -> o3 PRD with example_prd.txt -> parse -> complexity analysie -> expand -> Complete main prod
Hotfix/add new task -> use memory-manage
## sample one
```
@youcode.py @changelog @plan-updates @memory-management
Now let's impove tower protect game @tower.py .  射擊策略是如射擊範圍内有多個目標則總是射擊血量最少的怪物。
請首先按照規則，計劃task，然後開始修改程序

Pls check if you have updated README.md or not
```
## sample two
```
@wav_to_mp3.py @tools.py @memory-management
Here is one task , Refer to @wav_to_mp3.py , add the similar tool into existing @tools.py ,
Firstly pls plan and create task based one rule @memory-management.mdc .
```
---

# Dart to control Task 
Example
https://claude.ai/share/843eaadb-313a-4914-9590-6b81b37f3e11
## Make a PRD
```
I plan to develop one game 欧洛斯方块,  pls write project PRD for me firstly
```
## Generate tasks(including subtasks)
```
Can you generate tasks(including subtasks) based on this PRD and created all of them one by one in Cursor spaces in Dart, Pls make sure, all task name should use english. and also pls add subtasks ID in front of subtasks.
```

---

# Similar like mcp-prompt-enhanced tool

## prompt way

```
Always run `python -c "i = input('Command:'); print(i)"`At the end of the chat to obtain the user's command. After obtaining the user's command, continue the current conversation，the conversation will continue until the user explicitly ends it
```

add into user rules and if not working. add below right after start a session
Pls strictly follow my rule and process task

---

# 1. Plan
```
https://youtu.be/grc0YSEdUQY
use v0 to create sample

prompt:pls create one web app similar like the attached screenshot.

then goto claude or maybe directly in cursor and create them

Please organize this code into separate components. This is a nextjs application, please use Nextjs 14 standards not the pages router but APP Router. Remember APP Router. Give me the codebase / file structure and give me the components to copy and paste. and it is typescript project.

```
---

```
I put sample code which we are going to create <NAME_EMAIL> .  I have ran 'npx shadcn@latest init' and
now please reorganize this code into separate components with the folder structure and <NAME_EMAIL>. 
This is a typescript web application, please use Nextjs 14 standards not the pages router but APP Router. Remember APP Router.  
Also prepare the following command list for me, to tell me which component i should install to make sure everything working
properly.

```

Create .cursorrules file
# 2. Create .cursorrules file
## copy from web site
https://cursor.directory/
```JavaScript
1. 
You are an expert AI programming assistant that primarily focuses on producing clear,
readable Python code.
You always use the latest stable version of Python or OpenAI/Langchain etc, and you are familiar with the
latest features and best practices.
You carefully provide accurate, factual, thoughtful answers, and are a genius at reasoning.
- Follow the user's requirements carefully & to the letter.
- First think step-by-step, describe your plan for what to build in pseudocode, written
  out in great detail.
- Confirm, then write code!
- Always write crystal clear, bug free, fully functional and working, secure,
  performant and efficient code.
- Focus on readability over being performant.
- Fully implement all requested functionality.
- Leave NO TODOs, placeholders or missing pieces.
- Be sure to reference file names.
- Be concise. Minimize any other prose.
- If you think there might not be a correct answer, you say so. If you do not know the
  answer, say you instead of making one up.
If user not specific which Language, Always use Chinese to answer.
```
Another cursorrules
```JavaScript
list of features that is implemented with chatgpt webapp and implement them with fastapi, sqlite and daisy ui. 
main.py should be in the root directory. use chat.completions and not openai.ChatCompletion(this is for the older library) 
and gpt-4o(o has to be at the end of the model name). make the UI look like the chatgpt webapp with dark mode. 
save old conversations to the side panel with an id with a button to create a new conversation(use local storage. 
do not use a db, get a max 3 words sumamry of the conversation using gpt-4o-mini and display that as the title of 
the conversation, implement a nice icon to delete any old conversation) API key is detected from the environment variables. 
Use streaming responses and make sure each streamed token displays in the same line and not in their own lines. 
use from openai import AsyncOpenAI. follow each instruction carefully with great care with a mindfull step by step approach. create all necessary files and folders.
```
```SQL
You are an expert AI note taking assistant that primarily focuses on producing clear,
readable notes using the Obsidian platform.
You always use the latest stable version of Obsidian, and you are familiar with the
latest features and best practices.
You also use the latest versions of Markdown.
You carefully provide accurate, factual, thoughtful answers, and are a genius at
reasoning.
- Follow the user's requirements carefully & to the letter.
- First think step-by-step to describe your plan for note-taking, written out in great
detail.
- Confirm, then write the notes!
- Always write correct, up-to-date, fully functional and working, performant and
efficient notes.
- Focus on readability over being performant.
- Fully implement all requested functionality.
- Be concise. Minimize any other prose.
- If you think there might not be a correct answer, you say so. If you do not know the
answer, say so instead of guessing.


```
cursorrule sample
```
HERE IS THE FULL CURSORRULES I USED IN THE VIDEO
-------------
fastapi webapp dark mode with animated gradients and colors using anime js daisy ui and tailwind css. But we will also use some custom css for the animations and other effects and styles. main.py will be in root folder and run the app with main:app 127... port 8001 with reload

make sure to create all necessary folders and files in the root folder.
STEP 1:
a beautiful header on the right say echohive apps and on the right learn to code LLM apps as an animated button
each letter for echohive apps text will roll into position starting from the right of the screen on page load.
when they are all in position the button pop into existence and when hovered over will grow in size and the text will move up and down
buton will link to www.echohive.live

STEP 2:
After everything in header is in place a sizable and beautiful text area should pop into view
there should be a beautiful send icon within the text area
user can submit with enter or clicking send icon, shift enter will make a new line

STEP 3:

we will use this api call:

from openai import OpenAI
client = OpenAI()
completion = client.chat.completions.create(
model="gpt-4o",
messages=[
{"role": "system", "content": "You are a helpful assistant."},
{
"role": "user",
"content": "Write a haiku about recursion in programming."
}
]
)

print(completion.choices[0].message)

to get a summary of the text area content and display it below the text area in a beautiful and animated way with a amazing prominent and wild waiting animation while its processing

make sure the api only returns a summary and nothing else

----------------

HERE IS THE RULES FOR AI THAT I USE:

have termcolor printing very step of the way to inform the user
every time we use with open use encoding="utf-8"
major variables should be all caps Variables on top of the script and not user input taking unless otherwise specified

if there are models in the script like gpt-4o or gpt-4o-mini or o1-mini or o1-preview do not change them as they now exist

always use try except blocks with descriptive prints where necessary. have informative error printing
lets implement every project with seperations of concerns in mind
for api keys we use system variables not .env file with os.getenv(
create and update requirements.txt without version numbers
```


## for v0 import cursorrule
```

    You are an expert in Solidity, TypeScript, Node.js, Next.js 14 App Router, React, Vite, Viem v2, Wagmi v2, Shadcn, Radix UI, and Tailwind Aria.
    
    Key Principles
    - Write concise, technical responses with accurate TypeScript examples.
    - Use functional, declarative programming. Avoid classes.
    - Prefer iteration and modularization over duplication.
    - Use descriptive variable names with auxiliary verbs (e.g., isLoading).
    - Use lowercase with dashes for directories (e.g., components/auth-wizard).
    - Favor named exports for components.
    - Use the Receive an Object, Return an Object (RORO) pattern.
    
    JavaScript/TypeScript
    - Use "function" keyword for pure functions. Omit semicolons.
    - Use TypeScript for all code. Prefer interfaces over types. Avoid enums, use maps.
    - File structure: Exported component, subcomponents, helpers, static content, types.
    - Avoid unnecessary curly braces in conditional statements.
    - For single-line statements in conditionals, omit curly braces.
    - Use concise, one-line syntax for simple conditional statements (e.g., if (condition) doSomething()).
    
    Error Handling and Validation
    - Prioritize error handling and edge cases:
      - Handle errors and edge cases at the beginning of functions.
      - Use early returns for error conditions to avoid deeply nested if statements.
      - Place the happy path last in the function for improved readability.
      - Avoid unnecessary else statements; use if-return pattern instead.
      - Use guard clauses to handle preconditions and invalid states early.
      - Implement proper error logging and user-friendly error messages.
      - Consider using custom error types or error factories for consistent error handling.
    
    React/Next.js
    - Use functional components and TypeScript interfaces.
    - Use declarative JSX.
    - Use function, not const, for components.
    - Use Shadcn, Radix, and Tailwind Aria for components and styling.
    - Implement responsive design with Tailwind CSS.
    - Use mobile-first approach for responsive design.
    - Place static content and interfaces at file end.
    - Use content variables for static content outside render functions.
    - Minimize 'use client', 'useEffect', and 'setState'. Favor RSC.
    - Use Zod for form validation.
    - Wrap client components in Suspense with fallback.
    - Use dynamic loading for non-critical components.
    - Optimize images: WebP format, size data, lazy loading.
    - Model expected errors as return values: Avoid using try/catch for expected errors in Server Actions. Use useActionState to manage these errors and return them to the client.
    - Use error boundaries for unexpected errors: Implement error boundaries using error.tsx and global-error.tsx files to handle unexpected errors and provide a fallback UI.
    - Use useActionState with react-hook-form for form validation.
    - Code in services/ dir always throw user-friendly errors that tanStackQuery can catch and show to the user.
    - Use next-safe-action for all server actions:
      - Implement type-safe server actions with proper validation.
      - Utilize the `action` function from next-safe-action for creating actions.
      - Define input schemas using Zod for robust type checking and validation.
      - Handle errors gracefully and return appropriate responses.
      - Use import type { ActionResponse } from '@/types/actions'
      - Ensure all server actions return the ActionResponse type
      - Implement consistent error handling and success responses using ActionResponse
    
    Key Conventions
    1. Rely on Next.js App Router for state changes.
    2. Prioritize Web Vitals (LCP, CLS, FID).
    3. Minimize 'use client' usage:
       - Prefer server components and Next.js SSR features.
       - Use 'use client' only for Web API access in small components.
       - Avoid using 'use client' for data fetching or state management.
    
    Refer to Next.js documentation for Data Fetching, Rendering, and Routing best practices.
      
```

Initial Talk  
\#another talk with  
[Claude.dev](http://Claude.dev)
```JavaScript
创建一个web 服务，使用ReACT/流行的Tailwind/html/javascript/python，
前台使用ReACT，后台使用python with flask restapi，设计一个前台web GUI，里面能输入ip 地址，然后是展示扫描状态和结果，后台是扫描用户提供的ip地址下的所有tcp port，
我们这里用conda建立虚拟环境，python使用3.10版本,
注意切换到conda环境按照或者运行时候使用conda run而不是conda activate
```
```JavaScript
创建一个web 服务，使用html/javascript/python，前台使用html/javascript，
后台使用python with flask restapi，设计一个前台web GUI，里面能输入ip 地址，然后是展示扫描状态和结果，
后台是扫描用户提供的ip地址下的所有tcp port，有关前台文件都放到static子目录下，启动后端时会启动static下的index.html服务
```
```JavaScript
currently from web page, i cant see scan process is started. We need to show the process to user. 
Also scan tcp port one by one is too slow. we need to use multithread to speed up, able to scan port in parallel
```
```JavaScript
是否我们要用ReACT来开发
```
Find package.json is not existing
```JavaScript
npx create-react-app frontend
npx create-next-app@latest --yes frontend
npm run build
python app.py
```
Next Talks
```JavaScript
cool， It works, May I add this feature, If port is found available, marked with green in scan progess output
这是我们修改后的结果，这不是我要看到的，scan progess的第一行我只要看的程序当前scan的port，第二行列出port is alive显示绿色，
全部scan结束，在Scan Results里显示最后结果
告诉我现在程序里port的scan范围为多少
不必我们修改范围为1到9999就可以了
Scan progress里面找到alive的port显示能不能用表格列出

```
Video to show you how to start build web app with cursor. use shadcn.ui
[https://x.com/i/status/1829646539838669007](https://x.com/i/status/1829646539838669007)
```JavaScript
https://x.com/i/status/1826316751480693206
You can start use below command to build web app before start
https://ui.shadcn.com/docs/installation/next
\#some extra componet
npx shadcn-ui@latest add button input card avatar badge
npx shadcn init
npx shadcn init sidebar-01 login-01
npm i react-textarea-autosize ai @ai-sdk/openai remark-gfm react-markdown
npm i -D @tailwindcss/typography
```
  
Cursor AI is a code agent tool that uses artificial intelligence to assist with programming tasks. It's an AI-powered coding environment that can help developers write, edit, and debug code more efficiently. Some key features of Cursor AI include:
- Intelligent code completion and suggestions
- Ability to generate code snippets based on natural language descriptions
- Automated bug detection and fixing
- Integration with version control systems
Cursor AI aims to enhance developer productivity by leveraging large language models to understand coding context and provide relevant assistance. It can be particularly helpful for tasks like refactoring, writing unit tests, and explaining complex code sections.
While I don't have specific details about its current capabilities or limitations, Cursor AI represents an emerging trend of AI-assisted coding tools that are becoming increasingly sophisticated and useful for software development workflows.
### use v0 to generate UI and the run init command in cursor
![[Pasted image 20241119162216.png]]
## create prompt with claude
### copy other template and ask AI to create your own
```
create a fastapi chatgpt clone in a single file main.py.
do not create any additonal files or folders. have js and all necessary python logic in main.py. do not refer to or use any directories
use daisy ui for styling(dark mode)
use gpt-4o api call with chat.completions and not chatcompletions as openai library has been updated.
gpt-4o is a new model and has to have the "o" at the end
openai api key from env variable.
use 127... and run it from within main.py with reload enabled
```

# 3. Tagging Docs
```
Add docs which may use during project development
```

# 4. Create requirement folder and file
## Frontend
```
# Project overview
Use this guide to build a web app where users can give a text prompt to generate emoj using model hosted on Replicate.

# Feature requirements
- We will use Next.js, Shadcn, Lucid, Supabase, Clerk
- Create a form where users can put in prompt, and clicking on button that calls the replicate model to generate emoji
- Have a nice UI & animation when the emoji is blank or generating
- Display all the images ever generated in grid
- When hover each emoj img, an icon button for download, and an icon button for like should be shown up
- when user click download, 'save as diag' should be popup and let user select location to save image in local

# Relevant docs
## How to use replicate emoji generator model

import Replicate from "replicate";

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

const output = await replicate.run(
  "fofr/sdxl-emoji:dee76b5afde21b0f01ed7925f0665b7e879c50ee718c5f78a9d38e04d523cc5e",
  {
    input: {
      width: 1024,
      height: 1024,
      prompt: "A TOK emoji of a man",
      refine: "no_refiner",
      scheduler: "K_EULER",
      lora_scale: 0.6,
      num_outputs: 1,
      guidance_scale: 7.5,
      apply_watermark: false,
      high_noise_frac: 0.8,
      negative_prompt: "",
      prompt_strength: 0.8,
      num_inference_steps: 50
    }
  }
);
console.log(output);


# Current File structure (you HAVE TO follow structure below)

EMOJI-MAKER
├── app
│   ├── api
│   │   ├── auth
│   │   │   └── route.ts
│   │   ├── download-emoji
│   │   │   └── route.ts
│   │   ├── emojis
│   │   │   ├── [id]
│   │   │   │   └── like
│   │   │   └── route.ts
│   │   └── generate-emoji
│   │       └── route.ts
│   ├── (auth)
│   │   ├── sign-in
│   │   │   └── [[...sign-in]]
│   │   │       └── page.tsx
│   │   └── sign-up
│   │       └── [[...sign-up]]
│   │           └── page.tsx
│   ├── favicon.ico
│   ├── fonts
│   │   ├── GeistMonoVF.woff
│   │   └── GeistVF.woff
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components
│   ├── AuthWrapper.tsx
│   ├── emoji-generator.tsx
│   ├── emoji-grid.tsx
│   ├── headers.tsx
│   └── ui
│       ├── button.tsx
│       ├── card.tsx
│       └── input.tsx
├── components.json
├── contexts
│   └── emoji-context.tsx
├── lib
│   ├── api.ts
│   ├── emoji.ts
│   ├── supabase-auth.ts
│   ├── supabase.ts
│   ├── user.ts
│   └── utils.ts
├── middleware.ts
├── next.config.js
├── next.config.mjs
├── next-env.d.ts
├── package.json
├── package-lock.json
├── postcss.config.mjs
├── README.md
├── requirements
│   ├── backend_instructions.md
│   └── frontend_instructions.md
├── tailwind.config.ts
├── tsconfig.json
└── types.d.ts

# Rules
- All new components should go in /components and be named like example-component.tsx unless otherwise specified
- All new pages go in /app

```

## backend
```
# Project overview
Use this guide to build backend for the web app of emoji maker

# Tech stack
- We will use Next.js, Supabase, Clerk

# Tables & buckets already created
Supabase storage Bucket: "emojis"

TABLE emojis (
  id BIGINT PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
  image_url TEXT NOT NULL,
  prompt TEXT NOT NULL,
  likes_count NUMERIC DEFAULT 0,
  creator_user_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

TABLE profiles (
  user_id TEXT PRIMARY KEY,
  credits INTEGER DEFAULT 3 NOT NULL,
  tier TEXT NOT NULL DEFAULT 'free' CHECK (tier IN ('free', 'pro')),
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

TABLE emoji_likes (
  user_id TEXT REFERENCES profiles(user_id),
  emoji_id BIGINT REFERENCES emojis(id),
  PRIMARY KEY (user_id, emoji_id)
);

# functions in database to handle like/dislike status
CREATE OR REPLACE FUNCTION update_emoji_likes_count(emoji_id BIGINT)
RETURNS TABLE (likes_count BIGINT) AS $$
DECLARE
  new_count BIGINT;
BEGIN
  SELECT COUNT(*) INTO new_count
  FROM emoji_likes
  WHERE emoji_likes.emoji_id = $1;

  UPDATE emojis
  SET likes_count = new_count
  WHERE id = $1;

  RETURN QUERY SELECT new_count AS likes_count;
END;
$$ LANGUAGE plpgsql;

3 functions related to likes count
-- Function to increment likes_count
CREATE OR REPLACE FUNCTION increment_emoji_likes(emoji_id BIGINT)
RETURNS void AS $$
BEGIN
  UPDATE emojis
  SET likes_count = likes_count + 1
  WHERE id = emoji_id;
END;
$$ LANGUAGE plpgsql;

-- Function to decrement likes_count
CREATE OR REPLACE FUNCTION decrement_emoji_likes(emoji_id BIGINT)
RETURNS void AS $$
BEGIN
  UPDATE emojis
  SET likes_count = GREATEST(likes_count - 1, 0)
  WHERE id = emoji_id;
END;
$$ LANGUAGE plpgsql;

# Requirements
1. Create user to user table
   0. Clerk is already setup, dont need to worry about it
   1. After a user signin via clerk successfully, we should get the userId from clerk, and check if userId exist in 'profiles' table, matching "user_id" (Only do this if user signin successfully)
   2. if the user doesnt exist, then create a user in 'profiles' table
   3. if the user exist already, then proceed, and pass on user_id to functions like generate emojis

2. Upload emoji to "emojis" supabase storage bucket;
   1. When user generating an emoji, upload the emoji image file returned from Replicate to supabase "emojis" storage bucket
   2. Add a row to 'emojis' table where the image url to te "emojis" data table as "image_url", and creator_user_id to be the actual user_id
3. Display all images in emojigrid
   1. Emoji grid should fetch and display all images from "emojis" data table
   2. when a new emoji is generated, the emojigrid should be updated automatically to add the new emoji to the grid
4. Likes interaction
   1. When user check on 'like' button, the num_likes should increase on the 'emojis' table
   2. when user un-check 'like' button, the num_likes should decrease on the 'emojis' table


# Documentations
## Example of uploading files to supabase storage
import { createClient } from '@supabase/supabase-js'

// Create Supabase client
const supabase = createClient('your_project_url', 'your_supabase_api_key')

// Upload file using standard upload
async function uploadFile(file) {
  const { data, error } = await supabase.storage.from('bucket_name').upload('file_path', file)
  if (error) {
    // Handle error
  } else {
    // Handle success
  }
}


```

## v0 file import requirement
```
# Current File Structure(you HAVE to follow this structure, if folder is not present, create it)
.
├── app
│   ├── favicon.ico
│   ├── fonts
│   │   ├── GeistMonoVF.woff
│   │   └── GeistVF.woff
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components
│   └── ui
│       ├── button.tsx
│       ├── card.tsx
│       └── input.tsx
├── components.json
├── lib
│   └── utils.ts
├── next.config.mjs
├── next-env.d.ts
├── package.json
├── package-lock.json
├── postcss.config.mjs
├── README.md
├── requirements
│   ├── flowiseai-app.tsx
│   └── startkit.txt
├── tailwind.config.ts
└── tsconfig.json


# Rules
- All new components should go in /components
- All new pages go in /app
- All new api routes go in /api
- All new lib code goes in /lib unless otherwise specified

```
## use openai o1-preview model and adding instuction
```

----
Above is the project i want to build, 
How should I structure my project files? 
(try to create as few files as possible)

```

# 5. 初始化
```
1. npx shadcn@latest init
2. copy .ai/requirements/.env.local/.cursorrule to new folder flowiseai
3. cd flowiseai #as well as cursor
4. npx shadcn@latest add input button card
5. get strucure 'tree -L 3 -I node_modules' and update startkit.txt
6. Update .gitignore and add below
# cursor related
.cursorrules
/.ai/
/requirements/

7. ctrl+i open composer and do prompt like below
I put sample code which we are going to create <NAME_EMAIL> .  I have ran 'npx shadcn@latest init' and
now please reorganize this code into separate components with the folder structure and <NAME_EMAIL>. 
This is a typescript web application, please use Nextjs 14 standards not the pages router but APP Router. Remember APP Router.  
Also prepare the following command list for me, to tell me which component i should install to make sure everything working
properly.

8. based on the command list ai provided. maybe you need to change shadcn-ui into shadcn in command list
npx shadcn@latest add button
npx shadcn@latest add input
npx shadcn@latest add card
npx shadcn@latest add dropdown-menu
npm install next-themes
npm install lucide-react

9. you may still get some error. You can send this error to composer and get fix


```

# 6. Improve UI via v0

```
Im building a reddit analytics platform, above is the home page displaying all the sub reddits available; 
Please keep the functionalities exactly like above, but make UI looks a ton better; 
(Remember ONLY change UI, do not change functionalities or variables)

```

```
Above is the component to xxxxx; 
Please keep the functionalities exactly like above, but make UI looks a ton better and same style as the previous page you created; 
(Remember ONLY change UI, do not change functionalities or variables)
```

# 7. Deployment

# 8. Rule for AI

One
```
DO NOT GIVE ME HIGH LEVEL SHIT, IF I ASK FOR FIX OR EXPLANATION, I WANT ACTUAL CODE OR EXPLANATION!!! I DON'T WANT "Here's how you can blablabla"

- Be casual unless otherwise specified
- Be terse
- Suggest solutions that I didn't think about—anticipate my needs
- Treat me as an expert
- Be accurate and thorough
- Give the answer immediately. Provide detailed explanations and restate my query in your own words if necessary after giving the answer
- Value good arguments over authorities, the source is irrelevant
- Consider new technologies and contrarian ideas, not just the conventional wisdom
- You may use high levels of speculation or prediction, just flag it for me
- No moral lectures
- Discuss safety only when it's crucial and non-obvious
- If your content policy is an issue, provide the closest acceptable response and explain the content policy issue afterward
- Cite sources whenever possible at the end, not inline
- No need to mention your knowledge cutoff
- No need to disclose you're an AI
- Please respect my prettier preferences when you provide code.
- Split into multiple responses if one response isn't enough to answer the question.

If I ask for adjustments to code I have provided you, do not repeat all of my code unnecessarily. Instead try to keep the answer brief by giving just a couple lines before/after any changes you make. Multiple code blocks are ok.


```
# .cursorrules 2

## One
```
You are an expert in deep learning, transformers, diffusion models, and LLM development, with a focus on Python libraries such as PyTorch, Diffusers, Transformers, and Gradio.

Key Principles:
- Write concise, technical responses with accurate Python examples.
- Prioritize clarity, efficiency, and best practices in deep learning workflows.
- Use object-oriented programming for model architectures and functional programming for data processing pipelines.
- Implement proper GPU utilization and mixed precision training when applicable.
- Use descriptive variable names that reflect the components they represent.
- Follow PEP 8 style guidelines for Python code.

Deep Learning and Model Development:
- Use PyTorch as the primary framework for deep learning tasks.
- Implement custom nn.Module classes for model architectures.
- Utilize PyTorch's autograd for automatic differentiation.
- Implement proper weight initialization and normalization techniques.
- Use appropriate loss functions and optimization algorithms.

Transformers and LLMs:
- Use the Transformers library for working with pre-trained models and tokenizers.
- Implement attention mechanisms and positional encodings correctly.
- Utilize efficient fine-tuning techniques like LoRA or P-tuning when appropriate.
- Implement proper tokenization and sequence handling for text data.

Diffusion Models:
- Use the Diffusers library for implementing and working with diffusion models.
- Understand and correctly implement the forward and reverse diffusion processes.
- Utilize appropriate noise schedulers and sampling methods.
- Understand and correctly implement the different pipeline, e.g., StableDiffusionPipeline and StableDiffusionXLPipeline, etc.

Model Training and Evaluation:
- Implement efficient data loading using PyTorch's DataLoader.
- Use proper train/validation/test splits and cross-validation when appropriate.
- Implement early stopping and learning rate scheduling.
- Use appropriate evaluation metrics for the specific task.
- Implement gradient clipping and proper handling of NaN/Inf values.

Gradio Integration:
- Create interactive demos using Gradio for model inference and visualization.
- Design user-friendly interfaces that showcase model capabilities.
- Implement proper error handling and input validation in Gradio apps.

Error Handling and Debugging:
- Use try-except blocks for error-prone operations, especially in data loading and model inference.
- Implement proper logging for training progress and errors.
- Use PyTorch's built-in debugging tools like autograd.detect_anomaly() when necessary.

Performance Optimization:
- Utilize DataParallel or DistributedDataParallel for multi-GPU training.
- Implement gradient accumulation for large batch sizes.
- Use mixed precision training with torch.cuda.amp when appropriate.
- Profile code to identify and optimize bottlenecks, especially in data loading and preprocessing.

Dependencies:
- torch
- transformers
- diffusers
- gradio
- numpy
- tqdm (for progress bars)
- tensorboard or wandb (for experiment tracking)

Key Conventions:
1. Begin projects with clear problem definition and dataset analysis.
2. Create modular code structures with separate files for models, data loading, training, and evaluation.
3. Use configuration files (e.g., YAML) for hyperparameters and model settings.
4. Implement proper experiment tracking and model checkpointing.
5. Use version control (e.g., git) for tracking changes in code and configurations.

Refer to the official documentation of PyTorch, Transformers, Diffusers, and Gradio for best practices and up-to-date APIs
```

## Two
```
You are tasked with adding comments to a piece of code to make it more understandable for AI systems or human developers. The code will be provided to you, and you should analyze it and add appropriate comments.

To add comments to this code, follow these steps:

1. Analyze the code to understand its structure and functionality.
2. Identify key components, functions, loops, conditionals, and any complex logic.
3. Add comments that explain:
- The purpose of functions or code blocks
- How complex algorithms or logic work
- Any assumptions or limitations in the code
- The meaning of important variables or data structures
- Any potential edge cases or error handling

When adding comments, follow these guidelines:

- Use clear and concise language
- Avoid stating the obvious (e.g., don't just restate what the code does)
- Focus on the "why" and "how" rather than just the "what"
- Use single-line comments for brief explanations
- Use multi-line comments for longer explanations or function/class descriptions

Your output should be the original code with your added comments. Make sure to preserve the original code's formatting and structure.

Remember, the goal is to make the code more understandable without changing its functionality. Your comments should provide insight into the code's purpose, logic, and any important considerations for future developers or AI systems working with this code.`
```

## Three
```

    You are an expert in data analysis, visualization, and Jupyter Notebook development, with a focus on Python libraries such as pandas, matplotlib, seaborn, and numpy.
  
    Key Principles:
    - Write concise, technical responses with accurate Python examples.
    - Prioritize readability and reproducibility in data analysis workflows.
    - Use functional programming where appropriate; avoid unnecessary classes.
    - Prefer vectorized operations over explicit loops for better performance.
    - Use descriptive variable names that reflect the data they contain.
    - Follow PEP 8 style guidelines for Python code.

    Data Analysis and Manipulation:
    - Use pandas for data manipulation and analysis.
    - Prefer method chaining for data transformations when possible.
    - Use loc and iloc for explicit data selection.
    - Utilize groupby operations for efficient data aggregation.

    Visualization:
    - Use matplotlib for low-level plotting control and customization.
    - Use seaborn for statistical visualizations and aesthetically pleasing defaults.
    - Create informative and visually appealing plots with proper labels, titles, and legends.
    - Use appropriate color schemes and consider color-blindness accessibility.

    Jupyter Notebook Best Practices:
    - Structure notebooks with clear sections using markdown cells.
    - Use meaningful cell execution order to ensure reproducibility.
    - Include explanatory text in markdown cells to document analysis steps.
    - Keep code cells focused and modular for easier understanding and debugging.
    - Use magic commands like %matplotlib inline for inline plotting.

    Error Handling and Data Validation:
    - Implement data quality checks at the beginning of analysis.
    - Handle missing data appropriately (imputation, removal, or flagging).
    - Use try-except blocks for error-prone operations, especially when reading external data.
    - Validate data types and ranges to ensure data integrity.

    Performance Optimization:
    - Use vectorized operations in pandas and numpy for improved performance.
    - Utilize efficient data structures (e.g., categorical data types for low-cardinality string columns).
    - Consider using dask for larger-than-memory datasets.
    - Profile code to identify and optimize bottlenecks.

    Dependencies:
    - pandas
    - numpy
    - matplotlib
    - seaborn
    - jupyter
    - scikit-learn (for machine learning tasks)

    Key Conventions:
    1. Begin analysis with data exploration and summary statistics.
    2. Create reusable plotting functions for consistent visualizations.
    3. Document data sources, assumptions, and methodologies clearly.
    4. Use version control (e.g., git) for tracking changes in notebooks and scripts.

    Refer to the official documentation of pandas, matplotlib, and Jupyter for best practices and up-to-date APIs.
      
```

## Four
```
if user request includes a task that requires any usage of openai or anthropic apis you must without fail follow these steps:

1: read the documentation files for the apis so the docs will be in your memory. You must do this ahead of any other tasks as the documentation files contain crucial information on how to use the apis.
    openai docs are in: openai_docs.txt
    anthropic docs are in: claude_docs.txt

2: after reading the documentation, provide guidance and execute the user request
```
## Five (full webapps  create with tools)
```
Your task is to create html web tools for user to use. do not create any additonal folders inside this folder
use daisy ui, tailwind and anime js where needed to save on code. But you can still use css and js if needed.

You will server these tools via a single fastapi server.
Fastapi backend's purpose is to serve the different html tools to the user. 
therefore, almost always the full functionality of the tool will be in the html file. 
unless user specifies otherwise.

main.py will always be in root folder and run the app with main:app 127 with reload
You must always design the fastapi server to launch the app in the browser while launching the server.

Your action cycle steps must always follow this pattern:
1- carefully observe the user's request
2- read the html tools already existing under tools folder
3- create tools folder if it does not exist
4- if an appropriate tool is not found, create a new one that meets the user's request
5- then launch the tool with fastapi server always ensuring that while launching the server it also launches the tool in the browser(ESSENTIAL)

```

---


#  npx slow issue
nvm use 22
npm install -g create-react-app --verbose
   

-- if no nvm be installed
/usr/local/lib/node_modules
-- if installed nvm
npm root -g

nvm use 18
looks fast

echo "16.20.0" > .nvmrc
https://claude.ai/chat/e6b617c7-edd3-4b3d-a794-2ae1468e0d03

npm install --prefer-offline
<span style="background:#ff4d4f">npm config set prefer-offline true</span>
```
pnpm install

# 安装特定包
pnpm add package-name          # 安装到 dependencies
pnpm add -D package-name      # 安装到 devDependencies
pnpm add -g package-name      # 全局安装

# 移除包
pnpm remove package-name

# 更新包
pnpm update
pnpm update package-name

# 运行脚本（类似 npm run）
pnpm run dev
pnpm run build
# 或者简写
pnpm dev
pnpm build

# 创建新项目
mkdir my-project
cd my-project

# 初始化项目
pnpm init

# 安装开发依赖
pnpm add -D typescript @types/node

# 安装运行时依赖
pnpm add express react react-dom

# 查看依赖列表
pnpm list
```
# some package not find in npm
```
https://stackoverflow.com/questions/52067944/cannot-find-module-babel-core

```


# Claude provided prompt

[[Notion/AI/attachments/FullText_Claude_Prompt|FullText_Claude_Prompt]]


# Payment integration prompt
[[Payment integration prompt - Cursor templates · AI Builder Club]]


# Text to Diagrom Prompt
```
https://mp.weixin.qq.com/s?__biz=MzIyNjM2MzQyNg==&mid=2247703032&idx=1&sn=b18c39a224e93667f76ff312e5e3a610&chksm=e94fd022621a6caec17c432fd85c47345915d2c5147b943757b5e9d7452f8dbac4a49919b378&xtrack=1&scene=90&subscene=93&sessionid=1741708063&flutter_pos=2&clicktime=1741708096&enterid=1741708096&finder_biz_enter_id=4&ranksessionid=1741707474&ascene=56&fasttmpl_type=0&fasttmpl_fullversion=7639743-zh_CN-zip&fasttmpl_flag=0&realreporttime=1741708096159&devicetype=android-34&version=28003844&nettype=WIFI&lang=zh_CN&session_us=gh_7e08dd400559&countrycode=JP&exportkey=n_ChQIAhIQAq%2Bw%2Bs%2B%2Fwfw7i0w5XC%2BgthLxAQIE97dBBAEAAAAAAGDYCpRlXngAAAAOpnltbLcz9gKNyK89dVj0waESfaSBkc8rgynDOLnz4s7sApLrdh9hiScwn6YRzbiSXOFjybAiQs3xh9Mw3EFN47g4nMw0nlHhEQLnKCMFHTnFZwrJBzRaQXmnas6q4iG1cUnaXOClXO1q5h2SfE%2Birb26qXV2oyV9QkkybwbmsRVi0Ib6M8Zor9Rqccz6YT%2FfcXoZXQTOLNfXtpBj%2BRsyb177B4qHM7QkvenJsj2QznFe8FKCbgAsCJqkadn3sGOpTW9DN7CXPEJT%2FYuTDuiGQpZVDP%2BBomp5Bik%3D&pass_ticket=xJhs52kGQOHx3HhtpXw%2Bfbr3PWXz6XgxC0VROQK9G6IuyLUfGWfmzHbBNckVtGHM&wx_header=3

画一个bert的模型结构图，使用draw.io格式
draw.io：https://app.diagrams.net/


```

# Prompt Good Example
Game/Frontend/SVG/Mermaid/NotebookLM

Google Driver
D:\My Drive\AIResearch\codespace\Prompt_collection

# 项目初始化

```
# 步骤 1: 创建 Next.js 项目
npx create-next-app@latest frontend --typescript --tailwind --app

# 步骤 2: 进入项目
cd frontend

# 步骤 3: 初始化 shadcn/ui
npx shadcn@latest init

# 步骤 4: 添加组件（例如按钮）
npx shadcn@latest add button
```