---
Updated: 2023-05-14T09:44
tags:
  - AI->-Embedding
  - AI->-LangChain
  - AI->-Programming
Created: 2023-05-14T08:50
---
![[v2-6b6827c946fdacc1e23df8056589b1cf_1440w.jpg]]

> 一个基于AI-based的系统设计初探
## 一个普遍的困惑
在使用ChatGPT处理长文本时（比如梳理摘要、翻译，信息提取），我们经常会因为它的4k字数限制而望而却步。相信大部分人都想过这样的问题，要怎么喂数据给ChatGPT训练，或者是要怎么把文件一次性喂给他读。没有使用经验的小白，大概率会像下面这个小编一样使用：
![[v2-46ee230f6ea59af7a33e81f5361b0dd1_720w.webp]]
截图来源：https://mp.weixin.qq.com/s/gGq5T3jTsw7ux4AbSkw0eQ
我来重述一下，到底发生了什么？
这个小编需要将三段访谈记录，总共3万字，整理成一篇完整的文章。原本期待通过ChatGPT，将访谈记录交给它，在短时间内得到一篇完整的稿子。但由于ChatGPT的消化能力有限，无法一次性将全部文字输入。因此不得不将访谈记录拆分成每次1000字的量，逐一输入。同时，由于ChatGPT对输入内容要求具备上下文连续的逻辑，ta还需要对这些拆分后的文字进行初步处理。这一过程耗费了15个小时，让Ta感到极度不满和痛苦。
这个问题可以用ChatGPT整理成如下用户故事：

> 作为一名撰稿人，我希望能够借助ChatGPT轻松地将访谈记录整理成一篇完整的文章，而不是耗费大量时间进行拆分、处理和输入。我需要一个更智能、更高效的工具来帮助我完成这项工作，提高我的工作效率。
上面是关于整理访谈记录场景下的特化版本。很多场景都是类似的，比如让他翻译一大段文本，比如让它读一大堆论文，比如让它读大量法律文件和案例，比如，让它读公司近万字的产品文档，再比如，让它读历届的语文高考题目。这些比如，都是一个个发生在我周边学员使用的真实需求。
所以我还是习惯，把这个问题泛化，给出更进一步的通用解决方案。
反三还得举一，我们先举一个记者的工作流为例
## 一个记者的示例
1. 首先，他要把查阅各种资料，获取初步的结构化知识
2. 然后他需要把这些知识整理成一个采访提纲，再进行访谈
3. 得到访谈内容后，他还需要从录音稿中提炼出采访要点
4. 与此同时，他还需要构建一个故事结构，进行故事的写作和校对编辑，最终文章成型
当你把一个复杂任务拆分成了一个工作流的任务节点，你会发现，每一个环节其实都可以用来ChatGPT来完成。
假设我们「 ChatGPT 支持输入超长文本和多个文件」这一条件成立，我们可以
1. 让 ChatGPT读取我们收集的一大堆文件或网页，直接梳理出结构化的知识
2. 让ChatGPT 基于结构化的知识，分别输出采访提纲和故事结构
3. 让ChatGPT 基于长达几小时几万字的访谈内容，提炼出采访要点
4. 让ChatGPT 基于故事结构、采访要点和结构化知识，写出很长的初稿，再修改编辑成故事终稿
这是多么令人兴奋的事情！那我们要做的事情就非常确定了！往下需要设计的就是细节
1. 用户要以什么样的形式输入？
2. 如何让ChatGPT输入多篇内容？用户多次会话，也依然保持稳定的长期记忆？
3. ChatGPT要如何输出回复用户，让用户确信它不是在胡编乱造？
4. 如何做风险审核，以防用户输入了一些奇怪的文件？
5. 如何让输入的形式更丰富？支持音频，视频，图表？
6. 实现以上这一切如何不用训练，支持即插即用？
## 一个交互的开始
假设ChatGPT能输入很长文本，我们要怎么设计这个输入的交互？最简单的方式是先找找市面上已有的，做个产品调研。我看到了有三种方式：
第一种方式是，有一个上传的文件，支持.pdf的上传输入，也支持它们的下载url。上传完成后，后台服务端根据文件名后缀来判断要调用什么文件解析工具进行解析。代表有：[chatpaper](https://link.zhihu.com/?target=https%3A//chatpaper.org/)，[chatpdf](https://link.zhihu.com/?target=https%3A//www.chatpdf.com/)，和 [pandasGPT](https://link.zhihu.com/?target=https%3A//app.pandagpt.io/chat)
第二种方式是，有一个链接的输入框，你输入链接后可以解析网页，提取出字幕文件的文本。代表有：[BiliGPT](https://link.zhihu.com/?target=https%3A//b.jimmylv.cn/%23)
第三种则干脆做成了浏览器插件，在你上网的时候，可以直接随时随地快捷调用 ChatGPT 帮你总结，比如[ChatGPTBox](https://link.zhihu.com/?target=https%3A//chrome.google.com/webstore/detail/chatgptbox/********************************)、[Glarity](https://link.zhihu.com/?target=https%3A//glarity.app/en)
三种方式优势和局限整理如下：
|   |   |   |   |
|---|---|---|---|
|输入方式|代表产品|优势|局限|
|文件上传|chatpaper，chatpdf，和 pandasGPT|能直接读文件|遇到网页需要转换为文件后再上传文件的格式受限|
|输入指定视频页读字幕|BiliGPT|能直接总结视频|只支持指定的网站只支持字幕，没有评论|
|网页插件|ChatGPTBox、Glarity|能随时随地在浏览网页的时候做总结翻译和润色|信息碎片化没有汇总整理每次单个session，不支持长内容|
我们的需求是，能否嵌入在聊天窗口中，像飞书微信聊天那样，你发一个链接或文件，能够持久化地存放在当前的消息会话中。为什么要这样设计？因为这样
1. 更嵌入到我们的工作场景，符合我们的使用习惯。我们的链接和文件就是这样发送分享的。和我们在微信上给一个朋友发消息感觉一样。没有任何使用习惯上的迁移成本。
2. 我们可以快速mention其中一个文件来做chatPaper或chatPDF做的问答事情。当有多篇文件时，这样的指代就相当于搜索加了筛选项。
3. 多文件场景下，模型还可以回复和指代具体的链接或文件，像我们人一样mention。
4. 我们还可以支持多篇不同类型的内容混合输入。更多类型的媒体也容易支持，比如文字，链接，音频，图片，视频等等。
5. 系统能自动识别我们的状态。
    1. 在没有明确指令的时候，我们随便丢内容给聊天框，系统可以不用回复，只负责处理解析和储存到长期记忆中的事。
    2. 当有明确指令时，系统会根据当前聊天会话框中有的内容进行回答。
    3. 当指令和url混合时，系统能自动抽取输入的指令，问题和url进行处理。
6. 如果要做成网页插件，也可以变成一个类似于客服页面的聊天窗口，随时调出把看到的网页内容丢到聊天窗口里去
## 一个产品的体验
市面上有没有我描述的这样的产品呢？四处搜寻，还真的被我找到了：[filechat](https://link.zhihu.com/?target=https%3A//www.filechat.io/)
先来看它的能输入文件的类型，包括了直接的文件上传和url
支持聊天创建的创建，且多个文档输入
以及用对话的方式进行文档的梳理。
一切看起来前景很光明，似乎我不用自己设计产品了，因为市面上已经有了符合我一开始预期的。于是我迫不及待地想要上手一试。先随便丢了个文档给它。正好前段时间写了一篇[openai官方《提示词工程课》超详细中文笔记](https://link.zhihu.com/?target=https%3A//ec26ubh65w.feishu.cn/docx/PuULdQP3wojyZYxn157cnsDXnqe) ，看看它能不能看文档回答我的问题。
看下面的结果还行，但仔细一想，有点不大对劲。为什么输出这么少？于是我又深入地体验了一下。
看下面，发现机器人在和你打太极，基本完全不能用的状态。也就是说，chafiles它确实有了我之前说的好的产品概念，但技术质量上，却很不行。完全不能解决我们的问题。
## 一个技术的优化
我仔细看了它的传参和返回
好家伙，可以看到问题出在对中文文本的处理上。毕竟是国外的软件，也可以理解。于是我换了一篇外文的论文，并尝试用英文问它问题。
[arxiv.org/pdf/2304.1429](https://link.zhihu.com/?target=https%3A//arxiv.org/pdf/2304.14293.pdf)
发现效果依然很差，回答很空泛，仍有幻觉问题。具体细节不清楚，这是因为，它使用的技术是openai的嵌入质量。我们需要改进嵌入技术（比如说用谷歌的`universal-sentence-encoder`）来提高真实性并避免幻觉。
## 一个DEMO的实现
参考这个[repo](https://link.zhihu.com/?target=https%3A//github.com/bhaskatripathi/pdfGPT/blob/main/app.py), 我打算先快速做一个简单的demo，再提出一些可以优化的点，方便后续可以小步迭代。
### 1 输入输出
首先我们定义系统的输入输出。输入包括了可选上传文件或url，和输入的问题。输入包括了问题和指令。
可改进优化的点：
1. 支持问题和url混合输入
2. 把问答形式，变成可以多轮聊天对话形式
3. 上传文件可以和对话解耦
4. 新增创建聊天窗口功能
### 2 主流程
接着我们写 `question_answer` 的部分，定义好主流程。主流程包含了两个模块，一个是检索器，负责将长文本内容进行分片，向量和建立索引。第二个是问答系统，负责从向量数据库中检索后，用LLM的能力去生成给用户的回复。
可以改进优化的点
1. 支持更多url类型的下载文本解析
2. 支持更多的文件类型
### 3 文件解析和分块
这一部分是预处理阶段。比如如果输入是url，我们需要先对它进行下载再进行解析。然后对输入的pdf进行文本的抽取，再分块。
可以改进和优化的点
1. 支持更鲁棒的文本预处理方式，多语言
2. 更好的文本分块方式
    1. 有重叠的分块
    2. 按语义连续型分块
    3. 支持网页中多媒体的解析和分块
3. 分块引入元数据
    1. 创建时间
    2. 所在段落
    3. 文档id
    4. 文档来源
    5. 类型
    6. 文档标题，副标题，目录等字段
    7. 创建时间，修改时间等时效性字段
    8. ...
4. 如果是url，文档更新了要怎么处理，如何确保时效性
### 4 文本向量化和检索
有了分块。我们就可以把这些文本进行语义向量化。下面的例子用的是谷歌的[`Universal-Sentence-Encoder`](https://link.zhihu.com/?target=https%3A//www.tensorflow.org/hub/tutorials/semantic_similarity_with_tf_hub_universal_encoder)。要支持多语言也可以用[`Multilingual-Universal-Sentence-encoder`](https://link.zhihu.com/?target=https%3A//www.tensorflow.org/hub/tutorials/cross_lingual_similarity_with_tf_hub_multilingual_universal_encoder)。附录里提到了关于向量化模型工具的评测和候选。向量化之后，我们就可以计算问题/指令向量和文本嵌入向量之间的距离，来进行检索召回和排序。
一些改进的点
1. 用更前沿表征更好的向量嵌入模型
2. 用性能更好的向量检索库
3. 支持多模态的向量化，比如图像文本表格等统一表征
### 5 回复生成
写提示词模板，把tok-k召回的答案文本和问题文本交给LLM去生成回答。这里用的是`chatGPT`，要确保离线使用，也可以用`ChatGLM-6B`或者[`HuggingGPT`](https://link.zhihu.com/?target=https%3A//huggingface.co/spaces/microsoft/HuggingGPT)这类的开源模型。
一些改进的方向：
1. 要保证结果的精确，将温度temperature尽可能调低，最大token调高
2. 加入问题指令检测和兜底，在用户只输入url或文件，不进行提问和给指令时，保持静默
3. 使用离线模型。使用在线模型在有很多局限，比如：
    1. 网络延迟，网络环境不稳定
    2. 使用API带来了额外的成本。随着请求次数和数据量的增加，费用也在攀升，API对请求速率和并发请求数量进行了限制
    3. 数据隐私问题，将数据发送给API意味着数据在服务器之间传输，可能导致数据泄露，特别是在处理敏感信息的时候。
    4. 定制化的限制，API提供的预训练模型可能无法满足我们某些特定应用的需求，使用本地embedding模型更方便地进行调整和定制。
### 6 梳理整合
通过上面步骤，我们完成了让chatGPT突破4k字数限制的核心技术，基本能完成一个可用的对话文件/网页的应用了。往下更多的是产品细节的打磨，具体改进方向都写在每个模块了。
1. chatfiles为我们提供了现有成熟的产品界面和交互逻辑。我们在此基础上换皮或者UI改善就行。
2. mention其中一个文件来做chatPaper或chatPDF做的事情，可以通过在向量检索库中加入元数据实现。指定检索出mention的文档id进行召回和排序就可以了。
3. 多篇不同类型的内容的混合输入，只需要在文件解析和分块那，针对不同的特化逻辑使用不同的解析文件的包就好了。
4. 在没有明确指令的时候，我们随便丢内容给聊天框，模型可以不用回复，只负责处理解析和储存到长期记忆中的事。加一个问题指令的规则检测就好了。如果有文本指令+url的混合输入，可以写一个信息抽取的提示词模板，让chatGPT把问题，指令和url分别抽取出来后，再进行下一部分的判断。
提示词模板如下
5. 聊天窗的设计，我们只需要定义好，向量数据库会将同一个聊天窗下的所有文件都做向量索引，并包含了元数据。我们提问和提出指令的时候，它逻辑和问答一样，会先在这些文档里语义搜索，取top-k，再给到chatGPT生成回复
6. 更多类型的媒体也容易支持，比如文字，链接，音频，图片，视频等等。我们依然可以利用chatGPT的信息抽取能力，帮我们识别好意图和状态，根据不同的状态去调用不同的解析工具就行。
## 一个AI-based的升级
在 [AI-Based 应用是如何取代现有软件的？](https://zhuanlan.zhihu.com/p/626016413)我们说过，未来的应用是基于理解的。也就是用户任意输入，模型都能很鲁棒地回应用户的预期。我也小试牛刀了一下，基于SPQA的软件设计架构将上面的改进点进行重新设计。
假设我们现在这个能和任意多网页/文件对话的应用是基于AI-based，那相当于我们要设计一个Agent来处理我们上面的复杂业务逻辑。这个Agent首先能识别聊天窗中用户输入的意图，并标记为不同的`状态/State`，根据我们预先设定好的`策略/Policy`，去提出`问题/Question` （我是要选择工具？还是要回复用户？），最后执行策略中描述的`行动/Action`。
### 状态 / State
其实状态是写一个意图识别器prompt模板，我们需要把用户的任意输入都抽取成一个结构化的数据。我们只需要在之前意图识别的prompt模板上稍作改造就可以了。
我们来测试一下效果
看起来表现很好。有时格式会不同，都是temperature没调小和后处理的小问题。我们可以进入下一个环节的设计。
### 策略 / Policy
策略部分主要是定义根据什么状态做什么的规则。它作为一个配置文件会和状态一起输入，结合行动选择器，输出一个选择行动的json结构体。
### 问题 / Question
这部分核心在行动选择器，agent要问自己，根据当前状态和策略，我要选择什么行动？
效果如下
### 行动 / Action
这一部分的输入是`问题/Question` 的输出json结构体。我们向外提供一个action的接口，根据json中的字段描述去
选择具体要执行的任务，再执行响应的API就可以得到答案。这里是一个简化版本，我们可以让chatGPT去生成它们的函数签名。当然每一个动作下面的任务函数，我们也可以提前封装好。根据json中定义的进行调用。
## 一个未尽的总结
转换为AI-based架构后，我突然有种恍然大悟的感觉。对于任何开发上的变更删减，被简化成为了以下两个步骤：
1. 在策略/Policy中更新规则
2. 在行动/Action中注册规则中涉及到的任务函数
比如说，我要增加一个对视频画面进行理解的功能，实际上我需要做的无非是
1. 在策略/Policy中把最后一行的`视频，使用「视频字幕」`更新为`视频，使用「视频字幕」和「视频理解」`
2. 在Action中注册一个视频理解API调用函数，让它的输入输出保持和当前的框架一致。
而这两步，实现起来会非常快捷，老板一提需求，几分钟就能完成做好并发布上线。而且容错率高很鲁棒。
我们依然有很多的细节可以继续完善
1. 比如更追求极致地把长期记忆的理解性能效果做上去。可以参考[SCM4LLMs](https://link.zhihu.com/?target=https%3A//github.com/toufunao/SCM4LLMs)论文的思路。我们还可以让agent对我们聊天窗中内容的时效性有更拟人化的感知。
2. 我们还可以像[`AudioGPT`](https://link.zhihu.com/?target=https%3A//github.com/AIGC-Audio/AudioGPT)那样把更多的任务和工具接入到对话过程中，并支持更多样的输出
3. 还可以把这个聊天框做的插件化，嵌入在windows或ios或移动端或浏览器中，随时能调用。
4. 关于审核部分，可以参考[如何用ChatGPT做内容安全审核？](https://zhuanlan.zhihu.com/p/625934962)独立做一个agent
5. 基于这个框架，我们还可以在这基础上像openai那样设计出即插即用的插件。
想到这里，我不由地越来越坚信以chatGPT为主的大语言模型设计出的AI-based的应用会完全颠覆当前的软件生态。
## 一个宝藏的附录
### 网页结构化
1. [AwesomeWebScrapting](https://link.zhihu.com/?target=https%3A//github.com/Germey/AwesomeWebScraping/blob/main/python.md)
### 语音转录
1. [whisper](https://link.zhihu.com/?target=https%3A//openai.com/blog/introducing-chatgpt-and-whisper-apis)
### 图像识别文字
1. PaddleOCR使用说明：[PaddleOCR/quickstart.md at release/2.6 · PaddlePaddle/PaddleOCR · GitHub](https://link.zhihu.com/?target=https%3A//github.com/PaddlePaddle/PaddleOCR/blob/release/2.6/ppstructure/docs/quickstart.md)
2. PP-StructureV2使用说明：[PaddleOCR/PP-StructureV2_introduction.md at release/2.6 · PaddlePaddle/PaddleOCR](https://link.zhihu.com/?target=https%3A//github.com/PaddlePaddle/PaddleOCR/blob/release/2.6/ppstructure/docs/PP-StructureV2_introduction.md)
3. [PaddleOCR/ppstructure at release/2.6 · PaddlePaddle/PaddleOCR](https://link.zhihu.com/?target=https%3A//github.com/PaddlePaddle/PaddleOCR/tree/release/2.6/ppstructure)
### 图像描述工具
1. [GitHub - Vision-CAIR/MiniGPT-4: MiniGPT-4: Enhancing Vision-language Understanding with Advanced Lar](https://link.zhihu.com/?target=https%3A//github.com/Vision-CAIR/MiniGPT-4)
2. [scenex.jina.ai](https://link.zhihu.com/?target=http%3A//scenex.jina.ai/)
3. [midjourney](https://link.zhihu.com/?target=https%3A//www.midjourney.com/app/)
### 文档解析
1. [Python 超强大的PDF表格提取器 — Camelot](https://link.zhihu.com/?target=https%3A//blog.csdn.net/cqcre/article/details/128895284)
2. [三行 Python 代码提取 PDF 表格数据](https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzIwMDU0MTEzNA%3D%3D%26mid%3D2648058694%26idx%3D1%26sn%3De7e0a9706b67b594b9c0d28f8d576e98%26chksm%3D8ed91de7b9ae94f1de8f03986b0c459bb66f0d3a8a52e5e65fbdcdb7f3d4d95cf47dfa543849%26scene%3D27)
3. [Python解析PDF表格——PDFPlumber vs Camelot](https://link.zhihu.com/?target=https%3A//www.jianshu.com/p/dbce34122c2b)
4. [python-docx](https://link.zhihu.com/?target=https%3A//python-docx.readthedocs.io/en/latest/index.html%23)
5. [一键PDF转Word，PP-Structurev2文档分析模型深度解读！](https://link.zhihu.com/?target=https%3A//baijiahao.baidu.com/s%3Fid%3D1742117051391039096%26wfr%3Dspider%26for%3Dpc)
6. [Johnson7788：PP-StructureV2: 一个更强大的文件分析系统](https://zhuanlan.zhihu.com/p/577540749)
7. [文档智能利器 —— 文心ERNIE-Layout](https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzI4NzYzMTYwNw%3D%3D%26mid%3D2247494250%26idx%3D1%26sn%3D150d496354cd254834520b7ef27c6454%26chksm%3Debc81bf2dcbf92e4ab15a9a39e7ffd88b6ebd804b6377dbee4a74a71f2b6904d6c79b23cc2e1%26scene%3D27)
8. [Python：解析PDF文本及表格——pdfminer、tabula、pdfplumber 的用法及对比](https://link.zhihu.com/?target=https%3A//www.cnblogs.com/jhhh/p/16764629.html)
9. [github.com/DS3Lab/DocPa](https://link.zhihu.com/?target=https%3A//github.com/DS3Lab/DocParser)
10. [pydocparser文档解析工具](https://link.zhihu.com/?target=https%3A//github.com/stautonico/PyDocParser)
11. [文档图像分类、信息提取、信息结构化之 LayoutLM、LayoutLMv2、LayoutXLM —— 论文阅读笔记](https://link.zhihu.com/?target=https%3A//blog.csdn.net/m0_38007695/article/details/115803829)
### 视频字幕提取
1. [利用Python提取视频中的字幕（文字识别）](https://zhuanlan.zhihu.com/p/136264493)
### 文本向量化工具
1. [LangChain - 打造自己的GPT（五）拥有本地高效、安全的Sentence Embeddings For Chinese & English](https://zhuanlan.zhihu.com/p/622017658)
2. [huggingface.co/spaces/m](https://link.zhihu.com/?target=https%3A//huggingface.co/spaces/mteb/leaderboard)
3. [GitHub - imClumsyPanda/langchain-ChatGLM: langchain-ChatGLM, local knowledge based ChatGLM with lang](https://link.zhihu.com/?target=https%3A//github.com/imClumsyPanda/langchain-ChatGLM)向量检索库
- [milvus.io/](https://link.zhihu.com/?target=https%3A//milvus.io/)
### 鼠标插件工具