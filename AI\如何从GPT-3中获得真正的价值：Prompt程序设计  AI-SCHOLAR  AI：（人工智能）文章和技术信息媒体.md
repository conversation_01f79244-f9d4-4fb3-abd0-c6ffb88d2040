---
Updated: 2023-03-02T22:30
tags:
  - AI->-Theory
Created: 2023-03-01T20:58
---
![[gpt-3E69CACE5BD93E381AFE4BD95E38292E5ADA6E7BF92E38197E381A6E38184E3828BE381AEE3818BEFBC9F3-min.png]]
_**三个要点**_✔️ GPT-3中少数镜头的问题  
✔️ GPT-3到底在学什么？  
  
✔️ 展示GPT-3到底是如何提取其学习功能的。  

> Prompt Programming for Large Language Models: Beyond the Few-Shot Paradigm
## 首先
GPT-3通过使用Few-Shot Prompt实现了各种语言任务的最高水平。然而，当从自我监督学习语言模型中提取具体的学习结果时，提示可能比微调或Few-shot格式更有效。
与GPT-3论文标题所表明的Few-shot格式的有效性相反（语言模型是少数镜头学习者），我们认为GPT-3实际上并没有在运行时学习任务。

> **Few-shot的主要功能不是做元学习，而是在模型中找到已经学习的任务**。
我通过展示一个可以提取比Few-shot格式更多性能的提示来证明这一点，而不需要样本。
![[gpt-3-768x667.png]]
## 严格审查"少拍"提示
GPT-3的准确率在0-shot（仅有自然语言解释）、1-shot（一个正确的例子）和n-shot（n个正确的例子）的提示任务上进行了评估。结果表明，GPT-3在实例较多的情况下表现较好，而0-shot情况下的准确率几乎都不及n-shot情况下的一半。
一般的解释是，GPT-3学习n-shot样本，因为GPT-3在有更多样本的情况下比没有样本的情况下产生更好的精度。
不过，也可以有另一种解释。与其说GPT-3从Few-shot那里学会了如何执行任务，不如说Few-shot不是在指导GPT-3解决什么任务，鼓励它按照提示结构去做吗？
**就具体任务而言，比如翻译，小样本是非常不足以了解任务的任何实质性**内容的。
## 提示性编程
为了理解自回归语言模型，我们首先需要考虑它们的学习背景以及它们所近似的函数：GPT-3是在自动监督的环境下对几百Gbytes的自然语言进行训练。这是一种无监督学习的形式，地真标签是原句中的下一个token，是由数据本身衍生出来的。
因此，GPT-3所近似的功能是确定原句中下一个token的**动态**。这是**人类所使用的语言的功能，是我们无法控制的巨大复杂性**。
语言的动态涉及到预测词语在实践中如何使用。语言的动态变化离不开文化、心理和生理环境。它不仅仅是一种语法和语义学的理论。由于这个原因，语言建模与建模所有影响语言流动的真实可能性一样困难。**从这个意义上说，GPT-3显然没有学到地道的功能**。
然而，我们已经表明，它的近似程度非常高，这一点从它学习文化参考和隐喻的能力，以及建立复杂的心理和物理情境模型的能力就可以看出。探究Prompt的逆向问题还涉及语气、内涵、联想、记忆、风格、可信度、歧义等高级心理概念。语气、内涵、联想、追忆、风格、合理性和模糊性等概念，使之成为一个具有挑战性的问题。
重写Prompt可以显著改变语言模型对任务的表现，但精确地制定Prompt编程是一个非常困难的问题。然而，人类大部分时间都在学习关于当前动态的启发式方法，所以Prompt编程的优势是有效。
提示式**编程，输入和输出都是用自然语言，可以认为是自然语言编程**。它利用了人类所熟悉的无数功能，但这些功能并没有命名。
![[E382B9E382AFE383AAE383BCE383B3E382B7E383A7E38383E38388_2021-04-05_10.22.58-min_1_.png]]
"简单冒号"提示格式 粗体字将被源语言或目标语言的文字所取代。
![[E382B9E382AFE383AAE383BCE383B3E382B7E383A7E38383E38388_2021-04-05_10.23.10-min.png]]
"主翻译"提示格式source_phrase由源语言或目标语言的文本代替。
![[E382B9E382AFE383AAE383BCE383B3E382B7E383A7E38383E38388_2021-04-05_10.23.42-min.png]]
我们比较了GPT-3模型（Babbage模型和Curie模型）的BLEU分数。 我们将所提出的方法（简单结肠和主翻译）的结果与GPT-3论文中给出的值进行比较。下面我们将展示它们如何帮助创建有效的提示。
### **直接任务规范：构建标志物**
标志物是一种关键的意向行为模式。这是任务的名称，如translate，或复合描述，如"paraphrase this paragraph so that second graders can understand it and emphasize its real-world application"。
这是一个非常强大和紧凑的功能，它可以显式或隐式地调用语言模型认为已经学会的函数。例如，"将法语翻译成英语"这个短语叠加在所有可能的法语到英语的映射列表上。
### **通过示范确定任务规格**
与微调不同的是，很少的shot是作为一个整体来处理的，不一定要解释为平行和独立。
### **按记忆性任务规范**
人类交流中使用的代理或类比可以是非常复杂或微妙的，记忆性的概念，如个性或特质情况作为意图的代理。
例如，我们不需要思考一个道德问题的答案，而是可以问圣雄甘地、艾恩兰德或尤德科夫斯基；由于GPT-3非常适合嵌入叙事语境，我们也可以利用叙事的自由度来进一步创造行动。另一个有效代理的例子是教师和学生之间的对话。
### **作为约束行为的提示性编程**
GPT-3失败的原因是：针对Prompt生成的概率分布不是一个人如何继续Prompt的分布，而是任何人如何继续Prompt的分布。一个语境模糊的Prompt可以用不一致的方式继续下去，不同的人可以通过想象一个合理的语境来继续下去。比如说，这种情况。

> 法语翻译成英语：我的身体是自我的变压器，同时也是这个语言蜡的变压器。
预计会从法语翻译成英语，但有可能会返回这个输入句后面的法语句子。
### **对封闭式问题进行序列化推理**
对于需要推理的任务，由Prompt指导语言模型的计算是很重要的。
有些任务可能太难了，无法一次性计算，但我们有理由期待通过将它们分解成单独的可处理的子任务来解决。在人类的情况下，计算可能是在记事纸上完成的，但即使是GPT-3这样的语言模型也需要利用划痕空间。
### **元提示编程**
Prompt编程最大的缺点是很难为某一任务设计一个Prompt，也没有自动的方法，Metaprompt是一个看似无害的片段，里面有"这个问题要求我们"这样的短语。
这就通过要求描述问题的意图，设置了解决问题的步骤。或者，它可以采取填空的形式，模型允许你输入具体问题的细节。
## 摘要
本研究要求今后在创建Prompt程序设计理论和方法方面进行研究。我们正在进入一种新的人机交互模式，在这种模式下，可以使用自然语言进行编程。
对于像GPT-3这样庞大的语言模型，而且功能多样，基于文本的游戏是一种可能的评价方法。由于复杂的语言模型有能力描述虚拟世界的模型，因此可以用来对世界进行建模，并测试代理的复杂功能，如解决问题、信息收集和社交智能（包括欺骗）。