---
DocFlag:
  - Reference
  - Tested
Updated: 2023-03-23T22:28
tags:
  - AI->-Chatbot
  - AI->-VR-Human
Created: 2023-02-12T21:36
---
[https://aistudio.baidu.com/aistudio/projectdetail/5490717?forkThirdPart=1](https://aistudio.baidu.com/aistudio/projectdetail/5490717?forkThirdPart=1)
[https://aistudio.baidu.com/aistudio/projectdetail/5003396?ad-from=3648](https://aistudio.baidu.com/aistudio/projectdetail/5003396?ad-from=3648)
[http://t.csdn.cn/AUsy1](http://t.csdn.cn/AUsy1)
[https://computingforgeeks.com/install-use-ffmpeg-on-rocky-alma-9/](https://computingforgeeks.com/install-use-ffmpeg-on-rocky-alma-9/)
开源一
[https://github.com/PaddlePaddle/PaddleGAN](https://github.com/PaddlePaddle/PaddleGAN)
[https://github.com/PaddlePaddle/PaddleSpeech#quick-start](https://github.com/PaddlePaddle/PaddleSpeech#quick-start)
开源二
[https://developer.aliyun.com/article/860504](https://developer.aliyun.com/article/860504)
https://hyperonline.notion.site/Vtubing-with-Hyper-s-OBS-Plugin-for-Windows-********************************
https://www.aivo.co/blog/best-practices-for-developing-conversational-ai-video-avatar
---
Setup
python3 -m pip install --upgrade pip
git clone [https://gitee.com/xiejiehang/PaddleBoBo.git](https://gitee.com/xiejiehang/PaddleBoBo.git) -b develop
pip install common
pip install  dual
pip install tight
pip install data
pip install prox
pip install paddle
pip install paddlepaddle
pip install ppgan paddlespeech
```Plain
pywebrtcvad.c:1:10: fatal error: Python.h: No such file or directory
× Encountered error while trying to install package.
╰─> webrtcvad

dnf install python3-devel

```
---
\#generate video from static graph
cd /opt/install/ai/PaddleBoBo
```Plain
[root@MONSTER:/opt/install/ai/PaddleBoBo]# cat default.yaml
GANDRIVING:
  FOM_INPUT_IMAGE: './file/input/actress3.png'
  FOM_DRIVING_VIDEO: './file/input/zimeng.mp4'
  FOM_OUTPUT_VIDEO: './file/input/actress3.mp4'

TTS:
  SPEED: 1.0
  PITCH: 1.0
  ENERGY: 1.0

SAVEPATH:
  VIDEO_SAVE_PATH: './file/output/video/'
  AUDIO_SAVE_PATH: './file/output/audio/'
```
python create_virtual_[human.py](http://human.py/) --config default.yaml
\#genereate audit from text and merge video and audit into one
python general_demo.py --human ./file/input/actress3.mp4 --output ../actress3.mp4 --text 你好，盛钧白是把我
python general_demo.py --human ./file/input/actress3.mp4 --output ../actress3.mp4 --text 你好，是盛钧白把我
---
必须按照FFmpeg
https://computingforgeeks.com/install-use-ffmpeg-on-rocky-alma-9/
```Plain
dnf config-manager --set-enabled crb
dnf install --nogpgcheck https://mirrors.rpmfusion.org/free/el/rpmfusion-free-release-$(rpm -E %rhel).noarch.rpm -y
dnf install --nogpgcheck https://mirrors.rpmfusion.org/nonfree/el/rpmfusion-nonfree-release-$(rpm -E %rhel).noarch.rpm -y
dnf install ffmpeg ffmpeg-devel
ffmpeg -version
```
---
```Plain
python general_demo.py --human ./file/input/test.mp4 --output ../output.mp4 --text 各位开发者大家好，我是您的专属虚拟主播，很高兴能为您服务。

\#Got issues below
=============================================================================================================
Traceback (most recent call last):
  File "/mnt/f/ResearchDirection/wsl/ai/PaddleBoBo/general_demo.py", line 6, in <module>
    from PaddleTools.TTS import TTSExecutor
  File "/mnt/f/ResearchDirection/wsl/ai/PaddleBoBo/PaddleTools/TTS.py", line 11, in <module>
    from paddlespeech.cli.utils import MODEL_HOME
ImportError: cannot import name 'MODEL_HOME' from 'paddlespeech.cli.utils' (/usr/local/lib/python3.9/site-packages/paddlespeech/cli/utils.py)

vi ./PaddleTools/TTS.py
from paddlespeech.utils.env import MODEL_HOME
\#from paddlespeech.cli.utils import MODEL_HOME
=============================================================================================================
  File "/usr/local/lib64/python3.9/site-packages/numba/np/ufunc/decorators.py", line 3, in <module>
    from numba.np.ufunc import _internal
SystemError: initialization of _internal failed without raising an exception

```
---
Audio2Face
[https://www.nvidia.com/en-us/omniverse/apps/audio2face/](https://www.nvidia.com/en-us/omniverse/apps/audio2face/)
[https://github.com/FACEGOOD/Audio2Face](https://github.com/FACEGOOD/Audio2Face?spm=a2c6h.12873639.article-detail.7.23c873c7Sqsn2G)
"https://www.youtube.com/watch?v=bvT9yg3Uab4"
"[https://www.youtube.com/watch?v=Sxi-GEJKksw](https://www.youtube.com/watch?v=Sxi-GEJKksw)"
https://world.avatary.com/download
---
### D-ID API
[https://d-id.readme.io/reference/get-started](https://d-id.readme.io/reference/get-started)
```Plain
pip install did-sdk-python
import did.client


client = did.client.DIDClient(api_key="your_api_key")
with open("path/to/your/image.jpg", "rb") as image_file:
    image_bytes = image_file.read()
    deidentified_image_bytes = client.images.deidentify(image_bytes)


import did
import cv2
import numpy as np
# Create a client object with your API key
client = did.Client("<your API key>")
# Anonymize an image
image = open("path/to/image.jpg", "rb")
result = client.anonymize_images([image], level="medium")
anonymized_image = result["output"]["images"][0]["data"]

# Convert the base64-encoded data to a numpy array
anonymized_video_np = np.frombuffer(anonymized_image, np.uint8)
# Decode the video data to an OpenCV video object
anonymized_video = cv2.imdecode(anonymized_video_np, cv2.IMREAD_COLOR)
# Create a VideoWriter object to write the anonymized video to a file
fourcc = cv2.VideoWriter_fourcc(*"mp4v")  # or use other codec like "XVID"
out = cv2.VideoWriter("path/to/output_file.mp4", fourcc, 30.0, (anonymized_video.shape[1], anonymized_video.shape[0]))
# Write each frame of the anonymized video to the output file
for frame in anonymized_video:
    out.write(frame)
# Release the VideoWriter object
out.release()

```
```Plain
import requests
import json
url = "https://api.d-id.com/face-anonymization"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your API key>"
}
payload = {
    "images": ["<base64-encoded image>"],
    "parameters": {
        "anonymizationLevel": "medium"
    }
}
response = requests.post(url, headers=headers, data=json.dumps(payload))
result = response.json()
if "error" in result:
    print("Error: {}".format(result["error"]))
else:
    anonymized_image = result["output"]["images"][0]["data"]
```
```Plain
import did
import cv2
import numpy as np
# Create a client object with your API key
client = did.Client("<your API key>")
# Open the video file
cap = cv2.VideoCapture("path/to/video_file.mp4")
# Create a VideoWriter object to write the anonymized video to a file
fourcc = cv2.VideoWriter_fourcc(*"mp4v")
out = cv2.VideoWriter("path/to/output_file.mp4", fourcc, 30.0, (int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))))
# Loop over each frame of the video file
while cap.isOpened():
    # Read the next frame from the video file
    ret, frame = cap.read()
    if not ret:
        break
    # Anonymize the current frame
    result = client.anonymize_images([frame], level="medium")
    anonymized_frame = result["output"]["images"][0]["data"]
    # Convert the base64-encoded data to a numpy array
    anonymized_frame_np = np.frombuffer(anonymized_frame, np.uint8)
    # Decode the frame data to an OpenCV image object
    anonymized_frame_img = cv2.imdecode(anonymized_frame_np, cv2.IMREAD_COLOR)
    # Write the anonymized frame to the output file
    out.write(anonymized_frame_img)
# Release the VideoWriter and VideoCapture objects
out.release()
cap.release()
```
```Plain
import did
import cv2
import numpy as np
import time
# Set the input and output file paths
input_file = "path/to/input.txt"
output_file = "path/to/output.mp4"
# Set the D-ID API key
api_key = "<your API key>"
# Set the parameters for the video and anonymization
frame_size = (1280, 720)
fps = 30.0
anonymization_level = "medium"
# Create a client object with your API key
client = did.Client(api_key)
# Open the input text file
with open(input_file, "r") as f:
    # Initialize a list to store the frames of the output video
    video_frames = []
    # Loop over each line in the text file
    for line in f:
        # Strip whitespace from the line
        line = line.strip()
        # Check if the line is a valid image URL
        if line.startswith("http") and (line.endswith(".jpg") or line.endswith(".png")):
            # Download the image from the URL and convert it to a numpy array
            response = requests.get(line)
            image_data = np.frombuffer(response.content, np.uint8)
            image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            # Resize the image to the desired frame size
            frame = cv2.resize(image, frame_size)
            # Anonymize the frame using the D-ID API
            result = client.anonymize_images([frame], level=anonymization_level)
            anonymized_frame = result["output"]["images"][0]["data"]
            # Convert the base64-encoded data to a numpy array and decode it to an OpenCV image
            anonymized_frame_np = np.frombuffer(anonymized_frame, np.uint8)
            anonymized_frame_img = cv2.imdecode(anonymized_frame_np, cv2.IMREAD_COLOR)
            # Add the anonymized frame to the list of video frames
            video_frames.append(anonymized_frame_img)
    # Create a VideoWriter object to write the output video
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    out = cv2.VideoWriter(output_file, fourcc, fps, frame_size)
    # Write each frame of the output video to the VideoWriter object
    for frame in video_frames:
        out.write(frame)
    # Release the VideoWriter object
    out.release()
```
---