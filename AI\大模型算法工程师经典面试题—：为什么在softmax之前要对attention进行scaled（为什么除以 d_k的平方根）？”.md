---
Updated: 2024-08-29T17:57
tags:
  - AI->-Theory
Created: 2024-08-29T17:56
---
# ==NLP 经典面试题————：“为什么在进行softmax之前需要对attention进行scaled（为什么除以 d_k的平方根）？”==
## ==面试题==
==我们知道attention其实有很多种形式，而transformer论文中的attention是Scaled Dot-Porduct Attention 来计算keys和queries之间的关系。==
==如下图所示：==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpYhMm6SjqVJtpLibeN3P7CTOAVYl2EAuHBBb0CoMRdRHJ51ZjicTCnscg/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpYhMm6SjqVJtpLibeN3P7CTOAVYl2EAuHBBb0CoMRdRHJ51ZjicTCnscg/640?wx_fmt=jpeg&from=appmsg)
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpjzcia8NBJaiaB8JAj8AomNRs7bVpQjicXrYcuhbY7cun37WsUMGByXGdw/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpjzcia8NBJaiaB8JAj8AomNRs7bVpQjicXrYcuhbY7cun37WsUMGByXGdw/640?wx_fmt=jpeg&from=appmsg)
==公式一==
==在公式一中，作者对 Q 和 K 进行点积以获得注意力权重，然后这些权重用于加权平均 V 。但在实际实现中，这个点积会被缩放，即除以keys的维度的平方根，常常表示为 。这里 是key向量的维度。==
==细心的同学都会发现，Attention 计算公式 中会 除以 根号d，那问题来了！！！==
==**Attention为什么要除以根号d 呢？**==
==注：这个题目属于 NLP 面试中一个高频题，基本上问到 Attention 或者 Transformers 的时候都会问。（小编在找实习的时候，就被问了不止十次，现在抽空整理一下答案。）==
## ==标准答案==
==这个问题在《Attention is All Your Need》论文中，作者就对该问题进行解答。==
==While for small values of  the two mechanisms perform similarly, additive attention outperforms dot product attention without scaling for larger values of  [3]. We suspect that for large values of, the dot products grow large in magnitude, pushing the softmax function into regions where it has extremely small gradients . To counteract this effect, we scale the dot products by==
==如果你没看懂上面英文答案，那下面我就对其进行解释：==
==从论文中可以看出，随着 的值变大，点积的大小会增大，从而推动softmax函数往仅有很小的梯度的方向靠拢（分布集中在绝对值大的区域），导致softmax 函数容易导致梯度消失问题。==
==例如，假设Q和K的均值为0，方差为1。它们的矩阵乘积将有均值为0，方差为 （ 是Q或者K的维度大小）。因此， 的平方根被用于缩放（而非其他数值），因为，Q和K的矩阵乘积的均值本应该为0，方差本应该为1，这样会获得一个更平缓的softmax。==
==如果你在面试过程中也遇到该问题，可以回答：==
==**随着 的值变大，点积的大小会增大，如果没有及时对点积的大小进行缩放，那么万一点积的数量级很大，softmax的梯度就会趋向于0，也就会出现梯度消失问题。**==
## ==问题引申新问题==
==当你按上述答案回答后，基本能够回答上点，但是面试官为了考察你对该问题的深度，会进行问以下两个问题：==
1. ==为什么 变大会使得 softmax 梯度变小，从而导致梯度消失呢？==
2. ==除了 , 是否可以用其他值代替？==
3. ==self-attention一定要这样表达吗？==
4. ==有其他方法不用除根号dk吗？==
### ==问题一：为什么 变大会使得 softmax 梯度变小，从而导致梯度消失呢？==
==标准答案：====**输入softmax的值过大，会导致偏导数趋近于0，从而导致梯度消失**==
==下面我们将对该问题进行证明：==
==对于一个输入向量 ， softmax函数将其映射归一化到一个分布 。==
==此时，softmax函数会先采用一个自然底数 e 将输入中的元素间差距先“拉大”，然后归一化为一个分布。假设某个输入 x 中最大的的元素下标是 k ，如果输入的数量级变大(每个元素都很大)，那么 会非常接近1。==
==举个栗子，假定输入 ，对于不同量级的 a 产生的， 的值将发生什么变化：==
==a=1时，；== 
==a=10时，;== 
==a=100 时，(计算机精度限制)。==
==通过以下代码：==
```plain
from math import exp  
from matplotlib import pyplot as plt  
import numpy as np   
f = lambda x: exp(x * 2) / (exp(x) + exp(x) + exp(x * 2))  
x = np.linspace(0, 100, 100)  
y_3 = [f(x_i) for x_i in x]  
plt.plot(x, y_3)  
plt.show()  
```
==绘制图像如下：==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpibqFRjibAjFY2tXibCRRothGlTeIoSaBxJ1EEgLwOzgmP9w9duRXYicVNA/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpibqFRjibAjFY2tXibCRRothGlTeIoSaBxJ1EEgLwOzgmP9w9duRXYicVNA/640?wx_fmt=jpeg&from=appmsg)
==从图像中，我们发现数量级对于 softmax 函数的分布影响还是很大的。====**在数量级增大到某个值时，softmax 函数全部概率分布将趋于最大值对应阿标签**====。==
==接下来，我们一起来分析一个 softmax 函数的梯度，这里我们将 softmax 函数 设定为 ，softmax 函数对应的分布向量 的梯度为：==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpD7LTO0GrC2ZWAYHrT0H2HMA7oKk4dbWdVjboTeyjWmeDUBQ2LzeTuA/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpD7LTO0GrC2ZWAYHrT0H2HMA7oKk4dbWdVjboTeyjWmeDUBQ2LzeTuA/640?wx_fmt=jpeg&from=appmsg)
==通过将该矩阵展开：==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpKRVDr0SSV4ovpRJx8fQoVwaSjceruBZVU8wdxnRkV6icyfruehT9EEw/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpKRVDr0SSV4ovpRJx8fQoVwaSjceruBZVU8wdxnRkV6icyfruehT9EEw/640?wx_fmt=jpeg&from=appmsg)
==根据前面的讨论，当输入 x 的元素均较大时，softmax会把大部分概率分布分配给最大的元素假设我们的输入数量级很大，最大的元素是 1，那么就将产生一个接近one-hot的向量 ,此时上面的矩阵变为如下形式:==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexp5sB8iake3zCobsjs1ic16GWBicDnUibVB0zIRM4WA5QjVYibJo2anibJLeGw/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexp5sB8iake3zCobsjs1ic16GWBicDnUibVB0zIRM4WA5QjVYibJo2anibJLeGw/640?wx_fmt=jpeg&from=appmsg)
==可以看出，在输入的数量级很大时，梯度消失为0，造成参数更新困难，==
### ==问题二：除了 , 是否可以用其他值代替？==
==标准答案：====**为什么选择 ，时因为可以使得 Q 和 K 点积 趋向于 期望为0，方差为1的标准正态分布，说白了就是归一化。**==
==公式分析:==
==首先假设q和k都是服从期望为0，方差为1的独立的随机变量。==
==假设 ，则：==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpjoxhEJtKweeXic7BrfhgCWoT8oyibmW0Y6D2Vic8PDIYjI1Hyz2nX4Wrg/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpjoxhEJtKweeXic7BrfhgCWoT8oyibmW0Y6D2Vic8PDIYjI1Hyz2nX4Wrg/640?wx_fmt=jpeg&from=appmsg)
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpICCibfYkGnBlMMCFYibf1GibsqEmL3tficOFibhFMJLnTlnpNfam47BB5yw/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpICCibfYkGnBlMMCFYibf1GibsqEmL3tficOFibhFMJLnTlnpNfam47BB5yw/640?wx_fmt=jpeg&from=appmsg)
==此时==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpDo6NAdpiakYGlV6Y1x0skyK1U8UlNIdZjjSh6JpsW8KzU8we1cmJMPQ/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexpDo6NAdpiakYGlV6Y1x0skyK1U8UlNIdZjjSh6JpsW8KzU8we1cmJMPQ/640?wx_fmt=jpeg&from=appmsg)
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexptxm48zVdMKPE3Qcs1iaqS4Hcy9m4aSX3jPFR4n2E4SIrmGEIfFlmHGg/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexptxm48zVdMKPE3Qcs1iaqS4Hcy9m4aSX3jPFR4n2E4SIrmGEIfFlmHGg/640?wx_fmt=jpeg&from=appmsg)
==故有 Q 和 K 点积的均值 ，方差 。方差越大也就说明，点积的数量级越大(以越大的概率取大值)。那么一个自然的做法就是把方差稳定到1，做法是将 Q 和 K 点积除以，这样有:==
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexp2iaeb9MW8e3iba6EvG53aqXvJPwibslk7y7enKQVBXWknkmXpOvM16zjw/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fYvx2EicPwWWkFJvPGicOVexp2iaeb9MW8e3iba6EvG53aqXvJPwibslk7y7enKQVBXWknkmXpOvM16zjw/640?wx_fmt=jpeg&from=appmsg)
==将方差控制为1，也就有效地控制了前面提到的梯度消失的问题。==
### ==问题三：self-attention一定要这样表达吗？==
==不需要，能刻画相关性，相似性等建模方式都可以。最好速度快，模型好学，表达能力够。==
### ==问题四：有其他方法不用除根号dk吗？==
==有，同上，只要能做到每层参数的梯度保持在训练敏感的范围内，不要太大，不要太小。那么这个网络就比较好训练。方式有，比较好的初始化方法，类似于google的T5模型，就在初始化把这个事情干了。==
## ==致谢==
- ==为什么在进行softmax之前需要对attention进行scaled（为什么除以 d_k的平方根）  https://blog.csdn.net/ytusdc/article/details/121622205==
- ==算法岗常考面试题：transformer中的attention为什么要除以根号d_k https://blog.csdn.net/qq_43827595/article/details/125800615==
- ==[NLP]——Transformer中的attention为什么要做scale？https://blog.csdn.net/jokerxsy/article/details/116299343==
- ==transformer中的attention为什么scaled?  https://www.zhihu.com/question/339723385==
- ==self-attention为什么要除以根号d_k  https://blog.csdn.net/tailonh/article/details/120544719==
- ==Attention为什么要除以根号d  https://blog.csdn.net/qq_50988647/article/details/139562650==
==下面分享一下，我在春招过程中所整理的面试题资料。==
==yang19527/AwesomeInterview: 包含程序员面试大厂面试题和面试经验 (github.com)==
==https://github.com/yang19527/AwesomeInterview==
## ==大模型算法岗==
### ==大模型算法岗面试经验==
- [==【面试题解答】百度-NLP算法工程师面经==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483942&idx=1&sn=a5ba1da8459df0b76e1ea70bfa4dc068&scene=21#wechat_redirect)
- [==【面试题解答】美团-大模型算法工程师面经==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483919&idx=1&sn=c9a530ecce9e60af4fad4c06062ec9ce&scene=21#wechat_redirect)
- [==【面试题解答】小米-NLP算法工程师面试题==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483896&idx=1&sn=6b79f7eb585cc1d91a1f61010941477c&scene=21#wechat_redirect)
- [==【面试题解答】好未来-NLP算法工程师面经==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483884&idx=1&sn=e1f4d13589606786f2d2467e11b4e2dc&scene=21#wechat_redirect)
- [==【面试题解答】百度大模型算法工程师面经==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483862&idx=1&sn=0dc0ee080532d397b2b00bdd20c86260&scene=21#wechat_redirect)
- [==【面试题解答】昆仑天工大模型算法工程师==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483853&idx=2&sn=f717767538329ce17325de72aa58ba1b&scene=21#wechat_redirect)
- [==【面试题解答】阿里大模型算法工程师一面==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483839&idx=1&sn=b66447f92f4dbfa8be7922f53aa8ba4b&scene=21#wechat_redirect)
- [==【面试题解答】算法工程师面试常考手撕题==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483790&idx=1&sn=308fb18b66cc66b78f7e15822cdd6eff&scene=21#wechat_redirect)
- [==【面试题解答】搜狐大模型算法工程师==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483773&idx=1&sn=003c347fc05e1a3fa4328ac09dddb797&scene=21#wechat_redirect)
- [==【面试题解答】字节大模型算法实习生==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483757&idx=1&sn=79394fd14e39948d1fc98aa09e031561&scene=21#wechat_redirect)
- [==【面试题解答】理想汽车大模型算法实习生==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483750&idx=1&sn=18d9c270e8d58a32dc4792fbc5f8f6e8&scene=21#wechat_redirect)
- [==【面试题解答】百度大模型算法实习生面试题==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483745&idx=1&sn=ee37c895b25bf2a1f8387edf1d687e30&scene=21#wechat_redirect)
- [==【面试题解答】腾讯大模型算法实习生面试题==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483731&idx=1&sn=08cb4b390e80f3ca4a1e0fa2dd5a3020&scene=21#wechat_redirect)
- [==【面试题解答】阿里大模型算法工程师一面==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483723&idx=1&sn=baa9b82a7ac4f12e936ff8b58dcf8977&scene=21#wechat_redirect)
- [==【面试题解答】某大厂大模型算法工程师面试题==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483713&idx=1&sn=c90af03630f92999eed214d5dc9f06a3&scene=21#wechat_redirect)
- [==【面试题解答】说说百度大模型算法工程师二面经历==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483697&idx=1&sn=82e8cbb46aa2a0a656ae6f76ed225b03&scene=21#wechat_redirect)
- [==【面试题解答】阿里大模型算法工程师面试小结==](https://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247483686&idx=1&sn=79b3d0eb8a034cf7fe8746cd5e362899&scene=21#wechat_redirect)
- ==【面试题解答】荣耀NLP算法工程师 一面试题解答==
- ==【面试题解答】华为NLP算法工程师 一面试题解答==
- ==【面试题解答】SHEIN算法工程师 一面试题解答==
- ==【面试题解答】科大讯飞NLP大模型实习 一面试题解答==
- ==【面试题解答】网易NLP大模型实习 一面试题解答==
- ==【面试题解答】得物NLP算法工程师 一面试题解答==
- ==【面试题解答】字节 大模型算法工程师 一面试题解答==
- ==【面试题解答】知乎 大模型算法工程师 一面试题解答==
- ==【面试题解答】58同城NLP算法工程师 一面试题解答==
- ==【面试题解答】阿里智能NLP算法工程师 一面试题解答==
- ==【面试题解答】百度大模型算法师 一面试题解答==
- ==【面试题解答】商汤NLP算法工程师 实习一面面试题解答==
- ==【面试题解答】小米大模型实习一面面试题解答==
- ==【面试题解答】昆仑天工大模型实习一面面试题解答==
- ==【面试题解答】百度-NLP算法工程师面经==
- ==【面试题解答】小米-NLP算法工程师面试题==
- ==【面试题解答】百度大模型算法工程师面经==
- ==【面试题解答】昆仑天工大模型算法工程师==
- ==【面试题解答】SHEIN NLP算法工程师 一面试题解答==
- ==【面试题解答】淘天阿里妈妈NLP算法工程师 一面试题解答==
### ==大模型算法岗面试题（未包含答案）==
- ==【面试题】美团算法面试题汇总==
- ==【面试题】荣耀算法面试题汇总==
- ==【面试题】太初算法面试题汇总==
- ==【面试题】长亭科技大模型算法工程师面试题==
- ==【面试题】网易算法面试题汇总==
- ==【面试题】科大讯飞算法面试题汇总==
- ==【面试题】SHEIN 算法面试题汇总==
- ==【面试题】滴滴算法面试题汇总==
- ==【面试题】OPPO算法面试题汇总==
- ==【面试题】金山 大模型算法工程师面试题==
- ==【面试题】平安科技 大模型算法工程师面试题==
- ==【面试题】海康威视 大模型算法工程师面试题==
- ==【面试题】理想汽车大模型算法工程师面试题==
- ==【面试题】淘天算法面试题汇总==
- ==【面试题】快手算法面试题汇总==
- ==【面试题】搜狐算法面试题汇总==
## ==机器视觉算法岗==
### ==机器视觉算法岗面试经验==
- ==【面试题解答】字节CV算法工程师 一面试题解答==
- ==【面试题解答】趣玩科技公司CV算法工程师 一面试题解答==
- ==【面试题解答】百度 计算机视觉算法工程师 一面试题解答==
- ==【面试题解答】深信服CV算法工程师 一面试题解答==
### ==机器视觉岗面试题（未包含答案）==
- ==【面试题】商汤 大模型算法工程师面试题==
- ==【面试题】蚂蚁金服 大模型算法工程师面试题==
- ==【面试题】旷视 大模型算法工程师面试题==
## ==软件后台岗==
### ==软件后台岗面试经验==
- ==【面试题解答】WXG 软件后台岗 一面试题解答==
## ==数据挖掘算法岗==
### ==数据挖掘算法岗面试经验==
- ==【面试题解答】快手数据分析师 实习一面面试题解答==
- ==【面试题解答】字节数据科学算法师 实习一面试题解答==
- ==【面试题解答】美团数据科学算法工程师 一面试题解答==
- ==【面试题解答】招银网科 数据科学算法工程师 一面试题解答==
- ==【面试题解答】美团 数据挖掘算法工程师 一面试题解答==
- ==【面试题解答】快手数据科学算法工程师 一面试题解答==
- ==【面试题解答】万物心选数据科学算法工程师 一面试题解答==
## ==推荐系统算法岗==
### ==推荐系统算法岗面试经验==
- ==【面试题解答】快手推荐算法实习 一面试题解答==
- ==【面试题解答】联想 推荐系统算法工程师 一面试题解答==
## ==广告算法岗==
### ==广告算法岗面试经验==
- ==【面试题解答】快手广告算法工程师 一面试题解答==
==历史文章==
[==大模型常见面试题梳理——SFT==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485310&idx=1&sn=8a10be6209df2bd35099339f9bef743d&chksm=c1c23e70f6b5b7662855765e1ea7ad315f593dbefe8b1812c355ba4c56ee9aa0b09f5c9ee6f2&scene=21#wechat_redirect)
[==大模型Agent的核心还是prompt？==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485292&idx=1&sn=1a9b1d88ad480fa72ac67a34eafa81cc&chksm=c1c23e62f6b5b774bd8edd7ff20731959adce2f439c330f0cd3ca9d7b3fe35dea939359371f5&scene=21#wechat_redirect)
[==LLM并发加速部署方案（llama.cpp、vllm、lightLLM、fastLLM）==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485286&idx=1&sn=3dc228a715acdbd737814678168af9c2&chksm=c1c23e68f6b5b77e2153c0493ff6e610c521a281c0bacbd7754df3ddb51bd4dd082bb484d667&scene=21#wechat_redirect)
[==LLM 大模型学习必知必会系列(一)：VLLM性能飞跃部署实践：从推理加速到高效部署的全方位优化==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485267&idx=1&sn=caa7600e3365752214a4ec97792c1fe2&chksm=c1c23e5df6b5b74bc388b4c2d978c0a4a9b93366cee24774bc32faada90d947f38970e963c5c&scene=21#wechat_redirect)
[==Llama 3.1-405B训练推理技术==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485255&idx=1&sn=34733c810484697525c36a2c20bd9b7d&chksm=c1c23e49f6b5b75f8b6f2ff2078976c86f64946478201f96b64d503fdb0e3d8b8887ceed9a0b&scene=21#wechat_redirect)
[==腾讯OMG：支持多角色多概念在一张图片中生成，生成合影的问题终于解决了！==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485255&idx=7&sn=f8c4c31516e060ed1c40164feec912ed&chksm=c1c23e49f6b5b75f4772d2306c39eeaa6ef5b97531382b59b12e6a665296b58e5ed161a6ccc6&scene=21#wechat_redirect)
[==大模型微调炼丹心得十问==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485234&idx=1&sn=c32597915688a27916daa1e85ffc77fa&chksm=c1c23e3cf6b5b72ae2b0fa2a885a329fcf210611c7a4e62963c7b15ff7b0205d5b61473514c9&scene=21#wechat_redirect)
[==【专栏】文本提示技术详解：零样本、少样本、思维链等==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485234&idx=2&sn=fdaa1a622e9edc3bdd614c3b1cf54d87&chksm=c1c23e3cf6b5b72a756af09e4cebfac85db7622360f6c8e9f34a4f37e68e941a50560f94b646&scene=21#wechat_redirect)
[==解读：基于图的大模型提示技术==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485234&idx=4&sn=e5b4054a0d4419cf9b94088fac38fc37&chksm=c1c23e3cf6b5b72aeaf1b90e1c5f99cd85af9a34550f55fe8ed6e8626a778fadea8ed5bf8d87&scene=21#wechat_redirect)
[==腾讯开源PhotoMaker v2：几秒钟生成逼真人物照片！==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485234&idx=7&sn=80ce81c5fa5835f1461437326427b8a6&chksm=c1c23e3cf6b5b72a9cd0a0c85b403b92d609b5943b9af206f13e9c3f325eddfbb5dbe253561d&scene=21#wechat_redirect)
[==【LLM技术报告】Qwen2 Technical Report==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485228&idx=1&sn=ed66f5724f79ac954a89072e533105c7&chksm=c1c23e22f6b5b7345241d6ea90d3a1da079b6f7658185394009afee5b5a4262df3e12032921f&scene=21#wechat_redirect)
[==腾讯华为联手推出：IMAGDressing-v1开源换装写真技术==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485228&idx=3&sn=378e657e53746185ef030239784d8bcc&chksm=c1c23e22f6b5b73448db00d7c4bc8ecc9c0ecd9a2abb37a4aaffb426624319ec7b3493ed7a0e&scene=21#wechat_redirect)
[==多模态入门篇（六）——图解 LLaVA 推理流程==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485199&idx=2&sn=7b28a00c1747b67a5e531d62be5d68b4&chksm=c1c23e01f6b5b717d7a14bde78b195751eca527ef59525116525bf76ebe2c470d64ee2160876&scene=21#wechat_redirect)
[==DSPy: 革新语言模型应用的声明式编程框架==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485171&idx=2&sn=747730a10b9213b665a470a1dfaee2c8&chksm=c1c23ffdf6b5b6ebc3f8f9c8400799cf631ad6013d6aa5e91b04df8a335410ceaded3ebed76f&scene=21#wechat_redirect)
[==多模态视频分类Trick==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485171&idx=1&sn=de0074c63ced26878e9c44ca31dfdb62&chksm=c1c23ffdf6b5b6eb3919c13ddea9e8511696fad8d871e5e6ef1be09fc28af9b3bccc5ace0d60&scene=21#wechat_redirect)
[==扩散模型应用于NLP领域应用==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485179&idx=1&sn=c2024a3c2e706ec1867358089d1b584b&chksm=c1c23ff5f6b5b6e3087860bfbaf58add3c8d1f87dc7d30972f630978a6b65b6143ab9449d69b&scene=21#wechat_redirect)
[==如何构建 多模态搜索+RAG系统？==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485191&idx=1&sn=8fa7304c4300fedfb5c2c5e688de0662&chksm=c1c23e09f6b5b71fbef01395417a94036e813d739320c4b2eee3193156e3a422cede6ab84d2d&scene=21#wechat_redirect)
[==多模态大模型的一些经验总结==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485168&idx=1&sn=d6b3bcc1e5d0cf6c306f985553c066c5&chksm=c1c23ffef6b5b6e8d518401c73e1e068d35bcbcd284238733b61638a495b9e56ed125a81b8b1&scene=21#wechat_redirect)
[==多模态入门篇（五）——安装与部署 LLaVA==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485163&idx=1&sn=ef5502b241c0942a4e0d151d5b8c01b3&chksm=c1c23fe5f6b5b6f3bfd9e9c4318cc9a0f7e85c58ee32581129526c7df92a6f0379cd8d916662&scene=21#wechat_redirect)
[==从Function Call到零样本分类：意图识别阶段性总结==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485163&idx=4&sn=bc64389c05a63a1da431d8fc237654a5&chksm=c1c23fe5f6b5b6f3d9ee1a662ca62a76eb8246747fd91fc02778c128c7ef6b244299f6000804&scene=21#wechat_redirect)
[==RAG系列03：使用RAGAS进行自动化评估==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485112&idx=4&sn=e72111550446edb9b13d93e13100799a&chksm=c1c23fb6f6b5b6a0643d33656f0f13989ba2b8a4f811640eb5fe31c6f2c1eb1d4c1893357322&scene=21#wechat_redirect)
[==重磅发布，阿里QWen2开源，全面超越Llama3，成为开源第一大模型==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485162&idx=1&sn=cdd4f00fc382e3d4fcf18a5cd17e68e6&chksm=c1c23fe4f6b5b6f25986a4fbf44428af6ebb37be21625ba88b89e8a4d87f55055ad63c9df2f0&scene=21#wechat_redirect)
[==多头RAG：利用多头注意力提升复杂查询的检索准确性==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485163&idx=5&sn=cd8da46b79d18e0bdc7ac3f5693aa87e&chksm=c1c23fe5f6b5b6f3089792e6e3f74e798131f1a05d85a4a6605c3a147ebbf3fe90de0d3cde2e&scene=21#wechat_redirect)
[==从Function Call到零样本分类：意图识别阶段性总结==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485163&idx=4&sn=bc64389c05a63a1da431d8fc237654a5&chksm=c1c23fe5f6b5b6f3d9ee1a662ca62a76eb8246747fd91fc02778c128c7ef6b244299f6000804&scene=21#wechat_redirect)
[==HybridRAG: 融合知识图谱和向量检索的新型信息提取方法==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485310&idx=5&sn=0903ec30200790ac4987be927a1e0ec1&chksm=c1c23e70f6b5b76688081218e2f0c52ca9e85bbc9eb12f09f8968db7173b30228dad14532c6c&scene=21#wechat_redirect)
[==TripoSR：开源3D重建模型，0.5秒就能完成2D到3D图转换==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485310&idx=6&sn=7608a70f8899951a2493504739a0bb3e&chksm=c1c23e70f6b5b766f6d694b54fba9242719af43df3b9c23e3bee2f2528a65507efd84a52f51d&scene=21#wechat_redirect)
[==字节跳动发布HeadGAP：三张照片生成逼真3D数字人==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485310&idx=7&sn=1ed43c8f50abf5866ca0c5f97aa31560&chksm=c1c23e70f6b5b7666910893778c56cca51ebfc9669526d904108c5cae3a3b1d6201eeb50636e&scene=21#wechat_redirect)
[==大模型应用的10种架构模式==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485091&idx=4&sn=008bbb3c24787dda05af15b7509b5736&chksm=c1c23fadf6b5b6bb8dc816b25ef0f174a5f33a16e97e1a9f0cfc336a7d0da1ec1b37bcdba00a&scene=21#wechat_redirect)
[==RAG系列04：使用ReRank进行重排序==](http://mp.weixin.qq.com/s?__biz=MzkyNTY0Mjg0OQ==&mid=2247485091&idx=3&sn=473868b19ba5a84c6d26f0273e869188&chksm=c1c23fadf6b5b6bb040100a93746b47d6db68392bd4a1f5bf2a6aee8763fcea38eae2cbe0c03&scene=21#wechat_redirect)
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fb3yxADJGGjPNWicoRHXOkoqelwSdkUXfvibnJkBoGSeHw0cMrTadCCgibXuaDcVvXA3nPzibTpqOfVhw/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/goiboxqfW2fb3yxADJGGjPNWicoRHXOkoqelwSdkUXfvibnJkBoGSeHw0cMrTadCCgibXuaDcVvXA3nPzibTpqOfVhw/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp)