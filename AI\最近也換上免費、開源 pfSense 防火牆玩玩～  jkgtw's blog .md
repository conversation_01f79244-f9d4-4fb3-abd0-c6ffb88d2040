---
Updated: 2023-09-02T19:25
tags:
  - AI->-Infra
Created: 2023-09-01T02:45
---
![[3i3acxmanaba238Xa1.png]]
之前一直是買成品路由器用著，後來添購多網口的 Mini PC 後就開啟新世界了～
在 Mini PC 上面安裝個 [Proxmox VE](https://www.proxmox.com/en/proxmox-ve) 虛擬機系統，就可以隨意安裝各種系統來玩
剛開始不熟先跑個 [OpenWRT](https://openwrt.org/) 暖暖身，後來發現 [RouterOS CHR](https://mikrotik.com/download) 後就一直[用著](https://www.jkg.tw/p2531/)也蠻舒服
​
這兩套簡單白話來說：
OpenWRT 操作設定上最接近市面的成品路由器，而且有各種軟體包可以安裝，可玩性高而且是免費開源的
RouterOS 佔用資源小而且功能強大，但是用起來操作較為複雜，對於新手小白入門的門檻比較高
​
然而閒著蛋疼，最近一段時間又改裝了 [pfSense](https://www.pfsense.org/)，它跟 OpenWRT 一樣也是開源免費的
跟 pfSense 同門師兄弟的還有 [OPNsense](https://opnsense.org/)，看名字就知道，OPNsense 是在 pfSense 基礎上修改的分支
稍微比較一下之後，最後我選擇 pfSense 來玩玩，因為 pfSense 網路上討論度似乎比較高。。。🤷‍♂️
​
## pfSense 下載與安裝
直接去官網[下載](https://www.pfsense.org/download/)，官方有提供 ISO 格式，可以直接在 Proxmox VE 裡面載入使用
​
![[sot037333120037.png]]
​
安裝過程跟普通虛擬機安裝差不多，根據提示依照自己環境輸入設定即可
在安裝這種軟體路由時候，建議只先插上 WAN 的網路線，LAN 可以等設定完畢再插上比較不容易出問題
​
![[u51ero516715120o51671.png]]
👆 第一次安裝會有設定精靈，帶你一步步從零到正常上網
​
像我是申裝中華電信光世代，設定 PPPoE 帳號密碼後，馬上就可以正常上網
以上的設定過程跟市售成品路由器差不多，指定 DHCP 後端 IP 地址，很簡單就能搞定上網
接下來設定一下中華電信光世代不固定制的 IPv6 上網
​
​
## pfSense 中華電信光世代不固定制 IPv6 設定
![[u6ero68050620o6805.png]]
👆 先到 Interface 的 WAN 頁，找到 IPv6 Configuration Type，改成 DHCP6
​
接著繼續往下滑動，會看到 DHCP6 Client Configuration 的欄位
![[u50ero506895020o50689.png]]
👆 Request a IPv6 prefix 打勾，Prefix Delegation size 設定為 64
​
此頁設定完，記得繼續往下滑，按下 Save 按鈕保存
這時候看 Dashboard 資訊，應該能看見 WAN 已經能正常取得 IPv6 資料，接下來繼續設定 LAN 讓內部設備都能拿到 IPv6
​
![[u17ero171611720o17161.png]]
👆 IPv6 Configuration Type 改為 Track Interface
​
繼續往下滑，看到「Track IPv6 Interface」欄位
​
![[u42ero426114220o42611.png]]
👆 IPv6 Interface 改為 WAN
​
同樣設定完畢記得按下最下面的「Save」按鈕，保存之後稍等一、兩分鐘，內部網路的設備應該就能自動拿到 IPv6
（如果上述設定都做完依然沒有拿到 IPv6 的話，請把 pfSense 跟你的設備重新關機再啟動試試看）
此時設備也已經具備 IPv6 上網能力，可以打開一個 IPv6 網站測試，例如：
[https://ipv6.google.com](https://ipv6.google.com/)
​
能打開就表示沒問題，但是此時你如果去 [https://ipv6-test.com](https://ipv6-test.com/) 測試的話，大概只能拿到 18 分（滿分 20）
​
![[u53ero532215320o53221.png]]
​
這是因為防火牆把 ICMP 擋掉了，此時繼續回到 pfSense 的防火牆設定，在 Firewall / Rules 裡面新增一條規則
​
![[u58ero587485820o58748.png]]
👆 依照圖片設定，ICMP Subtypes 選 Echo request 即可
​
設定完畢後保存，記得還要按下 Apply Changes 的按鈕，讓防火牆規則生效
​
![[u32ero328803220o32880.png]]
​
按下 Apply 幾秒後再回到剛剛 [https://ipv6-test.com](https://ipv6-test.com/) 網站去跑一次測試
​
![[u2ero21290220o2129.png]]
👆 IPv6 輕鬆滿分
​
​
## pfSense GeoIP Blocking
喜歡用軟體路由另外一個原因是，可以輕鬆載入大量 CIDR IP 段，這對於經常在搭各種服務給自己用的人非常方便
例如搭一個 AdGuard Home 的服務出來，如果直接在網路上開放 53 端口，被惡意的人掃到就有可能被攻擊
除非你就是想搭一個公共的，打算服務普羅大衆，否則實在沒必要全面開放，防火牆開放越少就會越安全
​
假設你像我一樣，大部分時間都待在國內，那你可以只開放給國內的 IP 段連線使用而已
網路上已經有很多已經整理好基於國家 IP 段的資料，我們只要加載到 pfSense 防火牆裡面即可
這裡推薦這個項目，每隔幾個小時偵測到變化項目就會自動更新，非常方便
[https://github.com/herrbischoff/country-ip-blocks](https://github.com/herrbischoff/country-ip-blocks)
​
👆 打開項目網頁後，你想要找 IPv4 就點，想找 IPv6 就點
​
👆 接著找到你想要的國家代碼，例如台灣就是 tw，日本就是 jp，美國就是 us
​
取得內容以後，就全部拷貝起來，接著回到 pfSense 防火牆設定
​
👆 Firewall / Aliases / IP，右下角有個 Import 按鈕按下去
​
pfSense 允許一次 Import 很多資料
​
👆 如上依序輸入完資料後，Save 保存一下
​
建立好你想要的 Alias 以後，我們可以去 pfSense 防火牆弄 Port Forward 開端口了～
​
👆 來到 Port Forward 頁，點右下角 Add 新增規則
​
這裡我們就拿 AdGuard Home 舉例，DNS Server 通常要開 Port 53 UDP
​
👆 進來後，點一下「Display Advanced」
​
👆 依照上圖來填寫即可，最後也要記得保存跟 Apply
​
Source 那邊我們就填上剛剛新增的 Alias，也就是只有允許台灣 IP 才能連進來，非台灣的 IP 走 53 端口過來都會被拒絕
​
當然你可以再更激進一點，只加入你家裡跟公司的固定 IP，然後再加上你行動網路系統商的 IP 段
開放範圍越小也就越安全，但是太激進也會為你帶來臨時的不方便，例如你偶而會去客戶公司或者親戚朋友家裡使用 WiFi 時？
所以一般情況建議開放整個台灣段即可，如果臨時會去日本或者韓國，可以在出發前添加一條允許日本或者韓國的 CIDR
​
👆 另外 pfSense 也有支援第三方軟體包安裝
​
pfSense 可玩性也是蠻高，有些第三方軟體包也非常實用
像是跟 Pihole、AdGuard Home 類似可以擋廣告、惡意網站。。等等的 [pfBlockerNG](https://forum.netgate.com/category/62/pfblockerng)，就非常好用！
裝在 pfSense 裡面的話就可以少裝一套 AdGuard Home 啦！ 😁