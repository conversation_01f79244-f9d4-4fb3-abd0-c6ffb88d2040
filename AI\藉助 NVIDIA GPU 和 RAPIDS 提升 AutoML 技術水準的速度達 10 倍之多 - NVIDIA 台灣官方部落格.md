---
Updated: 2023-09-10T17:25
tags:
  - AI->-AutoML
  - AI->-Fine-Tuning
Created: 2023-09-09T20:48
---
[![](https://blogs.nvidia.com.tw/wp-content/uploads/sites/19/2022/03/Advancing-the-State_Pic3.png)](https://blogs.nvidia.com.tw/wp-content/uploads/sites/19/2022/03/Advancing-the-State_Pic3.png)
---
資料科學家經常會建立複雜的機器學習模型，實現最先進的機器學習解決方案。但是，這些技術的運算成本高昂，且在過去需要具備廣泛的背景知識、經驗和人力。 最近，在 GTC 上，AWS 資深資料科學家 Nick Erickson 主持了一場講座，分享將 AutoGluon、RAPIDS 與 NVIDIA GPU 運算的整合，如何簡化實現最先進的機器學習準確度，同時提高效能與降低成本。 本文章概述 Nick 講座的一些重點：
- AutoML 是什麼？AutoGluon 有何不同之處？
- AutoGluon 如何在 Kaggle 預測競賽中，使用三行程式碼勝過 99% 的人類資料科學團隊，而無須專家知識？
- AutoGluon 與 RAPIDS 的整合，如何將訓練速度加快 40 倍，將推論速度加快 10 倍？
### **AutoGluon** **是什麼？**
[AutoGluon](https://auto.gluon.ai/) 是開放原始碼 AutoML 函式庫，支持易於使用與易於擴充的 AutoML，並著重於自動堆疊整體化、深度學習，以及涵蓋文字、影像和表格資料的實際應用。AutoGluon 適合機器學習初學者和專家，讓您可以：
- 使用幾行程式碼，為原始資料快速製作深度學習和傳統機器學習解決方案的原型。
- 在適當的情況下自動利用最先進的技術，而無須專家知識。
- 利用自動超參數調整、模型選擇／整體化、架構搜尋和資料處理。
- 輕鬆改進／調整訂製模型和資料工作流程，或針對使用案例自訂 AutoGluon。
本文章著重於 AutoGluon-Tabular，一種 AutoGluon API，僅需要幾行 Python，即可在未經處理的表格資料集（例如 CSV 檔案）上，訓練高度準確的機器學習模型。 為了能瞭解 AutoGluon-Tabular 如何做到這一點，我們將會先解釋一些概念。
### **何謂監督式機器學習？**
[監督式機器學習](https://developer.nvidia.com/blog/gradient-boosting-decision-trees-xgboost-cuda/)是以有標籤訓練執行個體做為輸入及建立模型，以根據已知之範例的其他資訊（稱為執行個體的特徵），正確預測各個訓練範例的標籤。此做法之目的是建立準確的模型，自動標記具有未知標籤的未來資料。
![[Advancing-the-State_Pic-1.png]]
圖 1：監督式機器學習是使用有標籤資料建立模型，以針對無標籤資料進行預測。
在表格資料集中，欄代表變數（又稱為特徵）的測量值，列代表個別資料點。 例如，下表所示的小型資料集包含三個欄位：「有工作」、「有房子」和「收入」。在此範例中，「收入」是標籤（有時候稱為預測的目標變數），其他欄位則是嘗試預測收入的特徵。
![[Screen-Shot-2021-06-08-at-9.55.42-AM.png]]
表 1：收入資料集
監督式機器學習是一個探索性迭代過程，涉及資料準備、特徵工程、驗證分割、遺漏值處理、訓練、測試、超參數調整、整體化和評估機器學習模型，之後才能將模型運用於生產，以進行預測。
![[Advancing-the-State_Pic2.png]]
圖 2：機器學習是一個迭代過程，涉及特徵擷取、訓練和評估，之後才能部署模型，以進行預測。
### **AutoML 是什麼**
在過去，實現最先進的機器學習效能，需要廣泛的背景知識、經驗和人力。AutoML 會依據工具和自動化程度，使用不同的演算法技術，嘗試為機器學習工作流程尋找最佳特徵、超參數、演算法及／或演算法組合。從業人員和企業將耗時的機器學習工作流程[自動化](https://developer.nvidia.com/blog/accelerating-automated-and-explainable-machine-learning-with-rapids/)，即可應用機器學習，更快速、更輕鬆地解決業務問題。
### **使用 AutoGluon Tabular 分為 3 步驟執行 AutoML**
[AutoGluon Tabular](https://auto.gluon.ai/stable/api/autogluon.predictor.html#tabulardataset) 可以自動建立最先進的模型，使用兩個函數：[fit ()](https://auto.gluon.ai/stable/api/autogluon.predictor.html#autogluon.tabular.TabularPredictor.fit) 和 [predict ()](https://auto.gluon.ai/stable/api/autogluon.predictor.html#autogluon.tabular.TabularPredictor.predict)，根據同一列中的其他欄位，預測特定欄位的值，如下所示。
```Plain
from autogluon.tabular import TabularPredictor, TabularDataset
# load dataset
train_data = TabularDataset(DATASET_PATH)
# fit the model
predictor = TabularPredictor(label=LABEL_COLUMN_NAME).fit(train_data)
# make predictions on new data
prediction = predictor.predict(new_data)
```
fit() 函數研究資料集、執行資料預處理、適配多個模型，並進行組合以產生高準確度模型。更完整的試用範例，請參閱[預測表格欄位的 AutoGluon 快速入門教學](https://auto.gluon.ai/stable/tutorials/tabular_prediction/tabular-quickstart.html) 。
![[Advancing-the-State_Pic3.png]]
圖 3：AutoGluon fit() 函數會自動建立機器學習模型，透過 predict() 函數，根據同一列中的其他欄位，預測特定欄位的值。
AutoGluon憑藉此簡單的程式碼，擊敗其他 AutoML 框架及許多頂尖資料科學家。 針對來自 Kaggle 和 OpenML AutoML 基準之 50 個分類和迴歸任務進行測試的[廣泛評估](https://arxiv.org/pdf/2003.06505.pdf)顯示，AutoGluon 比 TPOT、H2O、AutoWEKA、auto-sklearn 和 Google AutoML Tables 更快速、更穩健、更準確。此外，在兩個熱門的 Kaggle 競賽中，AutoGluon 僅在原始資料上進行 4 小時的訓練，就擊敗了 99% 的參賽資料科學家。
_圖 4：AutoGluon 的表現勝過其他 AutoML 框架及許多頂尖的 Kaggle 資料科學家。_
### **AutoGluon** **有何不同****？**
大多數 AutoML 框架都著重於選擇組合演算法和最佳化超參數（Combined Algorithm Selection and Hyperparameter，CASH）的任務，以提供策略，從各種可能性中尋找最佳模型及其超參數。但是，CASH 具有一些缺點：
- 其需要多次反覆的模型訓練，且捨棄大部分模型，對最終結果沒有貢獻。
- 調整超參數的次數越多，過度適配驗證資料的風險越高。
- 在整體化時，調整超參數較没有幫助。
相較之下，AutoGluon-Tabular 優於其他框架，可以仰賴專業資料科學家使用的方法贏得競賽：將多個模型整體化及堆疊成多層。
### **整體化如何運作？**
整體學習方法結合了多種機器學習演算法，以獲得更好的模型。為了進一步瞭解此概念，讓我們回顧由決策樹組成整體的隨機森林。 決策樹建立了一個預測目標標籤的模型，方式是評估 if-then-else 和 true/false 特徵問題的樹狀結構，並估計評估做出正確決策之機率需要的最少問題數量。決策樹可以用於分類，以預測類別，或用於迴歸，以預測連續數值。例如，下方的決策樹（根據上表）嘗試使用特徵「有工作」和「有房子」的兩個決策節點，預測標籤「收入」。
_圖 5：具有兩個決策節點和三個葉子的簡單決策樹模型。_
決策樹的優點為容易解讀，但是存有過度適配和準確性的問題。建立一個準確的模型是介於適配不足與過度適配之間－模型預測符合訓練資料的行為方式，且可以概化，以準確預測未看見的資料。 決策樹嘗試尋找最佳分割，而將資料子集化，卻導致粗糙分割。 例如，下圖左側的資料集將要預測點的顏色，點越亮，值就越高。右側所示的決策樹，將資料分成粗糙的區塊。 後，我們將探討如何透過整體化，改進決策樹。
_圖 6：左側範例資料集的目標是預測點的顏色，點越亮，值就越高。此資料集的決策樹（右側），將資料分成粗糙的區塊。_
[整體化](https://en.wikipedia.org/wiki/Ensemble_learning)是經過驗證的方法，可以透過結合預測和改進概化，提高模型的準確度。[隨機森林](https://developer.nvidia.com/blog/accelerating-random-forests-up-to-45x-using-cuml/)是常見用於分類和迴歸的整體學習方法。隨機森林使用稱為 bagging（bootstrap aggregating，自舉匯聚）的技術，從資料集和特徵的隨機自舉樣本，建立平行的完整決策樹。預測方式是匯聚來自所有樹狀結構的輸出，減少變異數及提高預測準確度。最終預測是所有決策樹預測的多數類別或均值迴歸。隨機性是森林成功的關鍵，[bagging](https://en.wikipedia.org/wiki/Bootstrap_aggregating) 可以確保決策樹各不相同，以減少在個別樹狀結構上看到的過度適配問題。
![[Advancing-the-State_Pic-7.png]]
_圖 7：隨機森林是使用稱為 bagging 的技術，從資料集和特徵的隨機自舉樣本中建立決策樹。_
為了瞭解這如何提供更好的預測，讓我們看一個範例。以下是圖 6 所示之資料集的四種決策樹，測試資料點的預測顏色各不相同。我們可以看到，它們都提供了近似解，其概化程度無法做出準確的預測。
![[Advancing-the-State_Pic-8.png]]
_圖_ _8__：圖_ _6_ _所示之資料集的四種決策樹，測試資料點的預測顏色各不相同。圖片參考_ [_https://gist.github.com/tylerwx51/fc8b316337833c877785222d463a45b0_](https://gist.github.com/tylerwx51/fc8b316337833c877785222d463a45b0)
將四個決策樹組合及平均化時，粗糙的邊界會消失與平順化，如下方的隨機森林範例所示。現在，測試資料點的預測顏色與其他樹狀結構預測的顏色混合。
![[Advancing-the-State_Pic-9.png]]
_圖 9：圖 8 所示之四個決策樹的隨機森林模型。_
隨機森林中的所有決策樹都為次佳，在隨機方向上都錯誤。在將決策樹平均化時，錯誤原因會互相抵消，稱為變異數抵消。結果提高了品質，因為它們反映出大多數樹狀結構達成的決策。平均化可以限制誤差，即使有些樹狀結構錯誤，有些樹狀結構正確，因此，這一群樹狀結構集體朝著正確方向移動。 將許多不相關的決策樹組合時，會產生具有高預測能力的模型，以防止過度適配。這些概念是 AutoGluon 採用之常見機器學習演算法的基礎，例如[隨機森林、XGBoost](https://developer.nvidia.com/blog/gradient-boosting-decision-trees-xgboost-cuda/)、[Catboost](https://developer.nvidia.com/blog/catboost-fast-gradient-boosting-decision-trees/) 以及 LightGBM。
### **多層堆疊整體化**
您可以透過整體化更進一步，由經驗豐富的機器學習從業人員，將 RandomForest、CatBoost、k 近鄰等輸出結合，進一步提高模型準確度。在機器學習競賽社群中，很難找到透過單一模型贏得的競賽，每一個獲勝的解決方案都包含由模型組成的整體。 [堆疊](https://medium.com/rapids-ai/100x-faster-machine-learning-model-ensembling-with-rapids-cuml-and-scikit-learn-meta-estimators-d869788ee6b1)技術是使用「基礎」迴歸或分類模型集合的聚合預測做為特徵，訓練中繼分類器或迴歸器「堆疊器」模型。
_圖 10：堆疊技術。_
多層堆疊將堆疊器模型輸出的預測做為輸入，饋送至其他更高層的堆疊器模型。在多層中迭代此過程，一直是許多 Kaggle 競賽中的致勝策略。多層堆疊整體強大，卻很難穩健使用和實行，目前除 Autogluon 外，無任何 AutoML 框架採用。 不需要專家知識，AutoGluon 是使用 k 折 bagging 自動聚集和訓練新穎的多層堆疊整體化形式，如圖 11 所示。運作方式如下：
- 基礎：第一層有多個使用 k 折整體 bagging（將於下文探討）進行個別訓練及 bagging 的基礎模型。
- 串接：將基礎層模型預測與輸入特徵串接做為輸入，以訓練下一層。
- 堆疊：在串接層輸出上訓練多個堆疊器模型。不同於傳統的堆疊策略，AutoGluon 重新使用與堆疊器相同的基礎層模型類型（具有相同的超參數值）。此外，堆疊器模型不僅是使用上一層模型的預測做為輸入，同時以原始資料特徵本身做為輸入。
- 權重：最終堆疊層套應用整體選擇，以加權方式，匯聚堆疊器模型的預測。跨高容量模型堆疊匯聚預測，可以進一步防止過度適配。
![[Advancing-the-State_Pic11.png]]
_圖 11：AutoGluon 的多層堆疊整體化。_
### **k 折整體 Bagging**
AutoGluon 透過針對堆疊各層的所有模型進行 k 折整體 bagging，使用所有的可用資料進行訓練和驗證，以提高堆疊效能。k 折整體 bagging 與 k 折交叉驗證類似，是一種將訓練資料集最大化的方法，通常適用於調整超參數，以確定最佳模型參數。k 折交叉驗證，將資料隨機分成 k 個分割區（折疊）。每一個折疊使用一次做為驗證資料集，其餘部分（Out-Of-Fold，OOF）則用於訓練。使用 OOF 訓練集訓練模型，並透過驗證集評估模型，產生 k 個模型準確度測量值。AutoGluon 針對所有模型進行 bagging，以及從訓練期間未看見之分割區上的每一個模型取得 OOF 預測，而不是確定最佳模型，並捨棄其餘的模型。可以建立每一個模型的 k 折預測，做為下一層的中繼特徵。
_圖 12：k 折整體 Bagging。_
為了進一步提高預測準確度及減少過度適配，AutoGluon-Tabular 在訓練資料的 n 個不同隨機分割上重複 k 折 bagging 過程，以將重複 bag 的所有 OOF 預測平均化。選擇數字 n 的方式是在呼叫 fit() 函數時，估計在指定時間限制內可以完成多少輪。
### **為何** **AutoGluon** **需要** **GPU** **加速**
多層堆疊整體化可以提高準確度，但是，必須訓練數百個模型，此任務的運算密集程度高於基本機器學習使用案例，且成本比加權整體化高 10 至 20 倍。在過去，複雜性和運算需求會導致許多生產使用案例和大型資料集無法實行多層堆疊整體化。在擁有 AutoGluon 和 [NVIDIA GPU 運算](https://developer.nvidia.com/cuda-gpus)之後，情況已不再如此。 CPU 架構僅由幾個具有大量快取記憶體的核心組成，可以同時處理數個軟體執行緒。相較之下，[GPU 是由數百個核心組成](https://blogs.nvidia.com/blog/2009/12/16/whats-the-difference-between-a-cpu-and-a-gpu/)，可以同時處理數千個執行緒。在機器學習工作流程中，GPU 的效能比 CPU 快 20 倍以上，並可徹底改變深度學習領域。
_圖 13：CPU 僅由幾個核心組成，相較之下，GPU 是由數百個核心組成。_
[NVIDIA](https://www.nvidia.com/zh-tw/) 開發出 [RAPIDS](https://developer.nvidia.com/rapids)（開放原始碼資料分析和機器學習加速平台），完全以 [GPU](https://developer.nvidia.com/cuda-gpus) 執行端對端資料科學訓練工作流程。它仰賴 NVIDIA® CUDA® 基元最佳化低階運算，同時透過 Pandas、Scikit-Learn API 等人性化 Python 介面，展現 GPU 平行性和高記憶體頻寬。 使用 RAPIDS 的 [cuML](https://github.com/rapidsai/cuml)，支援隨機森林、XGBoost 等許多常見的機器學習演算法，適用於單 GPU 和大型資料中心部署。對於大型資料集而言，以 GPU 為基礎的建置可以加快機器學習模型訓練－[隨機森林可以加快達 45 倍](https://medium.com/rapids-ai/accelerating-random-forests-up-to-45x-using-cuml-dfb782a31bea?source=collection_home---6------4-----------------------)、[支援向量機超過 100 倍](https://medium.com/rapids-ai/fast-support-vector-classification-with-rapids-cuml-6e49f4a7d89e)、[k 近鄰高達 600 倍](https://medium.com/rapids-ai/accelerating-k-nearest-neighbors-600x-using-rapids-cuml-82725d56401e)。這些加速可以將夜間工作轉變為互動式工作、允許探索更大的資料集，且能在過去訓練單一模型需要的時間內嘗試數十種模型變體。
_圖 14：採用 GPU 和 RAPIDS 的資料科學工作流程。_
AutoGluon 的[最新版本](https://github.com/awslabs/autogluon/releases/tag/v0.2.0)與 RAPIDS 整合，充分利用 NVIDIA GPU 運算的潛力。AutoGluon可以透過這些整合，在 GPU 上訓練常見的機器學習演算法及提高效能，[以使更多受眾可以使用高效能 AutoML](https://github.com/rapidsai/cloud-ml-examples/tree/main/aws/autogluon)。
### **AutoGluon + RAPIDS 基準**
在[梯度提升機](https://resources.nvidia.com/en-us-xgboost)（GBM）[基準測試套件中使用的](https://github.com/RAMitchell/GBM-Benchmarks) 1.15 億列航空公司資料集，相較於使用 CPU 的 AutoGluon，顯示AutoGluon + RAPIDS 可將訓練加快 25 倍，而準確率為 81.92%，比 XGBoost 基準高 7%。因為固定啟動成本變得較不重要，使 GPU 偏好較長的訓練時間。
_圖 15：相較於使用 CPU 的 AutoGluon，AutoGluon + RAPIDS 將訓練加快了 25 倍，而準確率為 81.92%。_
為了達到 81.92% 的準確率，AutoGluon + RAPIDS 使用 GPU 訓練 4 小時，CPU 則是 4.5 天。
_圖 16：AutoGluon + RAPIDS 使用 GPU 訓練 4 小時，CPU 則是 4.5 天。_
使用 GPU 的 AutoGluon + RAPIDS，不僅速度更快，且成本更低，僅需要 CPU 的 ¼，即可訓練至相同的準確率（AWS EC2 定價：p3.2xlarge 0.9180 美元／小時，m5.2xlarge 0.1480 美元／小時）。
_圖 17：使用 GPU 之 AutoGluon + RAPIDS 的成本更低，僅需要 CPU 的 ¼，即可訓練至相同的準確率。_
### **開始使用**
開始使用 AutoGluon 和 RAPIDS：
- 啟動[搭載 p3.2xlarge GPU 的 AWS EC2 執行個體](https://aws.amazon.com/ec2/instance-types/p3/)
    - 為 CUDA 選擇深度學習 AMI
- [安裝 RAPIDS](https://rapids.ai/start.html)
- [安裝 AutoGluon-Tabular](https://auto.gluon.ai/stable/index.html)
- 試用[使用 Otto Group Product Classification Challenge 資料的 AutoGluon + RAPIDS Python notebook](https://www.kaggle.com/innixma/autogluon-rapids-top-1)
- [AutoGluon 網站](https://auto.gluon.ai/stable/index.html)提供眾多教學，讓開發人員可以利用機器學習處理表格、文字和影像資料（涵蓋分類／迴歸等基本任務以及物件偵測等進階任務）。
### **結論**
AutoGluon AutoML 工具套件可以輕鬆地針對複雜的業務問題，訓練和部署先進、準確的機器學習模型。此外，AutoGluon 與 RAPIDS 整合，可以充分利用 NVIDIA GPU 運算的潛力，將複雜模型的訓練速度加快 40 倍，將預測速度加快 10 倍。
### **若需要更多資訊，請參閱以下資源**
- [AutoGluon 網站](https://auto.gluon.ai/)
- [AutoGluon Github 儲存庫](https://github.com/awslabs/autogluon/)
- [More Progress for Big Impact in the Latest RAPIDS Release](https://medium.com/rapids-ai/more-progress-for-big-impact-in-the-latest-rapids-release-1c96b48fecf9)
- [RAPIDS Accelerates Data Science End-to-End](https://developer.nvidia.com/blog/gpu-accelerated-analytics-rapids/)
- [Accelerating Random Forests Up to 45x Using cuML](https://developer.nvidia.com/blog/accelerating-random-forests-up-to-45x-using-cuml/)
- [Gradient Boosting, Decision Trees, and XGBoost with CUDA](https://developer.nvidia.com/blog/gradient-boosting-decision-trees-xgboost-cuda/)
- [Fast, Accurate, and Simple Models for Tabular Data via Augmented Distillation whitepaper](https://arxiv.org/pdf/2006.14284.pdf)
- [AutoGluon-Tabular:Robust and Accurate AutoML for Structured Data whitepaper](https://arxiv.org/pdf/2003.06505.pdf)
- [XGBoost on GPUs](https://resources.nvidia.com/en-us-xgboost/)