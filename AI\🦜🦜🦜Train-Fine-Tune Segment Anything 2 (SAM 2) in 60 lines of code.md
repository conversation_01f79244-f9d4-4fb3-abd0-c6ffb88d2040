---
tags:
  - AI/FineTuning
  - AI/Image
Created: 2024-08-10T12:29
Comments:
  - https://github.com/ZiyuGuo99/SAM2Point
---
Memo
[==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TRAIN.py?source=post_page-----928dd29a63b3--------------------------------)
How Segment Anything works
Downloading SAM2 and setting environment
[==The repository provides code for running inference with the Meta Segment Anything Model 2 (SAM 2), links for…==](https://github.com/facebookresearch/segment-anything-2?source=post_page-----928dd29a63b3--------------------------------)
[==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code?source=post_page-----928dd29a63b3--------------------------------)
Downloading pre-trained model
Downloading training data
Preparing data reader
Loading the SAM model
Segment Anything General structure
Setting training parameters:
Main training loop
Loss functions
Segmentation loss
Score loss (optional)
Final step: Backpropogation and saving model
Inference: Loading and using the trained model:
[==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)
[==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)

# Memo

/opt/workspace/app/cursor/sam2/sam2Test.ipynb
/opt/workspace/app/cursor/sam2/sam2Test.py

```JavaScript
conda activate mlearn
wget -q https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_tiny.pt -P models/sam2
wget -q https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_small.pt -P models/sam2
wget -q https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_base_plus.pt -P models/sam2
wget -q https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_large.pt -P models/sam2
git clone https://github.com/facebookresearch/segment-anything-2.git
cd segment-anything-2
pip install -e .
above will remove jetson torch and torchvision
we have to install back
pip install  torch-2.3.0-cp310-cp310-linux_aarch64.whl torchvision-0.18.0a0+6043bc2-cp310-cp310-linux_aarch64.whl
from google.colab import drive
drive.mount('/content/drive')

\#Explain PROMPT
https://huggingface.co/microsoft/Florence-2-large
https://github.com/retkowsky/florence-2
\#Issues
https://github.com/facebookresearch/segment-anything-2/issues/48
Flash Attention kernel failed due to: No available kernel
I solved this issue by adding some lines after L22 in sam2\modeling\sam\transformer.py AND pip install -e . again
USE_FLASH_ATTN = False
MATH_KERNEL_ON = True
OLD_GPU = True

Use Florence2(sometimes we need to let human select) to get target object box, then use this box as prompt to SAM2 and then
SAM2 will automatically mask for you. Then use mask area to do blur/green/马赛克 and then output to image then to video
```

[![](https://miro.medium.com/v2/resize:fit:1200/1*GFVDKpN0JF-N8kRtnF2aJg.png)](https://miro.medium.com/v2/resize:fit:1200/1*GFVDKpN0JF-N8kRtnF2aJg.png)
[![](https://miro.medium.com/v2/resize:fill:55:55/1*rlrthvP128DI4GS5mgNnag.jpeg)](https://miro.medium.com/v2/resize:fill:55:55/1*rlrthvP128DI4GS5mgNnag.jpeg)
[==Sagi eppel==](https://medium.com/@sagieppel?source=post_page-----928dd29a63b3--------------------------------)
==13 min read==
==Aug 3, 2024==
[==SAM2 (Segment Anything 2)==](https://ai.meta.com/blog/segment-anything-2/) ==is a new model by Meta aiming to segment anything in an image without being limited to specific classes or domains. What makes this model unique is the scale of data on which it was trained: 11 million images, and 11 billion masks. This extensive training makes SAM2 a powerful starting point for training on new image segmentation tasks.==
==The question you might ask is if SAM can segment anything why do we even need to retrain it? The answer is that SAM is very good at common objects but can perform rather poorly on rare or domain-specific tasks.====

====However, even in cases where SAM gives insufficient results, it is still possible to significantly improve the model’s ability by fine-tuning it on new data. In many cases, this will take less training data and give better results then training a model from scratch.====

====This tutorial demonstrates how to fine-tune SAM2 on new data in just 60 lines of code (excluding comments and imports).==
==**The full training script of the can be found in:**==
[==fine-tune-train_segment_anything_2_in_60_lines_of_code/TRAIN.py at main ·…==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TRAIN.py?source=post_page-----928dd29a63b3--------------------------------)==

==[==----------------------------------------------------------------------------------==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TRAIN.py?source=post_page-----928dd29a63b3--------------------------------)

# [==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TRAIN.py?source=post_page-----928dd29a63b3--------------------------------)

[==github.com==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TRAIN.py?source=post_page-----928dd29a63b3--------------------------------)
[![](https://miro.medium.com/v2/resize:fit:875/1*GFVDKpN0JF-N8kRtnF2aJg.png)](https://miro.medium.com/v2/resize:fit:875/1*GFVDKpN0JF-N8kRtnF2aJg.png)
==SAM2 net diagram taken from== [==here==](https://github.com/facebookresearch/segment-anything-2)

# ==**How Segment Anything works**==

==The main way SAM works is by taking an image and a point in the image and predicting the mask of the segment that contains the point. This approach enables full image segmentation without human intervention and with no limits on the classes or types of segments (as discussed in== [==a previous post==](https://faun.pub/train-pointer-net-for-segmenting-objects-parts-and-materials-in-60-lines-of-code-ca328be8cef2)==).==
==The procedure for using SAM for full image segmentation:==

1. ==Select a set of points in the image==
2. ==Use SAM to predict the segment containing each point==
3. ==Combine the resulting segments into a single map==
   ==While SAM can also utilize other inputs like masks or bounding boxes, these are mainly relevant for interactive segmentation involving human input. For this tutorial, we’ll focus on fully automatic segmentation and will only consider single points input.==
   ==More details on the model are available on the== [==project website.==](https://ai.meta.com/blog/segment-anything-2/)

# ==**Downloading SAM2 and setting environment**==

==The SAM2 can be downloaded from:==
[==GitHub - facebookresearch/segment-anything-2: The repository provides code for running inference…==](https://github.com/facebookresearch/segment-anything-2?source=post_page-----928dd29a63b3--------------------------------)==

==[==-------------------------------------------------------------------------------------------------==](https://github.com/facebookresearch/segment-anything-2?source=post_page-----928dd29a63b3--------------------------------)

### [==The repository provides code for running inference with the Meta Segment Anything Model 2 (SAM 2), links for…==](https://github.com/facebookresearch/segment-anything-2?source=post_page-----928dd29a63b3--------------------------------)

[==github.com==](https://github.com/facebookresearch/segment-anything-2?source=post_page-----928dd29a63b3--------------------------------)
==If you don’t want to copy the training code, you can also download my forked version that already contains the TRAIN.py script.==
[==GitHub - sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code: The repository provides…==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code?source=post_page-----928dd29a63b3--------------------------------)==

==[==-----------------------------------------------------------------------------------------------------------==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code?source=post_page-----928dd29a63b3--------------------------------)

### [==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code?source=post_page-----928dd29a63b3--------------------------------)

[==github.com==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code?source=post_page-----928dd29a63b3--------------------------------)
==Follow the installation instructions on the github repository.==
==In general, you need Python >=3.11 and== [==PyTorch.==](https://pytorch.org/)
==In addition, we will use OpenCV this can be installed using:==
==_pip install opencv-python_==

## ==Downloading pre-trained model==

==You also need to download the pre-trained model from:==
[==**https://github.com/facebookresearch/segment-anything-2?tab=readme-ov-file#download-checkpoints**==](https://github.com/facebookresearch/segment-anything-2?tab=readme-ov-file#download-checkpoints)
==There are several models you can choose from all compatible with this tutorial. I recommend using the== [==small model==](https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_small.pt) ==which is the fastest to train.==

# ==Downloading training data==

==The next step is to download the dataset that will be used to fine-tune the model. For this tutorial, we will use the== [==LabPics1 dataset==](https://zenodo.org/records/3697452/files/LabPicsV1.zip?download=1) ==for segmenting materials and liquids. You can download the dataset from this URL:==
[==https://zenodo.org/records/3697452/files/LabPicsV1.zip?download=1==](https://zenodo.org/records/3697452/files/LabPicsV1.zip?download=1)

# ==**Preparing data reader**==

==The first thing we need to write is the data reader. This will read and prepare the data for the net.==
==The data reader needs to produce:==

1. ==An image==
2. ==Masks of all the segments in the image.==
3. ==And a== [==random point inside each mask==](https://faun.pub/train-pointer-net-for-segmenting-objects-parts-and-materials-in-60-lines-of-code-ca328be8cef2)
   ==Lets start by loading dependencies:==
   ==import numpy as np====

====import torch====

====import cv2====

====import os====

====from sam2.build_sam import build_sam2====

====from sam2.sam2_image_predictor import SAM2ImagePredictor==
==Next we list all the images in the dataset:==
==data_dir=r"LabPicsV1//"====

====data=[]====

====for ff, name in enumerate(os.listdir(data_dir+"Simple/Train/Image/")):====

====data.append({"image":data_dir+"Simple/Train/Image/"+name,"annotation":data_dir+"Simple/Train/Instance/"+name[:-4]+".png"})==
==Now for the main function that will load the training batch. The training batch includes: One random image, all the segmentation masks belong to this image, and a random point in each mask:==
==def read_batch(data):==

```plain
    ent  = data\[np.random.randint(len(data))\]   
    Img = cv2.imread(ent\["image"\])\[...,::-1\]  
    ann\_map = cv2.imread(ent\["annotation"\]) 
    r = np.min(\[1024 / Img.shape\[1\], 1024 / Img.shape\[0\]\])   
    Img = cv2.resize(Img, (int(Img.shape\[1\] \* r), int(Img.shape\[0\] \* r)))  
    ann\_map = cv2.resize(ann\_map, (int(ann\_map.shape\[1\] \* r), int(ann\_map.shape\[0\] \* r)),interpolation=cv2.INTER\_NEAREST)
    mat\_map = ann\_map\[:,:,0\]   
    ves\_map = ann\_map\[:,:,2\]   
    mat\_map\[mat\_map==0\] = ves\_map\[mat\_map==0\]\*(mat\_map.max()+1) 
    inds = np.unique(mat\_map)\[1:\]   
    points= \[\]  
    masks = \[\]   
    for ind in inds:  
        mask=(mat\_map == ind).astype(np.uint8)   
        masks.append(mask)  
        coords = np.argwhere(mask > 0)   
        yx = np.array(coords\[np.random.randint(len(coords))\])   
        points.append(\[\[yx\[1\], yx\[0\]\]\])  
    return Img,np.array(masks),np.array(points), np.ones(\[len(masks),1\])
```

==The first part of this function is choosing a random image and loading it:==
==ent = data[np.random.randint(len(data))]====

====Img = cv2.imread(ent["image"])[...,::-1]====

====ann_map = cv2.imread(ent["annotation"])====

====Note that OpenCV reads images as BGR while SAM expects images as RGB, using […,::-1] to change the image from BGR to RGB.==
==Note that OpenCV reads images as BGR while SAM expects RGB images. By using== ==_[…,::-1]_== ==we change the image from BGR to RGB.==
==SAM expects the image size to not exceed 1024, so we are going to resize the image and the annotation map to this size.==
==r = np.min([1024 / Img.shape[1], 1024 / Img.shape[0]])====

====Img = cv2.resize(Img, (int(Img.shape[1] * r), int(Img.shape[0] * r)))====

====ann_map = cv2.resize(ann_map, (int(ann_map.shape[1] * r), int(ann_map.shape[0] * r)),interpolation=cv2.INTER_NEAREST)==
==An important point here is that when resizing the annotation map (====_ann_map_====) we use== ==_INTER_NEAREST_== ==mode (nearest neighbors). In the annotation map, each pixel value is the index of the segment it belongs to. As a result, it’s important to use resizing methods that do not introduce new values to the map.==
==The next block is specific to the format of the LabPics1 dataset. The annotation map (====_ann_map_====) contains a segmentation map for the vessels in the image in one channel, and another map for the materials annotation in a different channel. We going to merge them into a single map.==
==mat_map = ann_map[:,:,0]====

====ves_map = ann_map[:,:,2]====

====mat_map[mat_map==0] = ves_map[mat_map==0]*(mat_map.max()+1)==
==What this gives us is a a map (====_mat_map_====) in which the value of each pixel is the index of the segment to which it belongs (for example: all cells with value 3 belong to segment 3). We want to transform this into a set of binary masks (0/1) where each mask corresponds to a different segment. In addition, from each mask, we want to extract a single point.==
==inds = np.unique(mat_map)[1:]====

====points= []====

====masks = []====

====for ind in inds:====

====mask = (mat_map == ind).astype(np.uint8)====

====masks.append(mask)====

====coords = np.argwhere(mask > 0)====

====yx = np.array(coords[np.random.randint(len(coords))])====

====points.append([[yx[1], yx[0]]])====

====return Img,np.array(masks),np.array(points), np.ones([len(masks),1])==
==This is it! We got the image (====_Img_====) a list of binary masks corresponding to segments in the image (====_masks_====) and for each mask the coordinate of a single point inside the mask (====_points_====).==
[![](https://miro.medium.com/v2/resize:fit:875/1*5fPMCXxvBulUltXd00ngwg.jpeg)](https://miro.medium.com/v2/resize:fit:875/1*5fPMCXxvBulUltXd00ngwg.jpeg)
==Example for a batch of training data: 1) An Image. 2) List of segments masks. 3) For each mask a single point inside the mask (marked red for visualization only)==

# ==Loading the SAM model==

==Now lets load the net:==
==sam2_checkpoint = "sam2_hiera_small.pt"====

====model_cfg = "sam2_hiera_s.yaml"====

====sam2_model = build_sam2(model_cfg, sam2_checkpoint, device="cuda")====

====predictor = SAM2ImagePredictor(sam2_model)==
==First, we set the path to the model weights in:== ==_sam2_checkpoint_== ==parameter. We downloaded the weights earlier from== [==here==](https://github.com/facebookresearch/segment-anything-2?tab=readme-ov-file#download-checkpoints)==.== ==**“sam2_hiera_small.pt”**== ==refer to the== [==small model==](https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_small.pt) ==but the code will work for any model you choose. Whichever model you choose you need to set the corresponding config file in the== ==_model_cfg_== ==parameter. The config files are already located in the sub folder== ==**_“_sam2_configs/”**== ==of the main repository.==

# ==Segment Anything General structure==

==Before setting training parameters we need to understand the basic structure of the SAM model.====

====SAM is composed of three parts:==

1. ==Image encoder, 2) Prompt encoder, 3) Mask decoder.====

   ====The image encoder is responsible for processing the image and creating the embedding that represents the image. This part consists of a VIT transformer and is the largest component of the net. We usually don’t want to train it, as it already gives good representation and training will demand lots of resources.====

   ====The prompt encoder processes the additional input to the net, in our case the input point.====

   ====The mask decoder takes the output of the image encoder and prompt encoder and produces the final segmentation masks. In general, we want to train only the mask decoder and maybe the prompt encoder. These parts are lightweight and can be fine-tuned fast with a modest GPU.==

# ==Setting training parameters:==

==We can enable the training of the mask decoder and prompt encoder by setting:==
==predictor.model.sam_mask_decoder.train(True)====

====predictor.model.sam_prompt_encoder.train(True)==
==Next, we define the standard adamW optimizer:==
==optimizer=torch.optim.AdamW(params=predictor.model.parameters(),lr=1e-5,weight_decay=4e-5)==
==We also going to use mixed precision training which is just a more memory-efficient training strategy:==
==scaler = torch.cuda.amp.GradScaler()==

# ==**Main training loop**==

==Now lets build the main training loop. The first part is reading and preparing the data:==
==for itr in range(100000):====

====with torch.cuda.amp.autocast():====

====image,mask,input_point, input_label = read_batch(data)====

====if mask.shape[0]==0: continue====

====predictor.set_image(image)==
==First we cast the data to mix precision for efficient training:==
==with torch.cuda.amp.autocast():==
==Next, we use the reader function we created earlier to read training data:==
==image,mask,input_point, input_label = read_batch(data)==
==We take the image we loaded and pass it through the image encoder (the first part of the net):==
==predictor.set_image(image)==
==Next, we process the input points using the net prompt encoder:==
==mask_input, unnorm_coords, labels, unnorm_box = predictor._prep_prompts(input_point, input_label, box=None, mask_logits=None, normalize_coords=True)====

====sparse_embeddings, dense_embeddings = predictor.model.sam_prompt_encoder(points=(unnorm_coords, labels),boxes=None,masks=None,)==
==Note that in this part we can also input boxes or masks but we are not going to use these options.==
==Now that we encoded both the prompt (points) and the image we can finally predict the segmentation masks:==
==batched_mode = unnorm_coords.shape[0] > 1====

====high_res_features = [feat_level[-1].unsqueeze(0) for feat_level in predictor._features["high_res_feats"]]====

====low_res_masks, prd_scores, _, _ = predictor.model.sam_mask_decoder(image_embeddings=predictor._features["image_embed"][-1].unsqueeze(0),image_pe=predictor.model.sam_prompt_encoder.get_dense_pe(),sparse_prompt_embeddings=sparse_embeddings,dense_prompt_embeddings=dense_embeddings,multimask_output=True,repeat_image=batched_mode,high_res_features=high_res_features,)====

====prd_masks = predictor._transforms.postprocess_masks(low_res_masks, predictor._orig_hw[-1])==
==The main part in this code is the== ==_**model.sam_mask_decoder**_== ==which runs the mask_decoder part of the net and generates the segmentation masks (====_low_res_masks_====) and their scores (====_prd_scores_====).==
==These masks are in lower resolution than the original input image and are resized to the original input size in the== ==_**postprocess_masks**_== ==function.==
==This gives us the final prediction of the net: 3 segmentation masks (====_prd_masks_====) for each input point we used and the masks scores (====_prd_scores_====).== ==_prd_masks_== ==contains 3 predicted masks for each input point but we only going to use the first mask for each point.== ==_prd_scores_== ==contains a score of how good the net thinks each mask is (or how sure it is in the prediction).==

# ==Loss functions==

## ==Segmentation loss==

==Now we have the net predictions we can calculate the loss. First, we calculate the segmentation loss, which means how good the predicted mask is compared to the ground true mask. For this, we use the standard cross entropy loss.==
==First we need to convert prediction masks (====_prd_mask_====) from logits into probabilities using the sigmoid function:==
==prd_mask = torch.sigmoid(prd_masks[:, 0])==
==Next we convert the ground truth mask into a torch tensor:==
==prd_mask = torch.sigmoid(prd_masks[:, 0])==
==Finally, we calculate the cross entropy loss (====_seg_loss_====) manually using the ground truth (====_gt_mask_====) and predicted probability maps (====_prd_mask_====):==
==seg_loss = (-gt_mask * torch.log(prd_mask + 0.00001) - (1 - gt_mask) * torch.log((1 - prd_mask) + 0.00001)).mean()==
==(we add 0.0001 to prevent the log function from exploding for zero values).==

## ==Score loss (optional)==

==In addition to the masks, the net also predicts the score for how good each predicted mask is. Training this part is less important but can be useful . To train this part we need to first know what is the true score of each predicted mask. Meaning, how good the predicted mask actually is. We are going to do it by comparing the GT mask and the corresponding predicted mask using intersection over union (IOU) metrics. IOU is simply the overlap between the two masks, divided by the combined area of the two masks. First, we calculate the intersection between the predicted and GT mask (the area in which they overlap):==
==inter = (gt_mask * (prd_mask > 0.5)).sum(1).sum(1)==
==We use threshold== ==_(prd_mask > 0.5)_== ==to turn the prediction mask from probability to binary mask.==
==Next, we get the IOU by dividing the intersection by the combined area (union) of the predicted and gt masks:==
==iou = inter / (gt_mask.sum(1).sum(1) + (prd_mask > 0.5).sum(1).sum(1) - inter)==
==We going to use the IOU as the true score for each mask, and get the score loss as the absolute difference between the predicted scores and the IOU we just calculated.==
==score_loss = torch.abs(prd_scores[:, 0] - iou).mean()==
==Finally, we merge the segmentation loss and score loss (giving much higher weight to the first):==
==loss = seg_loss+score_loss*0.05==

# ==Final step: Backpropogation and saving model==

==Once we get the loss everything is completely standard. We calculate backpropogation and update weights using the optimizer we made earlier:==
==predictor.model.zero_grad()====

====scaler.scale(loss).backward()====

====scaler.step(optimizer)====

====scaler.update()==
==We also want to save the trained model once every 1000 steps:==
==if itr%1000==0: torch.save(predictor.model.state_dict(), "model.torch")==
==Since we already calculated the IOU we can display it as a moving average to see how well the model prediction are improving over time:==
==if itr==0: mean_iou=0====

====mean_iou = mean_iou * 0.99 + 0.01 * np.mean(iou.cpu().detach().numpy())====

====print("step)",itr, "Accuracy(IOU)=",mean_iou)==
==And that it, we have trained/ fine-tuned the Segment-Anything 2 in less than 60 lines of code (not including comments and imports). After about 25,000 steps you should see major improvement .==
==The model will be saved to “model.torch”.==
==You can find the full training code at:==
[==https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TRAIN.py==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TRAIN.py)
==To see how to load and use the model we just trained check the next section.==

# ==Inference: Loading and using the trained model:==

==Now that the model as been fine-tuned, let’s use it to segment an image.==
==We going to do this using the following steps:==

1. ==Load the model we just trained.==
2. ==Give the model an image and a bunch of random points. For each point the net will predict the segment mask that contain this point and a score.==
3. ==Take these masks and stitch them together into one segmentation map.==
   ==The full code for doing that is available at:==
   [==fine-tune-train_segment_anything_2_in_60_lines_of_code/TEST_Net.py at main ·…==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)==

==[==--------------------------------------------------------------------------------------==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)

### [==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)

[==github.com==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)
==First, we load the dependencies and cast the weights to float16 this makes the model much faster to run (only possible for inference).==
==torch.autocast(device_type="cuda", dtype=torch.bfloat16).__enter__()==
==Next, we load a sample== [==image==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/sample_image.jpg) ==and a mask of the image region we want to segment (download== [==image==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/sample_image.jpg)==/==[==mask==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/sample_mask.png)==):==
==image_path = r"sample_image.jpg"====

====mask_path = r"sample_mask.png"====

====def read_image(image_path, mask_path):====

====img = cv2.imread(image_path)[...,::-1]====

====mask = cv2.imread(mask_path,0)==

```plain
    r = np.min(\[1024 / img.shape\[1\], 1024 / img.shape\[0\]\])  
    img = cv2.resize(img, (int(img.shape\[1\] \* r), int(img.shape\[0\] \* r)))  
    mask = cv2.resize(mask, (int(mask.shape\[1\] \* r), int(mask.shape\[0\] \* r)),interpolation=cv2.INTER\_NEAREST)  
    return img, mask  
```

==image,mask = read_image(image_path, mask_path)==
==Sample 30 random points inside the region we want to segment:==
==num_samples = 30====

====def get_points(mask,num_points):====

====points=[]====

====for i in range(num_points):====

====coords = np.argwhere(mask > 0)====

====yx = np.array(coords[np.random.randint(len(coords))])====

====points.append([[yx[1], yx[0]]])====

====return np.array(points)====

====input_points = get_points(mask,num_samples)==
==First, load the standard SAM model (same as in training)==
==sam2_checkpoint = "sam2_hiera_small.pt"====

====model_cfg = "sam2_hiera_s.yaml"====

====sam2_model = build_sam2(model_cfg, sam2_checkpoint, device="cuda")====

====predictor = SAM2ImagePredictor(sam2_model)==
==Next, Load the weights of the model we just trained (model.torch):==
==predictor.model.load_state_dict(torch.load("model.torch"))==
==Run the fine-tuned model to predict a mask for every point we selected:==
==with torch.no_grad():====

====predictor.set_image(image)====

====masks, scores, logits = predictor.predict(====

====point_coords=input_points,====

====point_labels=np.ones([input_points.shape[0],1])====

====)==
==Now we have a list of predicted masks and their scores. We want to somehow stitch them into a single consistent segmentation map. However, many of the masks overlap and might be inconsistent with each other. Since we randomly chose the points so it’s very likely that some points will fall in the same segment.====

====The approach to stitching is simple, we will sort the predicted masks according to their predicted scores:==
==np_masks = np.array(masks[:,0].cpu().numpy())====

====np_scores = scores[:,0].float().cpu().numpy()====

====shorted_masks = np_masks[np.argsort(np_scores)][::-1]==
==Now lets create an empty segmentation map and occupancy map:==
==seg_map = np.zeros_like(shorted_masks[0],dtype=np.uint8)====

====occupancy_mask = np.zeros_like(shorted_masks[0],dtype=bool)==
==Next, we add the masks one by one (from high to low score) to the segmentation map. We only add a mask if it’s consistent with the masks that were previously added, which means only if the mask we want to add has less than 15% overlap with already occupied areas.==
==for i in range(shorted_masks.shape[0]):====

====mask = shorted_masks[i]====

====if (mask*occupancy_mask).sum()/mask.sum()>0.15: continue====

====mask[occupancy_mask]=0====

====seg_map[mask]=i+1====

====occupancy_mask[mask]=1==
==And this is it.==
==_seg_mask_== ==now contains the predicted segmentation map with different values for each segment and 0 for the background.==
==We can turn this into a color map using:==
==rgb_image = np.zeros((seg_map.shape[0], seg_map.shape[1], 3), dtype=np.uint8)====

====for id_class in range(1,seg_map.max()+1):====

====rgb_image[seg_map == id_class] = [np.random.randint(255), np.random.randint(255), np.random.randint(255)]==
==And display:==
==cv2.imshow("annotation",rgb_image)====

====cv2.imshow("mix",(rgb_image/2+image/2).astype(np.uint8))====

====cv2.imshow("image",image)====

====cv2.waitKey()==
[![](https://miro.medium.com/v2/resize:fit:875/1*dP6bvtSAs1Gcq59-b5FNUQ.jpeg)](https://miro.medium.com/v2/resize:fit:875/1*dP6bvtSAs1Gcq59-b5FNUQ.jpeg)
==Example for segmentation results using fine-tuned SAM2==
==The full inference code is available at:==
[==fine-tune-train_segment_anything_2_in_60_lines_of_code/TEST_Net.py at main ·…==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)==

==[==--------------------------------------------------------------------------------------==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)

### [==The repository provides code for training/fine tune the Meta Segment Anything Model 2 (SAM 2) …==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)

[==github.com==](https://github.com/sagieppel/fine-tune-train_segment_anything_2_in_60_lines_of_code/blob/main/TEST_Net.py?source=post_page-----928dd29a63b3--------------------------------)
