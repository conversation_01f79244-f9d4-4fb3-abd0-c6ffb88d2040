---
Updated: 2024-08-22T20:31
tags:
  - AI->-Infra
  - AI->-Tools
URL: https://zhuanlan.zhihu.com/p/43628167?utm_id=0
Created: 2024-03-21T20:35
---
[![](https://pica.zhimg.com/v2-2f10fcb3651ef05302c43d7779a1b5bc_1440w.jpg?source=172ae18b)](https://pica.zhimg.com/v2-2f10fcb3651ef05302c43d7779a1b5bc_1440w.jpg?source=172ae18b)
ngrok 是一个反向代理，通过在公共端点和本地运行的 Web 服务器之间建立一个安全的通道，实现内网主机的服务可以暴露给外网。ngrok 可捕获和分析所有通道上的流量，便于后期分析和重放，所以ngrok可以很方便地协助服务端程序测试。
ngrok的使用并不复杂，主要步骤如下：
1. 进入ngrok官网（[ngrok.com/](https://link.zhihu.com/?target=https%3A//ngrok.com/)），注册ngrok账号并下载ngrok；
2. 根据官网给定的授权码，运行如下授权命令；
- 授权码
[![](https://pic2.zhimg.com/80/v2-09aaadb7bb2353e540bf21f4865d82dd_720w.webp)](https://pic2.zhimg.com/80/v2-09aaadb7bb2353e540bf21f4865d82dd_720w.webp)
- 授权命令
ngrok authtoken 授权码
授权码和账户是绑定的，在授权命令运行后，ngrok会将授权码保存在`~/.ngrok2/ngrok.yml`中，所以只需要运行一次，以后都可以使用。
3. 根据需要，运行命令开发端口。
ngrok http 80801
需要注意的是，这里的端口`8080`可以根据需要替换成其他端口。这条命令的意思是将本地8080端口对应的服务暴露到外网中。
运行后将出现如下结果：
[![](https://pic4.zhimg.com/80/v2-e4dc34ba67efe9ec792dcf0af9d2ea2f_720w.webp)](https://pic4.zhimg.com/80/v2-e4dc34ba67efe9ec792dcf0af9d2ea2f_720w.webp)
红色标注的方框内，第一个是http协议对应的外网地址，第二个是https协议对应的外网地址。这样，凡是访问[http://1a969631.ngrok.io](https://link.zhihu.com/?target=http%3A//1a969631.ngrok.io/)的请求都将发送到localhost:8080。
4.其他
每次启动ngrok都会分配一个新的外网域名，所以需要每次更换配置或者更换访问地址，不太方便。当然，ngrok也提供了解决方法，那就是付费，可以设置固定域名。
原文转载☞[ngrok使用教程](https://link.zhihu.com/?target=http%3A//www.chuanwazy.com/show%3Fid%3D246)
更多资源尽在[川娃资源网](https://link.zhihu.com/?target=http%3A//www.chuanwazy.com/resources)
  
# Download
```Python
\#Download
https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz
F:\OneDrive\Tools\ngrok\ngrok-v3-stable-linux-amd64.tgz
sudo tar -xvzf ~/Downloads/https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz -C /usr/local/bin
\#or install on ARM 
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc \
	| sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null \
	&& echo "deb https://ngrok-agent.s3.amazonaws.com buster main" \
	| sudo tee /etc/apt/sources.list.d/ngrok.list \
	&& sudo apt update \
	&& sudo apt install ngrok


curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc \
	| sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null \
	&& echo "deb https://ngrok-agent.s3.amazonaws.com buster main" \
	| sudo tee /etc/apt/sources.list.d/ngrok.list \
	&& sudo apt install ngrok

snap install ngrok



\#get tokens
https://dashboard.ngrok.com/get-started/your-authtoken
https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz
ngrok config add-authtoken *************************************************
\#deploy app online
ngrok http http://localhost:8080
ngrok http 80 \
  --oauth google \
  --oauth-allow-email <EMAIL> \
  --oauth-allow-domain example.com
  
ngrok http http://localhost:8080 --basic-auth 'username:a-very-secure-password'  
```
  
  
```JavaScript
as free version , only can start one session . We need to setup config to make multiple tunnel
(base) ray@jethome:~/.config/ngrok$ cat ngrok.yml
version: "2"
authtoken: *************************************************
tunnels:
   slackagent:
     addr: 5003
     proto: http
     \#hostname: jethome
     host_header: slackagent.jethome
   flowise:
     addr: 8300
     proto: http
     \#hostname: jethome
     host_header: flowise.jethome
ngrok start --all


vi /home/<USER>/snap/ngrok/246/.config/ngrok/ngrok.yml
[23:14:31]ray@jethome64:~$ cat /home/<USER>/snap/ngrok/246/.config/ngrok/ngrok.yml
version: "3"
agent:
    authtoken: *************************************************
tunnels:
   slackagent:
     addr: 8509
     proto: http
     host_header: mcpserver.jethome64

[22:33:07]ray@jethome64:~$ ngrok start --all

```
## Setup in Cursor
```
1. Start ngrok in Jethome64
   ngrok start --all
2. ollama cp qwen2.5:0.5b gemini-2.0-flash-thinking-exp
-- solve below error
(status code 404)
{"error":{"message":"model \"gemini-2.0-flash-thinking-exp\" not found, try pulling it first","type":"api_error","param":null,"code":null}}

 ollama run gemini-2.0-flash-thinking-exp:latest
1. https://bd80-240d-1a-4f3-9a00-f2da-f33d-4988-8657.ngrok-free.app/v1
```
# Configuring Multiple Tunnels in ngrok.yml (Version 3) - Updated

## Key Changes in Version 3

As of ngrok v3.16.0 (released in late 2024), there have been several important changes:

1. **Terminology Change**: "Tunnels" are now called "Agent Endpoints"
2. **Configuration Structure**: The structure has been completely redesigned
3. **Version Number**: Must use `version: 3` instead of `version: 2`

## Correct ngrok.yml Structure for Version 3

```yaml
version: 3
agent:
  authtoken: your_auth_token_here
  log_level: info
  log_format: json

endpoints:
  - name: website
    url: https://example1.ngrok.app
    description: "Frontend application"
    metadata: '{ "id": "frontend-app" }'
    upstream:
      url: localhost:8080
      
  - name: api
    url: https://api.example.com
    description: "Backend API"
    upstream:
      url: localhost:3000
      protocol: http2
    
  - name: database
    url: tcp://1.tcp.ngrok.io:12345
    upstream:
      url: localhost:5432
```

## Key Structure Changes

1. **Version Field**: Must be `version: 3`
2. **Agent Config**: All general settings now go under the `agent:` section
3. **Endpoints Format**: Endpoints are now defined as an array with `-` prefix  
4. **URL Format**: Combined protocol, domain, and port into `url:` field
5. **Address Format**: Local service address now defined as `upstream.url:`

## Advanced Configuration

```yaml
version: 3
agent:
  authtoken: your_auth_token_here
  web_addr: 127.0.0.1:4040
  
endpoints:
  - name: secure-site
    url: https://secure.example.com
    description: "Secured website with IP restrictions"
    upstream:
      url: localhost:8443
    traffic_policy:
      on_http_request:
        - name: "IP restriction policy"
          actions:
            - type: restrict-ips
              config:
                enforce: true
                allow:
                  - "***********/24"
                  - "10.0.0.0/8"
```

https://ngrok.com/docs/guides/other-guides/how-to-set-up-auth-on-your-endpoint-using-traffic-policy/?start-location=config
https://ngrok.com/docs/agent/config/v3/
https://ngrok.com/docs/integrations/google/oauth/
https://ngrok.com/blog-post/authentication-with-ngrok

ngrok http 8511 --oauth=google --oauth-allow-email=<EMAIL>

```
(search-server) [20:43:38]ray@jethome64 (base) ➜  ngrok cat ngrok.yml
version: "3"
agent:
    authtoken: *************************************************
tunnels:
   slackagent:
     addr: 8234
     proto: http
     host_header: ollama.jethome64
   omni_server:
     addr: 8511
     proto: http
     host_header: ollama.jethome64
     traffic_policy:
       on_http_request:
         - actions:
            - type: oauth
              config:
                provider: google


version: "3"
agent:
    authtoken: *************************************************
tunnels:
   slackagent:
     addr: 8234
     proto: http
     host_header: ollama.jethome64
   omni_server:
     addr: 8511
     proto: http
     host_header: ollama.jethome64
     traffic_policy:
       on_http_request:
         - actions:
            - type: basic-auth
              config:
                credentials:
                  - netcaster:qapl1209ws

```
ngrok start omni_server

```
(search-server) [21:52:40]ray@jethome64 (base) ➜  ngrok cat ngrok.yml
version: "3"
agent:
    authtoken: *************************************************
endpoints:
  - name: omni_server
    url: https://omni.ngrok.pro
    upstream:
      url: http://0.0.0.0:8511
    traffic_policy:
       on_http_request:
         - actions:
            - type: oauth
              config:
                provider: google
         - expressions:
             - "!(actions.ngrok.oauth.identity.email in ['<EMAIL>'])"
           actions:
            - type: deny

```

 google oauth
 ```
 version: "3"
 agent:
     authtoken: *************************************************
 endpoints:
   - name: omni_server
     url: https://omni.ngrok.pro
     upstream:
       url: http://0.0.0.0:8511
     traffic_policy:
       on_http_request:
         - actions:
           - type: oauth
             config:
               provider: google
               client_id: "216274997597-a6g0pkqpv05f0h140qcs1i5dja65eul6.apps.googleusercontent.com"
               client_secret: "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"
               scopes:
                 - https://www.googleapis.com/auth/userinfo.profile
                 - https://www.googleapis.com/auth/userinfo.email
   - name: dart
     url: https://dart.ngrok.pro
     upstream:
       url: http://0.0.0.0:8516
   - name: sequential-thinking
     url: https://thinker.ngrok.pro
     upstream:
       url: http://0.0.0.0:8520
 
```

### mix endpoint and tunnels
```

version: "3"
agent:
    authtoken: *************************************************
endpoints:
  - name: omni_server
    url: https://omni.ngrok.pro
    upstream:
      url: http://0.0.0.0:8511
#  - name: dart
#    url: https://dart.ngrok.pro
#    upstream:
#      url: http://0.0.0.0:8516
  - name: sequential-thinking
    url: https://thinker.ngrok.pro
    upstream:
      url: http://0.0.0.0:8520
tunnels:
  dart:
    addr: 8516
    proto: http
    host_header: ollama.jethome64
    traffic_policy:
      on_http_request:
         - actions:
           - type: basic-auth
             config:
               credentials:
                 - raysheng:qapl1209ws

```

---

## Starting Tunnels

The commands remain similar:

```bash
# Start all tunnels
ngrok start --all

# Start specific tunnels
ngrok start website api
```

## Backwards Compatibility Note

Version 3 still supports the old `tunnels:` configuration for backward compatibility, but it's recommended to migrate to the new `endpoints:` format as the old format is deprecated and will be removed in a future v4 release.
