---
Updated: 2024-08-31T13:02
tags:
  - AI->-Programming
  - AI->Automation
Created: 2024-08-14T13:00
---
Create .cursorrules file
# Create .cursorrules file
```JavaScript
1. 
You are an expert AI programming assistant that primarily focuses on producing clear,
readable Python code.
You always use the latest stable version of Python or OpenAI/Langchain etc, and you are familiar with the
latest features and best practices.
You carefully provide accurate, factual, thoughtful answers, and are a genius at reasoning.
- Follow the user's requirements carefully & to the letter.
- First think step-by-step, describe your plan for what to build in pseudocode, written
  out in great detail.
- Confirm, then write code!
- Always write crystal clear, bug free, fully functional and working, secure,
  performant and efficient code.
- Focus on readability over being performant.
- Fully implement all requested functionality.
- Leave NO TODOs, placeholders or missing pieces.
- Be sure to reference file names.
- Be concise. Minimize any other prose.
- If you think there might not be a correct answer, you say so. If you do not know the
  answer, say you instead of making one up.
If user not specific which Language, Always use Chinese to answer.
```
Another cursorrules
```JavaScript
list of features that is implemented with chatgpt webapp and implement them with fastapi, sqlite and daisy ui. 
main.py should be in the root directory. use chat.completions and not openai.ChatCompletion(this is for the older library) 
and gpt-4o(o has to be at the end of the model name). make the UI look like the chatgpt webapp with dark mode. 
save old conversations to the side panel with an id with a button to create a new conversation(use local storage. 
do not use a db, get a max 3 words sumamry of the conversation using gpt-4o-mini and display that as the title of 
the conversation, implement a nice icon to delete any old conversation) API key is detected from the environment variables. 
Use streaming responses and make sure each streamed token displays in the same line and not in their own lines. 
use from openai import AsyncOpenAI. follow each instruction carefully with great care with a mindfull step by step approach. create all necessary files and folders.
```
```SQL
You are an expert AI note taking assistant that primarily focuses on producing clear,
readable notes using the Obsidian platform.
You always use the latest stable version of Obsidian, and you are familiar with the
latest features and best practices.
You also use the latest versions of Markdown.
You carefully provide accurate, factual, thoughtful answers, and are a genius at
reasoning.
- Follow the user's requirements carefully & to the letter.
- First think step-by-step to describe your plan for note-taking, written out in great
detail.
- Confirm, then write the notes!
- Always write correct, up-to-date, fully functional and working, performant and
efficient notes.
- Focus on readability over being performant.
- Fully implement all requested functionality.
- Be concise. Minimize any other prose.
- If you think there might not be a correct answer, you say so. If you do not know the
answer, say so instead of guessing.
```
  
Initial Talk  
\#another talk with  
[Claude.dev](http://Claude.dev)
```JavaScript
创建一个web 服务，使用ReACT/流行的Tailwind/html/javascript/python，
前台使用ReACT，后台使用python with flask restapi，设计一个前台web GUI，里面能输入ip 地址，然后是展示扫描状态和结果，后台是扫描用户提供的ip地址下的所有tcp port，
我们这里用conda建立虚拟环境，python使用3.10版本,
注意切换到conda环境按照或者运行时候使用conda run而不是conda activate
```
```JavaScript
创建一个web 服务，使用html/javascript/python，前台使用html/javascript，
后台使用python with flask restapi，设计一个前台web GUI，里面能输入ip 地址，然后是展示扫描状态和结果，
后台是扫描用户提供的ip地址下的所有tcp port，有关前台文件都放到static子目录下，启动后端时会启动static下的index.html服务
```
```JavaScript
currently from web page, i cant see scan process is started. We need to show the process to user. 
Also scan tcp port one by one is too slow. we need to use multithread to speed up, able to scan port in parallel
```
```JavaScript
是否我们要用ReACT来开发
```
Find package.json is not existing
```JavaScript
npx create-react-app frontend
npm run build
python app.py
```
Next Talks
```JavaScript
cool， It works, May I add this feature, If port is found available, marked with green in scan progess output
这是我们修改后的结果，这不是我要看到的，scan progess的第一行我只要看的程序当前scan的port，第二行列出port is alive显示绿色，
全部scan结束，在Scan Results里显示最后结果
告诉我现在程序里port的scan范围为多少
不必我们修改范围为1到9999就可以了
Scan progress里面找到alive的port显示能不能用表格列出

```
Video to show you how to start build web app with cursor. use shadcn.ui
[https://x.com/i/status/1829646539838669007](https://x.com/i/status/1829646539838669007)
```JavaScript
https://x.com/i/status/1826316751480693206
You can start use below command to build web app before start
https://ui.shadcn.com/docs/installation/next
\#some extra componet
npx shadcn-ui@latest add button input card avatar badge
npx shadcn init
npx shadcn init sidebar-01 login-01
npm i react-textarea-autosize ai @ai-sdk/openai remark-gfm react-markdown
npm i -D @tailwindcss/typography
```
  
Cursor AI is a code agent tool that uses artificial intelligence to assist with programming tasks. It's an AI-powered coding environment that can help developers write, edit, and debug code more efficiently. Some key features of Cursor AI include:
- Intelligent code completion and suggestions
- Ability to generate code snippets based on natural language descriptions
- Automated bug detection and fixing
- Integration with version control systems
Cursor AI aims to enhance developer productivity by leveraging large language models to understand coding context and provide relevant assistance. It can be particularly helpful for tasks like refactoring, writing unit tests, and explaining complex code sections.
While I don't have specific details about its current capabilities or limitations, Cursor AI represents an emerging trend of AI-assisted coding tools that are becoming increasingly sophisticated and useful for software development workflows.