---
DocFlag:
  - Tested
Updated: 2024-03-18T11:09
tags:
  - AI->-Video
Created: 2023-06-15T00:04
---
Take a video and replace the face in it with a face of your choice. You only need one image of the desired face. No dataset, no training.
That's it, that's the software. You can watch some demos [here](https://drive.google.com/drive/folders/1KHv8n_rd3Lcr2v7jBq1yPSTWM554Gq8e?usp=sharing).
![[Notion/AI/🦜🦜🦜GitHub - s0md3v-roop- one-click deepfake (face swap)/attachments/demo.gif|demo.gif]]
## Disclaimer
Better deepfake software than this already exist, this is just a hobby project I created to learn about AI. Users must get consent from the concerned people before using their face and must not hide the fact that it is a deepfake when posting content online. I am not responsible for malicious behaviour of end-users.
To prevent misuse, it has a built-in check which prevents the program from working on inappropriate media.
## How do I install it?
**Issues according installation will be closed without ceremony from now on, we cannot handle the amount of requests.**
There are two types of installations: basic and gpu-powered.
- **Basic:** It is more likely to work on your computer but it will also be very slow. You can follow instructions for the basic install [here](https://github.com/s0md3v/roop/wiki/1.-Installation).
    
- **GPU:** If you have a good GPU and are ready for solving any software issues you may face, you can enable GPU which is wayyy faster. To do this, first follow the basic install instructions given above and then follow GPU-specific instructions [here](https://github.com/s0md3v/roop/wiki/2.-GPU-Acceleration).
    
## How do I use it?

> Note: When you run this program for the first time, it will download some models ~300MB in size.
Executing `python run.py` command will launch this window:
![[gui-demo.png]]
Choose a face (image with desired face) and the target image/video (image/video in which you want to replace the face) and click on `Start`. Open file explorer and navigate to the directory you select your output to be in. You will find a directory named `<video_title>` where you can see the frames being swapped in realtime. Once the processing is done, it will create the output file. That's it.
Don't touch the FPS checkbox unless you know what you are doing.
Additional command line arguments are given below:
```Plain
options:
 -h, --help show this help message and exit
 -f SOURCE_IMG, --face SOURCE_IMG
 use this face
 -t TARGET_PATH, --target TARGET_PATH
 replace this face
 -o OUTPUT_FILE, --output OUTPUT_FILE
 save output to this file
 --keep-fps maintain original fps
 --keep-frames keep frames directory
 --all-faces swap all faces in frame
 --max-memory MAX_MEMORY
 maximum amount of RAM in GB to be used
 --cpu-cores CPU_CORES
 number of CPU cores to use
 --gpu-threads GPU_THREADS
 number of threads to be use for the GPU
 --gpu-vendor {apple,amd,intel,nvidia}
 choice your GPU vendor
```
Looking for a CLI mode? Using the -f/--face argument will make the program in cli mode.
## Future plans
- Improve the quality of faces in results
- Replace a selective face throughout the video
- Support for replacing multiple faces
## Credits
- [henryruhs](https://github.com/henryruhs): for being an irreplacable contributor to the project
- [ffmpeg](https://ffmpeg.org/): for making video related operations easy
- [deepinsight](https://github.com/deepinsight): for their [insightface](https://github.com/deepinsight/insightface) project which provided a well-made library and models.
- and all developers behind libraries used in this project.
---
---
  
```JavaScript
<<< TEST>>>
https://www.youtube.com/watch?v=OI1LEN-SgLM
If you aren't good with following commands, here's a video tutorial
install python (and pip too if neeed)
install git
install ffmpeg
If you are on Windows, install Visual Studio 2022 (with desktop development C++).
git clone https://github.com/s0md3v/roop
cd roop && pip install -r requirements.txt
Download this file and keep it in roop directory. Mirror \#1, Mirror \#2, Mirror \#3, Mirror \#4. Rename it to inswapper_128.onnx if it isn't already.
https://drive.google.com/file/d/1eu60OrRtn4WhKrzM4mQv4F3rIuyUXqfl/view?usp=drive_link
and copy to
[raysheng@MONSTER:/opt/workspace/roop]$
----- below is my test -------
sudo su - raysheng
git clone https://github.com/s0md3v/roop.git

conda update -n base -c defaults conda
conda create -n roop
conda activate roop
conda search python
conda config --set solver classic
conda install python==3.10.9
make sure your ffmpeg is there
(roop) [raysheng@MONSTER:/opt/workspace/roop]$ ffmpeg
ffmpeg version 5.1.3 Copyright (c) 2000-2022 the FFmpeg developers
  built with gcc 11 (GCC)
\#pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
\#pip install tensorflow
\#pip install onnxruntime-gpu
\#modified some line in requirements.txt. otherwise you will get numpy version confliction
\#we manually install tensorflow and onnxruntime-gpu
pip install -r requirements.txt

setup xwin
export DISPLAY=localhost:10.0
xauth add MONSTER/unix:10  MIT-MAGIC-COOKIE-1  c7afd0f48866af265d16841739f26e75
xclock
python run.py
```