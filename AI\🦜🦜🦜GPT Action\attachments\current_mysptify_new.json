{"openapi": "3.1.0", "info": {"title": "Spotify Web API", "version": "1.0.0", "description": "API for interacting with Spotify Web API, including authentication and artist data retrieval."}, "servers": [{"url": "https://api.spotify.com/v1", "description": "Spotify Web API server"}], "paths": {"/artists/{artist_id}": {"get": {"operationId": "getArtistData", "summary": "Retrieves data for a specific artist using their Spotify ID", "parameters": [{"name": "artist_id", "in": "path", "required": true, "description": "The Spotify ID of the artist", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArtistResponse"}}}}}, "security": [{"oauth2": []}]}}, "/me/player/next": {"post": {"tags": ["Player"], "operationId": "skip-users-playback-to-next-track", "summary": "<PERSON><PERSON> To <PERSON>\n", "description": "Skips to next track in the user’s queue.\n", "parameters": [{"name": "device_id", "required": false, "in": "query", "schema": {"title": "Device ID", "description": "The id of the device this command is targeting. If not supplied, the user's currently active device is the target.", "example": "0d1841b0976bae2a3a310dd74c0f3df354899bc8", "type": "string"}}], "responses": {"204": {"description": "Command sent"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "429": {"$ref": "#/components/responses/TooManyRequests"}}, "security": [{"oauth2": ["user-modify-playback-state"]}]}}, "/me/player/previous": {"post": {"tags": ["Player"], "operationId": "skip-users-playback-to-previous-track", "summary": "<PERSON>p <PERSON>\n", "description": "Skips to previous track in the user’s queue.\n", "parameters": [{"name": "device_id", "required": false, "in": "query", "schema": {"title": "Device ID", "description": "The id of the device this command is targeting. If\nnot supplied, the user's currently active device is the target.\n", "example": "0d1841b0976bae2a3a310dd74c0f3df354899bc8", "type": "string"}}], "responses": {"204": {"description": "Command sent"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "429": {"$ref": "#/components/responses/TooManyRequests"}}, "security": [{"oauth2": ["user-modify-playback-state"]}]}}, "/me/player/play": {"put": {"tags": ["Player"], "operationId": "start-a-users-playback", "summary": "Start/Resume Playback\n", "description": "Start a new context or resume current playback on the user's active device.\n", "parameters": [{"name": "device_id", "required": false, "in": "query", "schema": {"title": "Device ID", "description": "The id of the device this command is targeting. If not supplied, the user's currently active device is the target.", "example": "0d1841b0976bae2a3a310dd74c0f3df354899bc8", "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"example": {"context_uri": "spotify:album:5ht7ItJgpBH7W6vJ5BqpPr", "offset": {"position": 5}, "position_ms": 0}, "type": "object", "additionalProperties": true, "properties": {"context_uri": {"type": "string", "description": "Optional. Spotify URI of the context to play.\nValid contexts are albums, artists & playlists.\n`{context_uri:\"spotify:album:1Je1IMUlBXcx1Fz0WE7oPT\"}`\n"}, "uris": {"type": "array", "description": "Optional. A JSON array of the Spotify track URIs to play.\nFor example: `{\"uris\": [\"spotify:track:4iV5W9uYEdYUVa79Axb7Rh\", \"spotify:track:1301WleyT98MSxVHPZCA6M\"]}`\n", "items": {"type": "string"}}, "offset": {"type": "object", "description": "Optional. Indicates from where in the context playback should start. Only available when context_uri corresponds to an album or playlist object\n\"position\" is zero based and can’t be negative. Example: `\"offset\": {\"position\": 5}`\n\"uri\" is a string representing the uri of the item to start at. Example: `\"offset\": {\"uri\": \"spotify:track:1301WleyT98MSxVHPZCA6M\"}`\n", "additionalProperties": true}, "position_ms": {"type": "integer", "description": "Indicates from what position to start playback. Must be a positive number. Passing in a position that is greater than the length of the track will cause the player to start playing the next song.\n"}}}}}}, "responses": {"204": {"description": "Playback started"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "429": {"$ref": "#/components/responses/TooManyRequests"}}, "security": [{"oauth2": ["user-modify-playback-state"]}]}}, "/me/player/pause": {"put": {"tags": ["Player"], "operationId": "pause-a-users-playback", "summary": "Pause Playback\n", "description": "Pause playback on the user's account.\n", "parameters": [{"name": "device_id", "required": false, "in": "query", "schema": {"title": "Device ID", "description": "The id of the device this command is targeting. If not supplied, the user's currently active device is the target.\n", "example": "0d1841b0976bae2a3a310dd74c0f3df354899bc8", "type": "string"}}], "responses": {"204": {"description": "Playback paused"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "429": {"$ref": "#/components/responses/TooManyRequests"}}, "security": [{"oauth2": ["user-modify-playback-state"]}]}}}, "components": {"parameters": {"PathAlbumId": {"in": "path", "name": "id", "required": true, "schema": {"title": "Spotify Album ID", "description": "The [Spotify ID](/documentation/web-api/concepts/spotify-uris-ids) of the album.\n", "example": "4aawyAB9vmqN3uQ7FjRGTy", "type": "string"}}, "PathPlaylistId": {"name": "playlist_id", "required": true, "in": "path", "schema": {"title": "Playlist ID", "description": "The [Spotify ID](/documentation/web-api/concepts/spotify-uris-ids) of the playlist.\n", "example": "3cEYpjA9oz9GiPac4AsH4n", "type": "string"}}, "QueryMarket": {"name": "market", "required": false, "in": "query", "schema": {"title": "Market", "description": "An [ISO 3166-1 alpha-2 country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2).\n  If a country code is specified, only content that is available in that market will be returned.<br/>\n  If a valid user access token is specified in the request header, the country associated with\n  the user account will take priority over this parameter.<br/>\n  _**Note**: If neither market or user country are provided, the content is considered unavailable for the client._<br/>\n  Users can view the country that is associated with their account in the [account settings](https://www.spotify.com/se/account/overview/).\n", "example": "ES", "type": "string"}}, "QueryLimit": {"name": "limit", "required": false, "in": "query", "schema": {"title": "Limit", "description": "The maximum number of items to return. Default: 20. Minimum: 1. Maximum: 50.\n", "default": 20, "example": 10, "type": "integer", "minimum": 1, "maximum": 50}}, "QueryOffset": {"name": "offset", "required": false, "in": "query", "schema": {"title": "Offset", "description": "The index of the first item to return. Default: 0 (the first item). Use with limit to get the next set of items.\n", "default": 0, "example": 5, "type": "integer"}}}, "responses": {"Unauthorized": {"description": "Bad or expired token. This can happen if the user revoked a token or\nthe access token has expired. You should re-authenticate the user.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "Forbidden": {"description": "Bad OAuth request (wrong consumer key, bad nonce, expired\ntimestamp...). Unfortunately, re-authenticating the user won't help here.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "NotFound": {"description": "The requested resource cannot be found.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "BadRequest": {"description": "The request contains malformed data in path, query parameters, or body.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "TooManyRequests": {"description": "The app has exceeded its rate limits.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"clientCredentials": {"tokenUrl": "https://accounts.spotify.com/api/token", "scopes": {"app-remote-control": "Communicate with the Spotify app on your device.\n", "playlist-read-private": "Access your private playlists.\n", "playlist-read-collaborative": "Access your collaborative playlists.\n", "playlist-modify-public": "Manage your public playlists.\n", "playlist-modify-private": "Manage your private playlists.\n", "user-library-read": "Access your saved content.\n", "user-library-modify": "Manage your saved content.\n", "user-read-private": "Access your subscription details.\n", "user-read-email": "Get your real email address.\n", "user-follow-read": "Access your followers and who you are following.\n", "user-follow-modify": "Manage your saved content.\n", "user-top-read": "Read your top artists and content.\n", "user-read-playback-position": "Read your position in content you have played.\n", "user-read-playback-state": "Read your currently playing content and Spotify Connect devices information.\n", "user-read-recently-played": "Access your recently played items.\n", "user-read-currently-playing": "Read your currently playing content.\n", "user-modify-playback-state": "Control playback on your Spotify clients and Spotify Connect devices.\n", "ugc-image-upload": "Upload images to Spotify on your behalf.\n", "streaming": "Play content and control playback on your other devices.\n"}}}}}, "schemas": {"ErrorObject": {"type": "object", "x-spotify-docs-type": "ErrorObject", "required": ["status", "message"], "properties": {"status": {"type": "integer", "minimum": 400, "maximum": 599, "description": "The HTTP status code (also returned in the response header; see [Response Status Codes](/documentation/web-api/concepts/api-calls#response-status-codes) for more information).\n"}, "message": {"type": "string", "description": "A short description of the cause of the error.\n"}}}, "ArtistResponse": {"type": "object", "properties": {"external_urls": {"type": "object", "properties": {"spotify": {"type": "string"}}}, "followers": {"type": "object", "properties": {"href": {"type": "string", "nullable": true}, "total": {"type": "integer"}}}, "genres": {"type": "array", "items": {"type": "string"}}, "href": {"type": "string"}, "id": {"type": "string"}, "images": {"type": "array", "items": {"type": "object", "properties": {"height": {"type": "integer"}, "url": {"type": "string"}, "width": {"type": "integer"}}}}, "name": {"type": "string"}, "popularity": {"type": "integer"}, "type": {"type": "string"}, "uri": {"type": "string"}}}}}}