---
Updated: 2023-09-11T23:35
tags:
  - AI->-Programming
Created: 2023-09-11T23:35
---
[![](http://mmbiz.qpic.cn/mmbiz_jpg/QB6G4ZoE186YGiciaqib3b3AkY0FmWZo3paoQGjQNibXxyj3C0e5ZlyuY6xTy9xY2gziaQzxpwP7eOphl1shtuFxiamQ/0?wx_fmt=jpeg&random=0.21752712806446928)](http://mmbiz.qpic.cn/mmbiz_jpg/QB6G4ZoE186YGiciaqib3b3AkY0FmWZo3paoQGjQNibXxyj3C0e5ZlyuY6xTy9xY2gziaQzxpwP7eOphl1shtuFxiamQ/0?wx_fmt=jpeg&random=0.21752712806446928)
---
![[Notion/AI/来了！《Python黑魔法指南》v3.0 版本/attachments/640|640]]
《Python黑魔法指南》原本是取材于我在 2018 年的一个文章系列 《谈谈 Python 那些不为人知的冷知识》，每一篇都会分享 5 个我认为比较冷门的知识、或者高效的技巧。
对于当时仅有 几千个读者我来说，能有这个阅读量说明读者还比较喜欢这个系列的文章。
![[Notion/AI/来了！《Python黑魔法指南》v3.0 版本/attachments/640 1|640 1]]
慢慢地随着内容的增多，就想着怎么样能把这么好的内容，分享到公域上，让更多的人看到，直到后来我接触到了 Sphinx ，立马就爱上了这款简单而不失优雅，纯粹而又专业的文档生成器。
于是花了几天的时间，就把这些内容整理成到 Github 上，目前在 Github 上有 2k 左右的星，是我获得星星最高的一个仓库。
大概在去年 2020年初，圈子里流行用电子书来吸引新粉关注，有不少同行依靠这个迅速成长起来，但他们用的好多都是未授权的盗版电子书，或者在 Github 上已经开源的电子资料。
看着虽然眼红，但我自己却没办法说服自己去那样做 -- 拿着别人的劳动成果，来为自己谋私利。
那怎么办呢？我突然想到我之前写过的那么多文章，那完全可以选一个主题，把同类型的文章整理在一起，制作一本属于我自己的电子书。
于是，同年5月份，我的第一本电子书《Python 黑魔法指南》v1.0 就面世了。内容虽然简单，但胜在题材新颖，全书 50 个冷知识看过之后，无一不说太爽了。
经过两次的迭代，《Python黑魔法指南》终于迎来了 v3.0 的版本，囊集了 100 多个开发小技巧，非常适合在闲时进行碎片阅读。
延续第二版的目录结构，第三版照样还是那七个章节，章节的介绍之前 v2.0 已经非常详细介绍过了，这里就不再赘述。
第三版的 tips 总共有 130 条左右 ，对比第二版本更新的 tips 数量达 50条，看过第二版的同学可以根据目录择需阅读。
第三版的 tips 总共有 130 条左右 ，对比第二版本更新的 tips 数量达 50条，看过第二版的同学可以根据目录择需阅读。
下面按照惯例对书的章节做一些介绍
## # 第一章：魔法冷知识
这一章节主要是整理了一些 **看了也没啥用的** 冷知识
比如 **小整数池**
```Plain
 >>> a = -6
>>> b = -6
>>> a is b
False
>>> a = 256
>>> b = 256
>>> a is b
 True
>>> a = 257
>>> b = 257
>>> a is b
False
>>> a = 257; b = 257
>>> a is b
True
```
比如 **intern 机制**
```Plain
 >>> s1="hello"
>>> s2="hello"
>>> s1 is s2
True
## intern
>>> s1="hell o"
>>> s2="hell o"
>>> s1 is s2
False
```
比如 **大数表示法**
```Plain
 >>> number=281_028_344
>>> number
281028344
```
## # 第二章：魔法命令行
这一章我收集了几乎所有你知道的，你不知道的关于 Python 在命令行上的神奇用法。
比如**最快查看包搜索方式**
```Plain
$ python3 -m site
sys.path = [
    '/home/<USER>',
    '/usr/local/Python3.7/lib/python37.zip',
    '/usr/local/Python3.7/lib/python3.7',
    '/usr/local/Python3.7/lib/python3.7/lib-dynload',
    '/home/<USER>/.local/lib/python3.7/site-packages',
    '/usr/local/Python3.7/lib/python3.7/site-packages',
]
USER_BASE: '/home/<USER>/.local' (exists)
USER_SITE: '/home/<USER>/.local/lib/python3.7/site-packages' (exists)
ENABLE_USER_SITE: True
```
比如**如何往 Python 终端中传入参数**
## # 第三章：炫技魔法操作
这个章节是取自我个人原创系列《Python炫技操作》里的文章，其中的多篇文章成为了爆款文章，不少大号均有转载。很多网友看完后直呼 "**卧槽，居然还能这样？！**"
比如**条件语句的七种写法**，随便摘取其中三种
```Plain
# 第一种
>>> msg1 = age1 > 18 and "已成年" or "未成年"
>>>
>>> print(msg1)
已成年
>>>
# 第二种
>>> msg1 = ("未成年", "已成年")[age1 > 18]
>>> print(msg1)
已成年
# 第三种
>>> msg1 = (lambda:"未成年", lambda:"已成年")[age1 > 18]()
>>> print(msg1)
已成年
```
比如**读取文件的六种方式**（里面的 filecache 非常好用）
## # 第四章：魔法进阶扫盲
这一章节主要深入理解 Python 中那些难点，将这些难点逐个击破？比如：
- 如何写出一个可以带参数的装饰器呢？
- 装饰器可以装饰函数，那么你知道如何装饰类吗？
- 描述符的访问规则是什么？
- 描述符在实际开发中有哪些使用场景？
## # 第五章：魔法开发技巧
这一章的内容，干货最多，不仅干而且非常实用。掌握这些代码编写技巧，对提高你代码的可读性、优雅性会很有帮助。
比如**如何嵌套 for 循环写成单行？**
比如**如何流式读取数G超大文件**
## # 第六章：良好编码习惯
如果每个人都有代码洁癖症，在交接代码的时候，就轻松多了。养成良好的编码习惯，利他又利已。
这一章节收集了一些比较好的编码习惯。
比如**利用any代替for循环**
比如**如何给模块属性上保险？**
## # 第七章：神奇魔法模块
这章节大家也许会比较熟悉，文章都在公众号发过，不少也成为了爆款文章。
主要收集了一些非常不错但知名度又不高的 Python 库。
比如 **文件读取利器 fileinput**
比如比**正则好上手的轻量数据提取神器 parse**
总之内容非常之多，全书PDF将近300页。多余的话不就多说了，这本书我已经打包好了，下载链接我放在了我的个人公众号里面。
点击上方卡片，回复 **magic** 获取