---
DocFlag:
  - Reference
  - Tested
Updated: 2023-04-07T07:49
tags:
  - AI->-Embedding
Created: 2023-04-07T07:44
---
还在纠结于反复查找开源项目的技术文档？
团队常因频繁搜索开源项目主页导致效率低下？
每天都要问一遍【开源项目中那些 “小白问题” 究竟有没有更快的解决方法？】
对此，只想对你说：赶紧试试 OSSChat！赶紧试试 OSSChat！赶紧试试 OSSChat！
![[181127_0COy_4937141.png]]
（**👉**链接在此：[https://osschat.io/）](https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fosschat.io%2F****%25EF%25BC%2589**)
OSSChat 究竟是谁？
介绍一下，这是我们最近做的一个很有意思的开源工具。它的主要目的是把一个开源社区所有沉淀下来的知识性内容打造成知识库，并通过 ChatGPT 的能力包装成问答机器人。**后续，我们也打算为各个开源社区提供免费的 embedded chatbot 的能力，解决主页内容搜索和用户 QA 这样的关键问题。**
简言之，有了 OSSChat，你就可以通过对话的方式直接与一个开源社区的所有知识直接交流，大幅提升开源社区信息流通效率。
在首发尝鲜版本中，OSSChat 提供了基于 Hugging Face、Pytorch、Milvus 等开源项目的深度问答服务，后续将陆续覆盖更多受欢迎的开源项目。**如果 OSSChat 上面还没有你的项目，请直接联系我们申请入驻！**
# **01.** **OSSChat：自带 “精准” 基因**
用过 ChatGPT 的小伙伴可能都遇到过类似的问题：当你向它询问专业垂域知识或实时信息时，由于受限于模型本身的训练数据集边界，ChatGPT 会生成看似正确但与事实不符的内容，这也是 ChatGPT 被业界广为诟病的一个重要缺陷。**为了解决这一问题，Zilliz 推出了崭新的 CVP 技术栈 —— 即 ChatGPT + 向量数据库（vector database）+ prompt-as-code。**
毋庸置疑，ChatGPT 在自然语言问答方面的能力十分出色，如果通过向量数据库为 ChatGPT 提供一个大规模可靠的知识库，并使用 prompt 将用户问题与相应的知识库文本对应起来，ChatGPT 便可以扬长避短，很大程度上避免 ChatGPT “捏造答案”。
可以说，OSSChat 完美展现了 CVP 技术栈的工作模式。具体来看，OSSChat 将各个开源项目的 GitHub 仓库（repo）及相关文档页面作为数据源（source of truth），这些数据被转换为 embedding 并存储在 Zilliz 向量数据库中，其他相关内容则存储在单独的数据存储器中。用户与 OSSChat 提问交互时，会在 Zilliz 向量数据库中触发相似性检索，随后检索结果会被发送给 ChatGPT 作为输入问题，最终生成精准答案。
![[181254_r6Wz_4937141.png]]
# **02.** **关于避免 ChatGPT 捏造事实这件事**
接下来可以通过几组典型的例子，展示 CVP Stack 帮助 ChatGPT 给出精准答案的能力。我们对比了相同问题下 ChatGPT 与 OSSChat 的答案（* _注：以下问答均翻译自英文，英文原文请移步 Zilliz Blog 查看_）：
### **Q1：Hugging Face 是否支持 OpenAI Whisper？**
**ChatGPT 回答**：据我所知，截至 2021 年 9 月，Hugging Face 尚未宣布支持 OpenAI Whisper。但是，他们将来可能会添加支持。OpenAI Whisper 是 OpenAI 的新语言模型，其他公司和开发人员将其整合到其平台和产品中可能需要一些时间。
**OSSChat 回答**：是的，Hugging Face 支持 OpenAI Whisper。
FlaxWhisperModel 类在 transformers 库中可用，这是 OpenAI Whisper 模型的包装器。您可以使用此类加载预训练的 OpenAI Whisper 模型，并将其用于各种自然语言处理任务，例如文本分类、问题回答和文本生成。
【_对于这个问题，ChatGPT 受到知识库限制，它的回答帮助并不大。但是 OSSChat 的回答却十分准确，因为这个答案直接来自 Hugging Face 文档网站。_】
### **Q2：**`torch.compile` **这个 API 可以用来做什么？**
**ChatGPT 回答**：截至 2021 年 9 月我的知识截止日期，PyTorch 中没有 `torch.compile` API，这是由 Facebook AI Research 开发的流行的开源机器学习库。但是，PyTorch 提供了与编译和优化神经网络模型相关的各种 API，例如 torch.nn.Module，它是 PyTorch 中所有神经网络模块的基类。torch.nn.Module 提供各种用于定义和优化神经网络模型的方法和钩子。
此外，PyTorch 提供了与 Just-In-Time （JIT）编译器（例如 TorchScript JIT 编译器）集成的接口，可以用于在各种硬件平台上优化和编译 PyTorch 模型。JIT 编译器可以将 PyTorch 模型转换为独立的、序列化的表示形式，可以在其他环境或语言中加载和执行。总之，虽然 PyTorch 没有 torch.compile API，但它提供了各种用于优化和编译神经网络模型的 API 和工具。
- OSSChat 回答：torch.compile API 的目的是通过预先编译来加速 PyTorch 代码。它可以处理任意 Python 代码，对现有代码的更改最小，并可以处理数据相关的控制流。它还可以处理非 PyTorch 函数调用，TorchScript 追踪将其视为常量并可能导致不正确的结果。
【_ChatGPT 长篇大论解释了 PyTorch API 是什么，并宣称 PyTorch API 中没有 torch.compile。但实际上，我们并没有提问 PyTorch API 是什么，且 PyTorch 中存在 torch.compile 这个 API。_】
### **Q3：Milvus 中的 TTL 是什么，如何设置？**
**ChatGPT 回答**：在 Milvus 中，TTL（Time To Live）是一项功能，允许您为数据设置到期时间。当您为 Milvus 中的集合或分区设置 TTL 时，Milvus 将自动删除任何已超过指定 TTL 的数据。要在 Milvus 中为集合或分区设置 TTL，您可以分别使用 `set_collection_ttl` 或 set\_partition\_ttl API。
以下是示例：
![[181431_3Jmr_4937141.png]]
在此示例中，我们在 Milvus 中创建了一个集合和一个分区。然后，我们使用 set\_collection\_ttl 和 set\_partition\_ttl API 分别为集合和分区设置了 7 天的 TTL。TTL 指定为 Unix 时间戳，它是自 1970 年 1 月 1 日以来的秒数。我们使用 datetime 模块将 TTL 添加到当前时间以计算到期时间。
请注意，您需要在启动 Milvus 时启用 TTL 功能。这可以通过在 Milvus 配置文件中设置 TTL\_CHECK\_INTERVAL 参数来完成。默认值为 60 秒。
- OSSChat 回答：TTL 在 Milvus 中代表 “Time to Live”，它是一项功能，可以为集合中的数据设置到期时间。当集合中的数据过期时，它将被自动清理，并且不会参与搜索或查询。
要在 Milvus 中为集合设置 TTL 值，您可以使用 Python 中的 `collection.set_properties()` 方法。例如，要将 TTL 值设置为 1800 秒（30 分钟），您可以使用以下代码：

> collection.set_properties(properties={"collection.ttl.seconds": 1800})
您还可以在创建集合时通过在集合的属性中指定 `collection.ttl.seconds` 属性来设置 TTL 值。该值应以秒为单位指定，应为 0 或更大。值为 0 表示 TTL 已禁用。
【_对于这个问题，ChatGPT 给我们提供了一个看似合理的答案，甚至还包含了代码片段。但实际上，这并不是正确答案，而是 ChatGPT 捏造出来的答案。相较之下，OSSChat 则给出了正确回答 —— Milvus 中的 TTL 是在集合设置中的一种属性，它的值以秒为单位。_】
![[181510_Y4hr_4937141.png]]
# **03.** **OSSChat 试用通道已开放**
目前，我们已经开放了 OSSChat 试用通道，所有用户均可免费使用。未来我们在不断完善其功能的同时，也将公布更多关于 OSSChat 搭建过程的细节。如果希望 OSSChat 聊天机器人能够出现你所开发的开源项目，_**也**__**欢迎复制链接填写申请：**_[_https://docs.google.com/forms/d/e/1FAIpQLSdXHXCyzs11u7lh2hWkAtWusHtn8fVEZjfrFXTvR7yHhXZDgQ/viewform_](https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdocs.google.com%2Fforms%2Fd%2Fe%2F1FAIpQLSdXHXCyzs11u7lh2hWkAtWusHtn8fVEZjfrFXTvR7yHhXZDgQ%2Fviewform)
当然，如果你对 OSSChat 的功能或者开源项目方面有更多的建议，也欢迎告诉我们！话不多说，OSSChat 等你！
![[181558_ElPC_4937141.png]]