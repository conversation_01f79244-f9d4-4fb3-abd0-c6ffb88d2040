---
DocFlag:
  - Reference
  - Tested
  - Testing
Updated: 2024-08-14T13:26
tags:
  - AI->-<PERSON><PERSON><PERSON><PERSON>
Created: 2023-04-14T20:36
---
  
Reference and 各类Key Information
ChatGLM
AtomGPT
Bark Voice generation
Auto-GPT
Lang FLow
ELK
Langchain
Installation Steps
Local LLM Mode
embedding library
document loader
Splitter
Vectorstores DB
Chain with embedding
Ask data from Pandas
Agent
plan_and_execute
gradio-llm-agents
Self Query -- get more medat data BUT only support Pinecore so far
Convert FASSI DB data to Pinecore DB
Rerank to improve
Voice Input
Convert to Graph
WEB UI
Flowise
setup MOSS
FrontEnd development
Tree of Thought
OpenAI Function Call
Financial GPT
metagpt
Some Keys required
elevenlabs
LangSmith
AUTOGEN Made by Microsoft
Gemini
crewAI
GPT4 New Feature
Nomic Embedding
Claude Function Call
Claude call for long output message
READER API (Similar like BROWSERLESS)
Browserless+BeautifulSoap方式
改写成Jina reader方式
DuckDuckGo Search API Test
  
  
# Reference and 各类Key Information
```JavaScript
\#Build one notion chatbot
https://www.youtube.com/watch?v=prbloUGlvLE
\#LangChain learning
https://github.com/gkamradt/langchain-tutorials
\#Adjust lang flow 
https://github.com/logspace-ai/langflow
\#LangChain Manual doc
https://python.langchain.com/en/latest/modules/memory.html
\#Register serpapi
https://serpapi.com/dashboard
\#Auto-GPT
https://github.com/Torantulino/Auto-GPT
# Some of LLM List
Llama/alpaca/vicuna/chatglm/gpt4all/stableLM/Open-Assistant/dolly
\#some API Keys
--- promptlayer
import promptlayer
promptlayer.api_key = "pl_85f106b4a78a60e4b2ab12bc065cd991"
https://promptlayer.com/
-- bing search api
https://portal.azure.com/
Endpoint: https://api.bing.microsoft.com/
key: ********************************
Location: global
--wolframalpha
https://developer.wolframalpha.com/portal/myapps/
APP NAME: langchain
APPID: RT4JVP-EQ38555X5A
USAGE TYPE: Personal/Non-commercial Only
-- openai gpt
https://platform.openai.com/account/usage
OPENAI_API_KEY=***************************************************
Setup limiation to $5
**************************************************************************************************************************
\#another key for translator
***************************************************
https://api.openai.com/v1/chat/completions
\#Under utility project created one key for aishell
********************************************************************************************************************************************************************
-- serpapi search
Web: https://serpapi.com/dashboard
Url: https://serpapi.com/search
SERPAPI_API_KEY: 3a3d4334ed51f17a8dbb50f7d7feaad14a88ff74e202bac42c2dffc93b26f445
-- Google serper search
web:https://serper.dev/
url: https://google.serper.dev/search
SERPER_API_KEY = e8d813d64fd9c79f007c9c0e74c98d8d5ac1d559
-- Google SearchAPI
https://www.searchapi.io/
API Key:WD2B3wHdTb8kXfFnynSP9kXZ
-- pinecone for ventor memory
https://app.pinecone.io/organizations/-NT1sGtQSsrc4XIbnLBZ/projects/asia-southeast1-gcp:2a58e95/indexes
autogpt-ray: 69f54066-5edb-4506-836f-a7dc9134f237
env       :  asia-southeast1-gcp
-- ElevenLabs Key for speak
https://beta.elevenlabs.io/speech-synthesis
********************************
""" check api manual https://api.elevenlabs.io/docs
*** you only can choice these it listed
ELEVENLABS_API_KEY=********************************
ELEVENLABS_VOICE_1_ID=AZnzlk1XvdvUeBnXmlld
ELEVENLABS_VOICE_2_ID=EXAVITQu4vr4xnSDxMaL
-- HuggingFace API key
https://huggingface.co/settings/tokens
Read only
HUGGINGFACE_API_TOKEN="*************************************"
By default, Auto-GPT uses DALL-e for image generation(IMAGE_PROVIDER=dalle). To use Stable Diffusion, a HuggingFace API Token is required.
Once you have a token, set these variables in your .env:

#根据
-- google search id from google cloud if serpAPI not working
GOOGLE_API_KEY=AIzaSyB1RJwM8-r_ozVBhnqRz7LXYmtA_QyE5eo
CUSTOM_SEARCH_ENGINE_ID=5531449e036a443c3
Create a new project by clicking on the "Select a Project" dropdown at the top of the page and clicking "New Project". Give it a name and click "Create".
Go to the APIs & Services Dashboard and click "Enable APIs and Services". Search for "Custom Search API" and click on it, then click "Enable".
Go to the Credentials page and click "Create Credentials". Choose "API Key".
Copy the API key and set it as an environment variable named GOOGLE_API_KEY on your machine. See setting up environment variables below.
Enable the Custom Search API on your project. (Might need to wait few minutes to propagate)
Go to the Custom Search Engine page and click "Add".
Set up your search engine by following the prompts. You can choose to search the entire web or specific sites.
Once you've created your search egine, click on "Control Panel" and then "Basics". Copy the "Search engine ID" and set it as an environment variable named CUSTOM_SEARCH_ENGINE_ID on your machine. See setting up environment variables below.
### you can setup your search engine here
https://programmablesearchengine.google.com/controlpanel/overview?cx=5531449e036a443c3
### to call your search engine
https://cse.google.com/cse?cx=5531449e036a443c3
### to check usage
https://console.cloud.google.com/apis/api/customsearch.googleapis.com/metrics?project=autogpt-netcaster
So far we only can check 4 site usage after autoGPT run
https://platform.openai.com/account/usage
https://console.cloud.google.com/apis/api/customsearch.googleapis.com/metrics?project=autogpt-netcaster
https://serpapi.com/dashboard
https://app.pinecone.io/organizations/-NT1sGtQSsrc4XIbnLBZ/projects/asia-southeast1-gcp:2a58e95/indexes/auto-gpt

https://vercel.com/dashboard
login via my github

Morning Star
https://rapidapi.com/apidojo/api/morning-star
const options = {
  method: 'GET',
  url: 'https://morning-star.p.rapidapi.com/market/get-commentaries',
  headers: {
    'X-RapidAPI-Key': '**************************************************',
    'X-RapidAPI-Host': 'morning-star.p.rapidapi.com'
  }
};

AirTable
https://airtable.com/create/apikey
https://airtable.com/workspaces
API Key: keyvI4PsWghnsF9mA
https://airtable.com/api
get baseid
app9AmBsPXS7UzmJS
and find table, get table id
tbltCUC2DIgzlGf9X

https://app.alpaca.markets/paper/dashboard/overview
generate new api key
https://www.postman.com/alpacamarkets/workspace/alpaca-public-workspace/collection/********-b219fac1-0270-441b-b62c-5f4848233b8b
API KEY: PKO7LC21W27V4WJL2JXR
API Secret: kYcRrAvGnaezbWPK8X4c33VnJtaoyWq0U54P9RJc
They provided screener (top mover and most active stocks) and News as well
https://www.marketaux.com/
API Token: f6ZA7bY7bCo0Iq0IScmJ9q5l5GdHU89rbt16qOzl
https://www.marketaux.com/news/country/us
# market news
https://www.browserless.io/
https://cloud.browserless.io/account/
BROWSERLESS_API_KEY：53dea3fc-fb83-495f-9f99-7efdbe7732cf
\#pipline
https://www.youtube.com/watch?v=YWx4UbYzRMU
https://medium.com/towards-artificial-intelligence/a-gpt-3-bot-that-uses-pubmed-abstracts-to-answer-science-questions-3f3b84f87e28
https://openi.nlm.nih.gov/
https://www.nlm.nih.gov/
https://scenex.jina.ai/api
Vb6aFSPiy6DvhbwvKFCi:9097177fe6cc28693fc091ac544069d36e30fc53f6a97f13bb1d72746666d6f7
pip install jinaai
\#ANTHRPO\C Claude
https://console.anthropic.com/dashboard
API Keys
************************************************************************************************************
\#Google Gemini API
AIzaSyCc-2j2RA6UOmLFO3chNge1zpl1p73NtrA
QroqAPI
https://console.groq.com/keys
https://api.groq.com/openai/v1/chat/completions
The base_url is https://api.groq.com/openai/v1
"api_base_url": "https://api.groq.com/openai/v1",
mixtral-8x7b-32768
https://console.groq.com/docs/text-chat
API Key
********************************************************
# groq demo 
https://github.com/yusufgencer/CustomAI-Studio.git

https://sec-api.io/
d7c2216d4a72d4e69d5588be69e5fb9b4d93ddd823669decc842845f0691085e
https://api.sec-api.io/xbrl-to-json?htm-url=https://www.sec.gov/Archives/edgar/320193/000032019324000069/aapl-20240330.htm&token=d7c2216d4a72d4e69d5588be69e5fb9b4d93ddd823669decc842845f0691085e
https://sec-api.io/docs/xbrl-to-json-converter-api/python-example
https://api.sec-api.io/filing-reader?token=d7c2216d4a72d4e69d5588be69e5fb9b4d93ddd823669decc842845f0691085e&url=https://www.sec.gov/Archives/edgar/320193/000032019324000069/aapl-20240330.htm&type=pdf
https://community.openai.com/t/how-you-deal-with-response-limit-in-a-single-api-request/500506/2
{
  "domain": "archive.sec-api.io",
  "method": "get",
  "path": "/{cik}/{accession-number}/{filename}",
  "operation": "retrieveFilingHtml",
  "operation_hash": "80d6dee1f46b84549714e26a4cb72aaf81984c55",
  "is_consequential": false,
  "params": {
    "cik": "320193",
    "accession-number": "000032019324000069",
    "filename": "aapl-20240330.htm"
  }
}
[debug] Response received
{
  "response_data": "ResponseTooLargeError"
}
ConnectionError: HTTPSConnectionPool(host='api.sec-api.io', port=443): Max retries exceeded with url:
 /xbrl-to-json?htm-url=https%3A%2F%2Fwww.sec.gov%2FArchives%2Fedgar%2F320193%2F000032019324000069%2Faapl-20240330.htm&token=d7c2216d4a72d4e69d5588be69e5fb9b4d93ddd823669decc842845f0691085e 
 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7ea85cff5860>: Failed to establish a new connection: [Errno -3] Temporary failure in name resolution'))
https://marketstack.com/
https://www.euronext.com/en
https://connect2.euronext.com/en/site-home-page?destination=/en/universes-homepage
https://towardsdatascience.com/best-5-free-stock-market-apis-in-2019-ad91dddec984

https://www.alphavantage.co/
https://www.alphavantage.co/documentation/ 
# It contains earning calendar
# Top Gainers, Losers, and Most Actively Traded Tickers (US Market)
# Economic Indicators
Your dedicated access key is: YKM2O216HAYWYASO. Please record this API key at a safe place for future data access.
https://www.alphavantage.co/documentation/
https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=MC&outputsize=full&apikey=YKM2O216HAYWYASO
https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=MBG&outputsize=full&apikey=YKM2O216HAYWYASO
https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=SHL&outputsize=full&apikey=YKM2O216HAYWYASO
https://finnhub.io/
cp3ddghr01qvi2qqhtegcp3ddghr01qvi2qqhtf0
https://finnhub.io/docs/api/financials
https://finnhub.io/api/v1/stock/financials-reported?symbol=AAPL&token=cp3ddghr01qvi2qqhtegcp3ddghr01qvi2qqhtf0
All GET request require a token parameter token=apiKey in the URL or a header X-Finnhub-Token : apiKey. 
You can find your API Key under Dashboard. If you are logged in, your API key will be automatically used in the examples so you can copy and paste them as is.
If your limit is exceeded, you will receive a response with status code 429.
# able to call python
https://github.com/Finnhub-Stock-API/finnhub-python

https://eodhd.com/financial-apis/stock-market-financial-news-api/
Your free API key: 6646e50e07ac52.******** is valid for 20 API requests per day.
https://eodhd.com/api/news?s=MC&offset=0&limit=10&api_token=6646e50e07ac52.********&fmt=json
https://eodhd.com/api/sentiments?s=MC&from=2024-05-10&to=2024-05-17&api_token=6646e50e07ac52.********&fmt=json
https://eodhd.com/api/news?s=MBG&offset=0&limit=10&api_token=6646e50e07ac52.********&fmt=json
https://eodhd.com/api/sentiments?s=MBG.F&from=2024-05-10&to=2024-05-17&api_token=6646e50e07ac52.********&fmt=json
        "symbols": [
            "DAI.MI",
            "DDAIF.US",
            "DMLRY.US",
            "DTRUY.US",
            "MBG.F",
            "MBG.US"
        ],

https://www.reddit.com/r/algotrading/comments/13i8o9s/sec_filings_how_much_does_it_cost_what_api_and/?onetap_auto=true&one_tap=true
https://rapidapi.com/last10k/api/sec-filings
https://www.sec.gov/privacy.htm\#security
https://dev.last10k.com/terms
https://site.financialmodelingprep.com/developer/docs
https://site.financialmodelingprep.com/developer/docs\#sec-filings-securities-and-exchange-commission-(s.e.c)
6RI4FlqecXKPpb6demLu9y3YHdR6O7zh
https://financialmodelingprep.com/api/v3/sec_filings/AAPL?type=10-k&page=0&apikey=6RI4FlqecXKPpb6demLu9y3YHdR6O7zh
\#Python
https://pypi.org/project/sec-edgar-downloader/
https://fishtail.ai/blog-2-accessing-company-financials-using-the-sec-edgar-api
\#jina
https://r.jina.ai/https://www.sec.gov/Archives/edgar/data/0000320193/000032019324000069/aapl-20240330.htm
\#https://studio.ai21.com/
R1EXMsJ6uadrq2n2LKTmCfgAJXtDq784
https://docs.ai21.com/reference/summarize-ref
https://docs.ai21.com/reference/summarize-api-ref
curl --request POST \
     --url https://api.ai21.com/studio/v1/summarize \
     --header 'Authorization: Bearer R1EXMsJ6uadrq2n2LKTmCfgAJXtDq784' \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data '
{
  "sourceType": "URL",
  "source": "https://www.sec.gov/Archives/edgar/data/0000320193/000032019324000069/aapl-20240330.htm",
  "focus": "Generate one financial report and focus on these numbers "
}
'
https://app.edenai.run/admin/api-settings/features-preferences
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiODM0OWZjMjItZDZlMC00MmRiLThmZGEtMzFjNmE3Yzg2ODE2IiwidHlwZSI6ImFwaV90b2tlbiJ9.EfpZm5nn7Azsv2F2OQ1WJt4n-bwIn2Cybo4mrHzHcJo
https://openrouter.ai/api/v1
api_key: 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
https://api.together.ai/settings/api-keys
TOGETHER_API_KEY: 862509e1042e6669071c3c6200524a27e4de480712917023c4207d6f696de49a
import os
from together import Together
client = Together(api_key=os.environ.get("TOGETHER_API_KEY"))
response = client.completions.create(
    model="codellama/CodeLlama-13b-Python-hf",
    prompt="def fibonacci(n): ",
    stream=True,
)
for chunk in response:
    print(chunk.choices[0].text or "", end="", flush=True)
    
    
https://dashboard.exa.ai/api-keys
EXA_API_KEY: 8b60983b-714d-4815-8748-53b1606929ca    
https://app.tavily.com/home
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
https://dashboard.cohere.com/
Cohere API Key: 4mhop6zSOwC6kbPfHwKUF5M6bUepPdsH2l21uGgN
https://cloud.llamaindex.ai/api-key
NotionMemoTalk
llx-rkgcyWTB0ffTnuto3wJgTmHfFrAHUxxjAx1PJKiywIdMzTEs
https://platform.deepseek.com/api_keys
***********************************
https://api.deepseek.com/v1
```
\#HuggingFace Hub
[[🦜🦜🦜Research Memo for LangChain]]
```JavaScript
\#Build one notion chatbot
https://www.youtube.com/watch?v=prbloUGlvLE
\#LangChain learning
https://github.com/gkamradt/langchain-tutorials
\#Adjust lang flow 
https://github.com/logspace-ai/langflow
\#LangChain Manual doc
https://python.langchain.com/en/latest/modules/memory.html
\#Register serpapi
https://serpapi.com/dashboard
\#Auto-GPT
https://github.com/Torantulino/Auto-GPT
# Some of LLM List
Llama/alpaca/vicuna/chatglm/gpt4all/stableLM/Open-Assistant/dolly
\#some API Keys
--- promptlayer
import promptlayer
promptlayer.api_key = "pl_85f106b4a78a60e4b2ab12bc065cd991"
https://promptlayer.com/
--wolframalpha
https://developer.wolframalpha.com/portal/myapps/
APP NAME: langchain
APPID: RT4JVP-EQ38555X5A
USAGE TYPE: Personal/Non-commercial Only
-- openai gpt
https://platform.openai.com/account/usage
OPENAI_API_KEY=***************************************************
Setup limiation to $5
-- serpapi search
https://serpapi.com/dashboard
SerpApi Key SERPAPI_API_KEY: 3a3d4334ed51f17a8dbb50f7d7feaad14a88ff74e202bac42c2dffc93b26f445
-- Google serper search
https://serper.dev/
 SERPER_API_KEY = e8d813d64fd9c79f007c9c0e74c98d8d5ac1d559
-- pinecone for ventor memory
https://app.pinecone.io/organizations/-NT1sGtQSsrc4XIbnLBZ/projects/asia-southeast1-gcp:2a58e95/indexes
autogpt-ray: 69f54066-5edb-4506-836f-a7dc9134f237
env       :  asia-southeast1-gcp
-- ElevenLabs Key for speak
https://beta.elevenlabs.io/speech-synthesis
********************************
""" check api manual https://api.elevenlabs.io/docs
*** you only can choice these it listed
ELEVENLABS_API_KEY=********************************
ELEVENLABS_VOICE_1_ID=AZnzlk1XvdvUeBnXmlld
ELEVENLABS_VOICE_2_ID=EXAVITQu4vr4xnSDxMaL
-- HuggingFace API key
https://huggingface.co/settings/tokens
Read only
HUGGINGFACE_API_TOKEN="*************************************"
By default, Auto-GPT uses DALL-e for image generation(IMAGE_PROVIDER=dalle). To use Stable Diffusion, a HuggingFace API Token is required.
Once you have a token, set these variables in your .env:
IMAGE_PROVIDER=sd
#根据
-- google search id from google cloud if serpAPI not working
GOOGLE_API_KEY=AIzaSyB1RJwM8-r_ozVBhnqRz7LXYmtA_QyE5eo
CUSTOM_SEARCH_ENGINE_ID=5531449e036a443c3
Create a new project by clicking on the "Select a Project" dropdown at the top of the page and clicking "New Project". Give it a name and click "Create".
Go to the APIs & Services Dashboard and click "Enable APIs and Services". Search for "Custom Search API" and click on it, then click "Enable".
Go to the Credentials page and click "Create Credentials". Choose "API Key".
Copy the API key and set it as an environment variable named GOOGLE_API_KEY on your machine. See setting up environment variables below.
Enable the Custom Search API on your project. (Might need to wait few minutes to propagate)
Go to the Custom Search Engine page and click "Add".
Set up your search engine by following the prompts. You can choose to search the entire web or specific sites.
Once you've created your search egine, click on "Control Panel" and then "Basics". Copy the "Search engine ID" and set it as an environment variable named CUSTOM_SEARCH_ENGINE_ID on your machine. See setting up environment variables below.
### you can setup your search engine here
https://programmablesearchengine.google.com/controlpanel/overview?cx=5531449e036a443c3
### to call your search engine
https://cse.google.com/cse?cx=5531449e036a443c3
### to check usage
https://console.cloud.google.com/apis/api/customsearch.googleapis.com/metrics?project=autogpt-netcaster
So far we only can check 4 site usage after autoGPT run
https://platform.openai.com/account/usage
https://console.cloud.google.com/apis/api/customsearch.googleapis.com/metrics?project=autogpt-netcaster
https://serpapi.com/dashboard
https://app.pinecone.io/organizations/-NT1sGtQSsrc4XIbnLBZ/projects/asia-southeast1-gcp:2a58e95/indexes/auto-gpt

https://vercel.com/dashboard
login via my github

Morning Star
https://rapidapi.com/apidojo/api/morning-star
const options = {
  method: 'GET',
  url: 'https://morning-star.p.rapidapi.com/market/get-commentaries',
  headers: {
    'X-RapidAPI-Key': '**************************************************',
    'X-RapidAPI-Host': 'morning-star.p.rapidapi.com'
  }
};

AirTable
https://airtable.com/create/apikey
https://airtable.com/workspaces
API Key: keyvI4PsWghnsF9mA
https://airtable.com/api
get baseid
app9AmBsPXS7UzmJS
and find table, get table id
tbltCUC2DIgzlGf9X

https://app.alpaca.markets/paper/dashboard/overview
generate new api key
https://www.postman.com/alpacamarkets/workspace/alpaca-public-workspace/collection/********-b219fac1-0270-441b-b62c-5f4848233b8b
API KEY: PKO7LC21W27V4WJL2JXR
API Secret: kYcRrAvGnaezbWPK8X4c33VnJtaoyWq0U54P9RJc

https://www.marketaux.com/
API Token: f6ZA7bY7bCo0Iq0IScmJ9q5l5GdHU89rbt16qOzl

https://www.browserless.io/
https://cloud.browserless.io/account/
BROWSERLESS_API_KEY：53dea3fc-fb83-495f-9f99-7efdbe7732cf

https://eodhd.com/financial-apis/stock-market-financial-news-api/
Your free API key: 6646e50e07ac52.******** is valid for 20 API requests per day.
https://eodhd.com/api/news?s=MC&offset=0&limit=10&api_token=6646e50e07ac52.********&fmt=json
https://eodhd.com/api/sentiments?s=mc.EU&from=2024-05-10&to=2024-05-17&api_token=6646e50e07ac52.********&fmt=json
DAI
https://www.km3am.com/
```
  
  
  
# ChatGLM
````JavaScript
<<< LLM ChatGLM setup >>>
https://github.com/THUDM/ChatGLM-6B
CPU+GPU 解决oom的问题
https://github.com/wangzhaode/ChatGLM-MNN
https://github.com/Jittor/JittorLLMs
\#Version 2
https://github.com/THUDM/ChatGLM2-6B

Download model
git clone https://huggingface.co/THUDM/chatglm-6b
git clone https://huggingface.co/THUDM/chatglm-6b-int4
git clone https://huggingface.co/THUDM/chatglm-6b-int8
\#Version 2
git clone https://huggingface.co/THUDM/chatglm2-6b
ChatGLM API方式调用
首先需要安装额外的依赖 `pip install fastapi uvicorn`，然后运行仓库中的 [api.py](api.py)：
```shell
python api.py
```
默认部署在本地的 8000 端口，通过 POST 方法进行调用
```shell
curl -X POST "http://127.0.0.1:8000" \
     -H 'Content-Type: application/json' \
     -d '{"prompt": "你好", "history": []}'
```
得到的返回值为
```shell
{
  "response":"你好👋！我是人工智能助手 ChatGLM-6B，很高兴见到你，欢迎问我任何问题。",
  "history":[["你好","你好👋！我是人工智能助手 ChatGLM-6B，很高兴见到你，欢迎问我任何问题。"]],
  "status":200,
  "time":"2023-03-23 21:38:40"
}
```
最后我重写了ChatGLM类，包装了HTTP的调用。找到了问题的根源是HISTORY，设定为1后，就再也不发生了。现在定义在key.py内
sudo su - raysheng
conda update -n base conda
pip install --upgrade pip
conda create -n ChatGLM
conda install python=3.10.12
conda activate ChatGLM
cd /opt/workspace/ChatGLM-6B/
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
\#install cuda toolkit
wget https://developer.download.nvidia.com/compute/cuda/12.2.0/local_installers/cuda-repo-rhel9-12-2-local-12.2.0_535.54.03-1.x86_64.rpm
rpm -i cuda-repo-rhel9-12-2-local-12.2.0_535.54.03-1.x86_64.rpm
dnf clean all
sudo dnf -y module install nvidia-driver:latest-dkms
dnf -y install cuda
-- will install into /usr/local/cuda
(base) [root@MONSTER:/etc/profile.d]# cat cuda.sh
export CUDA_HOME=/usr/local/cuda
export PATH=$PATH:/usr/local/cuda/bin
pip install fastapi
\#start local http service
python httpllm.py
tail -f logs/`date +'%Y-%m-%d'`.log

\#Play version2
1. make sure python key library version
transformers 库版本推荐为 4.30.2，torch 推荐使用 2.0 以上的版本，以获得最佳的推理性能。
pip install --upgrade transformers==4.30.2
2, Download latest llm module
git clone https://huggingface.co/THUDM/chatglm2-6b

3. modify key.py and point path to version 2 trained model
=========================================================================================================
<<< Visual ChatGLM >>>
https://github.com/THUDM/VisualGLM-6B.git
git clone https://huggingface.co/THUDM/visualglm-6b
\#download checkpoint file visualglm-6b.zip
wget https://cloud.tsinghua.edu.cn/f/348b98dffcc940b6a09d/?dl=1
\#after donwload , unzip and copy to visualglm-6b model folder
then orignal model+ trained_model
\#install taspberry motionEyeOS
\#setup vsftpd 
dnf install vsftpd
https://blog.csdn.net/juxiezuo_0722/article/details/82586025
groupadd motion
useradd -g motion -d /opt/workspace/VisualGLM-6B/motion motion
usermod -a -G motion raysheng
cd /opt/workspace/VisualGLM-6B
chmod g+rwx motion
(base) [root@MONSTER:/etc/vsftpd]# cat user_list
motion
(base) [root@MONSTER:/etc/vsftpd]# cat vsftpd.conf |grep "^[^#]"
anonymous_enable=NO
local_enable=YES
write_enable=YES
local_umask=022
dirmessage_enable=YES
xferlog_enable=YES
connect_from_port_20=YES
xferlog_std_format=YES
listen=Yes
pam_service_name=vsftpd
userlist_enable=YES
userlist_deny=NO
listen_port=4449
\#setup motioneyeos
http://************/

visual ChatGLM
https://github.com/THUDM/VisualGLM-6B
加载
model = AutoModel.from_pretrained("THUDM/visualglm-6b", trust_remote_code=True).quantize(8).half().cuda()
API 调用方式
echo "{\"image\":\"$(base64 path/to/example.jpg)\",\"text\":\"描述这张图片\",\"history\":[]}" > temp.json
curl -X POST -H "Content-Type: application/json" -d @temp.json http://127.0.0.1:8080
{
    "response":"这张图片展现了一只可爱的卡通羊驼，它站在一个透明的背景上。这只羊驼长着一张毛茸茸的耳朵和一双大大的眼睛，它的身体是白色的，带有棕色斑点。",
    "history":[('描述这张图片', '这张图片展现了一只可爱的卡通羊驼，它站在一个透明的背景上。这只羊驼长着一张毛茸茸的耳朵和一双大大的眼睛，它的身体是白色的，带有棕色斑点。')],
    "status":200,
    "time":"2023-05-16 20:20:10"
  }

pip install SwissArmyTransformer>=0.3.6

1. start llm
cd /opt/workspace/VisualGLM-6B/
conda activate ChatGLM
python httpllm.py
2. start and check vftpd
systemctl status vsftpd
tcp        0      0 0.0.0.0:4449            0.0.0.0:*               LISTEN      93/vsftpd
3.  start raspberry and check ************ video
4. start monitoring picture generated by raspberry motionEyeos
python detectmotion.py |tee detect.log 

To clone to my owner github repo
1. create personal token
*********************************************************************************************
2. create one repo in github and change remote repo to my repo
git remote set-url origin https://github.com/netcaster1/VisualGLM-6B
3. push to my repo
git push -u origin main  <- main or master
Username for 'https://github.com': <EMAIL>
Password for 'https://<EMAIL>@github.com':  <<previous token created>>
Enumerating objects: 252, done.


---------------------------------------------------
````
  
# AtomGPT
```JavaScript
<<< AtomGPT >>
git clone https://github.com/AtomEcho/AtomGPT.git
git clone https://huggingface.co/AtomEchoAI/AtomGPT_8k
git clone https://huggingface.co/AtomEchoAI/AtomGPT_8k_chat_4bit
python example/atomgpt_chat.py --model_name_or_path AtomEchoAI/AtomGPT_8k_chat_4bit --is_4bit
from transformers import AutoTokenizer
from auto_gptq import AutoGPTQForCausalLM
model = AutoGPTQForCausalLM.from_quantized(args.model_name_or_path, device="cuda:0")
tokenizer = AutoTokenizer.from_pretrained('AtomEchoAI/AtomGPT_checkpoint_8k_chat',use_fast=False)
input_ids = tokenizer(['<s>Human: 介绍一下北京\n</s><s>Assistant: '], return_tensors="pt",add_special_tokens=False).input_ids.to('cuda')        
generate_input = {
    "input_ids":input_ids,
    "max_new_tokens":512,
    "do_sample":True,
    "top_k":50,
    "top_p":0.95,
    "temperature":0.3,
    "repetition_penalty":1.3,
    "eos_token_id":tokenizer.eos_token_id,
    "bos_token_id":tokenizer.bos_token_id,
    "pad_token_id":tokenizer.pad_token_id
}
generate_ids  = model.generate(**generate_input)
text = tokenizer.decode(generate_ids[0])
print(text)
# prepare the inputs
text_prompt = "Let's try generating speech, with Bark, a text-to-speech model"
inputs = processor(text_prompt)
# generate speech
speech_output = model.generate(**inputs.to(device))
```
# Bark Voice generation
```JavaScript
<<< Bark Voice generation >>>
https://github.com/suno-ai/bark/tree/main
English 6[MAN] and 8[MAN] and 9[WOMAN]
https://colab.research.google.com/drive/1dWWkZzvu7L9Bunq9zvD-W02RFUXoW-Pd?usp=sharing
!pip install --upgrade --quiet pip
!pip install --quiet git+https://github.com/huggingface/transformers.git
from transformers import BarkModel
model = BarkModel.from_pretrained("suno/bark")
import torch
device = "cuda:0" if torch.cuda.is_available() else "cpu"
model = model.to(device)
from transformers import AutoProcessor
processor = AutoProcessor.from_pretrained("suno/bark")
```
---
---
# Auto-GPT  
  
```JavaScript
<<< Generated by Auto-GPT >>>
pip install -U langchain==0.0.138
import openai_secret_manager
import openai
from langchain_sdk import LangChainAPI
import wolframalpha
import argparse
import sys
import os
# Retrieve the API keys
google_api_key = openai_secret_manager.get_secret("google")["api_key"]
google_cse_id = openai_secret_manager.get_secret("google")["cse_id"]
openai_secret = openai_secret_manager.get_secret("openai")
wolframalpha_app_id = openai_secret_manager.get_secret("wolframalpha")["app_id"]
# Initialize the agents
google = GoogleAgent(google_api_key, google_cse_id)
wolframalpha = WolframAlphaAgent(wolframalpha_app_id)
human_as_tool = HumanAsToolAgent()
langchain = LangChainAPI("0.0.138")
# Initialize the OpenAI GPT-3.5 Turbo model
openai.api_key = openai_secret["api_key"]
model_engine = "davinci"  # Can use any GPT-3.5 Turbo model, such as "curie" or "babbage"
# Define the function to perform a Google search
def google_search(query):
    return google.search(query)
# Authenticate with Wolfram Alpha
secrets = openai_secret_manager.get_secret("wolfram_alpha")
wa_client = wolframalpha.Client(secrets["app_id"])
# Example code to utilize Wolfram Alpha agent
query = "What is the capital of France?"
res = wa_client.query(query)
answer = next(res.results).text
print(answer)
```
  
  
```JavaScript
<<< Auto-GPT play >>>
conda clean -a
conda install python=3.10
pip install protobuf==3.20.0　
pip install --force-reinstall -r requirements.txt
-- below is for spearker
sudo dnf install python3-gobject gtk3
sudo yum install cairo-devel gobject-introspection-devel
sudo yum install cairo-gobject-devel
pip install PyGObject
^^^^^^^^^
cp -rp .env.template  env.template.orig
mv .env.template .env
python scripts/main.py --help to see a list of all available command line arguments.
python scripts/main.py --ai-settings <filename> to run Auto-GPT with a different AI Settings file.
python scripts/main.py --use-memory  <memory-backend> to specify one of 3 memory backends: local, redis, pinecone or 'no_memory'.
python scripts/main.py --speak
python scripts/main.py --speak --continuous
python scripts/main.py --speak --gpt3only
GOOGLE_API_KEY=AIzaSyB1RJwM8-r_ozVBhnqRz7LXYmtA_QyE5eo
CUSTOM_SEARCH_ENGINE_ID=5531449e036a443c3
```
---
# Lang FLow  
  
```JavaScript
<<< Lang FLow >>>
https://github.com/logspace-ai/langflow
sudo  git clone https://github.com/logspace-ai/langflow.git
sudo chown raysheng.raysheng -R langflow/
conda create -n langflow
conda activate langflow
sudo yum install postgresql-devel
pip install langflow
pip install protobuf==3.20.0
pip install greenlet==2.0.1
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
paddlepaddle 2.4.1 requires protobuf<=3.20.0,>=3.1.0, but you have protobuf 4.22.3 which is incompatible.
onnx 1.12.0 requires protobuf<=3.20.1,>=3.12.2, but you have protobuf 4.22.3 which is incompatible.
streamlit 1.18.1 requires protobuf<4,>=3.12, but you have protobuf 4.22.3 which is incompatible.
playwright 1.30.0 requires greenlet==2.0.1, but you have greenlet 1.1.3.post0 which is incompatible.
paddlespeech 1.3.0 requires protobuf<=3.20.0,>=3.1.0, but you have protobuf 4.22.3 which is incompatible.
paddlenlp 2.5.0 requires dill<0.3.5, but you have dill 0.3.6 which is incompatible.
paddlenlp 2.5.0 requires protobuf<=3.20.0,>=3.1.0, but you have protobuf 4.22.3 which is incompatible.
espnet 202301 requires protobuf<=3.20.1, but you have protobuf 4.22.3 which is incompatible.
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
gevent 21.8.0 requires greenlet<2.0,>=1.1.0; platform_python_implementation == "CPython", but you have greenlet 2.0.1 which is incompatible.
S

langflow --host 0.0.0.0 --workers 8
OPENAI_API_KEY=***************************************************
SerpApi Key: 54adb7a2176d52b7bf3be2e537739cf29d454d9df4a17595fbc060790ed60948
Not supprt GPT3.5 and GPT4 well
https://github.com/logspace-ai/langflow/issues/141
```
  
---
---
  
# ELK
  
```JavaScript
<<< Prepare ELK database >> It is optional
https://www.evernote.com/shard/s30/nl/3347953/bd78508c-4d34-4888-9a83-3cffc7a4195a?title=ELKStudyMemo
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.7.0-linux-x86_64.tar.gz
sudo groupadd -g 2018 elkusr
sudo useradd -u 2018 -m -g elkusr elkusr
vi /etc/security/limits.conf
vi /etc/sysctl.conf
sysctl -p
ln -s /opt/workspace/ai/StudyWithChatGPT/elasticsearch-8.7.0 elk
chown -R elkusr.elkusr /opt/elk
vi /etc/profile.d/elk.sh
cd /opt/elk/config
vi elasticsearch.yml  -- !!! disable security 
/opt/elk/bin/elasticsearch -d -p /opt/elk/logs/elasticsearch.pid
curl -v http://************:9200/
./elasticsearch-sql-cli
```
```JavaScript
import sys
import json
from elasticsearch import Elasticsearch
def create_elasticsearch_client(host, port):
    return Elasticsearch(hosts=[{"host": host, "port": port}])
def write_to_elasticsearch(es, data):
    for item in data:
        res = es.index(index='my_index', doc_type='_doc', body=item)
        print("Indexed document with ID:", res['_id'])
def read_from_elasticsearch(es):
    res = es.search(index='my_index', body={"query": {"match_all": {}}})
    return res['hits']['hits']
def main(json_file, host, port):
    with open(json_file, 'r') as file:
        data = json.load(file)
    es = create_elasticsearch_client(host, port)
    write_to_elasticsearch(es, data)
    print("All documents have been indexed.")
    print("Reading data from Elasticsearch:")
    results = read_from_elasticsearch(es)
    for result in results:
        print(json.dumps(result["_source"], indent=2, ensure_ascii=False))
if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python <script_name> <json_file> <elasticsearch_host> <elasticsearch_port>")
        sys.exit(1)
    json_file = sys.argv[1]
    host = sys.argv[2]
    port = int(sys.argv[3])
    main(json_file, host, port)
```
python elasticsearch_demo.py /path/to/your/json/file.json <elasticsearch_host> <elasticsearch_port>
---
---
  
  
# Langchain
## Installation Steps
```JavaScript
<<< Langchain >>>
#写langchain，描述每个tool功能很重要，可以参见
https://github.com/gkamradt/langchain-tutorials/blob/main/agents/Agents.ipynb
download miniconda and install
https://docs.conda.io/en/latest/miniconda.html
conda update -n base conda
conda config --set auto_activate_base true
conda install -n base conda-libmamba-solver
conda config --set solver libmamba
conda config --set solver classic
conda create -n langchain
conda activate langchain
git clone https://github.com/hwchase17/langchain.git
conda install python==3.11.2
conda deactivate
conda activate langchain
pip install langchain==0.0.150 or conda install langchain -c conda-forge
pip install poetry==1.4.2

pip3 install numpy --pre torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/nightly/cu118
or
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
pip install ninja
https://visualstudio.microsoft.com/visual-cpp-build-tools/
visual studio installer
start developer command prompt
conda activate langchain
set DISTUTILS_USE_SDK=1
//enable long paths in windows
Open the Start menu, search for "Group Policy", and click on "Edit group policy".
Navigate to "Local Computer Policy > Computer Configuration > Administrative Templates > System > Filesystem".
Double-click on "Enable Win32 long paths" and set it to "Enabled". Click on "Apply" and "OK".
Restart your computer.
pip install detectron2@git+https://github.com/facebookresearch/detectron2.git@v0.6\#egg=detectron2
Above still not working 
So I changed 2 step
start visual studio development command prompt and conda activate langchain
git clone https://github.com/facebookresearch/detectron2.git
cd detectron2
set DISTUTILS_USE_SDK=1
pip install -e .
pip install google-api-python-client
pip install google-search-results
pip install art
\#voice input、output
pip install gtts
pip install pyttsx3
pip install langdetect
pip install keyboard
pip install playsound==1.2.2
pip install SpeechRecognition
pip install pyaudio
pip install pymysql
pip install mysqlclient
pip install "protobuf<3.21"
pip install peft==0.2
pip install rich
pip install wolframalpha
pip install pydub
pip install simpleaudio
# install blew packages for chatGLM
pip install transformers==4.27.1
pip install unstructured[local-inference]
pip install sentence-transformers
\#analysis openai call prompt
pip install promptlayer
# # Install package
pip install "unstructured[local-inference]"
!pip install "detectron2@git+https://github.com/facebookresearch/detectron2.git@v0.6\#egg=detectron2"
pip install layoutparser[layoutmodels,tesseract]
\#install wandb
pip install wandb==0.15.0
# install convert utf8
pip install chardet
pip install charset_normalizer charset-normalizer       2.1.1
pip install --force-reinstall charset-normalizer==3.1.0
# install tiktoken used by openAIembeddings
pip install tiktoken
pip install faiss-cpu
pip install nltk
# import nltk
# nltk.download('punkt')
python -c "import nltk; nltk.download()"
# python -c "import nltk; nltk.download('averaged_perceptron_tagger')"
A new window should open, showing the NLTK Downloader. Click on the File menu and select Change Download 
Directory. For central installation, set this to C:\nltk_data (Windows), /usr/local/share/nltk_data (Mac),
 or /usr/share/nltk_data (Unix). Next, select the packages or collections you want to download.
If you did not install the data to one of the above central locations, you will need to set the NLTK_DATA 
environment variable to specify the location of the data. (On a Windows machine, right click on “My Computer”
 then select Properties > Advanced > Environment Variables > User Variables > New...)
\#SQL Chain API
https://docs.sqlalchemy.org/en/20/core/engines.html\#mysql
https://python.langchain.com/en/latest/modules/chains/examples/sqlite.html
pip install icetk
pip install protobuf==3.20.3
pip install cpm_kernels

# install bark
git clone https://github.com/suno-ai/bark
cd bark
pip install .
pip install soundfile
# cast to google home
pip install pychromecast
```
  
## Local LLM Mode
```JavaScript
<<< Local LLM Mode >>>
\#local llm
https://github.com/StarRing2022/ZeYangGPT-Uni
# local cache
https://github.com/zilliztech/GPTCache
有GLM试用SQL和中心的调度，都不行，所以看来GLM最多只能试试embedding
好像当用了AgentType.CONVERSATIONAL_REACT_DESCRIPTION, ChatGLM就不会给Call了，直接调用了OpenAI
agent_chain = initialize_agent(tools, llm, agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
                       verbose=True, memory=memory, return_intermediate_steps=False, prefix=prefix, suffix=suffix
              )
```
![[Notion/AI/🦜🦜🦜Research Memo for LangChain/attachments/Untitled.png|Untitled.png]]
  
  
## embedding library
```JavaScript
embedding library
对于目前来说，文本向量可以通过 OpenAI 的 text-embedding-ada-002 模型生成，图像向量可以通过 clip-vit-base-patch32 模型生成，
而音频向量可以通过 wav2vec2-base-960h 模型生成。这些向量都是通过 AI 模型生成的，所以它们都是具有语义信息的。
Model	Usage
\#embedding model you can selected
https://python.langchain.com/en/latest/modules/models/text_embedding.html
https://openai.com/blog/new-and-improved-embedding-model
Ada	$0.0004 / 1K tokens
text-embedding-ada-002
TTS
https://gtts.readthedocs.io/en/latest/module.html\#examples
https://www.geeksforgeeks.org/text-to-speech-changing-voice-in-python/
Local DB
https://github.com/facebookresearch/faiss
\#content
E:\Working\temp\Download
F:\GODSpace\Downloads\BaiduYunGuanjia\倪海厦\中医古籍珍稀抄本精选
F:\OneDrive\ChineseMedicine\郑钦安
F:\ResearchDirection\AI\AI-Kanbo\origdataset
F:\GODSpace\Downloads\BaiduYunGuanjia\中医书籍6\医籍650部繁体文本\ҽ��650�������ı�
F:\GODSpace\Downloads\BaiduYunGuanjia\中医基础理论\中医古籍701
F:\GODSpace\Downloads\BaiduYunGuanjia\中医基础理论\09医藏-0869部
F:\GODSpace\Downloads\BaiduYunGuanjia\倪海厦\中医经典\《中医临证经验与方法》
\#F:\GODSpace\Downloads\BaiduYunGuanjia\倪海厦\伤寒论\Referece\伤寒论辑义.txt
\#F:\GODSpace\Downloads\BaiduYunGuanjia\倪海厦\伤寒论\Referece\洄溪医案--徐灵胎.txt
F:\GODSpace\Downloads\BaiduYunGuanjia\倪海厦\中医经典\历代本草药性汇解
### F:\GODSpace\Downloads\BaiduYunGuanjia\古籍
\#Non OpenAI 
https://zhuanlan.zhihu.com/p/617545684?utm_id=0
https://github.com/imClumsyPanda/langchain-ChatGLM
!pip install langchain
!pip install huggingface_hub
!pip install sentence_transformers
!pip install faiss-cpu
import os
os.environ["HUGGINGFACEHUB_API_TOKEN"] = "HUGGINGFACEHUB_API_TOKEN"
import requests
url = "https://raw.githubusercontent.com/hwchase17/langchain/master/docs/modules/state_of_the_union.txt"
res = requests.get(url)
with open("state_of_the_union.txt", "w") as f:
  f.write(res.text)
# Document Loader
from langchain.document_loaders import TextLoader
loader = TextLoader('./state_of_the_union.txt')
documents = loader.load()
import textwrap
def wrap_text_preserve_newlines(text, width=110):
    # Split the input text into lines based on newline characters
    lines = text.split('\n')
    # Wrap each line individually
    wrapped_lines = [textwrap.fill(line, width=width) for line in lines]
    # Join the wrapped lines back together using newline characters
    wrapped_text = '\n'.join(wrapped_lines)
    return wrapped_text
# Text Splitter
from langchain.text_splitter import CharacterTextSplitter
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
docs = text_splitter.split_documents(documents)
# Embeddings
from langchain.embeddings import HuggingFaceEmbeddings
embeddings = HuggingFaceEmbeddings()
# Vectorstore: https://python.langchain.com/en/latest/modules/indexes/vectorstores.html
from langchain.vectorstores import FAISS
db = FAISS.from_documents(docs, embeddings)
query = "What did the president say about the Supreme Court"
docs = db.similarity_search(query)
from langchain.chains.question_answering import load_qa_chain
from langchain import HuggingFaceHub
llm=HuggingFaceHub(repo_id="google/flan-t5-xl", model_kwargs={"temperature":0, "max_length":512})
chain = load_qa_chain(llm, chain_type="stuff")
query = "What did the president say about the Supreme Court"
docs = db.similarity_search(query)
chain.run(input_documents=docs, question=query)
\#work with pdf 
!pip install unstructured
!pip install chromadb
!pip install Cython
!pip install tiktoken
!pip install unstructured[local-inference]
from langchain.document_loaders import UnstructuredPDFLoader
from langchain.indexes import VectorstoreIndexCreator
# connect your Google Drive
from google.colab import drive
drive.mount('/content/gdrive', force_remount=True)
pdf_folder_path = '/content/gdrive/My Drive/data_2/'
os.listdir(pdf_folder_path)
loaders = [UnstructuredPDFLoader(os.path.join(pdf_folder_path, fn)) for fn in os.listdir(pdf_folder_path)]
index = VectorstoreIndexCreator(
    embedding=HuggingFaceEmbeddings(),
    text_splitter=CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)).from_loaders(loaders)
llm=HuggingFaceHub(repo_id="google/flan-t5-xl", model_kwargs={"temperature":0, "max_length":512})
from langchain.chains import RetrievalQA
chain = RetrievalQA.from_chain_type(llm=llm, 
                                    chain_type="stuff", 
                                    retriever=index.vectorstore.as_retriever(), 
                                    input_key="question")
chain.run('How was the GPT4all model trained?')
```
  
## document loader  
  
```JavaScript
<<< document loader>>
# # Install package
!pip install "unstructured[local-inference]"
!pip install "detectron2@git+https://github.com/facebookresearch/detectron2.git@v0.6\#egg=detectron2"
!pip install layoutparser[layoutmodels,tesseract]
# import nltk
# nltk.download('punkt')
============================================================================
from langchain.document_loaders.chatgpt import ChatGPTLoader
loader = ChatGPTLoader(log_file='./example_data/fake_conversations.json', num_logs=1)
loader.load()
============================================================================
Issue@@@ 
UnstructuredLoader or UnstructuredFileLoader looks endless loading a simple text file
from langchain.document_loaders import DirectoryLoader
loader = DirectoryLoader('../', glob="**/*.md")
docs = loader.load()
len(docs)
By default this uses the UnstructuredLoader class. However, you can change up the type of loader pretty easily
loader = DirectoryLoader('../', glob="**/*.md", loader_cls=TextLoader)
docs = loader.load()
============================================================================
from langchain.document_loaders import PyPDFLoader
loader = PyPDFLoader("example_data/layout-parser-paper.pdf")
pages = loader.load_and_split()
from langchain.vectorstores import FAISS
from langchain.embeddings.openai import OpenAIEmbeddings
faiss_index = FAISS.from_documents(pages, OpenAIEmbeddings())
docs = faiss_index.similarity_search("How will the community be engaged?", k=2)
for doc in docs:
    print(str(doc.metadata["page"]) + ":", doc.page_content)
from langchain.document_loaders import UnstructuredPDFLoader
loader = UnstructuredPDFLoader("example_data/layout-parser-paper.pdf")
data = loader.load()
============================================================================
This covers how to load Word documents into a document format that we can use downstream.
from langchain.document_loaders import UnstructuredWordDocumentLoader
loader = UnstructuredWordDocumentLoader("example_data/fake.docx")
data = loader.load()
============================================================================
from langchain.embeddings import HuggingFaceEmbeddings
embeddings = HuggingFaceEmbeddings()
text = "This is a test document."
query_result = embeddings.embed_query(text)
doc_result = embeddings.embed_documents([text])
============================================================================
```
[如何用Python中的NLTK对中文进行分析和处理？_寻必宝 (xunbibao.cn)](http://xunbibao.cn/article/122414.html)
[Python数据挖掘-NLTK文本分析+jieba中文文本挖掘 - 腾讯云开发者社区-腾讯云 (tencent.com)](https://cloud.tencent.com/developer/article/1774167#:~:text=NLTK%E7%9A%84%E5%85%A8%E7%A7%B0%E6%98%AFnatural%20language,toolkit%EF%BC%8C%E6%98%AF%E4%B8%80%E5%A5%97%E5%9F%BA%E4%BA%8Epython%E7%9A%84%20%E8%87%AA%E7%84%B6%E8%AF%AD%E8%A8%80%E5%A4%84%E7%90%86%20%E5%B7%A5%E5%85%B7%E9%9B%86%E3%80%82)
  
![[Notion/AI/🦜🦜🦜Research Memo for LangChain/attachments/Untitled 1.png|Untitled 1.png]]
  
## Splitter
```JavaScript
##### Splitter
============================================================================
# This is a long document we can split up.
with open('../../../state_of_the_union.txt') as f:
    state_of_the_union = f.read()
============================================================================
from langchain.text_splitter import CharacterTextSplitter
text_splitter = CharacterTextSplitter.from_tiktoken_encoder(chunk_size=100, chunk_overlap=0)
texts = text_splitter.split_text(state_of_the_union)
print(texts[0])
============================================================================
from langchain.text_splitter import RecursiveCharacterTextSplitter
text_splitter = RecursiveCharacterTextSplitter(
    # Set a really small chunk size, just to show.
    chunk_size = 100,
    chunk_overlap  = 20,
    length_function = len,
)
texts = text_splitter.create_documents([state_of_the_union])
print(texts[0])
print(texts[1])
============================================================================
from langchain.text_splitter import NLTKTextSplitter
text_splitter = NLTKTextSplitter(chunk_size=1000)
texts = text_splitter.split_text(state_of_the_union)
print(texts[0])
============================================================================
from transformers import GPT2TokenizerFast
from langchain.text_splitter import CharacterTextSplitter
tokenizer = GPT2TokenizerFast.from_pretrained("gpt2")
# This is a long document we can split up.
with open('../../../state_of_the_union.txt') as f:
    state_of_the_union = f.read()
text_splitter = CharacterTextSplitter.from_huggingface_tokenizer(tokenizer, chunk_size=100, chunk_overlap=0)
texts = text_splitter.split_text(state_of_the_union)
print(texts[0])
============================================================================
```
##   
Vectorstores DB  
```JavaScript
##### Vectorstores DB
============================================================================
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.text_splitter import CharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain.document_loaders import TextLoader
from langchain.document_loaders import TextLoader
loader = TextLoader('../../../state_of_the_union.txt')
documents = loader.load()
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
docs = text_splitter.split_documents(documents)
embeddings = OpenAIEmbeddings()
db = FAISS.from_documents(docs, embeddings)
query = "What did the president say about Ketanji Brown Jackson"
docs = db.similarity_search(query)
print(docs[0].page_content)
docs_and_scores = db.similarity_search_with_score(query)
docs_and_scores[0]

db.save_local("faiss_index")
new_db = FAISS.load_local("faiss_index", embeddings)
docs = new_db.similarity_search(query)
docs[0]
\#or use retriever
retriever = db.as_retriever()
docs = retriever.get_relevant_documents("what did he say about ketanji brown jackson")
--By default, the vectorstore retriever uses similarity search. If the underlying vectorstore 
--support maximum marginal relevance search, you can specify that as the search type.
retriever = db.as_retriever(search_type="mmr")
docs = retriever.get_relevant_documents("what did he say abotu ketanji brown jackson")
--You can also specify search kwargs like k to use when doing retrieval.
retriever = db.as_retriever(search_kwargs={"k": 1})
docs = retriever.get_relevant_documents("what did he say abotu ketanji brown jackson")
\#merge
db1 = FAISS.from_texts(["foo"], embeddings)
db2 = FAISS.from_texts(["bar"], embeddings)
db1.docstore._dict
db1.merge_from(db2)
db1.docstore._dict
============================================================================
```
```JavaScript
##### VectorStore-Backed Memory
from datetime import datetime
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.llms import OpenAI
from langchain.memory import VectorStoreRetrieverMemory
from langchain.chains import ConversationChain
from langchain.prompts import PromptTemplate
import faiss
from langchain.docstore import InMemoryDocstore
from langchain.vectorstores import FAISS

embedding_size = 1536 # Dimensions of the OpenAIEmbeddings
index = faiss.IndexFlatL2(embedding_size)
embedding_fn = OpenAIEmbeddings().embed_query
vectorstore = FAISS(embedding_fn, index, InMemoryDocstore({}), {})
# In actual usage, you would set `k` to be a higher value, but we use k=1 to show that
# the vector lookup still returns the semantically relevant information
retriever = vectorstore.as_retriever(search_kwargs=dict(k=1))
memory = VectorStoreRetrieverMemory(retriever=retriever)
# When added to an agent, the memory object can save pertinent information from conversations or used tools
memory.save_context({"input": "My favorite food is pizza"}, {"output": "thats good to know"})
memory.save_context({"input": "My favorite sport is soccer"}, {"output": "..."})
memory.save_context({"input": "I don't the Celtics"}, {"output": "ok"}) # 
# Notice the first result returned is the memory pertaining to tax help, which the language model deems more semantically relevant
# to a 1099 than the other documents, despite them both containing numbers.
print(memory.load_memory_variables({"prompt": "what sport should i watch?"})["history"])
llm = OpenAI(temperature=0) # Can be any valid LLM
_DEFAULT_TEMPLATE = """The following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.
Relevant pieces of previous conversation:
{history}
(You do not need to use these pieces of information if not relevant)
Current conversation:
Human: {input}
AI:"""
PROMPT = PromptTemplate(
    input_variables=["history", "input"], template=_DEFAULT_TEMPLATE
)
conversation_with_summary = ConversationChain(
    llm=llm, 
    prompt=PROMPT,
    # We set a very low max_token_limit for the purposes of testing.
    memory=memory,
    verbose=True
)
conversation_with_summary.predict(input="Hi, my name is Perry, what's up?")
```
  
## Chain with embedding
```JavaScript
#### Chain with embedding
About load_qa_chain chain_type explanation
https://python.langchain.com/en/latest/modules/chains/index_examples/question_answering.html
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.text_splitter import CharacterTextSplitter
from langchain.llms import OpenAI
from langchain.chains import ConversationalRetrievalChain
from langchain.document_loaders import TextLoader
loader = TextLoader("../../state_of_the_union.txt")
documents = loader.load()
# loaders = [....]
# docs = []
# for loader in loaders:
#     docs.extend(loader.load())
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
documents = text_splitter.split_documents(documents)
embeddings = OpenAIEmbeddings()
vectorstore = Chroma.from_documents(documents, embeddings)
qa = ConversationalRetrievalChain.from_llm(OpenAI(temperature=0), vectorstore.as_retriever())
chat_history = []
query = "What did the president say about Ketanji Brown Jackson"
result = qa({"question": query, "chat_history": chat_history})
chat_history = [(query, result["answer"])]
query = "Did he mention who she suceeded"
result = qa({"question": query, "chat_history": chat_history})
You can also easily return source documents from the ConversationalRetrievalChain. 
This is useful for when you want to inspect what documents were returned.
qa = ConversationalRetrievalChain.from_llm(OpenAI(temperature=0), vectorstore.as_retriever(), return_source_documents=True)
chat_history = []
query = "What did the president say about Ketanji Brown Jackson"
result = qa({"question": query, "chat_history": chat_history})
result['source_documents'][0]
If you are using a vector store that supports filtering by search distance, you can add a threshold value parameter.
vectordbkwargs = {"search_distance": 0.9}
qa = ConversationalRetrievalChain.from_llm(OpenAI(temperature=0), vectorstore.as_retriever(), return_source_documents=True)
chat_history = []
query = "What did the president say about Ketanji Brown Jackson"
result = qa({"question": query, "chat_history": chat_history, "vectordbkwargs": vectordbkwargs})

-------------------------------------------------------------------------
Question Answering
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.text_splitter import CharacterTextSplitter
from langchain.vectorstores import Chroma
from langchain.docstore.document import Document
from langchain.prompts import PromptTemplate
from langchain.indexes.vectorstore import VectorstoreIndexCreator
with open("../../state_of_the_union.txt") as f:
    state_of_the_union = f.read()
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
texts = text_splitter.split_text(state_of_the_union)
embeddings = OpenAIEmbeddings()
docsearch = Chroma.from_texts(texts, embeddings, metadatas=[{"source": str(i)} for i in range(len(texts))]).as_retriever()
query = "What did the president say about Justice Breyer"
docs = docsearch.get_relevant_documents(query)
from langchain.chains.question_answering import load_qa_chain
from langchain.llms import OpenAI
chain = load_qa_chain(OpenAI(temperature=0), chain_type="stuff")
query = "What did the president say about Justice Breyer"
chain.run(input_documents=docs, question=query)
query = "What did the president say about Justice Breyer"
chain({"input_documents": docs, "question": query}, return_only_outputs=True)
\#custom prompts
prompt_template = """Use the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer.
{context}
Question: {question}
Answer in Italian:"""
PROMPT = PromptTemplate(
    template=prompt_template, input_variables=["context", "question"]
)
chain = load_qa_chain(OpenAI(temperature=0), chain_type="stuff", prompt=PROMPT)
chain({"input_documents": docs, "question": query}, return_only_outputs=True)
-------------------------------------------------------
\#Retrieval Question/Answering
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.text_splitter import CharacterTextSplitter
from langchain.llms import OpenAI
from langchain.chains import RetrievalQA
from langchain.document_loaders import TextLoader
loader = TextLoader("../../state_of_the_union.txt")
documents = loader.load()
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
texts = text_splitter.split_documents(documents)
embeddings = OpenAIEmbeddings()
docsearch = Chroma.from_documents(texts, embeddings)
qa = RetrievalQA.from_chain_type(llm=OpenAI(), chain_type="stuff", retriever=docsearch.as_retriever())
query = "What did the president say about Ketanji Brown Jackson"
qa.run(query)
from langchain.chains.question_answering import load_qa_chain
qa_chain = load_qa_chain(OpenAI(temperature=0), chain_type="stuff")
qa = RetrievalQA(combine_documents_chain=qa_chain, retriever=docsearch.as_retriever())

from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.embeddings.cohere import CohereEmbeddings
from langchain.text_splitter import CharacterTextSplitter
from langchain.vectorstores.elastic_vector_search import ElasticVectorSearch
from langchain.vectorstores import Chroma
with open("../../state_of_the_union.txt") as f:
    state_of_the_union = f.read()
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
texts = text_splitter.split_text(state_of_the_union)
embeddings = OpenAIEmbeddings()
docsearch = Chroma.from_texts(texts, embeddings, metadatas=[{"source": f"{i}-pl"} for i in range(len(texts))])
from langchain.chains import RetrievalQAWithSourcesChain
from langchain import OpenAI
chain = RetrievalQAWithSourcesChain.from_chain_type(OpenAI(temperature=0), chain_type="stuff", retriever=docsearch.as_retriever())
chain({"question": "What did the president say about Justice Breyer"}, return_only_outputs=True)
from langchain.chains.qa_with_sources import load_qa_with_sources_chain
qa_chain = load_qa_with_sources_chain(OpenAI(temperature=0), chain_type="stuff")
qa = RetrievalQAWithSourcesChain(combine_documents_chain=qa_chain, retriever=docsearch.as_retriever())
qa({"question": "What did the president say about Justice Breyer"}, return_only_outputs=True)
from langchain.chains.question_answering import load_qa_chain
qa_chain = load_qa_chain(OpenAI(temperature=0), chain_type="stuff")
qa = RetrievalQA(combine_documents_chain=qa_chain, retriever=docsearch.as_retriever())
query = "What did the president say about Ketanji Brown Jackson"
qa.run(query)
============================================================================
loader = UnstructuredPDFLoader("book.pdf") 
pages = loader.load_and_split() 
embeddings = OpenAIEmbeddings() 
docsearch = Chroma.from_documents(pages, embeddings).as_retriever() 
docs = docsearch.get_relevant_documents(query) 
chain = load_qa_chain(OpenAI(temperature=0), chain_type="stuff") 
query = "Who is Rich Dad?" 
chain.run(input_documents=docs, question=query)
============================================================================
from transformers.tools import HfAgent
import textract
text = textract.process('sample.pdf').decode('utf-8')
agent = HfAgent("https://api-inference.huggingface.co/models/bigcode/startcoder")
agent.run("Can you summarize 'text' for me?", text = text)
agent.run("Based on 'text', What country borders Pakistan on the West?", text=text)

============================================================================
https://python.langchain.com/en/latest/modules/chains/examples/multi_retrieval_qa_router.html
《《Router Chains: Selecting from multiple prompts with MultiRetrievalQAChain》》
from langchain.chains.router import MultiRetrievalQAChain
from langchain.llms import OpenAI
from langchain.embeddings import OpenAIEmbeddings
from langchain.document_loaders import TextLoader
from langchain.vectorstores import FAISS
sou_docs = TextLoader('../../state_of_the_union.txt').load_and_split()
sou_retriever = FAISS.from_documents(sou_docs, OpenAIEmbeddings()).as_retriever()
pg_docs = TextLoader('../../paul_graham_essay.txt').load_and_split()
pg_retriever = FAISS.from_documents(pg_docs, OpenAIEmbeddings()).as_retriever()
personal_texts = [
    "I love apple pie",
    "My favorite color is fuchsia",
    "My dream is to become a professional dancer",
    "I broke my arm when I was 12",
    "My parents are from Peru",
]
personal_retriever = FAISS.from_texts(personal_texts, OpenAIEmbeddings()).as_retriever()
retriever_infos = [
    {
        "name": "state of the union", 
        "description": "Good for answering questions about the 2023 State of the Union address", 
        "retriever": sou_retriever
    },
    {
        "name": "pg essay", 
        "description": "Good for answer quesitons about Paul Graham's essay on his career", 
        "retriever": pg_retriever
    },
    {
        "name": "personal", 
        "description": "Good for answering questions about me", 
        "retriever": personal_retriever
    }
]

chain = MultiRetrievalQAChain.from_retrievers(OpenAI(), retriever_infos, verbose=True)
print(chain.run("What did the president say about the economy?"))
=============================---------========================
https://python.langchain.com/en/latest/modules/agents/toolkits/examples/vectorstore.html
Multiple Vectorstores
from langchain.agents.agent_toolkits import (
    create_vectorstore_agent,
    VectorStoreToolkit,
    VectorStoreInfo,
)
vectorstore_info = VectorStoreInfo(
    name="state_of_union_address",
    description="the most recent state of the Union adress",
    vectorstore=state_of_union_store
)
toolkit = VectorStoreToolkit(vectorstore_info=vectorstore_info)
agent_executor = create_vectorstore_agent(
    llm=llm,
    toolkit=toolkit,
    verbose=True
)
ruff_vectorstore_info = VectorStoreInfo(
    name="ruff",
    description="Information about the Ruff python linting library",
    vectorstore=ruff_store
)
router_toolkit = VectorStoreRouterToolkit(
    vectorstores=[vectorstore_info, ruff_vectorstore_info],
    llm=llm
)
agent_executor = create_vectorstore_router_agent(
    llm=llm,
    toolkit=router_toolkit,
    verbose=True
)
agent_executor.run("What did biden say about ketanji brown jackson is the state of the union address?")
============================================================================
Do embedding Researching without LLM
https://python.langchain.com/en/latest/modules/chains/generic/router.html\#embeddingrouterchain
from langchain.chains.router.embedding_router import EmbeddingRouterChain
from langchain.embeddings import CohereEmbeddings
from langchain.vectorstores import Chroma
names_and_descriptions = [
    ("physics", ["for questions about physics"]),
    ("math", ["for questions about math"]),
]
router_chain = EmbeddingRouterChain.from_names_and_descriptions(
    names_and_descriptions, Chroma, CohereEmbeddings(), routing_keys=["input"]
)
chain = MultiPromptChain(router_chain=router_chain, destination_chains=destination_chains, default_chain=default_chain, verbose=True)
print(chain.run("What is black body radiation?"))
============================================================================



============================================================================
```
![[Notion/AI/🦜🦜🦜Research Memo for LangChain/attachments/Untitled 2.png|Untitled 2.png]]
```JavaScript
<< Solve Unstructured loader no response or exit issue >>
https://python.langchain.com/docs/use_cases/question_answering.html
https://stackoverflow.com/questions/********/loading-data-using-unstructuredurlloader-of-langchain-halts-with-tp-num-c-buf
\#need to install package
pip install python-magic python-magic-bin
pip install tabulate pdf2image pytesseract
pip install libmagic
-----------------------------------------
from langchain.document_loaders.url import UnstructuredURLLoader
from langchain.text_splitter import CharacterTextSplitter
def get_content_from_urls(urls):
    loader = UnstructuredURLLoader(urls=urls)
    data = loader.load()
    
    return data
urls = [
"https://www.understandingwar.org/backgrounder/russian-offensive-campaign-assessment-february-8-2023",
"https://www.understandingwar.org/backgrounder/russian-offensive-campaign-assessment-february-9-2023"
]
data = get_content_from_urls(urls)
print(data)

# Document loader
from langchain.document_loaders import WebBaseLoader
loader = WebBaseLoader("https://lilianweng.github.io/posts/2023-06-23-agent/")
data = loader.load()

# Split
from langchain.text_splitter import RecursiveCharacterTextSplitter
text_splitter = RecursiveCharacterTextSplitter(chunk_size = 500, chunk_overlap = 0)
all_splits = text_splitter.split_documents(data)

# Store 
from langchain.vectorstores import Chroma
from langchain.embeddings import OpenAIEmbeddings
vectorstore = Chroma.from_documents(documents=all_splits,embedding=OpenAIEmbeddings())
\#Retrieval
question = "What are the approaches to Task Decomposition?"
docs = vectorstore.similarity_search(question)
len(docs)
from langchain.retrievers import SVMRetriever
svm_retriever = SVMRetriever.from_documents(all_splits,OpenAIEmbeddings())
docs_svm=svm_retriever.get_relevant_documents(question)
len(docs)
# MultiQueryRetriever
import logging
from langchain.chat_models import ChatOpenAI
from langchain.retrievers.multi_query import MultiQueryRetriever
logging.basicConfig()
logging.getLogger('langchain.retrievers.multi_query').setLevel(logging.INFO)
retriever_from_llm = MultiQueryRetriever.from_llm(retriever=vectorstore.as_retriever(),
                                                  llm=ChatOpenAI(temperature=0))
unique_docs = retriever_from_llm.get_relevant_documents(query=question)
len(unique_docs)
from langchain.chat_models import ChatOpenAI
llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0)
from langchain.chains import RetrievalQA
qa_chain = RetrievalQA.from_chain_type(llm,retriever=vectorstore.as_retriever())
qa_chain({"query": question})
```
  
---
## Ask data from Pandas
  
```JavaScript
<<< Ask data from Pandas >>
import pandas as pd 
from pandasai import PandasAI 
# Sample DataFrame 
df = pd.DataFrame({ 
"country": [ 
    "United States", "United Kingdom", "France", "Germany", "Italy", "Spain", "Canada", "Australia", "Japan", "China"],
"gdp": [ 
    19294482071552, 2891615567872, 2411255037952, 3435817336832, 1745433788416, 1181205135360, 1607402389504, 1490967855104, 
    4380756541440, 14631844184064], 
 "happiness_index": [ 
    6.94, 7.16, 6.66, 7.07, 6.38, 6.4, 7.23, 7.22, 5.87, 5.12] 
}) 
# Instantiate a LLM 
from pandasai.llm.openai import OpenAI 
llm = OpenAI() 
pandas_ai = PandasAI(llm) 
pandas_ai.run(df, prompt='Which are the 5 happiest countries?')
```
---
  
## Agent
```JavaScript
##### Agent
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.text_splitter import CharacterTextSplitter
from langchain.llms import OpenAI
from langchain.chains import RetrievalQA
llm = OpenAI(temperature=0)
from pathlib import Path
relevant_parts = []
for p in Path(".").absolute().parts:
    relevant_parts.append(p)
    if relevant_parts[-3:] == ["langchain", "docs", "modules"]:
        break
doc_path = str(Path(*relevant_parts) / "state_of_the_union.txt")
from langchain.document_loaders import TextLoader
loader = TextLoader(doc_path)
documents = loader.load()
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
texts = text_splitter.split_documents(documents)
embeddings = OpenAIEmbeddings()
docsearch = Chroma.from_documents(texts, embeddings, collection_name="state-of-union")
state_of_union = RetrievalQA.from_chain_type(llm=llm, chain_type="stuff", retriever=docsearch.as_retriever())
from langchain.document_loaders import WebBaseLoader
docs = loader.load()
ruff_texts = text_splitter.split_documents(docs)
ruff_db = Chroma.from_documents(ruff_texts, embeddings, collection_name="ruff")
ruff = RetrievalQA.from_chain_type(llm=llm, chain_type="stuff", retriever=ruff_db.as_retriever())
\#Create agent
# Import things that are needed generically
from langchain.agents import initialize_agent, Tool
from langchain.agents import AgentType
from langchain.tools import BaseTool
from langchain.llms import OpenAI
from langchain import LLMMathChain, SerpAPIWrapper
tools = [
    Tool(
        name = "State of Union QA System",
        func=state_of_union.run,
        description="useful for when you need to answer questions about the most recent state of the union address. Input should be a fully formed question."
    ),
    Tool(
        name = "Ruff QA System",
        func=ruff.run,
        description="useful for when you need to answer questions about ruff (a python linter). Input should be a fully formed question."
    ),
]
# Construct the agent. We will use the default agent type here.
# See documentation for a full list of options.
agent = initialize_agent(tools, llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True)
agent.run("What did biden say about ketanji brown jackson is the state of the union address?")

===========================================================================
import { MultiPromptChain } from "langchain/chains"; 
import OpenAIChat } from "langchain/llms/openai"; 
export const run async () => { 
   const llm = new OpenAIChat(); 
   const promptNames = ["physics", "math", "history"]; 
   const promptDescriptions = [ 
     "Good for answering questions about physics", 
	 "Good for answering math questions", 
	 "Good for answering questions about history", 
   ];
   
   const physicsTemplate = `You are a very smart physics professor. You are great at answering
   Here is a question: 
   {input}
   `;
   const mathTemplate = `You are a very good mathematician. You are great at answering math que 
   
   Here is a question: 
   {input}
   `; 
   
   const historyTemplate = `You are a very smart history professor. You are great at answering 
   
   Here is a question: {input}
   `; 
   
   const promptTemplates = [physicsTemplate, mathTemplate, historyTemplate]; 
   
   const multiPromptChain = MultiPromptChain.fromPrompts( 
	llm, 
	promptNames, 
	promptDescriptions, 
	promptTemplates 
   );
         
   const testPromise1 = multiPromptChain.call({ 
       input: "What is the speed of light?",
	});
```
  
  
  
---
## plan_and_execute
```JavaScript
https://python.langchain.com/en/latest/modules/agents/plan_and_execute.html#
from langchain.chat_models import ChatOpenAI
from langchain.experimental.plan_and_execute import PlanAndExecute, load_agent_executor, load_chat_planner
from langchain.llms import OpenAI
from langchain import SerpAPIWrapper
from langchain.agents.tools import Tool
from langchain import LLMMathChain
search = SerpAPIWrapper()
llm = OpenAI(temperature=0)
llm_math_chain = LLMMathChain.from_llm(llm=llm, verbose=True)
tools = [
    Tool(
        name = "Search",
        func=search.run,
        description="useful for when you need to answer questions about current events"
    ),
    Tool(
        name="Calculator",
        func=llm_math_chain.run,
        description="useful for when you need to answer questions about math"
    ),
]
model = ChatOpenAI(temperature=0)
planner = load_chat_planner(model)
executor = load_agent_executor(model, tools, verbose=True)
agent = PlanAndExecute(planner=planner, executer=executor, verbose=True)

agent.run("Who is Leo DiCaprio's girlfriend? What is her current age raised to the 0.43 power?")
```
---
  
## gradio-llm-agents
```JavaScript
https://blog.langchain.dev/gradio-llm-agents/
import os
if not os.getenv("OPENAI_API_KEY"):
    raise ValueError("OPENAI_API_KEY must be set")
from langchain.agents import initialize_agent
from langchain.llms import OpenAI
from gradio_tools import (StableDiffusionTool, ImageCaptioningTool, StableDiffusionPromptGeneratorTool,
                          TextToVideoTool)
from langchain.memory import ConversationBufferMemory
llm = OpenAI(temperature=0)
memory = ConversationBufferMemory(memory_key="chat_history")
tools = [StableDiffusionTool().langchain, ImageCaptioningTool().langchain,
         StableDiffusionPromptGeneratorTool().langchain, TextToVideoTool().langchain]
agent = initialize_agent(tools, llm, memory=memory, agent="conversational-react-description", verbose=True)
output = agent.run(input=("Please create a photo of a dog riding a skateboard "
                          "but improve my prompt prior to using an image generator."
                          "Please caption the generated image and create a video for it using the improved prompt."))
```
##   
Self Query -- get more medat data BUT only support Pinecore so far  
```JavaScript
<<< Self Query -- get more medat data BUT only support Pinecore so far >>>
https://python.langchain.com/en/latest/modules/indexes/retrievers/examples/self_query_retriever.html?highlight=SelfQueryRetriev
from langchain.llms import OpenAI
from langchain.retrievers.self_query.base import SelfQueryRetriever
from langchain.chains.query_constructor.base import AttributeInfo
metadata_field_info=[
    AttributeInfo(
        name="genre",
        description="The genre of the movie", 
        type="string or list[string]", 
    ),
    AttributeInfo(
        name="year",
        description="The year the movie was released", 
        type="integer", 
    ),
    AttributeInfo(
        name="director",
        description="The name of the movie director", 
        type="string", 
    ),
    AttributeInfo(
        name="rating",
        description="A 1-10 rating for the movie",
        type="float"
    ),
]
document_content_description = "Brief summary of a movie"
llm = OpenAI(temperature=0)
retriever = SelfQueryRetriever.from_llm(llm, vectorstore, document_content_description, metadata_field_info, verbose=True)
```
## Convert FASSI DB data to Pinecore DB  
  
```JavaScript
<<< Convert FASSI DB data to Pinecore DB >>>
pip install faiss
pip install pinecone-client
import faiss
import numpy as np
# 加载已有的FAISS索引
index = faiss.read_index("faiss_index.file")
# 获取向量数量
num_vectors = index.ntotal
# 从FAISS索引中提取向量
vectors = np.zeros((num_vectors, index.d), dtype=np.float32)
faiss.extract_index_vectors(index, vectors)
import pinecone
# Pinecone API密钥
pinecone_api_key = "<your-pinecone-api-key>"
# 初始化Pinecone客户端
pinecone.init(api_key=pinecone_api_key)
# 创建新的Pinecone名称空间
pinecone_namespace = "your_namespace"
pinecone.deinit()
# 初始化向量上传
pinecone.init(api_key=pinecone_api_key, environment="your_environment")
pinecone.create_namespace(pinecone_namespace)
# 将向量上传到Pinecone
vector_uploader = pinecone.VectorUploader(namespace=pinecone_namespace)
for i, vector in enumerate(vectors):
    item_id = f"item_{i}"  # 你可以根据实际情况为每个向量设置唯一的ID
    vector_uploader.add(item_id, vector)
vector_uploader.flush()
vector_uploader.deinit()
# 清理并退出Pinecone
pinecone.deinit()
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
导入数据步骤
1. 把要导入的文件放入，因为成功导入后文件会删除，自己做好备份
F:\ResearchDirection\AI\langchain\dataworld
2. 导入新数据时，要保证下面目录为空，如果是在已有数据库上叠加，那么把要叠加的库copy到这一目录
F:\ResearchDirection\AI\langchain\vector_store\db_import
3. 设置key.py
Data_Load_Mode = True
4. 运行SmartAI, 在导入模式下，运行完不会进入console而是退出，如果导入过程中失败，转换好的UTF文件不会删除，
你可以利用它，知道转换到哪里失败了。如果失败，你可以用其他编辑软件。比如Notepad++做下UTF8的格式转换，然后再次导入
5. 导入成功，dataworld下文件会删除，在F:\ResearchDirection\AI\langchain\vector_store
  目录下会生成新的目录，里面有最新的数据库文件，检查无误后，覆盖对应的下列数据库
VC_SHANGHAN = r"F:\ResearchDirection\AI\langchain\vector_store\db_shanghan"
VC_CAOBEN = r"F:\ResearchDirection\AI\langchain\vector_store\db_caoben"
VC_YILI = r"F:\ResearchDirection\AI\langchain\vector_store\db_yili"
VC_YIAN = r"F:\ResearchDirection\AI\langchain\vector_store\db_yian"
VC_KUBER= r"F:\ResearchDirection\AI\langchain\vector_store\db_kuber"
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
```
## Rerank to improve
```Python
from langchain.chains.question_answering import load_qa_chain
chain = load_qa_chain(OpenAI(), 
                      chain_type="map_rerank",
                      return_intermediate_steps=True
                      ) 
query = "who are openai?"
docs = docsearch.similarity_search(query,k=10)
results = chain({"input_documents": docs, "question": query}, return_only_outputs=True)
results
```
  
#   
Voice Input  
```JavaScript
<<< Voice Input >>
https://pypi.org/project/SpeechRecognition/
https://qiita.com/KENTAROSZK/items/3f393c000c2492034c1b
pip install SpeechRecognition
解决了语音和键盘同时输入
import pyttsx3
from langdetect import detect
from langdetect.lang_detect_exception import LangDetectException
from threading import Lock, Event

audio = pyttsx3.init()
mutex_lock = Lock() # Ensure only one sound is played at a time
# queue_semaphore = Semaphore(1) # The amount of sounds to queue before blocking the main thread
stop_flag = False # 键盘模块和语音识别模块，通过此flag来作为退出标记
recognition_allowed = threading.Event() #控制要不要让识别模块停一下，clear了就stop，set就开始，wait是等待set
recognition_allowed.set()  # 初始化为 "set" 状态，允许识别，
microphone_speech_lock = Lock() # gtts_speech有些问题，有时因为延迟，会让语音识别模块听到部分语音，然后作为输入了
为了确保在调用sr.Microphone()时不会调用say_text，我们可以在speak函数内部使用一个锁来实现同步。这样，
在recognize_speech函数中调用sr.Microphone()时，speak函数将等待锁释放，反之亦然。，在调用sr.Microphone()时，
speak函数将等待microphone_speech_lock释放。同样，在speak函数中调用gtts_speech时，recognize_speech函数将
等待microphone_speech_lock释放。这确保了sr.Microphone()和say_text之间的同步。

def gtts_speech(text, voice_style: str = "zh-cn", accent: str = "zh-cn"):
    if not text or text.isspace(): #有时text里面没有任何文字，无法识别是那种lang，就return
        return
    try:
        lang = detect(text)
    except LangDetectException:
        # 如果检测到异常，直接跳过语音输出
        return
    # 根据文本语言选择语言模型
    if lang == 'en':
        tts = gTTS(text=text, lang=lang, slow=False)
    elif lang == 'zh-cn':
        tts = gTTS(text, lang=voice_style, slow=False, lang_check=True, tld=accent)
    else:
        # 使用默认的语言模型
        tts = gTTS(text=text, slow=False)
    with mutex_lock:
        tts.save("speech.mp3")
        playsound("speech.mp3", True)
        os.remove("speech.mp3")
def pytts_speech(text):
    audio.setProperty('rate', 150)
    # audio.setProperty('volume', 0.8)
    audio.say(text)
    audio.runAndWait()

def say_text(text,  voice_style: str = "zh-cn", accent: str = "us", male_female: str = "female" ):
    global recognition_allowed
    def speak():
        global recognition_allowed, microphone_speech_lock
        with microphone_speech_lock:
            if os.environ["LISTEN_FLAG"] == "True":
                recognition_allowed.clear()
            try:
                gtts_speech(text, voice_style, accent)
                # time.sleep(1)
            finally:
                if os.environ["LISTEN_FLAG"] == "True":
                    recognition_allowed.set()  # 语音输出完成后，允许识别
    # Disable speech recognition while speaking
    voice_flag = os.environ["VOICE_FLAG"]
    if voice_flag == "True":
        if male_female == "female":
            # queue_semaphore.acquire(True)
            recognition_allowed.clear()  # 等待语音输出完成
            thread = threading.Thread(target=speak)
            thread.start()
        else:
            pytts_speech(text)


def recognize_speech(console, handle_input):
    global stop_flag, recognition_allowed, microphone_speech_lock
    recognizer = sr.Recognizer()
    while not stop_flag:
        if os.environ["LISTEN_FLAG"] == "False":
            # recognition_allowed.clear()  # 清除事件标志
            recognition_allowed.wait()  # 阻塞，等待事件标志被设置
            continue
        recognition_allowed.wait()  # 阻塞，等待事件标志被设置
        with microphone_speech_lock, sr.Microphone() as source:
            try:
                    audio = recognizer.listen(source, timeout=5)
                    text = recognizer.recognize_google(audio, language='zh-CN')
                    console.print(f"{text}")
                    handle_input(text, show_prompt=True, from_voice=True)
            except sr.UnknownValueError:
                pass
            except sr.WaitTimeoutError:
                pass
            except (EOFError, KeyboardInterrupt):
                # 按下 Ctrl+D 或 Ctrl+C，退出程序
                stop_flag = True
                console.print("声音识别模块已退出。")
                pass
            except sr.RequestError as e:
                console.print("无法连接到Google服务：{0}".format(e))
def process_result(result, keep_return : bool = False):
    # 定义一个处理Document对象的函数
    def process_document(doc):
        # 删除page_content中的全角空格
        if keep_return:
            doc.page_content = doc.page_content.replace('\u3000', '')
        else:
            doc.page_content = doc.page_content.replace('\u3000', '').replace('\n', '')
        # 删除metadata中的全角空格并处理source
        doc.metadata['source'] = os.path.basename(doc.metadata['source']).replace("-utf8"*4, "").replace(".txt", "").replace('\u3000', '')
        return f"{doc.page_content} ({doc.metadata['source']})"
    if isinstance(result, dict):
        # 将字典result转换为字符串，并处理metadata中的source
        processed_result = ""
        for key, value in result.items():
            if key == 'source_documents':
                value = '\n------------------------------------------------------------\n '.join([process_document(doc) for doc in value])
            processed_result += f"{key}: {value}\n"
        return processed_result
    elif isinstance(result, list):
        # 当result是Document列表时
        processed_result = '\n------------------------------------------------------------\n '.join([process_document(doc) for doc in result])
        return processed_result
    else:
        raise ValueError("Unsupported input type. Expected dict or list of Document objects.")

# Chat CLI Loop
def main():
    console = Console()
    console.print("-----------------------------------------------------------------------")
    console.print(text2art("SmartAI"))
    console.print("-----------------------------------------------------------------------")
    console.print(" Ray Sheng (<EMAIL>)")
    console.print("-----------------------------------------------------------------------")
    console.print("/ai : ask ai to answer your question")
    console.print("/say : Enable/Disable voice Output of AI")
    console.print("/listen : Enable/Disable voice Input of AI")
    console.print("/reset : Clear the conversation history")
    console.print("exit : exit program")
    console.print("-----------------------------------------------------------------------")
    input_lines = []
    def showprompt(show_prompt=True):
        if show_prompt:
            current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            console.print(f"{current_datetime} > ", end="")
    def showResult(text, show_prompt=True):
        syntax = Syntax(text, "markdown", theme="nord", line_numbers=False, word_wrap=True)
        console.print(syntax)
        showprompt(show_prompt)
    def handle_input(line, show_prompt=True, from_voice=False):
        global stop_flag
        nonlocal input_lines
        if line.lower() == "exit" or line.lower() == "退出":
            stop_flag = True
            return
        if line.lower() == "/say" or line.lower() == "语音":
            if os.environ["VOICE_FLAG"] == "True":
                console.print("语音输出已关闭")
                os.environ["VOICE_FLAG"] = "False"
            else:
                console.print("语音输出已开启")
                os.environ["VOICE_FLAG"] = "True"
            showprompt(show_prompt)
            return
        if line.lower() == "/listen" or line.lower() == "语音输入":
            if os.environ["LISTEN_FLAG"] == "True":
                # recognition_allowed.clear()  # 设置为 "clear" 状态，禁止识别
                console.print("语音输入已关闭")
                recognition_allowed.clear()
                os.environ["LISTEN_FLAG"] = "False"
            else:
                # recognition_allowed.set()  # 设置为 "set" 状态，允许识别
                console.print("语音输入已开启")
                os.environ["LISTEN_FLAG"] = "True"
                recognition_allowed.set()
            showprompt(show_prompt)
            return
        if line.lower() == "/ai" or line.lower() == "运行":
            console.print("Thinking...")
            md_content = "\n".join(input_lines)
            ai = agent_chain.run(input=md_content )
            input_lines.clear()
            say_text(ai)
            showResult(ai, show_prompt)
            return
        if line.lower() == "/reset" or line.lower() == "重置":
            input_lines.clear()
            memory.chat_memory.clear()
            console.print("Reset done")
            # memory.chat_memory.add_user_message(system_prefix)
            showprompt(show_prompt)
            return
        else:
            if from_voice:
                input_lines.append(line + "\n")
            else:
                input_lines.append(line)
            showprompt(show_prompt)
    def keyboard_input():
        global stop_flag
        current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        console.print(f"{current_datetime} > ", end="")
        line = input()
        while not stop_flag:
            try:
                handle_input(line)
                line = input()
            except (EOFError, KeyboardInterrupt):
                # 按下 Ctrl+D，退出程序
                stop_flag = True
                break

    voice_thread = threading.Thread(target=recognize_speech, args=(console, handle_input))
    voice_thread.start()
		# 因为键盘模块一般都等待在input，即使语音输入模块设了stop_flag也没用，这样会出现语音模块退了，但
     键盘模块还在等待输入，所以设键盘输入为守护线程，当键盘线程退了。main主线程就会退出，这样
      键盘线程就会强制终止，这是想到的唯一方法了。
    keyboard_thread = threading.Thread(target=keyboard_input)
    keyboard_thread.daemon = True
    keyboard_thread.start()
    voice_thread.join()  # 等待语音识别线程结束
    # keyboard_thread.join()  # 不再需要等待键盘输入线程结束
    if key.Disable_wandb == "False":
        WandbTracer.finish()
if __name__ == "__main__":
    main()
```
---
---
  
# Convert to Graph
```JavaScript
https://community.openai.com/t/knowledgegraph-gpt-converting-unstructured-text-into-a-knowledge-graph/48941
Create 2 program and extract pdf and website into graph db, then probably next step is convert into vector and ask AI
F:\ResearchDirection\AI\langchain\Pdf2Graph.py
F:\ResearchDirection\AI\langchain\Web2Graph.py
run above and generate graph file memory_graph.graphml
then use Gephu load it
```
  
  
  
  
  
---
---
# WEB UI
<<< WEB UI >>>
```JavaScript
Platform
https://vercel.com/docs
https://github.com/homanp/langchain-ui\#getting-started
https://langchain-ui.vercel.app/
https://github.com/homanp/vercel-langchain
https://zenn.dev/ttskch/articles/5f98aae0975720
\#install nodejs
https://nodejs.org/en/download
https://github.com/KawanaS/Machine-Learning-Web-App-Predicts-Survival-On-The-Titanic/tree/main
\#streamlit
https://docs.streamlit.io/library/cheatsheet
\#Render similar like vercel
https://dashboard.render.com/
https://vercel.com/dashboard
\#bubble
Frontend design 
https://bubble.io/welcome?ref=account-avatar-menu
https://bubble.io/academy?utm_source=Iterable&utm_medium=email&utm_campaign=onboarding&campaignId=5546653&templateId=7543662
one case
https://www.youtube.com/watch?v=qWv2vyOX0tk

\#flowiseAI
https://github.com/FlowiseAI/Flowise.git
\#some of sample
https://github.com/huggingface/chat-ui
https://huggingface.co/chat/
# Solara framework
https://github.com/widgetti/solara
https://solara.dev/
```
---
# Flowise
```JavaScript
<<< Flowise >>>
create new env for flowise
npm i -g yarn
git clone https://github.com/FlowiseAI/Flowise.git
cd Flowise
yarn install
\#due to antivirus tool. will fail, use this command to rerun
yarn build --force --no-cache
flowise-ui:build: The project was built assuming it is hosted at /.
flowise-ui:build: You can control this with the homepage field in your package.json.
flowise-ui:build:
flowise-ui:build: The build folder is ready to be deployed.
flowise-ui:build: You may serve it with a static server:
flowise-ui:build:
flowise-ui:build:   npm install -g serve
flowise-ui:build:   serve -s build
flowise-ui:build:
flowise-ui:build: Find out more about deployment here:
flowise-ui:build:
flowise-ui:build:   https://cra.link/deployment
flowise-ui:build:
flowise:build: cache bypass, force executing b47f79073e3c0192
flowise:build: $ tsc
 Tasks:    3 successful, 3 total
Cached:    0 cached, 3 total
  Time:    1m21.128s
Done in 81.39s.
A
```
Above looks old, pls refer to below
==[[🦜🦜🦜FlowiseAI-Flowise- Drag & drop UI to build your customized LLM flow]]====  
  
==
  
---
# setup MOSS
  
```JavaScript
<<< Try to setup MOSS >>
在windows下没有triton package
https://blog.csdn.net/ddrfan/article/details/130127401
cmake
https://pypi.org/project/cmake/\#files
triton
https://huggingface.co/r4ziel/xformers_pre_built/blob/main/triton-2.0.0-cp310-cp310-win_amd64.whl
```
# FrontEnd development
```JavaScript
<<< All Fultter development and Research >>>
For my next step frontend development. Recoded and design it as web api
```
[[🦜🦜🦜 Flutter Researching Memo]]  
  
  
# Tree of Thought
```JavaScript
<<< Tree of Thought >>
pip install guidance
https://github.com/microsoft/guidance
https://github.com/kyegomez/tree-of-thoughts/tree/main
```
# OpenAI Function Call
```JavaScript
<<< OpenAI Function Call related >>
https://python.langchain.com/docs/modules/agents/how_to/use_toolkits_with_openai_functions
# in langchain
from langchain import (
    LLMMathChain,
    OpenAI,
    SerpAPIWrapper,
    SQLDatabase,
    SQLDatabaseChain,
)
from langchain.agents import initialize_agent, Tool
from langchain.agents import AgentType
from langchain.chat_models import ChatOpenAI
from langchain.agents.agent_toolkits import SQLDatabaseToolkit
from langchain.schema import SystemMessage
db = SQLDatabase.from_uri("sqlite:///../../../../../notebooks/Chinook.db")
toolkit = SQLDatabaseToolkit(llm=ChatOpenAI(), db=db)
#!!!! Here is how to add system messages @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
agent_kwargs = {
    "system_message": SystemMessage(content="You are an expert SQL data analyst.")
}
llm = ChatOpenAI(temperature=0, model="gpt-3.5-turbo-0613")
agent = initialize_agent(
    toolkit.get_tools(), 
    llm, 
    agent=AgentType.OPENAI_FUNCTIONS, 
    verbose=True, 
    agent_kwargs=agent_kwargs,
)
agent.run("how many different artists are there?")
=======================================================================
https://python.langchain.com/docs/modules/agents/agent_types/openai_functions_agent
from langchain import (
    LLMMathChain,
    OpenAI,
    SerpAPIWrapper,
    SQLDatabase,
    SQLDatabaseChain,
)
from langchain.agents import initialize_agent, Tool
from langchain.agents import AgentType
from langchain.chat_models import ChatOpenAI

# Initialize the OpenAI language model
\#Replace <your_api_key> in openai_api_key="<your_api_key>" with your actual OpenAI key.
llm = ChatOpenAI(temperature=0, model="gpt-3.5-turbo-0613",openai_api_key="<your_api_key>")
# Initialize the SerpAPIWrapper for search functionality
\#Replace <your_api_key> in openai_api_key="<your_api_key>" with your actual SerpAPI key.
search = SerpAPIWrapper(serpapi_api_key="<your_api_key>")
# Initialize the LLMMathChain
llm_math_chain = LLMMathChain.from_llm(llm=llm, verbose=True)
# Initialize the SQL database using the Chinook database file
# Replace the file location to the custom Data Base
db = SQLDatabase.from_uri("sqlite:///../../../../../notebooks/Chinook.db")
# Initialize the SQLDatabaseChain with the OpenAI language model and SQL database
db_chain = SQLDatabaseChain.from_llm(llm, db, verbose=True)
# Define a list of tools offered by the agent
tools = [
    Tool(
        name="Search",
        func=search.run,
        description="Useful when you need to answer questions about current events. You should ask targeted questions."
    ),
    Tool(
        name="Calculator",
        func=llm_math_chain.run,
        description="Useful when you need to answer questions about math."
    ),
    Tool(
        name="FooBar-DB",
        func=db_chain.run,
        description="Useful when you need to answer questions about FooBar. Input should be in the form of a question containing full context."
    )
]
from langchain.prompts import MessagesPlaceholder
from langchain.memory import ConversationBufferMemory
agent_kwargs = {
    "extra_prompt_messages": [MessagesPlaceholder(variable_name="memory")],
}
memory = ConversationBufferMemory(memory_key="memory", return_messages=True)
agent = initialize_agent(
    tools, 
    llm, 
    agent=AgentType.OPENAI_FUNCTIONS, 
    verbose=True, 
    agent_kwargs=agent_kwargs, 
    memory=memory
)
mrkl.run(
    "Who is Leo DiCaprio's girlfriend? What is her current age raised to the 0.43 power?"
)

==============================
```
# Financial GPT
```JavaScript
<<< Financial GPT >>
conda create -n FinBot
conda install python==3.10.11 -n FinBot
git clone https://github.com/AI4Finance-Foundation/FinGPT.git
git clone https://github.com/AI4Finance-Foundation/FinNLP
git clone https://github.com/AI4Finance-Foundation/FinRL-Meta
pip install parsel PyPDF2 stockstats akshare
\#install ta-lib with special way
1. install dependency library
https://pypi.org/project/TA-Lib/
download http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-msvc.zip
Build TA-Lib Library
From Windows Start Menu, Start [VS2015 x64 Native Tools Command Prompt]
Move to C:\ta-lib\c\make\cdr\win32\msvc
Build the Library nmake
2. pip install ta-lib
3. install cuda torch so that gpu can be used
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
========================================================

git clone https://github.com/AI4Finance-Foundation/FinGPT.git
git clone https://github.com/AI4Finance-Foundation/FinNLP
git clone https://github.com/AI4Finance-Foundation/FinRL-Meta
#########################################
#引用自己库，指定路径
\#import sys
sys.path.append("../../../FinNLP")          # git clone https://github.com/AI4Finance-Foundation/FinNLP
sys.path.append("../../../FinRL-Meta/")     # git clone https://github.com/AI4Finance-Foundation/FinRL-Meta
#我们可以用这个来ignore warning
import warnings
warnings.filterwarnings("ignore")
#########################################
\#Get api key and secret key
1, https://app.alpaca.markets/paper/dashboard/overview
generate new api key
https://www.postman.com/alpacamarkets/workspace/alpaca-public-workspace/collection/********-b219fac1-0270-441b-b62c-5f4848233b8b
API KEY: PKO7LC21W27V4WJL2JXR
API Secret: kYcRrAvGnaezbWPK8X4c33VnJtaoyWq0U54P9RJc
Energencey Code: 366cb1c3-9e85-442d-a319-9f47e63b81ae for 2FA
2. pip install zipline
========================================================
Something fixed before able to run
finrl/meta/data_processors/processor_alpaca.py
from datetime import datetime, timedelta
# line 56th
day_delta = pd.Timedelta('1D').value
        if pd.Timedelta(time_interval).value < day_delta:
            NYSE_open_hour = "09:30"  # in NY
            NYSE_close_hour = "15:59"  # in NY
            data_df = barset.between_time(NYSE_open_hour, NYSE_close_hour)
        else:
            data_df = barset
# line 302
def get_trading_days(self, start, end):
        nyse = tc.get_calendar("NYSE")
        start = pytz.UTC.localize(datetime.strptime(start, '%Y-%m-%d'))
        end = pytz.UTC.localize(datetime.strptime(end, '%Y-%m-%d'))
        df = nyse.sessions_in_range(start.date(), end.date())
        trading_days = []
        for day in df:
            trading_days.append(str(day)[:10])
        return trading_days

============================
https://www.marketaux.com/
API Token: f6ZA7bY7bCo0Iq0IScmJ9q5l5GdHU89rbt16qOzl
# get news and judge negation/positive
F:\ResearchDirection\AI\FinBot\FinRL\shares_news_sentiment_classify.py
Read following news and list 3 stocks that may be affected, briefly judge each one 'positive' or 'negative':
Could Insurance Reduce Gun Violence?,
Rent Guidelines Board approves 3% rate hike for one-year leases,
Australia’s Treasurer to announce next RBA governor in July,
AI =>
"text": "\n1. American Outdoor Brands Corporation (AOBC): Positive - Could benefit from increased insurance related to gun violence.\n\n2. Equity Residential (EQR): Negative - Will be affected by the 3% rate hike for one-year leases.\n\n3. Commonwealth Bank of Australia (CBA): Positive - Could benefit from increased confidence in the economy due to the announcement of the next RBA governor.",
\#Read News and get response
F:\ResearchDirection\AI\FinBot\FinGPT\fingpt\chatgpt-robo-advisor-v1\ChatGPT_Robo_Advisor.ipynb
from meta.data_processors.yahoofinance import Yahoofinance
from finnlp.data_sources.news.finnhub_date_range import Finnhub_Date_Range
from finnlp.large_language_models.openai.openai_chat_agent import Openai_Chat_Agent
news_downloader = Finnhub_Date_Range({"token":"Your Finnhub Token"})
news_downloader.download_date_range_stock(start_date = start_date,end_date = end_date, stock = "AAPL")
\#Here we can see what news and social media source we can get
F:\ResearchDirection\AI\FinBot\FinNLP\finnlp\data_sources\news
F:\ResearchDirection\AI\FinBot\FinNLP\finnlp\data_sources\social_media
\#Free Proxy List
F:\ResearchDirection\AI\FinBot\FinNLP\finnlp\utils\get_proxy.py
\#Data Source
https://www.globaldata.com/company-profile/twitter-inc/
https://www.macrotrends.net/stocks/stock-screener
https://data.nasdaq.com/data/LBMA/GOLD-gold-price-london-fixing
https://app.alpaca.markets/brokerage/new-account
https://rapidapi.com/apidojo/api/morning-star/pricing
https://pypi.org/project/wrds/
https://wrds-www.wharton.upenn.edu/login/?next=/pages/support/programming-wrds/programming-python/querying-wrds-data-python/
YahooFinance
https://finazon.io/dataset/sip_non_pro?ref=JDSrs8W
morningstar
-- get finanacial data from gov
https://data.sec.gov/submissions/CIK##########.json
https://data.sec.gov/api/xbrl/companyconcept/CIK##########/us-gaap/AccountsPayableCurrent.json
https://data.sec.gov/api/xbrl/companyfacts/CIK##########.json
https://data.sec.gov/api/xbrl/frames/us-gaap/AccountsPayableCurrent/USD/CY2019Q1I.json
https://data.sec.gov/submissions/CIK0000320193.json
https://data.sec.gov/api/xbrl/companyconcept/CIK0000320193/us-gaap/AccountsPayableCurrent.json
https://data.sec.gov/api/xbrl/companyfacts/CIK0000320193.json
https://data.sec.gov/api/xbrl/frames/us-gaap/AccountsPayableCurrent/USD/CY2024Q1I.json
https://www.sec.gov/Archives/edgar/data/1045810
# Economics Calendar
https://site.financialmodelingprep.com/developer/docs/economic-calendar-api
https://developer.dowjones.com/site/docs/newswires_apis/dow_jones_calendar_live_api/index.gsp
\#To be researching
--- Get data from website and analyze
research code
/opt/workspace/researcher/labTest/AllKindsTest.ipynb
https://stockradars.co/
https://www.sec.gov/dera/data/financial-statement-data-sets
https://www.sec.gov/edgar/sec-api-documentation
https://catalog.data.gov/dataset/
https://zhuanlan.zhihu.com/p/********
\#News
https://finnhub.io/dashboard
Reuters
\#Social media
reddit
twitter

7：31 download completed -> 7：:56 Cleanup -> TechIndicator 8:00 -> VIx 8:07
8:14 -> 8:39
1. 15min model training and do trading
2. 1min Model training and do trading
```
#   
metagpt  
```JavaScript
\#install metagpt
https://github.com/geekan/MetaGPT
conda create -n metagpt
conda activate metagpt
sudo npm install -g @mermaid-js/mermaid-cli
conda install python==3.11.2
pip install numpy==1.24.3
pip install Cython
pip install .
python setup.py install

OPENAI_API_KEY
***************************************************

OPENAI_API_KEY: "***************************************************"
OPENAI_API_MODEL: "gpt-4"
MAX_TOKENS: 1500
RPM: 10
SERPAPI_API_KEY: "3a3d4334ed51f17a8dbb50f7d7feaad14a88ff74e202bac42c2dffc93b26f445"
GOOGLE_API_KEY: "AIzaSyB1RJwM8-r_ozVBhnqRz7LXYmtA_QyE5eo"
GOOGLE_CSE_ID: "5531449e036a443c3"
SERPER_API_KEY: "e8d813d64fd9c79f007c9c0e74c98d8d5ac1d559"

```
## Some Keys required
```JavaScript
# get HF hub API Key
https://huggingface.co/docs/api-inference/quicktour\#get-your-api-token
langchain
*************************************
\#find model and usage
https://huggingface.co/tasks
\#autotrain
https://huggingface.co/docs/autotrain/getting_started
\#To use .env  loadenv file
pip install python-dotenv
https://thinkinfi.com/huggingface-download-and-save-model-to-custom-path/
os.environ["TFHUB_CACHE_DIR"] = 'D:/tfhub_cache'
# Download model to custom cache directory
model = BertForQuestionAnswering.from_pretrained('bert-large-uncased-whole-word-masking-finetuned-squad', cache_dir="D:/Question_Answering/huggingface_cache/")
tokenizer = BertTokenizer.from_pretrained('bert-large-uncased-whole-word-masking-finetuned-squad', cache_dir="D:/Question_Answering/huggingface_cache/")
```
  
# elevenlabs
```JavaScript
import openai # pip install openai # openai.api_key "<openai-api-key>"
import elevenlabs # pip install elevenlabs # elevenlabs.set_api_key("<eleven-api-key>")
def write(prompt: str):
    for chunk in openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=[{ "role": "user", "content": prompt }],
        stream=True,
        if (text_chunk := chunk["choices"][0]["delta"].get("content")) is not None:
            yield text_chunk
text_stream = write("A one-sentence relaxing speech.")
audio_stream = elevenlabs.generate(
    text=text_stream,
    voice="Nicole",
    stream=True,
)
output = elevenlabs.stream(audio_stream)
```
  
# LangSmith
The best way to analysis pdf file data so far with langSmith
```JavaScript
# create api key in langsmith
ls__4d241d2c1d9442779ef86a07d6b4358d
export LANGCHAIN_TRACING_V2=true
export LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"
export LANGCHAIN_API_KEY="ls__4d241d2c1d9442779ef86a07d6b4358d"
export LANGCHAIN_PROJECT="pj-jasonpdf"
from dotenv import load_dotenv
from langchain.chat_models import ChatOpenAI
from langchain.prompts.chat import (
    ChatPromptTemplate,
    SystemMessagePromptTemplate,
    AIMessagePromptTemplate,
    HumanMessagePromptTemplate,
)
from langchain.schema import AIMessage, HumanMessage, SystemMessage
load_dotenv()
chat = ChatOpenAI()
messages = [
    SystemMessage(
        content="You are a helpful assistant that translates English to French."
    ),
    HumanMessage(
        content="Translate this sentence from English to French. I love programming."
    ),
]
chat(messages)
```
  
  
# AUTOGEN Made by Microsoft
  

> [!info] AutoGen Studio 2.0: AI Development UI  
> If you’ve been captivated by the promise of AutoGen but found yourself hesitant due to its seemingly complex setup, you’re not alone.  
> [https://autogen-studio.com/autogen-studio-ui](https://autogen-studio.com/autogen-studio-ui)  
```JavaScript
https://microsoft.github.io/autogen/
/opt/workspace/researcher/mirror.py
/opt/workspace/researcher/AutoGenExampleTest.ipynb
pip install pyautogen
!!! Make qwen also can run it.
# you need to make cert key properly got installed and pls refer to MeetingMom Tool notes for details
G:\ResearchDirection\AI\langchain\echohive\autogen\OAI_CONFIG_LIST.json
[
    {
        "model": "gpt-4",
        "api_key": "***************************************************"
    },
    {
        "model": "qwen",
        "api_key": "none",
        "api_base": "https://************:8002/v1",
        "max_tokens": 2048
    }
]
# open ai related call is inside this file
G:\ResearchDirection\AI\miniconda3\envs\langchain\Lib\site-packages\autogen\oai\completion.py
\#187 line
ADD: openai.api_base = config.pop("api_base", openai.api_base)
\#1005 line
if ("usage" not in response) or (response["usage"] is None):
            return 0
\#108 line
retry_wait_time= 60

G:\ResearchDirection\AI\miniconda3\envs\langchain\Lib\site-packages\autogen\agentchat\conversable_agent.py
\#387
print(colored(sender.name, "yellow"), "====> ", f"{self.name}:\n", flush=True)
\#403 line
if "function_call" in message and message['function_call'] is not None:
\#653 line 
if "function_call" in message and message["function_call"] is not None:
\#874
execute_code_blocks
\#708
no_human_input_msg = "----------------------------------"
print(colored(f"\n{no_human_input_msg}", "red"), flush=True)
\#727
print(colored("\n@@@@@ Thinking how to reply ....", "yellow"), flush=True)
# print(colored("\n>>>>>>>> USING AUTO REPLY...", "red"), flush=True)
\#885
f"\n@@@@@@ EXECUTING CODE BLOCK {i} (inferred language is {lang})...",
\#977
colored(f"\n@@@@@ AI is using {func_name}... @@@@", "magenta"),
# colored(f"\n>>>>>>>> EXECUTING FUNCTION {func_name}...", "magenta"),

@###################################################
Server side add able to handle system role
#/opt/workspace/mom/openai_api.py
\#279
elif role == "system":
            messages.append(
                ChatMessage(role="system", content=content.lstrip("\n").rstrip())
            )
#对于对话里夹杂system role也进行了处理
else:
            raise HTTPException(
                status_code=400, detail=f"Invalid request: Incorrect role {role}."
            )
===================================
    # 预处理前的初始化
    processed_messages = []
    # 遍历原始的messages列表
    i = 0
    while i < len(messages):
        content = messages[i].content.lstrip("\n").rstrip()
        if messages[i].role == "system":
            system += content + "\n"
        else:
            processed_messages.append(messages[i])
        i += 1
    # 将预处理后的列表赋值回messages
    messages = processed_messages
===================================    
    query = _TEXT_COMPLETION_CMD
    if messages[-1].role == "user":
        query = messages[-1].content
        messages = messages[:-1]
==============================================================================
from autogen import AssistantAgent, UserProxyAgent, config_list_from_json, config_list_from_models
# Import the openai api key
config_list = config_list_from_json(
    env_or_file="OAI_CONFIG_LIST",
    file_location=f"G:\ResearchDirection\AI\langchain\AIJason\microsoft-autogen-experiments",
    filter_dict={
        "model": ["qwen"],
    },
)
# Create assistant agent
if config_list[0]['model'] == "qwen":    
    assistant = AssistantAgent(name="assistant", 
                           system_message="Must put code into code block. ",
                           llm_config={"config_list": config_list})
else:
    assistant = AssistantAgent(name="assistant", 
                           llm_config={"config_list": config_list})
# Create user proxy agent
user_proxy = UserProxyAgent(
    name="user_proxy",
    # system_message="You must add 'TERMINATE' in the end when everything is done. ",
    code_execution_config={"work_dir": "coding"})
# Start the conversation
user_proxy.initiate_chat(
    assistant, message="Plot a chart of NVDA, AAPL and TESLA stock price change YTD.", clear_history=True)
---
def generate_llm_config(tool):
    # Define the function schema based on the tool's args_schema
    function_schema = {
        "name": tool.name.lower().replace (' ', '_'),
        "description": tool.description,
        "parameters": {
            "type": "object",
            "properties": {},
            "required": [],
        },
    }
    if tool.args is not None:
      function_schema["parameters"]["properties"] = tool.args
    return function_schema
# Instantiate the ReadFileTool
read_file_tool = ReadFileTool()
custom_tool = CircumferenceTool()
# Construct the llm_config
llm_config = {
  \#Generate functions config for the Tool
  "functions":[
      generate_llm_config(custom_tool), # ERROR: this one fails becuase - Invalid schema for function 'circumference_calculator': 'float' is not valid under any of the given schemas
      generate_llm_config(read_file_tool),
  ],
  "config_list": config_list,  # Assuming you have this defined elsewhere
  "request_timeout": 120,
}
from langchain.agents.agent_toolkits import SparkSQLToolkit
toolkit = SparkSQLToolkit(db=spark_sql, llm=llm)22
tools = []
function_map = {}
for tool in toolkit.get_tools():
    tool_schema = generate_llm_config(tool)
    print(tool_schema)
    tools.append(tool_schema)
    function_map[tool.name] = tool._run
# Construct the llm_config
llm_config = {
  \#Generate functions config for the Tool
  "functions": tools,
  "config_list": config_list,  # Assuming you have this defined elsewhere
  "request_timeout": 120,
}
# Register the tool and start the conversation
user_proxy.register_function(
    function_map = function_map
)

===============================================================================
**config - Configuration for the openai API call. This is used as parameters for calling openai API. 
The "prompt" or "messages" parameter can contain a template (str or Callable) which will be 
instantiated with the context. Besides the parameters for the openai API call, it can also contain:
max_retry_period (int): the total time (in seconds) allowed for retrying failed requests.
retry_wait_time (int): the time interval to wait (in seconds) before retrying a failed request.
seed (int) for the cache. This is useful when implementing "controlled randomness" for the completion.

Something Note:
1. continuously call functions according to the situation until it thinks it has solved it. So far, only GPT-4 can do
2. the best method is not to set a global OpenAI API Base, in the setting file, set the API Base for GPT4, 
   When you want to call the OpenAI API or langchain ChatOpenAI,
    openai.api_base = "https://************:8002/v1"
    llm = ChatOpenAI(temperature=0, model_name="qwen")
3. There is cache under blew folder and you can cleanup all subfolders to make sure not using cache
G:\ResearchDirection\AI\langchain\.cache
4. reviewer and interpreter , we have to use gpt4 for better result
5. try to use local ai but find only summary part we can use. Others local AI can't select play roler will.
6. If use docker, hter is the way to specify python version
code_execution_config={"work_dir": "planning", "use_docker": "python:3.11"},
7. 最后让脚色自己在chat message提到下一个谁去处理比如“@Writer，pls go ahead”,或者计数器“Add round number at the beginning of message”

=====================================================
Updated at 2024/04/10
/opt/workspace/researcher/jason/self-learning-agent/self-learning-agent.ipynb
https://microsoft.github.io/autogen/docs/tutorial/tool-use/
https://microsoft.github.io/autogen/docs/tutorial/conversation-patterns
New ConversableAgent and registerfunction ,  定义chatflow
Group Chat in a Sequential Chat
import os
from autogen import ConversableAgent
student_agent = ConversableAgent(
    name="Student_Agent",
    system_message="You are a student willing to learn.",
    llm_config={"config_list": [{"model": "gpt-4", "api_key": os.environ["OPENAI_API_KEY"]}]},
)
teacher_agent = ConversableAgent(
    name="Teacher_Agent",
    system_message="You are a math teacher.",
    llm_config={"config_list": [{"model": "gpt-4", "api_key": os.environ["OPENAI_API_KEY"]}]},
)
chat_result = student_agent.initiate_chat(
    teacher_agent,
    message="What is triangle inequality?",
    summary_method="reflection_with_llm",
    max_turns=2,
)

chat_results = number_agent.initiate_chats(
    [
        {
            "recipient": adder_agent,
            "message": "14",
            "max_turns": 2,
            "summary_method": "last_msg",
        },
        {
            "recipient": multiplier_agent,
            "message": "These are my numbers",
            "max_turns": 2,
            "summary_method": "last_msg",
        },
        {
            "recipient": subtracter_agent,
            "message": "These are my numbers",
            "max_turns": 2,
            "summary_method": "last_msg",
        },
        {
            "recipient": divider_agent,
            "message": "These are my numbers",
            "max_turns": 2,
            "summary_method": "last_msg",
        },
    ]
)
-----------------------------------------------------------------------------------------
from tools import generate_img, fix_hands, upscale_img, review_img 
import os
from dotenv import load_dotenv
from autogen import ConversableAgent
from autogen import register_function
from autogen import GroupChat, GroupChatManager
load_dotenv()
create_agents
img_generator = ConversableAgent(
    name="Image_Generator",
    system_message="""
You are a world class AI image prompt engineer, you will be given an image of a cloth, and text prompt (either original prompt or the info given); with the info given, you will use generate_img to generate a realistic photo of a person wearing specific cloth.
You will continously iterate img based on feedback from img_reviewer until the img_reviewer is satisfied with the result
""",
    llm_config={
        "config_list": [
            {
                "model": "gpt-4-turbo-preview",
                "api_key": os.environ["OPENAI_API_KEY"],
            },
        ],
        "timeout": 120,
    }
)
img_reviewer = ConversableAgent(
    name="Image_Reviewer",
    system_message="""
You will review both original cloth img as well as img generated by the img_generator based on img url; 
classify if the cloth in image from img_generator is 95% match with the original cloth img
if more than 95% match, just return "95% match", if not 95% match, list out the discrepancy,
and generate an iterated text prompt for the img_generator model to fill the gap of discrepancy
""",
    llm_config={
        "config_list": [
            {
                "model": "gpt-4-turbo-preview",
                "api_key": os.environ["OPENAI_API_KEY"],
            }
        ],
        "timeout": 120,
    },
    is_termination_msg=lambda msg: "95% match" in msg["content"].lower(),
)
img_finetunner = ConversableAgent(
    name="Image_Finetunner", 
    system_message="""
turn the final img generated & approved by img_generator & img_reviewer into a high quality image,
fix hand distortion & upscale the final image
after image upscaled, return "TERMINATE" to end the conversation  
""",
    llm_config={
        "config_list": [
            {
                "model": "gpt-4-turbo-preview",
                "api_key": os.environ["OPENAI_API_KEY"],  
            }
        ],
        "timeout": 120,
    },
    is_termination_msg=lambda msg: "terminate" in msg["content"].lower(),
)
# The user proxy agent is used for interacting with the assistant agent
# and executes tool calls.
user_proxy = ConversableAgent(
   name="User", 
   llm_config={
       "config_list": [
           {
               "model": "gpt-4-turbo-preview",
               "api_key": os.environ["OPENAI_API_KEY"],
           }
       ],
   },
   is_termination_msg=lambda msg: msg.get("content") is not None
       and "TERMINATE" in msg["content"],
   human_input_mode="NEVER",
)
\#register function
register_function(
   generate_img,
   caller=img_generator,
   executor=user_proxy,
   name="generate_img",
   description="Generate image with ai model based on original cloth img & text prompt",
)
register_function(
   review_img,
   caller=img_reviewer,
   executor=user_proxy,
   name="review_img",
   description="compare & review images",
)
register_function(
   upscale_img,
   caller=img_finetuner,
   executor=user_proxy,
   name="upscale_img",
   description="upscale image to make it high quality",
)
# create & trigger group chat
group_chat = GroupChat(
   agents=[img_generator, img_reviewer, user_proxy],
   message="",
   max_rounds=7,
)
group_chat_manager = GroupChatManager(
   groupchat=group_chat,
   llm_config={
       "config_list": [{"model": "gpt-4", "api_key": os.environ["OPENAI_API_KEY"]}]
   }
)
cloth_img = "https://media.istockphoto.com/id/830738810/photo/blue-baby-coat-child-fashion-wear-isolated-nobody.jpg?s=612x612&w=0&k=20&c="
base_prompt = "woman with blue hair, wear blue cloth, in a cafe in paris"
chat_result = user_proxy.initiate_chats(
[
   {
       "recipient": group_chat_manager,
       "message": f"""
           *original_cloth_image: {cloth_img}
           *base_prompt*: {base_prompt}
           ----
           Generate a realistic photo based on the original cloth img & base prompt above that pass 100% match from img_reviewer.
           """,
       "summary_method": "reflection_with_llm", \#reflection_with_llm or last_msg
       "summary_args": {
           "summary_prompt": "Including the latest latest_ai_img_url, as well as original_cloth_img, latest_prompt"
       },
   },
   {
       "recipient": img_finetuner,
       "message": "Finetune the latest ai generated img, fixing hand distortion if there is any, and then upscale the image that returned from fix_hand to make it high quality",
   },
]
)
return user_proxy.last_message()["content"]
```
[[Generate “Verified” Python Code Using AutoGen Conversable Agents]]  
  
[https://learn.deeplearning.ai/courses/ai-agentic-design-patterns-with-autogen/lesson/5/tool-use-and-conversational-chess](https://learn.deeplearning.ai/courses/ai-agentic-design-patterns-with-autogen/lesson/5/tool-use-and-conversational-chess)
# Gemini
```Python
/opt/workspace/researcher/geminiTest.ipynb

import openai
# Replace "YOUR_ANYSCALE_ENDPOINT_API_KEY" with your Anyscale endpoint API key
client = openai.OpenAI(api_key="YOUR_ANYSCALE_ENDPOINT_API_KEY", base_url="https://api.endpoints.anyscale.com/v1")
# Set the model and temperature for chat completion
model = "mistralai/Mixtral-8x7B-Instruct-v0.1"
temperature = 0.7
# Create the chat completion request
chat_completion = client.chat.completions.create(
    model=model,
    temperature=temperature,
    messages=[
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "How are you?"},
    ],
)
# Print the chat completion response
print(chat_completion)
```
  
# crewAI
```JavaScript
https://github.com/joaomdmoura/crewAI
Similar like microsoft AutoGent
```
[[DLAI - Multi AI Agent Systems with crewAI]]
# GPT4 New Feature
```Python
/opt/workspace/researcher/gpt4_newfeature.ipynb
```
  
# Nomic Embedding
```LaTeX
\#get api key
https://atlas.nomic.ai/data/jbsheng/org/keys
NOMIC_API_KEY=nk-TsArY8F32NocDZRJmFVdqrCkCke0C5zATUVk8vS2yG4
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
# Path to your local .txt file
file_path = '/opt/workspace/researcher/nomic_embedding/santiquanji_liucixin_simple.txt'
# Use the LocalFileLoader to load the document into the docs variable
docs = TextLoader(file_path=file_path).load()
text_splitter = RecursiveCharacterTextSplitter(chunk_size=7500, chunk_overlap=100)
doc_splits = text_splitter.split_documents(docs)
import os
# from google.colab import userdata
os.environ['NOMIC_API_KEY'] = 'nk-TsArY8F32NocDZRJmFVdqrCkCke0C5zATUVk8vS2yG4'
from langchain_community.vectorstores import Chroma
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableLambda, RunnablePassthrough
from langchain_nomic import NomicEmbeddings
from langchain_nomic.embeddings import NomicEmbeddings
vectorstore = Chroma.from_documents(
    documents=doc_splits,
    embedding=NomicEmbeddings(model="nomic-embed-text-v1"),
)
retriever = vectorstore.as_retriever()
os.environ['OPENAI_API_KEY'] = "***************************************************"

from langchain_community.chat_models import ChatOllama
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
# Prompt
template = """please answer the question in Chinese based only on the following context:
{context}
Question: {question}
"""
prompt = ChatPromptTemplate.from_template(template)
chain = (
    {"context": retriever, "question": RunnablePassthrough()}
    | prompt
    | ChatOpenAI(temperature=0, model="gpt-4-1106-preview")
    | StrOutputParser()
)
response = chain.invoke("找到有关大兴安岭的描述?")
'大兴安岭的描述如下：
两年以后，大兴安岭。
“顺山倒咧——”
随着这声嘹亮的号子，一棵如巴特农神庙的巨柱般高大的落叶松轰然倒下，叶文洁感到大地抖动了一下。她拿起斧头和短锯，
开始去除巨大树身上的枝丫。每到这时，她总觉得自己是在为一个巨人整理遗体。她甚至常常有这样的想象：
这巨人就是自己的父亲。两年前那个凄惨的夜晚，她在太平间为父亲整理遗容时的感觉就在这时重现。巨松上那绽开的树皮，
似乎就是父亲躯体上累累的伤痕。\n\n
内蒙古生产建设兵团的六个师四十一个团十多万人就分布在这辽阔的森林和草原之间。刚从城市来到这陌生的世界时，
很多兵团知青都怀着一个浪漫的期望：当苏修帝国主义的坦克集群越过中蒙边境时，他们将飞快地武装起来，
用自己的血肉构成共和国的第一道屏障。事实上，这也确实是兵团组建时的战略考虑之一。但他们渴望的战争就像草原天边那跑死马的远山，
清晰可见，但到不了眼前，于是他们只有垦荒、放牧和砍伐。这些曾在“大串联”中燃烧青春的年轻人很快发现，
与这广阔天地相比，内地最大的城市不过是个羊圈；在这寒冷无际的草原和森林间，燃烧是无意义的，一腔热血喷出来，
比一堆牛粪凉得更快，还不如后者有使用价值。但燃烧是他们的命运，他们是燃烧的一代。
于是，在他们的油锯和电锯下，大片的林海化为荒山秃岭；在他们的拖拉机和康拜因（联合收割机）下，大片的草原被犁成粮田，
然后变成沙漠。\n\n
叶文洁看到的砍伐只能用疯狂来形容，高大挺拔的兴安岭落叶松、四季常青的樟子松、亭亭玉立的白桦、耸入云天的山杨、西伯利亚冷杉，
以及黑桦、柞树、山榆、水曲柳、钻天柳、蒙古栎，见什么伐什么，几百把油锯如同一群钢铁蝗虫，她的连队所过之处，只剩下一片树桩。'
```
  
# Claude Function Call
```Python
/opt/workspace/researcher/anthropic-cookbook/tool_use/calculator_tool.ipynb
MODEL_NAME = "claude-3-sonnet-20240229"
ANTHROPIC_API_KEY="************************************************************************************************************"
```
## Claude call for long output message
```Python
client = Anthropic()
MODEL_NAME = "claude-3-5-sonnet-20240620"
def extract_info(image_path, haiku_prompt):
    # 读取 JPG 图片并转换为 PNG
    with Image.open(image_path) as img:
        png_buffer = io.BytesIO()
        img.save(png_buffer, format='PNG')
        png_buffer.seek(0)
        base64_encoded_png = base64.b64encode(png_buffer.getvalue()).decode("utf-8")
    
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "image", "source": {"type": "base64", "media_type": "image/png", "data": base64_encoded_png}},
                {"type": "text", "text": haiku_prompt}
            ]
        }
    ]
    
    response = client.messages.create(
        model=MODEL_NAME,
        max_tokens=8192,
        messages=messages,
        extra_headers={"anthropic-beta": "max-tokens-3-5-sonnet-2024-07-15"}
    )
    
    return response.content[0].text
```
  
# READER API (Similar like BROWSERLESS)
https://github.com/jina-ai/reader
## Browserless+BeautifulSoap方式
```Python
BROWSERLESS_API_KEY = "53dea3fc-fb83-495f-9f99-7efdbe7732cf"
def scrape(url: str) -> str:
    # scrape website, and also will summarize the content based on objective if the content is too large
    # objective is the original objective & task that user give to the agent, url is the url of the website to be scraped
    print(f"Scraping website...{url}")
    # Define the headers for the request
    headers = {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
    }
    # Define the data to be sent in the request
    data = {
        "url": url
    }
    # Convert Python object to JSON string
    data_json = json.dumps(data)
    # Send the POST request
    response = requests.post(
        "https://chrome.browserless.io/content?token=53dea3fc-fb83-495f-9f99-7efdbe7732cf", headers=headers, data=data_json)
    # Check the response status code
    if response.status_code == 200:
        soup = BeautifulSoup(response.content, "html.parser")
        text = soup.get_text()
        # print("CONTENTTTTTT:", text)
        if len(text) > 8000:
            output = summary(text)
            # output = summary_v2(text)
            logging.info(f"scrape function returned from {url} (summarized): {output}")
            return output
        else:
            logging.info(f"scrape function returned from {url}: {text}")
            return text
    else:
        error_message = f"HTTP request failed with status code {response.status_code}"
        logging.error(error_message)       
```
## 改写成Jina reader方式
```Python
import requests
import json
import logging
import json,os, requests, datetime
from langchain.chat_models import ChatOpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains.summarize import load_summarize_chain
from langchain import PromptTemplate
def summary(content):
    llm = ChatOpenAI(temperature=0, model="gpt-3.5-turbo-16k-0613")
    # openai.api_base = "https://************:8002/v1"
    # llm = ChatOpenAI(temperature=0, model_name="qwen")
    text_splitter = RecursiveCharacterTextSplitter(
        separators=["\n\n", "\n"], chunk_size=10000, chunk_overlap=500)
    docs = text_splitter.create_documents([content])
    map_prompt = """
    Write a detailed summary of the following text for a research purpose:
    "{text}"
    SUMMARY:
    """
    map_prompt_template = PromptTemplate(
        template=map_prompt, input_variables=["text"])
    summary_chain = load_summarize_chain(
        llm=llm,
        chain_type='map_reduce',
        map_prompt=map_prompt_template,
        combine_prompt=map_prompt_template,
        verbose=False
    )
    output = summary_chain.run(input_documents=docs,)
    return output
# Get today's date
today_date = datetime.datetime.now().strftime('%Y-%m-%d')
filename = f"/opt/workspace/researcher/logfile_{today_date}.log"
# Set up logging configuration
logging.basicConfig(filename=filename, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
def scrape(url: str) -> str:
    print(f"Scraping website...{url}")
    
    # Prepare the URL to use the Jina Reader API
    jina_url = f"https://r.jina.ai/{url}"
    
    try:
        # Send the GET request
        response = requests.get(jina_url)
        
        # Check the response status code
        if response.status_code == 200:
            # Assuming the API returns the processed text directly
            text = response.text
            # print("Scraped content:", text[:500])  # Preview first 500 characters
            
            if len(text) > 8000:
                output = summary(text)  # Assuming 'summary' is a defined function to summarize the text
                logging.info(f"Scrape function returned from {url} (summarized): {output[:500]}")  # Log preview
                return output
            else:
                logging.info(f"Scrape function returned from {url}: {text[:500]}")  # Log preview
                return text
        else:
            error_message = f"HTTP request failed with status code {response.status_code}"
            logging.error(error_message)
            return error_message
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}")
        return f"An error occurred: {str(e)}"

url_to_scrape = "https://github.com/jina-ai/reader"  # Specify the relative URL path
result = scrape(url_to_scrape)
print("Final scraped content:", result[:500])  # Display the final content, preview first 500 characters
```
# **DuckDuckGo Search API Test**
```YAML
https://blog.csdn.net/east196/article/details/136032358
/opt/workspace/researcher/AllKindsTest.ipynb
https://duckduckgo.com/duckduckgo-help-pages/settings/params/
import asyncio
from duckduckgo_search import AsyncDDGS
async def aget_results(word):
    results = await AsyncDDGS(proxy=None).atext(word, max_results=100)
    # results = await AsyncDDGS().atext('', region='cn-zh', safesearch='off', timelimit='d', max_results=5)
    return results
def format_result(result, index):
    return f"{index}. Title: {result['title']}\n   URL: {result['href']}\n   Snippet: {result['body']}\n"
async def main():
    words = ["sun", "earth", "moon"]
    tasks = [aget_results(w) for w in words]
    results = await asyncio.gather(*tasks)
    for i, word_results in enumerate(results):
        print(f"\n{words[i].capitalize()}")
        print("-----------------")
        for j, result in enumerate(word_results[:3], 1):  # Limit to first 3 results
            print(format_result(result, j))
        print(f"(... {len(word_results) - 3} additional results omitted for brevity ...)")
    
await main()
# async
results = await AsyncDDGS().achat('describe the characteristic habits and behaviors of humans as a species', model="gpt-4o-mini")
results = await AsyncDDGS().anews('最近日经的股灾', region='cn-zh', safesearch='off', timelimit='d', max_results=10)
from IPython.display import Image, display, Audio, Markdown, clear_output
USER_QUESTION = "pls give me a detail summary in chinese according to the search results" 
ANSWER_INPUT = f"""
Generate an answer to the user's question based on the given search results. 
TOP_RESULTS: {results}
USER_QUESTION: {USER_QUESTION}
Include as much information as possible in the answer. Reference the relevant search result urls as markdown links.
"""
completion = client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[{"role": "user", "content": ANSWER_INPUT}],
    temperature=0.5,
    stream=True,
)
text = ""
for chunk in completion:
    if chunk.choices[0].delta.content is not None:
        text += chunk.choices[0].delta.content
    clear_output(wait=True)
    display(Markdown(text))
```