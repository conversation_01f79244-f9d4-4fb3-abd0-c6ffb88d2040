---
Updated: 2024-08-23T12:08
tags:
  - AI->-Business
  - AI->-RAG
URL: https://github.com/D-Star-AI/dsRAG
Created: 2024-07-31T15:15
---
研究结果
安装
初始化
导入文件并且建库
看一下chunk数据
准备rerank和计算相似度
用LLM回答用户
我们这里用到了ollama llama3.1
一些数据验证
dsRAG
Semantic sectioning 语义分割
AutoContext 自动上下文
Relevant Segment Extraction
Tutorial 教程
Installation 安装
Quickstart 快速入门
Basic customization 基本定制
Architecture 建筑学
KnowledgeBase object 知识库对象
Components 组件
VectorDB 矢量数据库
ChunkDB
Embedding 嵌入
Reranker
LLM
Config dictionaries 配置字典
Document upload flow 文件上传流程
Query flow 查询流程
Community and support 社区和支持
# 研究结果
## 安装
```Python
/opt/workspace/dsRAG/examples/dsRAG_motivation.ipynb
conda activate MinerU
pip install dsrag
https://dashboard.cohere.com/
Cohere API Key: 4mhop6zSOwC6kbPfHwKUF5M6bUepPdsH2l21uGgN
```
## 初始化
  
```Python
import sys
sys.path.append("../")
from dsrag.knowledge_base import KnowledgeBase
from dsrag.document_parsing import extract_text_from_pdf
from dsrag.rse import get_best_segments
import cohere
import os
from scipy.stats import beta
import numpy as np
import matplotlib.pyplot as plt
# set API keys
import os
from dsrag.llm import OllamaAPI,AnthropicChatAPI, OpenAIChatAPI
from dsrag.embedding import CohereEmbedding, OpenAIEmbedding, OllamaEmbedding
from dsrag.reranker import CohereReranker, NoReranker
import ollama
os.environ["OPENAI_API_KEY"] = "***************************************************"
os.environ["CO_API_KEY"] = "4mhop6zSOwC6kbPfHwKUF5M6bUepPdsH2l21uGgN"
ollamaclient = ollama.Client(host = "http://************:8234")
local_llm = OllamaAPI(model="llama3.1", temperature=0.2, max_tokens=8192, client=ollamaclient)
remote_llm = OpenAIChatAPI(model="gpt-4o-mini")
```
## 导入文件并且建库
```Python
# load in some data
\#file_path = "../tests/data/levels_of_agi.pdf"
file_path = "../tests/data/JuneyaoAirlines.pdf_all.txt"
doc_id = os.path.basename(file_path).split(".")[0] # grab the file name without the extension so we can use it as the doc_id
# kb_id = "levels_of_agi"
kb_id = "JuneyaoAirlines"
if file_path.endswith(".pdf"):
    document_text = extract_text_from_pdf(file_path)
else:
    with open(file_path, "r") as f:
        document_text = f.read()
print (document_text[:1000])
kb = KnowledgeBase(kb_id=kb_id, exists_ok=False, storage_directory='poctest', language='zh', auto_context_model=local_llm)
kb.add_document(doc_id=doc_id, text=document_text)
```
### 看一下chunk数据
```Python
# load in chunks
kb = KnowledgeBase(kb_id=kb_id, exists_ok=True, storage_directory='poctest')
num_chunks = len(kb.chunk_db.data[doc_id])
print (num_chunks)
chunks = []
for i in range(num_chunks):
    chunk = {
        "section_title": kb.chunk_db.get_section_title(doc_id, i),
        "chunk_text": kb.chunk_db.get_chunk_text(doc_id, i),
    }
    chunks.append(chunk)
chunks[0]
# print all section titles
unique_section_titles = []
for i in range(num_chunks):
    section_title = chunks[i]["section_title"]
    if section_title not in unique_section_titles:
        print (section_title)
        unique_section_titles.append(section_title)
```
## 准备rerank和计算相似度
```Python
def transform(x):
    """
    Transformation function to map the absolute relevance value to a value that is more uniformly distributed between 0 and 1
    - This is critical for the new version of RSE to work properly, because it utilizes the absolute relevance values to calculate the similarity scores
    - The relevance values given by the Cohere reranker tend to be very close to 0 or 1. This beta function used here helps to spread out the values more uniformly.
    """
    a, b = 0.4, 0.4  # These can be adjusted to change the distribution shape
    return beta.cdf(x, a, b)
def rerank_documents(query: str, documents: list) -> list:
    """
    Use Cohere Rerank API to rerank the search results
    """
    # model = "rerank-english-v3.0"
    model = "rerank-multilingual-v3.0"
    client = cohere.Client(api_key=os.environ["CO_API_KEY"])
    decay_rate = 30
    reranked_results = client.rerank(model=model, query=query, documents=documents)
    results = reranked_results.results
    reranked_indices = [result.index for result in results]
    reranked_similarity_scores = [result.relevance_score for result in results] # in order of reranked_indices
    
    # convert back to order of original documents and calculate the chunk values
    similarity_scores = [0] * len(documents)
    chunk_values = [0] * len(documents)
    for i, index in enumerate(reranked_indices):
        absolute_relevance_value = transform(reranked_similarity_scores[i])
        similarity_scores[index] = absolute_relevance_value
        v = np.exp(-i/decay_rate)*absolute_relevance_value # decay the relevance value based on the rank
        chunk_values[index] = v
    return similarity_scores, chunk_values
    
# run this chunk through the Cohere Rerank API with and without the context header
similarity_scores, chunk_values = rerank_documents(query, [documents[chunk_index_to_inspect], documents_no_context[chunk_index_to_inspect]])
print (f"Similarity with contextual chunk header: {similarity_scores[0]}")
print (f"Similarity without contextual chunk header: {similarity_scores[1]}")
# get max value and index id of the similarity scores
max_index = similarity_scores.index(max(similarity_scores))
max_value = max(similarity_scores)
print (f"Max value: {max_value} at index: {max_index}")

# be sure you're using the Nike 10-K KB for these next few cells, as we'll be focusing on a single example query for that document
# query = "Nike stock-based compensation expenses"
query = "公司负责人是谁？"
similarity_scores, chunk_values = rerank_documents(query, documents)
irrelevant_chunk_penalty = 0.1
all_relevance_values = [[v - irrelevant_chunk_penalty for v in chunk_values]]
document_splits = []
max_length = 30
overall_max_length = 50
minimum_value = 0.5
# get_best_segments solves a constrained version of the maximum sum subarray problem
best_segments, scores = get_best_segments(all_relevance_values, document_splits, max_length, overall_max_length, minimum_value)
print (best_segments)
print (scores)
print ()
# print the best segments
for segment_start, segment_end in best_segments:
    # concatenate the text of the chunks in the segment
    segment_text = f"[{document_context}]\n"
    for i in range(segment_start, segment_end):
        chunk_text = chunks[i]["chunk_text"]
        segment_text += chunk_text + "\n"
    print (segment_text)
    print ("\n---\n")
    
    
    
```
## 用LLM回答用户
### 我们这里用到了ollama llama3.1
[https://developers-blog.org/ollama-python-library-tutorial-with-examples/](https://developers-blog.org/ollama-python-library-tutorial-with-examples/)
```Python
from termcolor import colored
def generate_response(client, query, context):
    # Prepare the messages for the chat completion
    messages = [
        {"role": "system", "content": "You are a helpful assistant. Answer the query based on the provided context. in 100 words or less. unless user asks for more detail. Do not refer to any author but just combine the best pieces of the provided content to answer the query. Only answer based on the provided content."},
        {"role": "user", "content": f"Context:\n{context}\n\nQuery: {query}"}
    ]
    # Create a streaming chat completion
    stream = client.chat(
        model="gpt-4o",
        messages=messages,
        stream=True
    )
    # Print the response as it's generated
    for chunk in stream:
        if chunk['message']['content'] is not None:
            print(colored(chunk['message']['content'], "green"), end='', flush=True)
    print()
generate_response(ollamaclient, query, segment_text)
```
##   
一些数据验证  
```Python
# plot the relevance values of the best segment
best_segment_chunk_indexes = list(range(best_segments[0][0], best_segments[0][1]))
best_segment_chunk_values = chunk_values[best_segments[0][0]:best_segments[0][1]]
plt.figure(figsize=(12, 5))
plt.title(f"Relevance values of the best segment")
plt.ylim(0, 1)
plt.xlabel("Chunk index")
plt.ylabel("Query-chunk similarity")
plt.scatter(best_segment_chunk_indexes, best_segment_chunk_values)
# print the individual chunks in the best segment - annotated with their chunk indexes and relevance values
for chunk_index in best_segment_chunk_indexes:
    chunk_text = chunks[chunk_index]["chunk_text"]
    chunk_value = chunk_values[chunk_index]
    print (f"Chunk index: {chunk_index} - Relevance value: {chunk_value}")
    print (chunk_text)
    print ("\n---\n")
```
  
  
[![](https://opengraph.githubassets.com/96733f267b46fdf2356a34350551b61ac4ca85c28210c4f484087844d9561f13/D-Star-AI/dsRAG)](https://opengraph.githubassets.com/96733f267b46fdf2356a34350551b61ac4ca85c28210c4f484087844d9561f13/D-Star-AI/dsRAG)
# ==dsRAG==
[![](https://camo.githubusercontent.com/a7889ab037af32976b8f630a20a9a278c6dbf23bcb0ef8eaa359f089071b664e/68747470733a2f2f696d672e736869656c64732e696f2f646973636f72642f313233343632393238303735353837353838312e7376673f6c6162656c3d446973636f7264266c6f676f3d646973636f726426636f6c6f723d373238394441)](https://camo.githubusercontent.com/a7889ab037af32976b8f630a20a9a278c6dbf23bcb0ef8eaa359f089071b664e/68747470733a2f2f696d672e736869656c64732e696f2f646973636f72642f313233343632393238303735353837353838312e7376673f6c6162656c3d446973636f7264266c6f676f3d646973636f726426636f6c6f723d373238394441)
==dsRAG is a retrieval engine for unstructured data. It is especially good at handling challenging queries over dense text, like financial reports, legal documents, and academic papers. dsRAG achieves substantially higher accuracy than vanilla RAG baselines on complex open-book question answering tasks. On one especially challenging benchmark,== [==FinanceBench==](https://arxiv.org/abs/2311.11944)==, dsRAG gets accurate answers 83% of the time, compared to the vanilla RAG baseline which only gets 19% of questions correct.====  
  
====dsRAG 是一个非结构化数据检索引擎。在复杂的开卷问题解答任务中，dsRAG 的准确率大大高于 RAG 基线。在一个特别具有挑战性的基准==[==FinanceBench==](https://arxiv.org/abs/2311.11944)==上，dsRAG 在 83% 的时间内获得了准确的答案，相比之下，vanilla RAG 基线只能正确回答 19% 的问题。==
==There are three key methods used to improve performance over vanilla RAG systems:====  
  
====与普通 RAG 系统相比，有三种主要方法可用于提高性能：==
1. ==Semantic sectioning 语义分割==
2. ==AutoContext 自动上下文==
3. ==Relevant Segment Extraction (RSE)====  
      
    ====相关片段提取 (RSE)==
### ==Semantic sectioning 语义分割==
==Semantic sectioning uses an LLM to break a document into sections. It works by annotating the document with line numbers and then prompting an LLM to identify the starting and ending lines for each “semantically cohesive section.” These sections should be anywhere from a few paragraphs to a few pages long. The sections then get broken into smaller chunks if needed. The LLM is also prompted to generate descriptive titles for each section. These section titles get used in the contextual chunk headers created by AutoContext, which provides additional context to the ranking models (embeddings and reranker), enabling better retrieval.====  
  
====语义分节使用 LLM 将文档分成若干节。它的工作原理是在文档中标注行号，然后提示 LLM 以确定每个 "语义连贯的部分 "的起始行和终止行。这些部分的长度从几段到几页不等。如果需要，这些部分还可以分成更小的块。LLM 也会被提示为每个部分生成描述性标题。这些章节标题将用于 AutoContext 创建的上下文块标题中，从而为排序模型（嵌入和 reranker）提供额外的上下文，从而实现更好的检索。==
### ==AutoContext 自动上下文==
==AutoContext creates contextual chunk headers that contain document-level and section-level context, and prepends those chunk headers to the chunks prior to embedding them. This gives the embeddings a much more accurate and complete representation of the content and meaning of the text. In our testing, this feature leads to a dramatic improvement in retrieval quality. In addition to increasing the rate at which the correct information is retrieved, AutoContext also substantially reduces the rate at which irrelevant results show up in the search results. This reduces the rate at which the LLM misinterprets a piece of text in downstream chat and generation applications.====  
  
====AutoContext 可创建包含文档级和章节级上下文的上下文块标头，并在嵌入前将这些块标头预置到块中。这样，嵌入就能更准确、更完整地呈现文本的内容和含义。在我们的测试中，这一功能极大地提高了检索质量。除了提高正确信息的检索率，AutoContext 还大大降低了搜索结果中出现不相关结果的比率。这降低了 LLM 在下游聊天和生成应用中曲解文本的比率。==
### ==Relevant Segment Extraction==
==提取相关片段==
==Relevant Segment Extraction (RSE) is a query-time post-processing step that takes clusters of relevant chunks and intelligently combines them into longer sections of text that we call segments. These segments provide better context to the LLM than any individual chunk can. For simple factual questions, the answer is usually contained in a single chunk; but for more complex questions, the answer usually spans a longer section of text. The goal of RSE is to intelligently identify the section(s) of text that provide the most relevant information, without being constrained to fixed length chunks.====  
  
====相关片段提取 (RSE) 是一个查询时后处理步骤，它可以提取相关块群，并将它们智能地组合成较长的文本部分，我们称之为片段。这些片段为 LLM 提供了比任何单个块更好的上下文。对于简单的事实性问题，答案通常包含在一个单独的语块中；但对于更复杂的问题，答案通常跨越较长的文本部分。RSE 的目标是智能地识别提供最相关信息的文本部分，而不受限于固定长度的文本块。==
==For example, suppose you have a bunch of SEC filings in a knowledge base and you ask “What were Apple’s key financial results in the most recent fiscal year?” RSE will identify the most relevant segment as the entire “Consolidated Statement of Operations” section, which will be 5-10 chunks long. Whereas if you ask “Who is Apple’s CEO?” the most relevant segment will be identified as a single chunk that mentions “Tim Cook, CEO.”====  
  
====例如，假设您的知识库中有大量 SEC 文件，您会问 "苹果公司最近一个财年的主要财务业绩是什么？RSE 会将最相关的部分识别为整个 "合并运营报表 "部分，长度为 5-10 块。而如果您问 "谁是苹果公司的首席执行官？"，最相关的部分将被识别为提及 "蒂姆-库克，首席执行官 "的单个块。==
# ==Tutorial 教程==
### ==Installation 安装==
==To install the python package, run====  
  
====要安装 python 软件包，请运行==
==pip install dsrag==
### ==Quickstart 快速入门==
==By default, dsRAG uses OpenAI for embeddings and AutoContext, and Cohere for reranking, so to run the code below you'll need to make sure you have API keys for those providers set as environmental variables with the following names:== ==`OPENAI_API_KEY`== ==and== ==`CO_API_KEY`====.== ==**If you want to run dsRAG with different models, take a look at the "Basic customization" section below.**====  
  
====默认情况下，dsRAG 使用 OpenAI 进行嵌入和 AutoContext，使用 Cohere 进行重排，因此要运行下面的代码，您需要确保将这些提供商的 API 密钥设置为环境变量，名称如下：====`OPENAI_API_KEY`== ==和== ==`CO_API_KEY`====.====**如果您想使用不同的模型运行 dsRAG，请参阅下面的 "基本自定义 "部分。**==
==You can create a new KnowledgeBase directly from a file using the== ==`create_kb_from_file`== ==helper function:====  
  
====您可以使用== ==`create_kb_from_file`== ==辅助函数直接从文件创建新的知识库：==
==from dsrag.create_kb import create_kb_from_file  
file_path = "dsRAG/tests/data/levels_of_agi.pdf"  
kb_id = "levels_of_agi"  
kb = create_kb_from_file(kb_id, file_path)  
==
==KnowledgeBase objects persist to disk automatically, so you don't need to explicitly save it at this point.====  
  
====知识库对象会自动保存到磁盘上，因此此时无需明确保存。==
==Now you can load the KnowledgeBase by its== ==`kb_id`== ==(only necessary if you run this from a separate script) and query it using the== ==`query`== ==method:====  
  
====现在，您可以通过====`kb_id`====加载知识库（只有在从单独脚本运行时才需要），并使用====`query`====方法进行查询：==
==from dsrag.knowledge_base import KnowledgeBase  
kb = KnowledgeBase("levels_of_agi")  
search_queries = ["What are the levels of AGI?", "What is the highest level of AGI?"]  
results = kb.query(search_queries)  
for segment in results:  
print(segment)  
==
### ==Basic customization 基本定制==
==Now let's look at an example of how we can customize the configuration of a KnowledgeBase. In this case, we'll customize it so that it only uses OpenAI (useful if you don't have API keys for Anthropic and Cohere). To do so, we need to pass in a subclass of== ==`LLM`== ==and a subclass of== ==`Reranker`====. We'll use== ==`gpt-4o-mini`== ==for the LLM (this is what gets used for document and section summarization in AutoContext) and since OpenAI doesn't offer a reranker, we'll use the== ==`NoReranker`== ==class for that.====  
  
====现在我们来看一个例子，看看如何自定义知识库的配置。在本例中，我们将对其进行自定义，使其只使用 OpenAI（如果你没有 Anthropic 和 Cohere 的 API 密钥，这将非常有用）。为此，我们需要传递== ==`LLM`== ==的子类和== ==`Reranker`== ==的子类。我们将使用== ==`gpt-4o-mini`== ==来表示 LLM（这是在 AutoContext 中用于文档和章节摘要的内容），由于 OpenAI 不提供重排名器，因此我们将使用== ==`NoReranker`== ==类来表示。==
==from dsrag.llm import OpenAIChatAPI  
from dsrag.reranker import NoReranker  
llm = OpenAIChatAPI(model='gpt-4o-mini')  
reranker = NoReranker()  
kb = KnowledgeBase(kb_id="levels_of_agi", reranker=reranker, auto_context_model=llm)  
==
==Now we can add documents to this KnowledgeBase using the== ==`add_document`== ==method. Note that the== ==`add_document`== ==method takes in raw text, not files, so we'll have to extract the text from our file first. There are some utility functions for doing this in the== ==`document_parsing.py`== ==file.====  
  
====现在，我们可以使用== ==`add_document`== ==方法将文档添加到知识库中。请注意，====`add_document`== ==方法接收的是原始文本，而不是文件，因此我们必须先从文件中提取文本。在== ==`document_parsing.py`== ==文件中，有一些实用的函数可以实现这一点。==
==from dsrag.document_parsing import extract_text_from_pdf  
file_path = "dsRAG/tests/data/levels_of_agi.pdf"  
text = extract_text_from_pdf(file_path)  
kb.add_document(doc_id=file_path, text=text)  
==
# ==Architecture 建筑学==
## ==KnowledgeBase object 知识库对象==
==A KnowledgeBase object takes in documents (in the form of raw text) and does chunking and embedding on them, along with a few other preprocessing operations. Then at query time you feed in queries and it returns the most relevant segments of text.====  
  
====知识库对象接收文档（原始文本形式），并对其进行分块和嵌入，以及其他一些预处理操作。然后在查询时输入查询，它就会返回最相关的文本片段。==
==KnowledgeBase objects are persistent by default. The full configuration needed to reconstruct the object gets saved as a JSON file upon creation and updating.====  
  
====知识库对象默认情况下是持久的。在创建和更新时，重构对象所需的全部配置会被保存为一个 JSON 文件。==
## ==Components 组件==
==There are five key components that define the configuration of a KnowledgeBase, each of which are customizable:====  
  
====知识库的配置由五个关键部分组成，每个部分都可以定制：==
1. ==VectorDB 矢量数据库==
2. ==ChunkDB==
3. ==Embedding 嵌入==
4. ==Reranker==
5. ==LLM==
==There are defaults for each of these components, as well as alternative options included in the repo. You can also define fully custom components by subclassing the base classes and passing in an instance of that subclass to the KnowledgeBase constructor.====  
  
====每种组件都有默认设置，知识库中也包含其他选项。你还可以定义完全自定义的组件，方法是对基类进行子类化，并将该子类的实例传递给知识库的构造函数。==
### ==VectorDB 矢量数据库==
==The VectorDB component stores the embedding vectors, as well as a small amount of metadata.====  
  
====VectorDB 组件存储嵌入向量以及少量元数据。==
==The currently available options are:====  
  
====目前可用的选项有==
- ==`BasicVectorDB`==
- ==`WeaviateVectorDB`==
### ==ChunkDB==
==The ChunkDB stores the content of text chunks in a nested dictionary format, keyed on== ==`doc_id`== ==and== ==`chunk_index`====. This is used by RSE to retrieve the full text associated with specific chunks.====  
  
====ChunkDB 以嵌套字典格式存储文本块的内容，关键字为== ==`doc_id`== ==和== ==`chunk_index`== ==。RSE 使用它来检索与特定块相关的全文。==
==The currently available options are:====  
  
====目前可用的选项有==
- ==`BasicChunkDB`==
### ==Embedding 嵌入==
==The Embedding component defines the embedding model.====  
  
====嵌入组件定义了嵌入模型。==
==The currently available options are:====  
  
====目前可用的选项有==
- ==`OpenAIEmbedding`==
- ==`CohereEmbedding`==
- ==`VoyageAIEmbedding`==
- ==`OllamaEmbedding`==
### ==Reranker==
==The Reranker components define the reranker. This is used after the vector database search (and before RSE) to provide a more accurate ranking of chunks.====  
  
====排序器（Reranker）组件定义了排序器。它在矢量数据库搜索后（RSE 前）使用，以提供更准确的数据块排序。==
==The currently available options are:====  
  
====目前可用的选项有==
- ==`CohereReranker`==
- ==`VoyageReranker`==
### ==LLM==
==This defines the LLM to be used for document title generation, document summarization, and section summarization in AutoContext.====  
  
====这将定义用于 AutoContext 中文档标题生成、文档摘要和章节摘要的 LLM 。==
==The currently available options are:====  
  
====目前可用的选项有==
- ==`OpenAIChatAPI`==
- ==`AnthropicChatAPI 人类学聊天程序`==
- ==`OllamaChatAPI`==
## ==Config dictionaries 配置字典==
==There are two config dictionaries that can be passed in to== ==`add_document`== ==(====`auto_context_config`== ==and== ==`semantic_sectioning_config`====) and one that can be passed in to== ==`query`== ==(====`rse_params`====).====  
  
====有两个配置字典可以传入== ==`add_document`====（====`auto_context_config`== ==和== ==`semantic_sectioning_config`====）和一个可传入== ==`query`== ==的参数（====`rse_params`== ==）。==
==Default values will be used for any parameters not provided in these dictionaries, so if you just want to alter one or two parameters there's no need to send in the full dictionary.====  
  
====这些字典中未提供的任何参数都将使用默认值，因此如果您只想更改一两个参数，就无需发送完整的字典。==
==auto_context_config 自动上下文配置==
- ==use_generated_title: bool - whether to use an LLM-generated title if no title is provided (default is True)====  
      
    ====use_generated_title: bool - 如果没有提供标题，是否使用 LLM 生成的标题（默认为 True）。==
- ==document_title_guidance: str - guidance for generating the document title====  
      
    ====document_title_guidance: str - 生成文件标题的指南==
- ==get_document_summary: bool - whether to get a document summary (default is True)====  
      
    ====get_document_summary: bool - 是否获取文档摘要（默认为 True）==
- ==document_summarization_guidance: str==
- ==get_section_summaries: bool - whether to get section summaries (default is False)====  
      
    ====get_section_summaries: bool - 是否获取章节摘要（默认为 False）==
- ==section_summarization_guidance: str==
==semantic_sectioning_config====  
  
====语义分割配置==
- ==llm_provider: the LLM provider to use for semantic sectioning - only "openai" and "anthropic" are supported at the moment====  
      
    ====llm_provider：用于语义分节的 LLM 提供者 - 目前仅支持 "openai "和 "anthropic"。==
- ==model: the LLM model to use for semantic sectioning====  
      
    ====模型：用于语义分段的 LLM 模型==
- ==use_semantic_sectioning: if False, semantic sectioning will be skipped (default is True)====  
      
    ====use_semantic_sectioning：如果为 False，将跳过语义分割（默认为 True）。==
==rse_params 参数==
- ==max_length: maximum length of a segment, measured in number of chunks====  
      
    ====max_length：数据段的最大长度，以块数为单位==
- ==overall_max_length: maximum length of all segments combined, measured in number of chunks====  
      
    ====allall_max_length：所有分段合计的最大长度，以块数为单位==
- ==minimum_value: minimum value of a segment, measured in relevance value====  
      
    ====minimum_value：数据段的最小值，以相关性值衡量==
- ==irrelevant_chunk_penalty: float between 0 and 1====  
      
    ====irrelevant_chunk_penalty: 0 和 1 之间的浮点数==
- ==overall_max_length_extension: the maximum length of all segments combined will be increased by this amount for each additional query beyond the first====  
      
    ====overall_max_length_extension （最大总长度扩展）： 除第一个查询点外，每增加一个查询点，所有分段的最大总长度就会相应增加。==
- ==decay_rate 衰变率==
- ==top_k_for_document_selection: the number of documents to consider====  
      
    ====top_k_for_document_selection：要考虑的文件数量==
## ==Document upload flow 文件上传流程==
==Documents -> semantic sectioning -> AutoContext -> chunking -> embedding -> chunk and vector database upsert====  
  
====文件 -> 语义分段 -> 自动上下文 -> 块 -> 嵌入 -> 块和矢量数据库插入==
## ==Query flow 查询流程==
==Queries -> vector database search -> reranking -> RSE -> results====  
  
====查询 -> 向量数据库搜索 -> 重排 -> RSE -> 结果==
# ==Community and support 社区和支持==
==You can join our== [==Discord==](https://discord.gg/NTUVX9DmQ3) ==to ask questions, make suggestions, and discuss contributions.====  
  
====您可以加入我们的==[==讨论==](https://discord.gg/NTUVX9DmQ3)==，提出问题、建议和讨论贡献。==
==If you need additional support, we also offer consulting and custom development services. Reach out to== <EMAIL> ==for more information.====  
  
====如果您需要更多支持，我们还提供咨询和定制开发服务。如需了解更多信息，请联系== <EMAIL> ==。==