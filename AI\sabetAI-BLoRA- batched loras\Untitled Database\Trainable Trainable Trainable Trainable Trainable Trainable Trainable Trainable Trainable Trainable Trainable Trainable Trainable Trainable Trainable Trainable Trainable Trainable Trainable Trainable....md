---
Column 2: " But what if you wanted to inference all of your adapters at the same time? The LoRA operation is pretty simple! It creates an output of the same shape as the adapted layer, and then adds them together. That has got to be broadcastable, right?"
Column 3: " It is! If you have a matching number of LoRA adapters, you can fashion an operation to apply on each respective batch. Multiple models, that share the same weights."
Column 4:
  - "[[265577590-a99a7503-e022-4012-84fb-4626d8a15cc5.png]]"
  - "[[265577628-759326cb-d4da-402c-940b-ad479144b6e4.png]]"
  - "[[265577642-b335b30c-438c-494b-ad74-65debcd1910e.png]]"
---
