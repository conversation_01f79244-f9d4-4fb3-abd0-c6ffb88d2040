---
DocFlag:
  - Reference
  - Tested
Updated: 2024-06-07T11:37
tags:
  - AI->-TimeSeries
URL: https://research.google/blog/a-decoder-only-foundation-model-for-time-series-forecasting/
Created: 2024-05-13T12:58
---
Reference
特色
安装
程序编写
  
# Reference
```JavaScript
\#model download
https://huggingface.co/google/timesfm-1.0-200m
huggingface-cli download google/timesfm-1.0-200m
huggingface-cli download google/timesfm-1.0-200m --local-dir .
\#github
https://github.com/google-research/timesfm
```
# 特色
```Python
它可以对最多 512 个时间点的上下文长度和任何范围长度执行单变量时间序列预测，并具有可选的频率指示器
地平线长度可以设置为任何值。我们建议将其设置为您的应用程序的预测任务中所需的最大范围长度。我们通常建议水平长度<=上下文长度，但这不是函数调用中的要求。
#不能太长
I made same as max context 512.
it show me below
```
![[Notion/AI/🦜🦜🦜 TimesFM A decoder-only foundation model for time-series forecasting/attachments/Untitled.png|Untitled.png]]
#   
安装  
```JavaScript
conda env create --file=environment.yml
conda activate tfm_env
pip install -e .
Note that the four parameters are fixed to load the 200m model
We provide APIs to forecast from either array inputs or pandas dataframe. Both forecast methods expect (1) the input time series contexts, (2) along with their frequencies. Please look at the documentation of the functions tfm.forecast() and tfm.forecast_on_df() for detailed instructions.
In particular regarding the frequency, TimesFM expects a categorical indicator valued in {0, 1, 2}:
0 (default): high frequency, long horizon time series. We recommend using this for time series up to daily granularity.
1: medium frequency time series. We recommend using this for weekly and monthly data.
2: low frequency, short horizon time series. We recommend using this for anything beyond monthly, e.g. quarterly or yearly.
This categorical value should be directly provided with the array inputs. For dataframe inputs, we convert the conventional letter coding of frequencies to our expected categories, that
0: T, MIN, H, D, B, U
1: W, M
2: Q, Y
```
  
```Python
# CUDA 12.X installation
$ pip install --upgrade "jax[cuda12_pip]" -f https://storage.googleapis.com/jax-releases/jax_cuda_releases.html
#### OR ####
# CUDA 11.X installation
# Note: wheels only available on linux.
pip install --upgrade "jax[cuda11_pip]" -f https://storage.googleapis.com/jax-releases/jax_cuda_releases.html
pip install --upgrade "jax[cuda]" -f https://storage.googleapis.com/jax-releases/jax_cuda_releases.html
pip install --upgrade "jax[cuda12_pip]" -f https://storage.googleapis.com/jax-releases/jax_cuda_releases.html --force
Successfully installed jaxlib-0.4.28+cuda12.cudnn89
(research) [raysheng@MONSTER researcher]$ python -c "import jax; print(f'Jax backend: {jax.default_backend()}')"
Jax backend: gpu
(research) [raysheng@MONSTER researcher]$ python gputest.py 
CUDA availability: True
CUDA version: 11.8
```
  
# 程序编写
```Python
import torch
import numpy as np
import pandas as pd
import timesfm
torch.manual_seed(0)
torch.cuda.manual_seed(0)
np.random.seed(0)
\#let's confirm gpu is being used by jax
import jax; print(f'Jax backend: {jax.default_backend()}')
# Read the CSV file into a DataFrame
df = pd.read_csv('labTest/data/USD-JPY-data.csv')
# Remove rows with null data
df = df.dropna()
# Remove rows with infinite data
df = df.replace([np.inf, -np.inf], np.nan).dropna()
# 重置索引
df = df.reset_index(drop=True)

# Assuming you want to forecast the 'Close' values
df['unique_id'] = 'T0'  # Add a unique identifier column with a constant value
df = df.rename(columns={'Date': 'ds', 'Close': 'y'})  # Rename 'Date' to 'ds' and 'Close' to 'y'
# Select only the required columns
df = df[['unique_id', 'ds', 'y']]
df.head()
tfm = timesfm.TimesFm(
    context_len=512, \#64 or 512
    horizon_len=128,
    input_patch_len=32,
    output_patch_len=128,
    num_layers=20,
    model_dims=1280,
    backend='gpu'
)
# tfm.load_from_checkpoint(repo_id="google/timesfm-1.0-200m")
tfm.load_from_checkpoint(checkpoint_path="/opt/workspace/researcher/timesfm/model/checkpoints")
forecast_df = tfm.forecast_on_df(
    inputs=df,
    freq="D",  # monthly
    value_name="y",
    num_jobs=-1,
)
import plotly.graph_objects as go
# 将ds列转换为datetime类型
df['ds'] = pd.to_datetime(df['ds'])
forecast_df['ds'] = pd.to_datetime(forecast_df['ds'])
# 创建交互式图形
fig = go.Figure()
# 添加历史数据
fig.add_trace(go.Scatter(x=df['ds'], y=df['y'], name='Historical Data', line=dict(color='royalblue')))
# 添加预测数据
fig.add_trace(go.Scatter(x=forecast_df['ds'], y=forecast_df['timesfm'], name='Forecast', line=dict(color='tomato')))
# 添加分位数分布
quantiles = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
for q in quantiles:
    col_low = f'timesfm-q-{q:.1f}'
    col_high = f'timesfm-q-{1-q:.1f}'
    fig.add_trace(go.Scatter(x=forecast_df['ds'], y=forecast_df[col_low], name=f'{q*100:.0f}% Prediction Interval - Low', line=dict(color='tomato', width=0), showlegend=False))
    fig.add_trace(go.Scatter(x=forecast_df['ds'], y=forecast_df[col_high], name=f'{q*100:.0f}% Prediction Interval - High', line=dict(color='tomato', width=0), fill='tonexty', fillcolor='rgba(255, 99, 71, 0.3)', showlegend=False))
# 设置图形布局
fig.update_layout(
    title='Forecast with Quantile Distribution',
    xaxis_title='Date',
    yaxis_title='Value',
    hovermode='x',
    xaxis=dict(
        rangeselector=dict(
            buttons=list([
                dict(count=1, label="1m", step="month", stepmode="backward"),
                dict(count=6, label="6m", step="month", stepmode="backward"),
                dict(count=1, label="YTD", step="year", stepmode="todate"),
                dict(count=1, label="1y", step="year", stepmode="backward"),
                dict(step="all")
            ])
        ),
        rangeslider=dict(visible=True),
        type="date"
    )
)
# 显示图形
fig.show()
```
![[Notion/AI/🦜🦜🦜 TimesFM A decoder-only foundation model for time-series forecasting/attachments/Untitled 1.png|Untitled 1.png]]