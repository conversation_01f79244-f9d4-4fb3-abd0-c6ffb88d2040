---
Updated: 2024-04-29T08:50
tags:
  - AI->-Tools
  - AI->Automation
URL: https://www.landiannews.com/download/103637.html
Created: 2024-04-29T08:33
---
捕风记录仪是个通过记录屏幕上的所有内容从而实现记忆搜索的开源软件，该软件可以通过 OCR 文本识别或对画面描述进行搜索和摘要浏览活动，在必要的时候你可以通过捕风记录仪回溯你的操作记录，例如某个时刻浏览过某个网页、某个时刻打开过的 Word 文档、文档改动或丢失后可以通过捕风记录仪找回内容等。
在 Mac 上有款同类软件名为 Rewind，捕风记录仪是在 Windows 平台上的 Rewind 替代工具，所有数据都在本地记录并计算，不会上传到云端因此可以保证安全与隐私。
[[开源/AI] 捕风记录仪(Windrecorder)记录屏幕上的所有活动内容并总结/回溯](https://img.lancdn.com/landian/2024/04/103637-1.png)
[![](https://img.lancdn.com/landian/2024/04/103637-1.png)](https://img.lancdn.com/landian/2024/04/103637-1.png)
[[开源/AI] 捕风记录仪(Windrecorder)记录屏幕上的所有活动内容并总结/回溯](https://img.lancdn.com/landian/2024/04/103637-2.png)
[![](https://img.lancdn.com/landian/2024/04/103637-2.png)](https://img.lancdn.com/landian/2024/04/103637-2.png)
**工作原理：**
捕风记录仪开启后会在后台自动运行并逐段录制长度为 15 分钟的视频，视频录制完毕后会对视频片段进行索引，当屏幕没有发生变化、窗口标题在跳过列表、电脑进入锁屏时，捕风记录仪都会自动暂停录制。
在空闲的时候捕风记录仪将在后台自动对录制的视频进行分析和维护，包括压缩和清理视频、OCR 识别以及进行图像嵌入识别等。
说起这个工作原理蓝点网想起了群晖 DSM 附带的监控套件，该监控套件在持续录制过程中也会对视频画面进行分析，例如检测到异动时会视频流中标记一下，用户可以快速跳转到这个标记查看当时的异动。
捕风记录仪通过录制视频再处理的方式帮助用户记录内容，当然由于是每 15 分钟一个视频片段，因此也可能存在 15 分钟的分析延迟。
[[开源/AI] 捕风记录仪(Windrecorder)记录屏幕上的所有活动内容并总结/回溯](https://img.lancdn.com/landian/2024/04/103637-3.png)
[![](https://img.lancdn.com/landian/2024/04/103637-3.png)](https://img.lancdn.com/landian/2024/04/103637-3.png)
**图像嵌入索引为可选扩展：**
此功能需要单独安装，启用后每小时录制的视频占用的空间在 2~100MB 之间，具体取决于画面变化和显示器数量等；每个月大约占用 10~20GB 空间，根据不同的视频压缩预设，具体占用的空间也可以减少；SQlite 数据库每个月大约 160MB。
**最佳使用方法：**
安装并部署捕风记录仪后在 webui 中设置为开机自启动，即可无感记录一些内容。
当屏幕没有变化或锁屏、睡眠时就会进入自动维护状态，这样在用户有需要的时候就可以打开捕风记录仪查看各种信息。
**安装和部署方法：**
该软件需要 ffmpeg、Git、Python 的支持，因此实际使用过程中用户需要提前安装这些软件环境，具体可以通过项目主页查看：[https://github.com/yuka-friends/Windrecorder](https://github.com/yuka-friends/Windrecorder)
**隐私提醒：**
捕风记录仪是一款非常实用的开源软件，这可以帮助你记录所有活动并在后续进行回溯，然而这也存在隐私问题，因此你需要确保你的 PC 只有你自己可以访问，否则可能会泄露隐私。
版权声明：感谢您的阅读，除非文中已注明来源网站名称或链接，否则均为蓝点网原创内容。转载时请务必注明：来源于蓝点网、标注作者及[本文完整链接](https://www.landiannews.com/download/103637.html)，谢谢理解。