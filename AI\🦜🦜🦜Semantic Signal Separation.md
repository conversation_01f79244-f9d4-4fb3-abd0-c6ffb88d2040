---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-31T13:08
tags:
  - AI->-Competition
  - AI->-Dataset
  - AI->-Theory
URL: https://medium.com/towards-data-science/semantic-signal-separation-769f43b46779
Created: 2024-03-31T12:21
---
[![](https://miro.medium.com/v2/resize:fit:1200/1*3_2zDivWp758eLvJPhp8Aw.png)](https://miro.medium.com/v2/resize:fit:1200/1*3_2zDivWp758eLvJPhp8Aw.png)
## ==Understand Semantic Structures with Transformers and Topic Modeling====  
  
====使用 Transformer 和主题建模来理解语义结构==
==[==
[![](https://miro.medium.com/v2/resize:fill:55:55/1*u8Ez5oORTX0C98rL5AVjnQ.jpeg)](https://miro.medium.com/v2/resize:fill:55:55/1*u8Ez5oORTX0C98rL5AVjnQ.jpeg)
==](https://towardsdatascience.com/@power.up1163?source=post_page-----769f43b46779--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:30:30/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:30:30/1*CJe3891yB1A1mzMdqemkdg.jpeg)
==](https://medium.com/towards-data-science?source=post_page-----769f43b46779--------------------------------)==
==We live in the age of big data. At this point it’s become a cliche to say that data is the oil of the 21st century but it really is so. Data collection practices have resulted in huge piles of data in just about everyone’s hands.====  
  
====我们生活在大数据时代。现在说数据是 21 世纪的石油已经成为陈词滥调，但确实如此。数据收集的实践导致了几乎每个人手中都有大量的数据堆积。==
==Interpreting data, however, is no easy task, and much of the industry and academia still rely on solutions, which provide little in the ways of explanations. While deep learning is incredibly useful for predictive purposes, it rarely gives practitioners an understanding of the mechanics and structures that underlie the data.====  
  
====然而，解释数据并不是一件容易的事情，许多行业和学术界仍然依赖于提供很少解释的解决方案。虽然深度学习在预测方面非常有用，但很少能让从业者理解数据背后的机制和结构。==
==Textual data is especially tricky. While natural language and concepts like “topics” are incredibly easy for humans to have an intuitive grasp of, producing operational definitions of semantic structures is far from trivial.====  
  
====文本数据特别棘手。虽然自然语言和“主题”等概念对人类来说非常容易直观理解，但是产生语义结构的操作性定义远非简单。==
==In this article I will introduce you to different conceptualizations of discovering latent semantic structures in natural language, we will look at operational definitions of the theory, and at last I will demonstrate the usefulness of the method with a case study.====  
  
====在本文中，我将向您介绍在自然语言中发现潜在语义结构的不同概念化方法，我们将研究该理论的操作定义，并最后通过一个案例研究来展示该方法的实用性。==
## ==Theory: What is a “Topic”?====  
  
====理论：什么是“主题”？==
==While topic to us humans seems like a completely intuitive and self-explanatory term, it is hardly so when we try to come up with a useful and informative definition. The Oxford dictionary’s definition is luckily here to help us:====  
  
====尽管对于人类来说，主题似乎是一个完全直观和不言自明的术语，但当我们试图提出一个有用和信息丰富的定义时，它并不那么容易。幸运的是，牛津词典的定义可以帮助我们：==
==A subject that is discussed, written about, or studied.====  
  
====一个被讨论、写作或研究的主题。==
==Well, this didn’t get us much closer to something we can formulate in computational terms. Notice how the word== ==_subject,_== ==is used to hide all the gory details. This need not deter us, however, we can certainly do better.====  
  
====嗯，这并没有让我们更接近以计算方式表达的东西。注意主语这个词是用来隐藏所有令人不快的细节的。然而，这并不应该阻碍我们，我们肯定可以做得更好。==
[![](https://miro.medium.com/v2/resize:fit:875/1*yn5GtJlnF2oaKxxQq5ndJA.png)](https://miro.medium.com/v2/resize:fit:875/1*yn5GtJlnF2oaKxxQq5ndJA.png)
==Semantic Space of Academic Disciplines====  
  
====学术学科的语义空间==
==In Natural Language Processing, we often use a spatial definition of semantics. This might sound fancy, but essentially we imagine that semantic content of text/language can be expressed in some continuous space (often high-dimensional), where concepts or texts that are related are closer to each other than those that aren’t. If we embrace this theory of semantics, we can easily come up with two possible definitions for topic.====  
  
====在自然语言处理中，我们经常使用空间定义的语义。这听起来可能很高级，但本质上我们想象文本/语言的语义内容可以在某个连续的空间（通常是高维空间）中表达，相关的概念或文本之间比不相关的更接近。如果我们接受这种语义理论，我们可以很容易地提出两种可能的主题定义。==
## ==Topics as Semantic Clusters====  
  
====主题作为语义聚类==
==A rather intuitive conceptualization is to imagine topic as groups of passages/concepts in semantic space that are closely related to each other, but not as closely related to other texts. This incidentally means that one passage== ==_can only belong to one topic at a time._====  
  
====一个相当直观的概念化是将主题想象为在语义空间中彼此密切相关但与其他文本不太相关的一组段落/概念。顺便提一下，这意味着一个段落一次只能属于一个主题。==
[![](https://miro.medium.com/v2/resize:fit:875/1*c1O8Ma1z2F_fLTqeGVDlbQ.png)](https://miro.medium.com/v2/resize:fit:875/1*c1O8Ma1z2F_fLTqeGVDlbQ.png)
==Semantic Clusters of Academic Disciplines====  
  
====学术学科的语义聚类==
==This clustering conceptualization also lends itself to thinking about topics== ==_hierarchically._== ==You can imagine that the topic “animals” might contain two subclusters, one which is “Eukaryates”, while the other is “Prokaryates”, and then you could go down this hierarchy, until, at the leaves of the tree you will find actual instances of concepts.====  
  
====这种聚类概念也适用于层次化思考主题。你可以想象主题“动物”可能包含两个子聚类，一个是“真核生物”，而另一个是“原核生物”，然后你可以沿着这个层次结构往下走，直到在树的叶子节点上找到实际的概念实例。==
==Of course a limitation of this approach is that longer passages might contain multiple topics in them. This could either be addressed by splitting up texts to smaller, atomic parts (e.g. words) and modeling over those, but we can also ditch the clustering conceptualization alltogether.====  
  
====当然，这种方法的局限性在于较长的段落可能包含多个主题。可以通过将文本分割成较小的、原子性的部分（例如单词）并对其进行建模来解决这个问题，但我们也可以完全放弃聚类的概念化。==
## ==Topics as Axes of Semantics====  
  
====主题作为语义的轴线==
==We can also think of topics as the underlying dimensions of the semantic space in a corpus. Or in other words: Instead of describing what groups of documents there are we are explaining variation in documents by finding underlying== ==**semantic signals**====.====  
  
====我们也可以将主题视为语料库中语义空间的基本维度。换句话说，我们通过寻找潜在的语义信号来解释文档的变化，而不是描述文档的分组情况。==
[![](https://miro.medium.com/v2/resize:fit:875/1*ipBNb76EXaEOUXTUAssMuA.png)](https://miro.medium.com/v2/resize:fit:875/1*ipBNb76EXaEOUXTUAssMuA.png)
==Underlying Axes in the Semantic Space of Academic Disciplines====  
  
====学术学科语义空间中的基本轴线==
==We are explaining variation in documents by finding underlying semantic signals.====  
  
====我们通过寻找潜在的语义信号来解释文档中的变化。==
==You could for instance imagine that the most important axes that underlie restaurant reviews would be:====  
  
====您可以想象一下，支撑餐厅评论的最重要的因素可能是：==
1. ==Satisfaction with the food====  
      
    ====对食物的满意度==
2. ==Satisfaction with the service====  
      
    ====服务满意度==
==I hope you see why this conceptualization is useful for certain purposes. Instead of us finding “good reviews” and “bad reviews”, we get an understanding of what it is that drives differences between these. A pop culture example of this kind of theorizing is of course the political compass. Yet again, instead of us being interested in finding “conservatives” and “progressives”, we find the== ==**factors**== ==that differentiate these.====  
  
====我希望你能明白这种概念化对于某些目的是有用的。我们不再寻找“好评”和“差评”，而是理解驱使它们之间差异的因素。一个流行文化的例子就是政治罗盘。同样地，我们不再关注“保守派”和“进步派”，而是找到区分它们的因素。==
## ==Let’s Model! 让我们建模！==
==Now that we got the philosophy out of the way, we can get our hands dirty with designing computational models based on our conceptual understanding.====  
  
====既然我们已经解决了哲学问题，我们可以开始根据我们的概念理解来设计计算模型了。==
## ==Semantic Representations 语义表示==
==Classically the way we represented the semantic content of texts, was the so-called== ==**bag-of-words**== ==model. Essentially you make the very strong, and almost trivially wrong assumption, that the unordered collection of words in a document is constitutive of its semantic content. While these representations are plagued with a number of issues (==[==curse of dimensionality==](https://en.wikipedia.org/wiki/Curse_of_dimensionality)==, discrete space, etc.) they have been demonstrated useful by decades of research.====  
  
====经典地，我们表示文本的语义内容的方式是所谓的词袋模型。基本上，你做出了非常强大且几乎显然错误的假设，即文档中无序的词语集合构成了其语义内容。虽然这些表示存在许多问题（维度灾难、离散空间等），但经过几十年的研究证明它们是有用的。==
==Luckily for us, the state of the art has progressed beyond these representations, and we have access to models that can represent text in context.== [==Sentence Transformers==](http://sbert.net/) ==are transformer models which can encode passages into a high-dimensional continuous space, where semantic similarity is indicated by vectors having high== [==cosine similarity==](https://en.wikipedia.org/wiki/Cosine_similarity)==. In this article I will mainly focus on models that use these representations.====  
  
====幸运的是，现在的技术已经超越了这些表示方法，我们可以使用能够在上下文中表示文本的模型。句子转换器是可以将段落编码为高维连续空间的变压器模型，其中语义相似性由具有高余弦相似度的向量表示。在本文中，我主要关注使用这些表示方法的模型。==
## ==Clustering Models 聚类模型==
==Models that are currently the most widespread in the topic modeling community for contextually sensitive topic modeling (==[==Top2Vec==](https://github.com/ddangelov/Top2Vec)==,== [==BERTopic==](https://maartengr.github.io/BERTopic/index.html)==) are based on the clustering conceptualization of topics.====  
  
====目前在主题建模社区中最广泛使用的模型（Top2Vec、BERTopic）是基于主题的聚类概念。==
[![](https://miro.medium.com/v2/resize:fit:476/1*_WDVbdIV1SS9RhMxpy4jkw.png)](https://miro.medium.com/v2/resize:fit:476/1*_WDVbdIV1SS9RhMxpy4jkw.png)
==Clusters in Semantic Space Discovered by BERTopic (figure from BERTopic’s documentation)====  
  
====BERTopic 的语义空间中发现的聚类（来自 BERTopic 的文档）==
==They discover topics in a process that consists of the following steps:====  
  
====他们通过以下步骤来发现话题：==
1. ==Reduce dimensionality of semantic representations using== [==UMAP==](https://umap-learn.readthedocs.io/en/latest/)==  
      
    ====使用 UMAP 降低语义表示的维度==
2. ==Discover cluster hierarchy using== [==HDBSCAN==](https://hdbscan.readthedocs.io/en/latest/index.html)==  
      
    ====使用 HDBSCAN 发现聚类层次结构==
3. ==Estimate importances of terms for each cluster using post-hoc descriptive methods (c-TF-IDF, proximity to cluster centroid)====  
      
    ====使用事后描述性方法（c-TF-IDF，与聚类中心的接近程度）估计每个聚类的术语重要性==
==These models have gained a lot of traction, mainly due to their interpretable topic descriptions and their ability to recover hierarchies, as well as to learn the number of topics from the data.====  
  
====这些模型因其可解释的主题描述和恢复层次结构的能力，以及从数据中学习主题数量的能力而受到了广泛关注。==
==If we want to model nuances in topical content, and understand factors of semantics, clustering models are not enough.====  
  
====如果我们想要模拟主题内容中的细微差别，并理解语义因素，聚类模型是不够的。==
==I do not intend to go into great detail about the practical advantages and limitations of these approaches, but most of them stem from philosophical considerations outlined above.====  
  
====我不打算详细讨论这些方法的实际优势和局限性，但大部分都源于上述的哲学考虑。==
## ==Semantic Signal Separation====  
  
====语义信号分离==
==If we are to discover the axes of semantics in a corpus, we will need a new statistical model.====  
  
====如果我们要在语料库中发现语义的轴线，我们将需要一个新的统计模型。==
==We can take inspiration from classical topic models, such as== ==**Latent Semantic Allocation.**== ==LSA utilizes matrix decomposition to find latent components in== ==_bag-of-words_== ==representations. LSA’s main goal is to find words that are highly correlated, and explain their cooccurrence as an underlying semantic component.====  
  
====我们可以从经典的主题模型中汲取灵感，例如潜在语义分配。LSA 利用矩阵分解在词袋表示中找到潜在的组成部分。LSA 的主要目标是找到高度相关的词，并将它们的共现解释为潜在的语义组成部分。==
==Since we are no longer dealing with bag-of-words, explaining away correlation might not be an optimal strategy for us. Orthogonality is not statistical independence. Or in other words: Just because two components are uncorrelated, it does not mean that they are statistically independent.====  
  
====由于我们不再处理词袋模型，解释相关性可能不是我们的最佳策略。正交性并不等同于统计独立性。换句话说，两个组件之间没有相关性，并不意味着它们在统计上是独立的。==
==Orthogonality is not statistical independence====  
  
====正交性不等于统计独立性==
==Other disciplines have luckily come up with decomposition models that discover maximally independent components.== ==**Independent Component Analysis**== ==has been extensively used in Neuroscience to discover and remove noise signals from EEG data.====  
  
====其他学科幸运地提出了分解模型，可以发现最大独立的组件。独立成分分析在神经科学中被广泛用于发现和去除脑电图数据中的噪音信号。==
[![](https://miro.medium.com/v2/resize:fit:800/0*6ir4SARZ60W3jlhJ.png)](https://miro.medium.com/v2/resize:fit:800/0*6ir4SARZ60W3jlhJ.png)
==Difference between Orthogonality and Independence Demonstrated with PCA and ICA (Figure from scikit-learn’s documentation)====  
  
====正交性和独立性的区别通过 PCA 和 ICA 进行演示（来自 scikit-learn 文档的图表）==
==The main idea behind Semantic Signal Separation is that we can find maximally independent underlying semantic signals in a corpus of text by decomposing representations with ICA.====  
  
====语义信号分离的主要思想是通过使用 ICA 分解表示，我们可以在文本语料库中找到最大独立的潜在语义信号。==
==We can gain human-readable descriptions of topics by taking terms from the corpus that rank highest on a given component.====  
  
====我们可以通过从语料库中选择在给定组件上排名最高的术语来获得关于主题的可读描述。==
## ==Case Study: Machine Learning Papers====  
  
====案例研究：机器学习论文==
==To demonstrate the usefulness of Semantic Signal Separation for understanding semantic variation in corpora, we will fit a model on a dataset of approximately 118k machine learning abstracts.====  
  
====为了展示语义信号分离在理解语义变化方面的实用性，我们将在一个包含约 118k 个机器学习摘要的数据集上拟合一个模型。==
==To reiterate once again what we’re trying to achieve here: We want to establish the dimensions, along which all machine learning papers are distributed. Or in other words we would like to build a spatial theory of semantics for this corpus.====  
  
====再次强调我们在这里的目标：我们希望建立一个维度，用来描述所有机器学习论文的分布。换句话说，我们想为这个语料库建立一个语义的空间理论。==
==For this we are going to use a Python library I developed called== [==Turftopic==](https://x-tabdeveloping.github.io/turftopic/)==, which has implementations of most topic models that utilize representations from transformers, including Semantic Signal Separation. Additionally we are going to install the HuggingFace datasets library so that we can download the corpus at hand.====  
  
====为此，我们将使用我开发的名为 Turftopic 的 Python 库，其中包含了大多数利用 transformers 表示的主题模型的实现，包括语义信号分离。此外，我们还将安装 HuggingFace 数据集库，以便能够下载所需的语料库。==
==pip install turftopic datasets==
==Let us download the data from HuggingFace:====  
  
====让我们从 HuggingFace 下载数据==
==from datasets import load_dataset==
==ds = load_dataset("CShorten/ML-ArXiv-Papers", split="train")==
==We are then going to run Semantic Signal Separation on this data. We are going to use the== [==all-MiniLM-L12-v2==](https://huggingface.co/sentence-transformers/all-MiniLM-L12-v2) ==Sentence Transformer, as it is quite fast, but provides reasonably high quality embeddings.====  
  
====然后我们将对这些数据运行语义信号分离。我们将使用全 MiniLM-L12-v2 句子转换器，因为它速度相当快，但提供了相对高质量的嵌入。==
==from turftopic import SemanticSignalSeparation==
==model = SemanticSignalSeparation(10, encoder="all-MiniLM-L12-v2")====  
  
====model.fit(ds["abstract"])==
==model.print_topics()==
[![](https://miro.medium.com/v2/resize:fit:875/1*9652pxOEz0D0VWnD5bwViw.png)](https://miro.medium.com/v2/resize:fit:875/1*9652pxOEz0D0VWnD5bwViw.png)
==Topics Found in the Abstracts by Semantic Signal Separation====  
  
====语义信号分离在摘要中发现的主题==
==These are highest ranking keywords for the ten axes we found in the corpus. You can see that most of these are quite readily interpretable, and already help you see what underlies differences in machine learning papers.====  
  
====这些是我们在语料库中发现的十个轴的最高排名关键词。你可以看到大多数关键词都很容易解释，并且已经帮助你看到了机器学习论文中的差异所在。==
==I will focus on three axes, sort of arbitrarily, because I found them to be interesting. I’m a Bayesian evangelist, so Topic 7 seems like an interesting one, as it seems that this component describes how probabilistic, model based and causal papers are. Topic 6 seems to be about noise detection and removal, and Topic 1 is mostly concerned with measurement devices.====  
  
====我将专注于三个轴，有点随意选择，因为我发现它们很有趣。作为一个贝叶斯派的传道者，第 7 个主题似乎很有趣，因为它描述了概率、基于模型和因果论的论文。第 6 个主题似乎是关于噪声检测和去除，而第 1 个主题主要涉及测量设备。==
==We are going to produce a plot where we display a subset of the vocabulary where we can see how high terms rank on each of these components.====  
  
====我们将制作一个图表，显示词汇的一个子集，以便我们可以看到每个组成部分上高频词的排名。==
==First let’s extract the vocabulary from the model, and select a number of words to display on our graphs. I chose to go with words that are in the 99th percentile based on frequency (so that they still remain somewhat visible on a scatter plot).====  
  
====首先，让我们从模型中提取词汇，并选择一些词汇在图表上显示。我选择了根据频率处于 99 百分位数的词汇（这样它们在散点图上仍然能够保持一定的可见性）。==
==import numpy as np==
==vocab = model.get_vocab()==
==document_term_matrix = model.vectorizer.transform(ds["abstract"])====  
  
====frequencies = document_term_matrix.sum(axis=0)====  
  
====frequencies = np.squeeze(np.asarray(frequencies))==
==selected_terms_mask = frequencies > np.quantile(frequencies, 0.99)==
==We will make a== ==_DataFrame_== ==with the three selected dimensions and the terms so we can easily plot later.====  
  
====我们将使用三个选定的维度和术语创建一个 DataFrame，以便稍后可以轻松绘制。==
==import pandas as pd==
==terms_with_axes = pd.DataFrame({====  
  
===="inference": model.components_[7][selected_terms],====  
  
===="measurement_devices": model.components_[1][selected_terms],====  
  
===="noise": model.components_[6][selected_terms],====  
  
===="term": vocab[selected_terms]====  
  
====})==
==We will use the Plotly graphing library for creating an interactive scatter plot for interpretation. The X axis is going to be the inference/Bayesian topic, Y axis is going to be the noise topic, and the color of the dots is going to be determined by the measurement device topic.====  
  
====我们将使用 Plotly 图形库创建一个交互式散点图进行解释。X 轴将是推理/贝叶斯主题，Y 轴将是噪声主题，点的颜色将由测量设备主题确定。==
==import plotly.express as px==
==px.scatter(====  
  
====terms_with_axes,====  
  
====text="term",====  
  
====x="inference",====  
  
====y="noise",====  
  
====color="measurement_devices",====  
  
====template="plotly_white",====  
  
====color_continuous_scale="Bluered",====  
  
====).update_layout(====  
  
====width=1200,====  
  
====height=800====  
  
====).update_traces(====  
  
====textposition="top center",====  
  
====marker=dict(size=12, line=dict(width=2, color="white"))====  
  
====)==
[![](https://miro.medium.com/v2/resize:fit:875/1*3_2zDivWp758eLvJPhp8Aw.png)](https://miro.medium.com/v2/resize:fit:875/1*3_2zDivWp758eLvJPhp8Aw.png)
==Plot of Most Frequent Terms in the Corpus Distributed by Semantic Axes====  
  
====语料库中最常见术语的分布情况的情节==
==We can already infer a lot about the semantic structure of our corpus based on this visualization. For instance we can see that papers that are concerned with efficiency, online fitting and algorithms score very low on statistical inference, this is somewhat intuitive. On the other hand what Semantic Signal Separation has already helped us do in a data-based approach is confirm, that deep learning papers are not very concerned with statistical inference and Bayesian modeling. We can see this from the words “network” and “networks” (along with “convolutional”) ranking very low on our Bayesian axis. This is one of the criticisms the field has received. We’ve just given support to this claim with empirical evidence.====  
  
====我们可以根据这个可视化结果推断出我们语料库的语义结构。例如，我们可以看到关于效率、在线拟合和算法的论文在统计推断方面得分非常低，这在某种程度上是直观的。另一方面，语义信号分离已经帮助我们在基于数据的方法中确认，深度学习论文对统计推断和贝叶斯建模不太关注。我们可以从“网络”和“网络”（以及“卷积”）这些词在我们的贝叶斯轴上排名非常低来看出这一点。这是该领域所受到的批评之一。我们刚刚用实证证据支持了这一观点。==
==Deep learning papers are not very concerned with statistical inference and Bayesian modeling, which is one of the criticisms the field has received. We’ve just given support to this claim with empirical evidence.====  
  
====深度学习论文对统计推断和贝叶斯建模不太关注，这是该领域所受到的批评之一。我们刚刚通过实证证据支持了这一观点。==
==We can also see that clustering and classification is very concerned with noise, but that agent-based models and reinforcement learning isn’t.====  
  
====我们还可以看到，聚类和分类非常关注噪音，但基于代理模型和强化学习则不关注。==
==Additionally an interesting pattern we may observe is the relation of our Noise axis to measurement devices. The words “image”, “images”, “detection” and “robust” stand out as scoring very high on our measurement axis. These are also in a region of the graph where noise detection/removal is relatively high, while talk about statistical inference is low. What this suggests to us, is that measurement devices capture a lot of noise, and that the literature is trying to counteract these issues, but mainly not by incorporating noise into their statistical models, but by preprocessing. This makes a lot of sense, as for instance, Neuroscience is known for having very extensive preprocessing pipelines, and many of their models have a hard time dealing with noise.====  
  
====此外，我们还可以观察到一个有趣的模式，即我们的噪声轴与测量设备之间的关系。词语“图像”、“图像”、“检测”和“鲁棒”在我们的测量轴上得分非常高。这些词语也位于图表中噪声检测/去除相对较高的区域，而关于统计推断的讨论较少。这给我们的提示是，测量设备捕捉到了很多噪声，而文献试图通过预处理来对抗这些问题，但主要不是通过将噪声纳入统计模型中。这是有道理的，因为例如，神经科学以其非常复杂的预处理流程而闻名，他们的许多模型在处理噪声时遇到了困难。==
[![](https://miro.medium.com/v2/resize:fit:875/1*3XxJ3vq4uRpW8qfcRyb1Cw.png)](https://miro.medium.com/v2/resize:fit:875/1*3XxJ3vq4uRpW8qfcRyb1Cw.png)
==Noise in Measurement Devices’ Output is Countered with Preprocessing====  
  
====通过预处理来抵消测量设备输出中的噪音==
==We can also observe that the lowest scoring terms on measurement devices is “text” and “language”. It seems that NLP and machine learning research is not very concerned with neurological bases of language, and psycholinguistics. Observe that “latent” and “representation is also relatively low on measurement devices, suggesting that machine learning research in neuroscience is not super involved with representation learning.====  
  
====我们还可以观察到，在测量设备上得分最低的术语是“文本”和“语言”。似乎自然语言处理和机器学习研究对语言的神经基础和心理语言学不太关注。观察到“潜在”和“表示”在测量设备上也相对较低，这表明神经科学中的机器学习研究与表示学习关系不是很密切。==
[![](https://miro.medium.com/v2/resize:fit:875/1*iBXk6ftJsUl_UmzXctDuvw.png)](https://miro.medium.com/v2/resize:fit:875/1*iBXk6ftJsUl_UmzXctDuvw.png)
==Text and Language are Rarely Related with Measurement Devices====  
  
====文本和语言很少与测量设备相关==
==Of course the possibilities from here are endless, we could spend a lot more time interpreting the results of our model, but my intent was to demonstrate that we can already find claims and establish a theory of semantics in a corpus by using Semantic Signal Separation.====  
  
====当然，从这里开始的可能性是无限的，我们可以花更多时间解释我们模型的结果，但我的目的是要证明我们已经可以通过使用语义信号分离在语料库中找到主张并建立语义理论。==
==Semantic Signal Separation should mainly be used as an exploratory measure for establishing theories, rather than taking its results as proof of a hypothesis.====  
  
====语义信号分离主要应作为建立理论的探索性措施，而不是将其结果作为假设的证据。==
==One thing I would like to emphasize is that Semantic Signal Separation should mainly be used as an exploratory measure for establishing theories, rather than taking its results as proof of a hypothesis. What I mean here, is that our results are sufficient for gaining an intuitive understanding of differentiating factors in our corpus, an then building a theory about what is happening, and why it is happening, but it is not sufficient for establishing the theory’s correctness.====  
  
====我想强调的一件事是，语义信号分离主要应作为建立理论的探索性措施，而不是将其结果作为假设的证明。我所指的是，我们的结果足以获得对语料库中不同因素的直观理解，并建立关于发生了什么以及为什么发生的理论，但这并不足以证明该理论的正确性。==
## ==Conclusion 结论==
==Exploratory data analysis can be confusing, and there are of course no one-size-fits-all solutions for understanding your data. Together we’ve looked at how to enhance our understanding with a model-based approach from theory, through computational formulation, to practice.====  
  
====探索性数据分析可能会令人困惑，当然也没有适用于理解数据的一刀切解决方案。我们一起看了如何通过理论、计算公式和实践来增强我们的理解，采用基于模型的方法。==
==I hope this article will serve you well when analysing discourse in large textual corpora. If you intend to learn more about topic models and exploratory text analysis, make sure to have a look at some of my other articles as well, as they discuss some aspects of these subjects in greater detail.====  
  
====我希望这篇文章在分析大型文本语料库中的话语时能对您有所帮助。如果您打算了解更多关于主题模型和探索性文本分析的内容，请务必也看一下我的其他文章，因为它们更详细地讨论了这些主题的一些方面。==
==_(( Unless stated otherwise, figures were produced by the author. ))_====  
  
====_除非另有说明，图表由作者制作。_==