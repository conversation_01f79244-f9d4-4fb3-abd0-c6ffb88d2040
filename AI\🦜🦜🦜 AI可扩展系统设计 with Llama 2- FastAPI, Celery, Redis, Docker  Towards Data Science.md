---
DocFlag:
  - Reference
  - Tested
Updated: 2023-09-24T19:59
tags:
  - AI->-Infra
Created: 2023-07-31T16:26
---
  
# Leveraging Llama 2 Features in Real-world Applications: Building Scalable Chatbots with FastAPI, Celery, Redis, and Docker
## An In-Depth Exploration: Open vs Closed Source LLMs, Unpacking Llama 2’s Unique Features, Mastering the Art of Prompt Engineering, and Designing Robust Solutions with FastAPI, Celery, Redis, and Docker
[![](https://miro.medium.com/v2/resize:fill:88:88/2*K_VENFQPxpVXGGsR05mVfQ.jpeg)](https://miro.medium.com/v2/resize:fill:88:88/2*K_VENFQPxpVXGGsR05mVfQ.jpeg)
[![](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)
[<PERSON><PERSON>](https://medium.com/@luisroque?source=post_page-----406f1cbeb935--------------------------------)
·
[Follow](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fsubscribe%2Fuser%2F2195f049db86&operation=register&redirect=https%3A%2F%2Ftowardsdatascience.com%2Fleveraging-llama-2-features-in-real-world-applications-building-scalable-chatbots-with-fastapi-406f1cbeb935&user=Lu%C3%ADs+Roque&userId=2195f049db86&source=post_page-2195f049db86----406f1cbeb935---------------------post_header-----------)
Published in
[Towards Data Science](https://towardsdatascience.com/?source=post_page-----406f1cbeb935--------------------------------)
·
14 min read
·
5 days ago
# Introduction
In an unexpected move, Meta open-sourced their Large Language Model (LLM), Llama 2, a few days ago in a decision that could reshape the current landscape of AI development. It offers an alternative to the main companies in the space such as OpenAI and Google that decided to maintain tight control over their AI models, limiting accessibility and restricting broader innovation. Hopefully, Meta’s decision will spark a collective response from the open-source community, counteracting the trend of restricting access to the advances in the field. Llama 2’s new license even goes further and allows commercial use, granting developers and businesses opportunities to leverage the model within existing and new products.
The Llama2 family consists of pre-trained and fine-tuned LLMs, including Llama2 and Llama2-Chat, scaling up to 70B parameters. These models have proven to perform better than open-source models on various benchmarks [1]. They also hold their ground against some closed-source models, offering a much-needed boost to open-source AI development [2].
Figure 1: The Llama 2 family ([image source](https://unsplash.com/photos/lpxf698eF6s))
[![](https://miro.medium.com/v2/resize:fit:700/0*4Z3d1Af4hJ78Dv0W)](https://miro.medium.com/v2/resize:fit:700/0*4Z3d1Af4hJ78Dv0W)
If you follow the Open LLM leaderboard from HuggingFace [1], you can see that Meta’s Llama 2 holds a strong third-place position. After the LLama 2 announcement, Stability AI released FreeWilly1 and FreeWilly2 [3]. FreeWilly1 is a fine-tuned version of Llama, and FreeWilly2 of Llama 2. Stability AI shared that they fine-tuned both models on an Orca-style Dataset. The Orca dataset is a large, structured collection of augmented data designed to fine-tune LLMs, where each entry consists of a question and a corresponding response from GPT-4 or GPT-3.5. Why are we not using the FreeWilly2 model? Unfortunately, while Llama 2 allows commercial use, FreeWilly2 can only be used for research purposes, governed by the Non-Commercial Creative Commons license…
  
  
```JavaScript
https://blog.ovhcloud.com/doing-big-automation-with-celery/
install it under llama
pip install celery
pip install redis
pip install python-dotenv
MUST INSTALL ASYNC FLASK version
pip install 'flask[async]'
dnf install podman
dnf install docker
\#step 1 , start redis
docker run --name redis-db -p 6379:6379 -d redis
You will need to perform for each key a "type" command:

> type <key>
and depending on the response perform:
for "string": get <key>
for "hash": hgetall <key>
for "list": lrange <key> 0 -1
for "set": smembers <key>
for "zset": zrange <key> 0 -1 withscores

(base) [root@MONSTER:/opt/workspace/large_laguage_models/llama2]# cat /usr/local/bin/getredis.sh
#!/bin/sh -eu
keys=`redis-cli keys '*'`
if [ "$keys" ]; then
    echo "$keys" | while IFS= read -r key; do
        type=`echo | redis-cli type "$key"`
        case "$type" in
            string) value=`echo | redis-cli get "$key"`;;
            hash) value=`echo | redis-cli hgetall "$key"`;;
            set) value=`echo | redis-cli smembers "$key"`;;
            list) value=`echo | redis-cli lrange "$key" 0 -1`;;
            zset) value=`echo | redis-cli zrange "$key" 0 -1 withscores`;;
        esac
        echo "> $key ($type):"
        echo "$value" | sed -E 's/^/    /'
    done
fi

2. start worker
celery -A celery_worker worker --loglevel=info --concurrency=5   (default 24 workers)
celery list
celery graph
celery status
3. start app web server
client --http request--> web server --send tasks -> redis broker --send tasks -> celery workers(talk to dedicated llm) --send tasks results-> redis brokers 
web server --query task results --> redis broker
web server --- http response ---> client 

restart all
ps -ef|grep celery and kill -9
docker ps
docker restart
redis-cli -> flushdb
```
  
# celery worker
```JavaScript
import base64
# from flask import  request, jsonify
import torch
import os
from dotenv import load_dotenv
import gc 
import json
from celery import Celery, signals
from whisperx.alignment import align, load_align_model
from whisperx.asr import load_model
from whisperx.audio import load_audio
from whisperx.diarize import DiarizationPipeline, assign_word_speakers
from pyannote.audio.pipelines.speaker_verification import PretrainedSpeakerEmbedding
from sklearn.preprocessing import StandardScaler
from pyannote.audio import Audio
from pyannote.core import Segment
# from mom_serverx_v3 import app
load_dotenv()
HF_TOKEN = os.getenv("HUGGINGFACEHUB_API_TOKEN")
# 加载模型
def get_device():
    return "cuda" if torch.cuda.is_available() else "cpu"
def make_celery(app_name=__name__):
    backend = broker = 'redis://************:6379/0'
    return Celery(app_name, backend=backend, broker=broker)

celery = make_celery()

@signals.worker_process_init.connect
def setup_model(signal, sender, **kwargs):
    sampling_rate = 22050
    global audio_model,emb_model,emb_audio,diarize_model,model_loader
    audio_model = load_model("large-v2", device=get_device(),                    
            compute_type="float16", task="transcribe",  download_root="/opt/workspace/mom/model")
    emb_model = PretrainedSpeakerEmbedding("speechbrain/spkrec-ecapa-voxceleb", device=get_device())
    emb_audio = Audio(sample_rate=sampling_rate, mono="downmix")
    diarize_model = DiarizationPipeline(use_auth_token=HF_TOKEN, device=get_device())

@celery.task
def transcribe_audio_worker(filename):
    
    def cleargc():
        gc.collect()
        torch.cuda.empty_cache()
        
    def get_segments_embedingtable(audio_file, model, audio, result):
        for segment in result.get('segments', []):        
            speaker = Segment(segment['start'], segment['end'])
            waveform, _ = audio.crop(audio_file, speaker)
            embedding = model(waveform[None])
            segment['embedding'] = embedding    
        return result
    # from mom_serverx_v3 import app
    # with app.app_context():
    try:
        # output_dir = "/opt/workspace/mom/wav/"
        batch_size = 16
        print_progress =False
        
        # # 获取上传的文件
        # file = data['file'].encode('utf-8')
        # b = base64.b64decode(file)
        
        # file = request.files['file']
        # if file:
            # 存储文件到临时位置
        temp_file_path = filename
        # file.save(temp_file_path)
        
        # 执行音频转录
        tmp_audio = load_audio(temp_file_path)
        result = audio_model.transcribe(tmp_audio, batch_size=batch_size,  print_progress=print_progress)
        # diarize_segments = diarize_model(tmp_audio)
        # result = whisperx.assign_word_speakers(diarize_segments, result)            
        result = get_segments_embedingtable(temp_file_path, emb_model, emb_audio, result)  
        # all_embeddings_3 = [segment['embedding'] for segment in result3.get('segments', [])]
        # 序列化嵌套在 result['segments'] 中的 NumPy 数组
        for segment in result['segments']:
            segment['embedding'] = segment['embedding'].tolist()
        
        cleargc()
        
        # 删除临时文件
        os.remove(temp_file_path)
        # 返回转录结果
        return json.dumps(result['segments'])
    except Exception as e:
        return json.dumps({"error": str(e)}), 500
```
  
# http server
```JavaScript
# import base64
import uuid
from fastapi import FastAPI, File, UploadFile
from fastapi.responses import JSONResponse
# from pydantic import BaseModel
from dotenv import load_dotenv
from typing import Any
import uvicorn
from celery.result import AsyncResult
from celery_worker import transcribe_audio_worker

load_dotenv()
app = FastAPI()

@app.get("/task/{task_id}")
async def get_task(task_id: str) -> Any:
    result = AsyncResult(task_id)
    if result.ready():
        res = result.get()
        return {"status":"Finished", "result": res}
    else:
        return {"status": "Task not completed yet"}
    
    
@app.post("/transcribe/")
async def transcribe_audio(file: UploadFile = File(...)) :
    
    # generate a random filename
    filename = "/opt/workspace/mom/wav/"+ str(uuid.uuid4())
    # Save the file
    with open(filename, "wb") as buffer:
        buffer.write(file.file.read())
    
    # 获取上传的文件    
    # file = request.files['file']
    task = transcribe_audio_worker.delay(filename)
    return {"task_id": task.id}
if __name__ == "__main__":
    # app.run(host='0.0.0.0', port=8123)
    uvicorn.run(app, host='0.0.0.0', port=8123)
```
  
# client
```JavaScript
API_HOST = "************"
API_PORT = 8123
 
def get_task_status(task_id):
    while True:
        conn = http.client.HTTPConnection(API_HOST, API_PORT)
        conn.request("GET", f"/task/{task_id}")
        response = conn.getresponse()
        # print(response.status, response.reason, response.read().decode())
        data = json.loads(response.read().decode())
        conn.close()
        status = data["status"]
        if "Task not completed yet" not in status:
            # change to a list
            result = json.loads(data['result'])
            return result
        time.sleep(0.5)
def transcribe_audio_via_server2(temp_file_path: str, server_url: str = "http://************:8123/transcribe/" ):
    """
    使用服务器进行音频转录。
    参数:
    - temp_file_path: 临时音频文件的路径
    - server_url: 服务器API的URL
    返回:
    - result: 转录结果
    """
    
    try:
        with open(temp_file_path, 'rb') as f:            
            response = requests.post(server_url, files={"file": f})
        
        if response.status_code == 200:
            result = response.json()
            task_id = result["task_id"]
            result = get_task_status(task_id)
            
            # 反序列化 JSON 数据以获取原始的 result['segments']
            for segment in result:
                segment['embedding'] = np.array(segment['embedding'])            
            
            return "Success", result
        else:
            print("Error:", response.status_code, response.text)
            return "Error", None
    except Exception as e:
        print(f"An error occurred: {e}")
        return "Error", None
```