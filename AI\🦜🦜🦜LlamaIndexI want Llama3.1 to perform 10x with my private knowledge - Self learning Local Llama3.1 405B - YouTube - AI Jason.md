---
Updated: 2024-08-26T00:03
tags:
  - AI->-RAG
  - AI->-YouTube
  - AI->-llama
  - AI->Automation
URL: https://www.youtube.com/watch?v=2PKCOVqhngY
Created: 2024-07-31T14:08
---
测试笔记
Setup slack app
Further improvement
Start slace as service
# 测试笔记
```YAML
\#https://cloud.llamaindex.ai/
create one index
1. notion token you can get from below
https://www.notion.so/profile/integrations/
**************************************************
2. get db id (or pages id if required)
3. huggingface embeding
token: *************************************
https://huggingface.co/SmartCreationAI/bge-large-zh-v1.5
https://huggingface.co/BAAI/bge-large-zh-v1.5
In LlamaIndex
fill model endpoints like this 'BAAI/bge-large-zh-v1.5'  <~~~!!! looks not working
4. openai
(base) [raysheng@MONSTER:/opt/workspace]$ ***************************************************

https://api.cloud.llamaindex.ai/api/pipeline/625e9304-e0c9-4b18-8f44-b6f9164cf8f4/retrieve
from llama_index.indices.managed.llama_cloud import LlamaCloudIndex
# pip install llama-index-indices-managed-llama-cloud
index = LlamaCloudIndex(
  name="Notion Memo Talk", 
  project_name="Default",
  api_key="llx-..."
)
nodes = index.as_retriever().retrieve(query)
response = index.as_query_engine().query(query)

https://cloud.llamaindex.ai/api-key
NotionMemoTalk
llx-rkgcyWTB0ffTnuto3wJgTmHfFrAHUxxjAx1PJKiywIdMzTEs
SlactText
llx-Rzxbys1kEIQQXBKEY0jE7XhnKe6uGkkDrkmyfExOs8MStBe4
```
## Setup slack app
```YAML
\#1 create app in slack and assign permisssion and get token
\#2 create below simple program and get user slack user id
import os
import logging
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from slack_bolt.adapter.flask import SlackRequestHandler
from slack_bolt import App
from dotenv import load_dotenv, find_dotenv
from flask import Flask, request, jsonify
load_dotenv(find_dotenv())
SLACK_BOT_TOKEN = os.environ["SLACK_BOT_TOKEN"]
SLACK_SIGNING_SECRET = os.environ["SLACK_SIGNING_SECRET"]
SLACK_BOT_USER_ID = os.environ["SLACK_BOT_USER_ID"]
app = App(token=SLACK_BOT_TOKEN)
flask_app = Flask(__name__)
handler = SlackRequestHandler(app)
slack_client = WebClient(token=SLACK_BOT_TOKEN)
def get_bot_user_id():
    try:
        response = slack_client.auth_test()
        return response["user_id"]
    except SlackApiError as e:
        print(f"Error: {e}")
id = get_bot_user_id()
print(f"Bot user ID: {id}")
#
SLACK_BOT_TOKEN=*********************************************************
SLACK_SIGNING_SECRET=ea6b3d4d4fc3174baa0474e0c6856b8a
SLACK_BOT_USER_ID=U07FEMRJTK9
NOTION_API_KEY=
LLAMA_CLOUD_API=llx-Rzxbys1kEIQQXBKEY0jE7XhnKe6uGkkDrkmyfExOs8MStBe4
FIREWAORKS_API_KEY=Ft7XPmQ2lzmxw2MesUc4clva528WjlnngrDBktqwkXh38cjO
\#1, start app.py
\#2. ngrok http 5003
ngrok start --all
\#3  login slack api -> select you app LiamaIndexBo -> Event subscription
https://3ad7-92-202-133-157.ngrok-free.app/slack/events
https://api.slack.com/apps/A07FXMYA8VA
\#4  add subscribe to bot events and save!!
https://api.slack.com/apps/A07FXMYA8VA/event-subscriptions?
```
[[🦜🦜🦜ngrok使用教程 - 知乎]]
```YAML
pip install --upgrade llama-index
pip install llama-index-llms-fireworks
pip install llama-index-llms-ollama
https://fireworks.ai/account/profile
# but not support from Japan
Ft7XPmQ2lzmxw2MesUc4clva528WjlnngrDBktqwkXh38cjO
```
##   
Further improvement  
```YAML
add knowledge and save into notion
then tirgger llamaindex to sync notion page back
```
##   
Start slace as service  
```JavaScript
Tmux
conda activate research
python /opt/workspace/slack-kb-agent/app.py
then open another tmux windo
ngrok http 5003
ngrok start --all
Update events url
https://api.slack.com/apps/A07FXMYA8VA/event-subscriptions?
```
![[Notion/AI/🦜🦜🦜LlamaIndexI want Llama3.1 to perform 10x with my private knowledge - Self learning Local Llama3.1 405B - YouTube - AI Jason/attachments/image.png|image.png]]
  
  
  
  
  
  
[![](https://i.ytimg.com/vi/2PKCOVqhngY/maxresdefault.jpg)](https://i.ytimg.com/vi/2PKCOVqhngY/maxresdefault.jpg)
  
[https://www.youtube.com/watch?v=2PKCOVqhngY](https://www.youtube.com/watch?v=2PKCOVqhngY)