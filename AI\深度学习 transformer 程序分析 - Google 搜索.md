---
DocFlag:
  - Reference
  - ToBeTested
Updated: 2023-03-23T22:26
tags:
  - AI->-Programming
  - AI->-ToDO
Created: 2023-03-18T11:28
---
### [深度学习基础-9.1 Transformer模型（下）-网易公开课](https://open.163.com/newview/movie/free?pid=PHGR3GT03&mid=LHGR3I4VP)
[163.comhttps://open.163.com › movie › free](https://open.163.com/newview/movie/free?pid=PHGR3GT03&mid=LHGR3I4VP)
2._分析_与建模一(法1)人类发展... 1459播放.
### [DeepMind发文详解Transformer机器学习架构 - 麻省理工科技评论](https://www.mittrchina.com/news/detail/10903)
[mittrchina.comhttps://www.mittrchina.com › detail](https://www.mittrchina.com/news/detail/10903)
Jul 26, 2022 — 伪代码的语法没有具体标准，因为它并不是用于可执行_程序_。伪代码的主要意义在于使算法被各种编程语言更容易地表示。 不过，不知什么原因，_深度学习_ ...
### [深度学习基础| 超详细逐步图解Transformer - 简书](https://www.jianshu.com/p/052902aa9ee4)
[https://www.jianshu.com › ...](https://www.jianshu.com/p/052902aa9ee4)
Nov 5, 2021 — _深度学习_基础| RNN家族全面_解析_. _深度学习_基础| ... 熬了一晚上，我从零实现了_Transformer_模型，把代码讲给你听 ... _深度学习_地震去噪入门（附_程序_）.
### [3行代码就能可视化Transformer的奥义| 开源 - 腾讯云](https://cloud.tencent.com/developer/article/1840985)
[Tencenthttps://cloud.tencent.com › article](https://cloud.tencent.com/developer/article/1840985)
Jul 1, 2021 — 这时的_Transformer_网络在一阶段的逻辑公式中作为输入时，能够_学习_并展现出一种多步骤的逻辑推理。 这就对研究团队产生了启发：能不能编写一种_程序_，对 ...
### [Transformers+世界模型，竟能拯救深度强化学习？ - 51CTO](https://www.51cto.com/article/717932.html)
[51cto.comhttps://www.51cto.com › article](https://www.51cto.com/article/717932.html)
Sep 4, 2022 — 但最近，康奈尔大学有研究人员，正试着用_Transformers_将强化_学习_与世界 ... _Transformer_很快成为专注于_分析_和预测文本的单词识别等应用_程序_的引领者。
### [保姆级教程：图解Transformer - Jack Cui](https://cuijiahua.com/blog/2021/01/dl-basics-3.html)
[cuijiahua.comhttps://cuijiahua.com › dl-basics-3](https://cuijiahua.com/blog/2021/01/dl-basics-3.html)
### [深度学习-Transformer实战系列（2021最新） - 经管之家](https://bbs.pinggu.org/thread-10682067-1-1.html)
[pinggu.orghttps://bbs.pinggu.org › thread-106...](https://bbs.pinggu.org/thread-10682067-1-1.html)
课程主要包括四大模块： 1.算法讲解； 2.论文_分析_； 3.源码解读； 4.实战应用。 课程整体风格通俗 ...
### [从Python到水一篇AI论文（核心or Sci三区+）（目录） - 博客园](https://www.cnblogs.com/nickchen121/p/16470443.html)
[https://www.cnblogs.com › nickche...](https://www.cnblogs.com/nickchen121/p/16470443.html)
Jul 12, 2022 — ... 第五篇_深度学习_（待续）; 第六篇Pytorch（待续）; 第七篇_Transformer_ ... 018 人生中第一次用Python 写的一个小_程序__猜年龄（再次强调，重视基础 ...