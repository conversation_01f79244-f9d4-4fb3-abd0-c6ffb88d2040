---
Updated: 2024-03-08T09:36
tags:
  - AI->-Embedding
  - AI->-Theory
  - AI->-Voice
URL: https://zhuanlan.zhihu.com/p/387483956?utm_id=0
Created: 2024-03-08T07:36
---
## 一、概述
**谱聚类（spectral clustering）**是一种广泛使用的聚类算法，比起传统的K-Means算法，谱聚类对数据分布的适应性更强，聚类效果也很优秀，同时聚类的计算量也小很多。
谱聚类是从图论中演化出来的算法，后来在聚类中得到了广泛的应用。它的主要思想是把所有的数据看做空间中的点，这些点之间可以用边连接起来。距离较远的两个点之间的边权重值较低，而距离较近的两个点之间的边权重值较高，通过对所有数据点组成的图进行切图，让切图后不同的子图间边权重和尽可能的低，而子图内的边权重和尽可能的高，从而达到聚类的目的。
## 二、基础概念
### 1. 无向权重图
对于一个图 G ，我们一般用点的集合 V 和边的集合 E 来描述。即为 G(V,E) 。其中 V 即为我们数据集里面所有的点 (v_1,v_2,...v_n) 。对于 V 中的任意两个点，可以有边连接，也可以没有边连接。我们定义权重 w_{ij} 为点 v_i 和点 v_j 之间的权重。由于我们是无向图，所以 w_{ij}=w_{ji} 。
对于图中的任意一个点 v_i ，它的度 d_i 定义为和它相连的所有边的权重之和，即
​ d_i=\Sigma_{j-1}^nw_{ij}
利用每个点度的定义，我们可以得到一个 n*n 的度矩阵 D ，它是一个对角矩阵，只有主对角线有值，对应第 i 行的第 i 个点的度数，定义如下：
[![](https://pic3.zhimg.com/80/v2-70003ceebaec9496a14f1e06b2abe246_720w.webp)](https://pic3.zhimg.com/80/v2-70003ceebaec9496a14f1e06b2abe246_720w.webp)
### 2. 相似矩阵
利用所有点之间的权重值，我们可以得到图的邻接矩阵 W ，它也是一个 n*n 的矩阵，第 i 行的第 j 个值对应我们的权重 w_{ij} 。在谱聚类中，基本思想是，距离较远的两个点之间的边权重值较低，而距离较近的两个点之间的边权重值较高。
构建邻接矩阵 W 的方法有三类。 ϵ -邻近法， K 邻近法和全连接法。
- ϵ **邻近法**：设置了一个距离阈值 ϵ ，然后用欧式距离 s_{ij} 度量任意两点 x_i 和 x_j 的距离。即相似矩阵的 s_{ij}=||x_i−x_j||_2^2 _,_ 然后根据 s_{ij} 和 ϵ 的大小关系，来定义邻接矩阵 W 如下：
​从上式可见，两点间的权重要不就是 \epsilon ，要不就是0，没有其他的信息了。距离远近度量很不精确，因此在实际应用中，我们很少使用 \epsilon -邻近法。
- **K-邻近法**：利用KNN算法遍历所有的样本点，取每个样本最近的 k 个点作为近邻，只有和样本距离最近的 k 个点之间的 w_{ij}>0 。但是这种方法会造成重构之后的邻接矩阵 W 非对称，我们后面的算法需要对称邻接矩阵。为了解决这个问题，一般采取下面两种方法之一：
​ 第一种K邻近法是只要一个点在另一个点的K近邻中，则保留 s_{ij}
​ 第二种K邻近法是必须两个点互为K近邻中，才能保留 s_{ij}
- **全连接法**：相比前两种方法，第三种方法所有的点之间的权重值都大于0，因此称之为全连接法。可以选择不同的核函数来定义边权重，常用的有多项式核函数，高斯核函数和Sigmoid核函数。最常用的是高斯核函数，此时相似矩阵和邻接矩阵相同：
​ w_{ij}=s_{ij}=exp(-\frac{||x_i−x_j||_2^2}{2\sigma^2})
在实际的应用中，使用第三种全连接法来建立邻接矩阵是最普遍的，而在全连接法中使用高斯径向核RBF是最普遍的。
### 3. 拉普拉斯矩阵
拉普拉斯矩阵 L=D−W 。 D 即度矩阵，它是一个对角矩阵。而 W 即邻接矩阵。
拉普拉斯矩阵有一些很好的性质：
1. 拉普拉斯矩阵是对称矩阵，这可以由 D 和 W 都是对称矩阵而得。
2. 由于拉普拉斯矩阵是对称矩阵，则它的所有的特征值都是实数。
3. 对于任意的向量 f ，我们有 f^TLf=\frac{1}{2}\Sigma_{i,j=1}^nw_{ij}(f_i-f_j)^2 ，这个由拉普拉斯矩阵的定义可以得到。
4. 拉普拉斯矩阵是半正定的，且对应的 n 个实数特征值都大于等于0，即 0=\lambda_1\leq\lambda_2\leq...\leq\lambda_n ，且最小的特征值为0，这个由性质3可以得出。
### 4. 无向图切图
对于无向图 G 的切图，我们的目标是将图 G(V,E) 切成相互没有连接的k个子图，每个子图点的集合为： A_1,A_2,..A_k ，它们满足 A_i∩A_j=∅ ，且 A_1∪A_2∪...∪A_k=V 。
对于任意两个子图点的集合 A,B⊂V ， A∩B=∅ ，我们定义 A 和 B 之间的切图权重为：
​ W(A,B)=\Sigma_{i\in{A},j\in{B}}w_{ij}
那么对于k个子图点的集合： A_1,A_2,..A_k ，我们定义切图 cut 为：
​ cut(A_1,A_2,..A_k)=\frac{1}{2}\Sigma_{i=1}^kW(A_i,\bar{A_i})
其中 \bar{A_i} 为 A_i 的补集，即除 A_i 子集外其他 V 的子集的并集。
那么如何切图可以让子图内的点权重和高，子图间的点权重和低呢？一个自然的想法就是最小化 cut(A_1,A_2,...A_k) ，但是可以发现，这种极小化的切图存在问题，如下图：
这样的切割结果不是我们想要的分割结果，虽然分割的 cut(A_1,A_2,...A_k) 最小，但是会造成分割出很多单个离散的样本点作为一类，分割的类别不均匀。
## 三、切图聚类
在谱聚类中，为了避免最小切图导致的切图效果不佳，我们需要对每个子图的规模做出限定，一般来说，有两种切图方式，一种是**RatioCut**，另一种是**Ncut**。
### 1. RatioCut切图：
在RatioCut中，对每个切图，不光考虑最小化 cut(A_1,A_2,...A_k) ，还同时考虑最大化每个子图点的个数，即：
​ RatioCut(A_1,A_2,...A_k)=\frac{1}{2}\Sigma_{i=1}^k\frac{W(A_i,\bar{A_i})}{|A_i|}
接下来需要最小化这个 RatioCut 函数。我们引入指示向量 h_{j}\in{h_1,h_2,...h_k},j=1,2,...k ，对于任意一个向量 h_j , 它是一个n维向量（n为样本数），我们定义 h_{ij} 为
那么对于 h_i^TLh_i ，由拉普拉斯矩阵的性质3有：
​ h_i^TLh_i=\frac{1}{2}\Sigma_{m=1}\Sigma_{n=1}w_{mn}(h_{im}-h_{in})^2
代入指示向量的定义，化简后可得：
h_i^TLh_i=\frac{cut(A_i,\bar{A_i})}{|A_i|}
可以看出，对于某一个子图 i ，它的RatioCut对应于 h^T_iLh_i ，那么对于k个子图，RatioCut函数表达式为：
RatioCut(A_1,A_2,...A_k)=\Sigma_{i=1}^kh_i^TLh_i=\Sigma_{i=1}^k(H^TLH)_{ii}=tr(H^TLH)
其中 tr(H^TLH) 为矩阵的迹。也就是说，RatioCut切图实际上就是最小化 tr(H^TLH) 。
对于 tr(H^TLH) 中每一个优化子目标 h^T_iLh_i ，其中 h 是单位正交基， L为对称矩阵。我们要找到最小的L的特征值，而对于 tr(H^TLH)=∑_{i=1}^kh^T_iLh_i ，则我们的目标就是找到k个最小的特征值，也就是说，此时我们进行了维度规约，将维度从n降到了k，从而近似可以解决这个问题。
通过找到L的最小的k个特征值，可以得到对应的k个特征向量，这k个特征向量组成一个 n*k 维度的矩阵，即为我们的H。一般需要对H矩阵按行做标准化，即
h_{ij}^*=\frac{h_{ij}}{(\Sigma_{t=1}^kh_{it}^2)^{1/2}}
由于我们在使用维度规约的时候损失了少量信息，导致得到的优化后的指示向量 h 对应的H现在不能完全指示各样本的归属，因此一般在得到 n*k 维度的矩阵H后还需要对每一行进行一次传统的聚类，比如使用K-Means聚类。
### 2. NCut切图：
Ncut切图和RatioCut切图很类似，但是把Ratiocut的分母 |A_i| 换成 vol(A_i) ，即 A_i 类别中所有边的权重和。由于子图样本的个数多并不一定权重就大，我们切图时基于权重也更合我们的目标，因此一般来说Ncut切图优于RatioCut切图。
NCut(A_1,A_2,...A_k)=\frac{1}{2}\Sigma_{i=1}^k\frac{W(A_i,\bar{A_i})}{vol(A_i)}
Ncut切图使用了子图权重 \frac{1}{\sqrt{vol(A_i)}} 来标示指示向量h，定义如下：
对于 h_i^TLh_i 有：
h_i^TLh_i=\frac{1}{2}\Sigma_{m=1}\Sigma_{n=1}w_{mn}(h_{im}-h_{in})^2=\frac{cut(A_i,\bar{A_i})}{vol(A_i)}
推导过程与RatioCut相同。也就是说，我们的优化目标依然是
NCut(A_1,A_2,...A_k)=\Sigma_{i=1}^kh_i^TLh_i=\Sigma_{i=1}^k(H^TLH)_{ii}=tr(H^TLH)
但是此时我们的 H^TH≠I ，而是 H^TDH=I 。推导如下：
h_i^TDh_i=\Sigma_{j=1}^nh_{ij}^2d_j=\frac{1}{vol(A_i)}\Sigma_{j\in A_i}d_j=\frac{1}{vol(A_i)}vol(A_i)
此时我们的H中的指示向量 h 并不是标准正交基，所以在RatioCut里面的降维思想不能直接用。怎么办呢？其实只需要将指示向量矩阵H做一个小小的转化即可。
我们令 H=D^{−1/2}F ，则 H^TLH=F^TD^{−1/2}LD^{−1/2}F ，可以发现这个式子和RatioCut基本一致，只是中间的L变成了 D^{−1/2}LD^{−1/2} ，这样我们就可以继续按照RatioCut的思想，求出 D^{−1/2}LD^{−1/2} 的最小的前k个特征值，然后求出对应的特征向量，并标准化，得到最后的特征矩阵F,最后对F进行一次传统的聚类（比如K-Means）即可。
一般来说， D^{−1/2}LD^{−1/2} 相当于对拉普拉斯矩阵L做了一次标准化，即 \frac{L_{ij}}{\sqrt{d_i*d_j}} 。
## 四、算法流程
一般来说，谱聚类主要的注意点为相似矩阵的生成方式，切图的方式以及最后的聚类方法。最常用的相似矩阵的生成方式是基于高斯核距离的全连接方式，最常用的切图方式是Ncut。而到最后常用的聚类方法为K-Means。下面以Ncut总结谱聚类算法流程。
输入：样本集 D=(x_1,x_2,...,x_n) ，相似矩阵的生成方式，降维后的维度 k_1 ，聚类方法，聚类后的维度 k_2 。
输出： 簇划分 C(c_1,c_2,...c_{k2})
1. 根据输入的相似矩阵的生成方式构建样本的相似矩阵S。
2. 根据相似矩阵S构建邻接矩阵W，构建度矩阵D。
3. 计算出拉普拉斯矩阵L。
4. 构建标准化后的拉普拉斯矩阵 D^{−1/2}LD^{−1/2} 。
5. 计算 D^{−1/2}LD^{−1/2} 最小的 k_1 个特征值所各自对应的特征向量 f 。
6. 将各自对应的特征向量 f 组成的矩阵按行标准化，最终组成 n*k_1 维的特征矩阵F。
7. 对F中的每一行作为一个 k_1 维的样本，共n个样本，用输入的聚类方法进行聚类，聚类维数为 k_2 。
8. 得到簇划分 C(c_1,c_2,...c_{k2}) 。
## 五、算法总结
谱聚类算法是一个使用起来简单，但是理解清楚却不是那么容易的算法，它需要你有一定的数学基础。
谱聚类算法的主要优点有：
1. 谱聚类只需要数据之间的相似度矩阵，因此对于处理稀疏数据的聚类很有效。这点传统聚类算法比如K-Means很难做到。
2. 由于使用了降维，因此在处理高维数据聚类时的复杂度比传统聚类算法好。
谱聚类算法的主要缺点有：
1. 如果最终聚类的维度非常高，则由于降维的幅度不够，谱聚类的运行速度和最后的聚类效果均不好。
2. 聚类效果依赖于相似矩阵，不同的相似矩阵得到的最终聚类效果可能很不同。
## 六、Python实现
```Plain
def calculate_w_ij(a,b,sigma=1):
    w_ab = np.exp(-np.sum((a-b)**2)/(2*sigma**2))
    return w_ab
# 计算邻接矩阵
def Construct_Matrix_W(data,k=5):
    rows = len(data) # 取出数据行数
    W = np.zeros((rows,rows)) # 对矩阵进行初始化：初始化W为rows*rows的方阵
    for i in range(rows): # 遍历行
        for j in range(rows): # 遍历列
            if(i!=j): # 计算不重复点的距离
                W[i][j] = calculate_w_ij(data[i],data[j]) # 调用函数计算距离
        t = np.argsort(W[i,:]) # 对W中进行行排序，并提取对应索引
        for x in range(rows-k): # 对W进行处理
            W[i][t[x]] = 0
    W = (W+W.T)/2 # 主要是想处理可能存在的复数的虚部，都变为实数
    return W
def Calculate_Matrix_L_sym(W): # 计算标准化的拉普拉斯矩阵
    degreeMatrix = np.sum(W, axis=1) # 按照行对W矩阵进行求和
    L = np.diag(degreeMatrix) - W # 计算对应的对角矩阵减去w
    # 拉普拉斯矩阵标准化，就是选择Ncut切图
    sqrtDegreeMatrix = np.diag(1.0 / (degreeMatrix ** (0.5))) # D^(-1/2)
    L_sym = np.dot(np.dot(sqrtDegreeMatrix, L), sqrtDegreeMatrix) # D^(-1/2) L D^(-1/2)
    return L_sym
def normalization(matrix): # 归一化
    sum = np.sqrt(np.sum(matrix**2,axis=1,keepdims=True)) # 求数组的正平方根
    nor_matrix = matrix/sum # 求平均
    return nor_matrix
W = Construct_Matrix_W(your_data) # 计算邻接矩阵
L_sym = Calculate_Matrix_L_sym(W) # 依据W计算标准化拉普拉斯矩阵
lam, H = np.linalg.eig(L_sym) # 特征值分解
t = np.argsort(lam) # 将lam中的元素进行排序，返回排序后的下标
H = np.c_[H[:,t[0]],H[:,t[1]]] # 0和1类的两个矩阵按行连接，就是把两矩阵左右相加，要求行数相等。
H = normalization(H) # 归一化处理
model = KMeans(n_clusters=20) # 新建20簇的Kmeans模型
model.fit(H) # 训练
labels = model.labels_ # 得到聚类后的每组数据对应的标签类型
res = np.c_[your_data,labels] # 按照行数连接data和labels
```