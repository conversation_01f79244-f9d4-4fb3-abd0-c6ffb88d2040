---
DocFlag:
  - Reference
  - Tested
Updated: 2024-07-31T13:30
tags:
  - AI->-GPT4
  - AI->-YouTube
  - AI/OpenAI
URL: https://vimeo.com/990334325/56b552bc7a?&signup=true#_=_
Created: 2024-07-30T18:29
Reference:
  - /opt/app/researcher/OpenAIAgents.py
---
[![](https://i.vimeocdn.com/video/1906110802-004124f88ff9c5cbf18c391ea219cb5e1e2d4701c07bad773e1a0b9590d6cb73-d?f=webp)](https://i.vimeocdn.com/video/1906110802-004124f88ff9c5cbf18c391ea219cb5e1e2d4701c07bad773e1a0b9590d6cb73-d?f=webp)
  
# Simple Function Call
```Python
from openai import OpenAI
# from demo_util import color
import json
import inspect
client = OpenAI()
# === Demo Loop ===
model = "gpt-4o-mini"
system_message = (
    "You are a customer support agent for ACME Inc."
    "Always answer in a sentence or less."
    "Follow the following routine with the user:"
    "1. First, ask probing questions and understand the user's problem deeper.\n"
    " - unless the user has already provided a reason.\n"
    "2. Propose a fix (make one up).\n"
    "3. ONLY if not satisfied, offer a refund.\n"
    "4. If accepted, search for the ID and then execute refund."
    ""
)
def look_up_item(search_query):
    """Use to find item ID.
    Search query can be a description or keywords."""
    item_id = "item_132612938"
    print(color("Found item:", "green"), item_id)
    return item_id
def execute_refund(item_id, reason="not provided"):
    print(color("\n\n=== Refund Summary ===", "green"))
    print(color(f"Item ID: {item_id}", "green"))
    print(color(f"Reason: {reason}", "green"))
    print("=====================\n")
    print(color("Refund execution successful!", "green"))
    return "success"
def escalate_to_human(summary):
    """Only call this if explicitly asked to."""
    print(color("Escalating to human agent...", "red"))
    print("\n=== Escalation Report ===")
    print(f"Summary: {summary}")
    print("========================\n")
    exit()
    
tools = [execute_refund, look_up_item, escalate_to_human]
def color(text, color_name):
    color_codes = {
        "blue": "\033[94m",
        "yellow": "\033[93m",
        "magenta": "\033[95m",
        # Add more colors as needed
    }
    reset_code = "\033[0m"
    
    if color_name in color_codes:
        return f"{color_codes[color_name]}{text}{reset_code}"
    else:
        return text  # Return uncolored text if color not found
    
def function_to_schema(func) -> dict:
    type_map = {
        str: "string",
        int: "integer",
        float: "number",
        bool: "boolean",
        list: "array",
        dict: "object",
        type(None): "null",
    }
    try:
        signature = inspect.signature(func)
    except ValueError as e:
        raise ValueError(
            f"Failed to get signature for function {func.__name__}: {str(e)}"
        )
    parameters = {}
    for param in signature.parameters.values():
        try:
            param_type = type_map.get(param.annotation, "string")
        except KeyError as e:
            raise KeyError(
                f"Unknown type annotation {param.annotation} for parameter {param.name}: {str(e)}"
            )
        parameters[param.name] = {"type": param_type}
    required = [
        param.name
        for param in signature.parameters.values()
        if param.default == inspect._empty
    ]
    
    return {
        "type": "function",
        "function": {
            "name": func.__name__,
            "description": func.__doc__ or "",
            "parameters": {
                "type": "object",
                "properties": parameters,
                "required": required,
            },
        },
    }
def execute_tool_call(tool_call, tools_map):
    name = tool_call.function.name
    args = json.loads(tool_call.function.arguments)
    
    print(color("Assistant:", "yellow"), color(f"{name}({args})", "magenta"))
    
    # call corresponding function with provided arguments
    return tools_map[name](**args)
           
def run_full_turn(system_message, tools, messages):
    num_init_messages = len(messages)
    messages = messages.copy()
    
    while True:
        
        # turn python functions into tools and save a reverse map
        tool_schemas = [function_to_schema(tool) for tool in tools]
        tools_map = {tool.__name__: tool for tool in tools}
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "system", "content": system_message}] + messages,
            tools=tool_schemas or None,
        )
        message = response.choices[0].message
        messages.append(message)
        if message.content:
            print(color("Assistant:", "yellow"), message.content)
        if not message.tool_calls:
            break
        
        # === 2. handle tool calls ===
        for tool_call in message.tool_calls:
            result = execute_tool_call(tool_call, tools_map)
            
            result_message = {
                "role": "tool",
                "tool_call_id": tool_call.id,
                "content": result,
            }
            messages.append(result_message)
            
    # === 3. return new messages ===
    return messages[num_init_messages:]
messages = []
while True:
    user = input(color("User: ", "blue") + "\033[90m")
    messages.append({"role": "user", "content": user})
    
    new_messages = run_full_turn(system_message, tools, messages)
    messages.extend(new_messages)
```

# Prompt Cache
```python
import asyncio
from pydantic import BaseModel
from openai import AsyncOpenAI

client = AsyncOpenAI()

class TextClassification(BaseModel):
    category: str
    confidence: float

async def classify_text():
    with open('text.txt', 'r', encoding='utf-8') as file:
        system_message = file.read()
    
    completion = await client.beta.chat.completions.parse(
        model="gpt-40",
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": "classify the text"},
        ],
        response_format=TextClassification
    )

    print(completion.choices[0].message.parsed)
    print(completion.usage.prompt_tokens_details.cached_tokens)

async def main():
    # Run the first task immediately
    first_task = asyncio.create_task(classify_text())
    
    # Create the remaining tasks with a delay
    delayed_tasks = [
        asyncio.create_task(delayed_classify(i * 0.5)) for i in range(1, 5)
    ]
    
    # Gather and await all tasks
    await asyncio.gather(first_task, *delayed_tasks)

async def delayed_classify(delay):
    print("start")
    await asyncio.sleep(delay)
    await classify_text()

if __name__ == "__main__":
    asyncio.run(main())
```
# Agent Code
```Python
from openai import OpenAI
# from demo_util import color
import json
import inspect
from pydantic import BaseModel
class Agent(BaseModel):
    name: str = "Agent"
    model: str = "gpt-4o"
    instructions: str= "You are a helpful agent."
    tools: list = []
    
class Response(BaseModel):
    messages: list
client = OpenAI()
# === Demo Loop ===
def look_up_item(search_query):
    """Use to find item ID.
    Search query can be a description or keywords."""
    item_id = "item_132612938"
    print(color("Found item:", "green"), item_id)
    return item_id
def execute_refund(item_id, reason="not provided"):
    print(color("\n\n=== Refund Summary ===", "green"))
    print(color(f"Item ID: {item_id}", "green"))
    print(color(f"Reason: {reason}", "green"))
    print("=====================\n")
    print(color("Refund execution successful!", "green"))
    return "success"
def escalate_to_human(summary):
    """Only call this if explicitly asked to."""
    print(color("Escalating to human agent...", "red"))
    print("\n=== Escalation Report ===")
    print(f"Summary: {summary}")
    print("========================\n")
    exit()
    
def transfer_to_sales_agent():
    """User for anything sales or buying related."""
    return sales_agent
def transfer_to_issues_and_repairs():
    """User for issues, repairs, or refunds."""
    return issues_and_repairs_agent
def transfer_back_to_triage():
    """Call this if the user brings up a topic outside of your purview,
    including escalating to human."""
    return triage_agent
triage_agent = Agent(
    name="Triage Agent",
    instructions=(
        "You are a customer service bot for ACME Inc. "
        "Introduce yourself. Always be very brief. "
        "Gather information to direct the customer to the right department. "
        "But make your questions subtle and natural."
    ),
    tools=[transfer_to_sales_agent, transfer_to_issues_and_repairs, escalate_to_human]
)
def execute_order(product, price: int):
    """Price should be in USD."""
    print("\n\n=== Order Summary ===")
    print(f"Product: {product}")
    print(f"Price: ${price}")
    print("===================\n")
    confirm = input("Confirm order? y/n: ").strip().lower()
    if confirm == "y":
        print(color("Order execution successful!", "green"))
        return "Success"
    else:
        print(color("Order cancelled!", "red"))
        return "User cancelled order."

sales_agent = Agent(
    name="Sales Agent",
    instructions=(
        "You are an agent for ACME Inc. "
        "Introduce yourself. Always be very brief. "
        "Gather information to direct the customer to the right department. "
        "But make your questions subtle and natural."
    ),
    tools=[transfer_back_to_triage, execute_order]
)
issues_and_repairs_agent = Agent(
    name="Issues and Repairs Agent",
    instructions=(
        "You are an Issues and Repairs Agent agent for ACME Inc."
        "Always answer in a sentence or less."
        "Introduce yourself first (company, role), and immediately start this routine with the user:"
        "1. First, ask specific, probing questions and understand the user's problem deeper.\n"
        " - unless the user has already provided a reason.\n"
        "2. Propose a fix (make one up). Wait for the user to try it.\n"
        "3. ONLY if not satesfied, offer a refund.\n"
        "4. If accepted, search for the ID and then execute refund."
    ),
    tools=[execute_refund, look_up_item, escalate_to_human]
)

def color(text, color_name):
    color_codes = {
        "blue": "\033[94m",
        "yellow": "\033[93m",
        "magenta": "\033[95m",
        # Add more colors as needed
    }
    reset_code = "\033[0m"
    
    if color_name in color_codes:
        return f"{color_codes[color_name]}{text}{reset_code}"
    else:
        return text  # Return uncolored text if color not found
    
def function_to_schema(func) -> dict:
    type_map = {
        str: "string",
        int: "integer",
        float: "number",
        bool: "boolean",
        list: "array",
        dict: "object",
        type(None): "null",
    }
    try:
        signature = inspect.signature(func)
    except ValueError as e:
        raise ValueError(
            f"Failed to get signature for function {func.__name__}: {str(e)}"
        )
    parameters = {}
    for param in signature.parameters.values():
        try:
            param_type = type_map.get(param.annotation, "string")
        except KeyError as e:
            raise KeyError(
                f"Unknown type annotation {param.annotation} for parameter {param.name}: {str(e)}"
            )
        parameters[param.name] = {"type": param_type}
    required = [
        param.name
        for param in signature.parameters.values()
        if param.default == inspect._empty
    ]
    
    return {
        "type": "function",
        "function": {
            "name": func.__name__,
            "description": func.__doc__ or "",
            "parameters": {
                "type": "object",
                "properties": parameters,
                "required": required,
            },
        },
    }
def execute_tool_call(tool_call, tools_map, current_name):
    name = tool_call.function.name
    args = json.loads(tool_call.function.arguments)
    
    print(color(f"{current_name}:", "yellow"), color(f"{name}({args})", "magenta"))
    
    # call corresponding function with provided arguments
    return tools_map[name](**args)
           
def run_full_turn(agent, messages):
    
    current_agent = agent
    num_init_messages = len(messages)
    messages = messages.copy()
    
    while True:
        
        # turn python functions into tools and save a reverse map
        tool_schemas = [function_to_schema(tool) for tool in current_agent.tools]
        tools_map = {tool.__name__: tool for tool in current_agent.tools}
        response = client.chat.completions.create(
            model=current_agent.model,
            messages=[{"role": "system", "content": current_agent.instructions}] + messages,
            tools=tool_schemas or None,
        )
        message = response.choices[0].message
        messages.append(message)
        if message.content:
            print(color(f"{current_agent.name}:",  "yellow"), message.content)
        if not message.tool_calls:
            break
        
        # === 2. handle tool calls ===
        for tool_call in message.tool_calls:
            result = execute_tool_call(tool_call, tools_map, current_agent.name)
            
            if type(result) is Agent:
                current_agent = result
                result = f"transformed agent: {current_agent.name}"
            
            result_message = {
                "role": "tool",
                "tool_call_id": tool_call.id,
                "content": result,
            }
            messages.append(result_message)
            
    # === 3. return new messages ===
    return messages[num_init_messages:]

messages = []
while True:
    user = input(color("User: ", "blue") + "\033[90m")
    messages.append({"role": "user", "content": user})
    
    new_messages = run_full_turn(triage_agent, messages)
    messages.extend(new_messages)
```
  
## ==Privacy Preference Center==
==When you visit any website, it may store or retrieve information on your browser, mostly in the form of cookies. This information might be about you, your preferences or your device and is mostly used to make the site work as you expect it to. The information does not usually directly identify you, but it can give you a more personalized web experience. Because we respect your right to privacy, you can choose not to allow some types of cookies. Click on the different category headings to find out more and change our default settings. However, blocking some types of cookies may impact your experience of the site and the services we are able to offer.====  
  
==[==More information==](https://cookiepedia.co.uk/giving-consent-to-cookies)
### ==Manage Consent Preferences==
### ==Performance Cookies==
==Performance Cookies==
==These cookies allow us to count visits, identify traffic sources and understand how our services are used to measure and improve performance. If you do not allow these cookies, we will not know when you visited our site and we will not be able to monitor its performance.==
### ==Targeting Cookies==
==Targeting Cookies==
==Our advertising partners may set these cookies on our site. They can be used to show you our ads on third-party sites, measure the effectiveness of those ads, or prevent ads from being shown to you. They do not store personal information directly, but rather uniquely identify your browser and device. If you do not allow these cookies, you will not receive targeted advertising.==
### ==Functional Cookies==
==Functional Cookies==
==These cookies improve functionality and personalization. They may be set by us or by other third-party providers whose services we add to our pages. If you do not allow these cookies, some or all of these additional functions may not work properly.==
### ==Strictly Necessary Cookies==
==Always Active==
==These cookies are necessary for the website to function and cannot be disabled. You may be able to configure your Internet browser to block strictly necessary cookies. However, if you block cookies in this category, which do not collect or store any personal information, this could affect some parts of the website and cause it to function incorrectly.==