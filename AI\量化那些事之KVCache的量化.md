---
Updated: 2024-05-11T18:01
tags:
  - AI->-Theory
Created: 2024-05-11T18:01
---
==众所周不知，KVCache 会随着上下文加长而加长，每个 token 生成时都要读取整个 KVCache（==[==基础知识链接==](https://zhuanlan.zhihu.com/p/670515231)==）。在当下超长提示词以及超长上下文已经成为 AI 助手一个非常重要的产品属性的情况下，如何对基于 KVCache 的推理速度和推理服务进行优化就变得非常重要了。==
==我在之前有一个类似的回答：==[==200k输入输出的大模型如何做训练与推理？==](https://www.zhihu.com/question/643748680/answer/3398661229) ==中间简单的写了写====**超长提示词**====情况下== ==**KVCache 几乎形同参数**====了的特点。那么，有一个非常简单直接的思路就是用量化参数一样的方法量化 KVCache。==
==所以今天就来写写 KVCache 量化的一些最新工作。==
==模型参数在量化时有个特点，就是可以一次量次无数次使用。但是相比模型原始还可以进行多个并发请求的合并共享参数数据来说，KVCache 是紧密依赖于当前请求线程的，不同请求依赖的前续提示词是完全不同的，是无法合并的。量化过程在一次完整的请求过程中一次量化多次使用，但是其他请求就不能复用了，因此面临的访存墙问题将更加严重。从这个角度出发，我们会发现，相比优化 MLP 矩阵乘法部分的量化，KVCache 部分其实是更加紧迫的，尤其是超长提示词长度的时候。==
==看一下定量的数据，和== [==Numbers every LLM developer should know==](https://link.zhihu.com/?target=https%3A//github.com/ray-project/llm-numbers%231-mb-gpu-memory-required-for-1-token-of-output-with-a-13b-parameter-model) ==中的数据类似，估计一个 7B 参数模型为序列中的每个 tensor 消耗近 1MB 的状态。在具有 24GB RAM 的 4096 GPU上，粗略计算表明，由于存储了 13GB 的模型参数后还剩下 11GB，这部分显存一般就是给推理过程留下来做 KVCache 用的了。那么一个 token 的 KV 参数量都是 1x4096 的，f16 下就是 8k * 2，200k 的 token 输入下就是 3200M 也就是 3.2G 的显存量，如果输出时还需要再存 200k 的序列的话，那么需要被 cache 的显存量就翻一倍。这看似好像是够的，但是在服务系统多请求情况下，每个 request 对应模型参数虽然一样，但是 cache 都不一样，不是很大的 batch size 就可以挤爆显存了。挤爆显存之后会出现灾难性的性能损耗，因为存不下的 cache 会比替换到内存甚者磁盘，那么不停地丢弃 HBM 中的数据去读取内存只会让延迟呈指数级爆炸。==
==老规矩还是几篇相关的文章放在一起读，“====**对比学习**====”。==
==都是近期的文章，这说明是业界一股脑的都同时发现了这个优化方向。==
1. ==KVQuant: Towards 10 Million Context Length LLM Inference with KV Cache Quantization. From== ==**UC Berkeley**====.== [==http://arxiv.org/abs/2401.18079==](https://link.zhihu.com/?target=http%3A//arxiv.org/abs/2401.18079)
2. ==QAQ: Quality Adaptive Quantization for LLM KV Cache，From Nanjing Univ.== [==http://arxiv.org/abs/2403.04643==](https://link.zhihu.com/?target=http%3A//arxiv.org/abs/2403.04643)
3. ==Atom: Low-bit Quantization for Efficient and Accurate LLM Serving，23 年 11 月的文章，==[==http://arxiv.org/abs/2310.19102==](https://link.zhihu.com/?target=http%3A//arxiv.org/abs/2310.19102)==，Tianqi Chen 团队出品，这篇文章已经把 KVCache 的量化过程完整的实现了一遍，是 KVQuant 文章的引文。==
4. ==Kivi: Plug-and-play 2bit kv cache quantization with streaming asymmetric quantization.== [==https://arxiv.org/abs/2402.02750==](https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2402.02750) ==24 年 2 月份的文章，已经非常新了，但也是 KVQuant 论文的引文。==
5. ==IntactKV: Improving Large Language Model Quantization by Keeping Pivot Tokens Intact.== [==https://arxiv.org/abs/2403.01241==](https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2403.01241) ==24 年 3 月份的文章，都很新。作者来自，清华深研、华为诺亚、中科院自动化所、港中文。这篇是本文发布之后评论中有小伙伴提醒才发现，真的是论文读不过来啊。==
6. ==QServe: W4A8KV4 Quantization and System Co-design for Efficient LLM Serving.== [==https://arxiv.org/pdf/2405.04532==](https://link.zhihu.com/?target=https%3A//arxiv.org/pdf/2405.04532) ==24 年 5 月的，话说这篇博客最初发布都是在 4 月。。==
==更新日志：==
- ==4 月 16 日，第一版==
- ==4 月 23 日，增加 IntactKV==
- ==5 月 9 日，增加 Qserve==
### ==总体感觉是：==
==啃了一两周时间了，但是深感理解还是不到位，先说一下感受：==
==KIVI 方法最近相邻的 Cache 不量化，远的才量化，所以最简单直接且有效，符合 KISS 原则；IntactKV 也是不量化关键标记，也很简单直接有效；KVQuant 文章内容最丰富；QAQ 提出了一些不同的发现；ATOM 最早发布，并且实现的是 A4W4 的方案。==
### ==KVCache 优化的特点：==
==相对来说，KVCache 的量化优化方法是要复杂于 MLP 矩阵乘法部分的量化的。量化的====**目标**====是为了提高运行速度，以及减少对内存、显存的占用，同时量化方法需要非常有技巧才能让量化的结果不比之前的方法差太多。==
==所以这里就有一个重要的问题，====**KVCache 的优化与 MLP 的优化有什么区别**====？那么接下来一个问题就是 KVCache 部分的特点和数据 characteristic/distribution 都是什么？==
==而我们同时会更加关注的是上下文长度对优化过程的影响：对于短序列长度，内存消耗的主要贡献者是权重矩阵，因此最佳策略是最小化模型大小以减少内存消耗以及带宽需求。对于长序列长度，主要瓶颈就变成整个推理过程中缓存键（Key）和值（Value）激活所需的内存，也就相当于 KVCache 缓存的部分变成了某种意义上新的“权重”和“参数”。==
==另外就是 KVCache 的数据虽然形同参数，但是与离线的参数一次性量化好可以不停使用不同，KVCache 的数据是随时生成的，随后马上就进行量化过程的再进行存储和后续使用，虽然也是一次性量化好的，但是这个一次性是在整个推理过程中的，是占有一部分在线资源和延时的。==
==量化形式也是个很重要的问题，只量化 KVCache 的值计算还是使用 fp16 计算是比较常见的方式。==
### ==KVCache 的数据分布特点，Characteristic：==
==在 KVQuant 论文中，作者发现，在**处理长序列和较大的批量时，激活内存成为了主要的性能瓶颈，特别是在模型权重已经量化到较低精度的情况下。**对于LLaMA-7B模型，当序列长度达到128K时，KV缓存成为了主要的瓶颈。此外，如果模型权重已经被量化，即使在 32K 的序列长度下，KV 缓存也是主要的瓶颈。==
[![](https://pic1.zhimg.com/80/v2-6b7b27f45b8b1d7d233d8da25cbfee1c_720w.webp)](https://pic1.zhimg.com/80/v2-6b7b27f45b8b1d7d233d8da25cbfee1c_720w.webp)
==而在 QAQ 和 KIVI 中，作者也都发现 Key cache 和 Value cache 之间存在很大的区别，Key cache 量化难度远高于 value 。 KVQuant 采用的方法是在 Key 前面的 rope 前就完成量化，实际使用时则反量化之后再做一次 rope ；QAQ 是采用了混合 bit 数和全精度保留 outlier 的量化策略，KIVI 则是用两种不同的量化分组粒度策略完成不同的量化过程。==
### ==KVQuant ：==
==KVQuant 文章读起来还是很硬气的，至少从写作的角度出发看是这样的，第三章介绍方法一共八节，直观感觉就是其中至少有三节是可以单独拎出来做一篇文章的，虽然其中的方法也是多以借鉴为主。并且，本文在内容和写作方面都是很实在的，是非常值得一读的，可以单独拎出来的几个技术点中作者团队的问题的剖析和解决方法都是非常值得学习和借鉴的。==
==论文的 Appendix 有 17 个！里面有很多细节和消融实验，好猛，非常值得学习。==
==![<<{"width":690,"height":343}>>](data:image/svg+xml;utf8,====)==
==先看一下结果，主要还是在== ==**128k 的上下文长度**====上的对比，可以看到这个方法在 2、3、4bit 上的表现还是很牛的。这里 -1% 为后缀的是作者发现可以去除 1% 异常值的方法。==
==按文章第三章方法介绍的章节来：==
==3.1）Per-Channel Key Quantization 这个技术没有新鲜感，毕竟还有很多更小 block 的 quant 方法，不过在 KVCache 的量化过程中，还是需要实践才能知道实际的量化区域如何分布才是最优选择。而在 Atom 和 FlexGen 中使用的事 per-token 的量化粒度。这里我初读时的一个疑问就是，为什么要强调是对 Key 的量化，Value 部分不需要 per-channel 的量化吗？在 A16Wx 的方案中， Query 肯定是不需要量化的，它是作为输入 embedding 先和 Key 相乘进入 Softmax 之后再和 Value 相乘。这里文中给出的结论放在 Appendix D 中了，所以最后作者采用的事 per-channel 的 Key 和 per-token 的 Value 这样的组合。其实结合其他几篇文章来看，确实也都是采用了这个方法，原因是两者的异常值和数值分布完全不同。==
==3.2）Pre-RoPE Key 量化（Pre-RoPE Key Quantization），作者发现，在应用 RoPE 之前，Key 矩阵在特定通道中表现出结构化的异常值。然而，在应用 RoPE 之后，异常值通道的大小变得不那么一致，所以作者对 Key 采用 RoPE 之前的按通道进行的量化操作。反正存之前的 embedding 到时候再算一次 RoPE 就好了。具体的理论和消融实验放在 Appendix A 和 Appendix E 了。不知道这样的特点针对 Value 和 Query 有没有类似的特点？==
==![<<{"width":690,"height":256}>>](data:image/svg+xml;utf8,====)==
==3.3）nuqX：非均匀 KV 缓存量化（Non-Uniform KV Cache Quantization），作者发现，现有的均匀和非均匀量化方法（这个我在== [==笔记：Llama.cpp 代码浅析（四）：量化那些事==](https://zhuanlan.zhihu.com/p/672983861) ==中专门分析了啥是均匀和非均匀的量化方方）在量化标志放置方面都不理想。所以，会选择一种每层敏感性加权的非均匀数据类型来更好地表示分布的技术，其中使用了迭代的求解一个 k-means 问题的方法。这个方法我直观感受就是 llama.cpp 中使用的 k-Quant 方法（暂时存疑，还需要验证），所以不仅 KVCache 时候可以使用，其他的量化过程都可以使用。==
==3.4）向量级的密集和稀疏量化（Per-Vector Dense-and-Sparse Quantization），作者发现，KVCache 中的异常值会影响量化分辨率的效率，可以隔离一小部分数值异常值。那么====**什么叫做密集稀疏量化呢**====？简单说就是保持一定比例，这里是 1% 异常值不量化，并以全精度的形式将它们存储在一个另稀疏矩阵中。这样可以单独隔离每个向量中的异常值。这个方法我对它的效率持一定的保留意见，虽然它对 outlier 可能造成的精度影响降到了最低。==
==3.5）对于极低比特精度的情况，作者发现量化后的激活值可能会显著偏离它们对应原始值的分布。所以作者提出了一种轻量级的 Q-Norm 层来调整量化后的分布，使其更接近原始分布。作者说这个方法是从这篇文章中吸取的思路：==[==Norm Tweaking: High-performance Low-bit Quantization of Large Language Models==](https://link.zhihu.com/?target=http%3A//arxiv.org/abs/2309.02784)==。该方法更加考虑 calibration 数据对 weights 产生的数据分布变化。这里必须要注意的是，calibration 数据只能在离线时候用，在线时候没法承受这样的计算负担，所以这里才是用分布去修正。==
==3.6）在之前的研究中，已经表明模型倾向于将第一个token作为注意力汇聚点（Attention Sink），这意味着即使这些初始token在语义上并不重要，模型也会为它们分配较高的分数。所以针对这个特点，本文也展示了模型对初始token的扰动也是敏感的。所以采用了仅对序列中第一个token保持高精度的策略，这听起来是很直观的。不过这种非对称的结构不知道存储和计算单元是否足够友好了？==
==3.7）Offline Calibration versus Online Computation，激活量化的一个关键挑战在于，我们要么需要实时（on-the-fly）计算统计信息（这可能会很昂贵），要么需要使用离线校准数据（这可能会对准确性产生负面影响）。在线计算（online）与离线（offline）计算量化参数的选择问题，就是一个 tradeoff 了，不过最后作者选择了，离线计算统计信息是可取的，即在运行推理之前使用校准数据。==
==我认为，KVQuant 中对 KVCache 部分的优化抓住了 KVCache 中几个特定的问题，而不是照搬其他对参数量化过程的方法。==
### ==QAQ：==
==![<<{"width":690,"height":242}>>](data:image/svg+xml;utf8,====)==
==按作者给出的主要核心贡献来说：==
==首先，Key cache 和 value cache 对量化展现出不同的敏感性，并且== ==**key cache 对于量化更加敏感**====（这其实也是KVQuant 中要对 key 在 rope 前后进行对比的原因）。所以本文给出的方法是将两者区分开进行独立的量化策略。对于 value cache ，作者认为，可以看做都是同分布的，所以很容易找到相应的量化参数。对于 key cache ，因为在 softmax 之前，分布变得比较震荡（？），所以不能用 value cache 部分的方法，而是采用了在线计算量化位数的方法来完成每一次的 cache 量化过程，简单说就是根据数学公式计算，这一次需要用 int8，下一次则发现 int4 可能就够了，这倒是挺有趣的。所以混合精度量化方法。==
==并且，作者还提出了一个预测未来注意力分数的机制（4.2 Attention score prediction），这个机制的目的简单说，是如果能够预测接下来====**一个滑动窗口大小的序列中不会出现更大的激活值，就一直维持着低比特的量化精度**====，如果不能的话，再通过前文的公式计算提高量化精度。==
==其次，先前的研究（Scissorhands: Exploiting the persistence of importance hypothesis for LLM KV cache compression at test time. arXiv preprint arXiv:2305.17118, 2023.）提出了重要性持久性的假设，并主张基于重要性进行压缩。作者则发现，尽管这一假设在大多数情况下是有效的，但仍存在一些例外情况。这意味着在基于注意力进行量化时需要对例外情况进行仔细处理，处理机制简单说就是如果认为重要的激活值存在持续性的话，那么某些例外情况会一直起到负面影响。所以要用滑动窗口来使得隔一段时间这些异常值也就自然而然的退出掉了。这也是前文提到的预测未来注意力中使用滑动窗口机制的理论原因。==
==第三，异常值起着至关重要的作用。尽管这一点在权重和激活量化中已经得到认可，我们验证了异常值对KV（键值）缓存量化也产生了显著影响。因此，需要为量化异常值提供专门的处理方法。具体来说作者保持异常值不量化，并以全精度的形式将它们存储在一个稀疏矩阵中。这个方法吧，其他量化方法中也用过，包括前面的 KVQuant 就也用了，对于它维持精度的作用是没问题的，但是这个方法对于加速和内存优化来说，是一定有损伤的，且让整个方法变得更加复杂。==
==另外文章还提到一个压缩比的评价指标来描述方法，和参数量化中的 bpw 的作用相同。在实验中，QAQ在3位量化下实现了高达10倍的压缩比，同时对模型性能的影响微乎其微。因为 QAQ 是个混合精度方法，所以也是可以通过配置来实现不同压缩比的设置的。==
==最后看一下效果：QAQ 声称在模型推理性能上的影响可以忽略不计，并且在某些情况下，压缩比可达到10倍。==
### ==KIVI：==
==本文的发现表明，key cache 应该按通道量化，即沿通道维度对元素进行分组并一起量化。相比之下，value cache 应该按标记量化。这个在 KVQuant 中已经应用了，不过鉴于几篇文章出来的时间差不多，也还好。基于这一分析，作者开发了一个无需调整的== ==**2bit**== ==KVCache 量化算法，名为KIVI。本文相比前面几篇文章的亮点就在于 2bit 了。==
==凭借硬件友好的实现，KIVI可以使Llama（Llama-2）、Falcon和Mistral模型在使用2.6倍更少的峰值内存使用量（包括模型权重）的同时，几乎保持相同的质量。这种内存使用量的减少使得批量大小可以增加多达4倍，带来了在真实LLM推理工作负载上的2.35倍至3.47倍的吞吐量提升。==
==那么问题就来了，前面两篇文章都考虑了很多 KVCache 的特点以及量化可能带来的问题，都不敢直接上很低比特的量化方法，QAQ 甚至还采用了混合比特的方案。所以 KIVI 为什么可以 2bit 呢？==
==文章中说 KIVI 采用了一种异构量化方法，对 Key 缓存和 Value 缓存采用不同的量化策略，这并不稀奇，前面几篇都用到了的。==
==但是通过阅读文章，我现在的感觉是，这篇文章核心的架构优势之处在于，它有一个====**全精度的滑动窗口**====（KIVImaintains a full precision KV cache sliding window for the local relevant tokens.），也就是说，实际上，====**它在窗口内，是没有量化的，窗口外才量化**====。运算时，则是窗口内的结果和窗口外的结果通过非量化和量化运算之后进行拼接，得到完整的 attention 结果。那么，如果相邻文本相关度比较高的情况下，相当于关联度很高的计算部分都没有啥量化过程，换句话说，也就是没有啥量化误差了。==
==![<<{"width":690,"height":254}>>](data:image/svg+xml;utf8,====)==
==**虽然很鸡贼，但是我的直观感受是这个方法有可能是最 simple 并且最有效果的 KVCache 方案。**==
### ==ATOM：==
==本文的方法简单说是一种低比特量的高服务吞吐量方法，所以本文既能有延时优化，又能有整体服务吞吐率的优化目标，与前面几篇不太一样，不过本质上还是把单个请求优化好，自然就可以完成服务吞吐的提升。与 FP16 相比，Atom 将端到端吞吐量提高了高达7.73倍，与 INT8 量化相比提高了2.53倍，同时保持了相同的延迟目标。本文是 chen tianqi 团队的工作，在今天一起对比学习的文章中是最早 post 在 arxiv 上面的。==
==Atom 也采用混合精度量化，并保留一小部分但重要的激活和权重以高精度保存，以保持准确性。这和 KVQuant 和 QAQ 是一样的。不过 Atom 很牛的一点是，它的混合精度不是 int4 和 fp16 去混，而是 int4 和 int8 去混，因为 atom 认为 int8 已经足够表达 outlier 了，这和前面几篇文章都有所不同。另外就是最后 atom 通过混合精度和下文所提到的 reorder 重排序方法，====**实现了 A4W4 的量化方法**====，这与前面几篇文章 A16W4 或 A16W2 的方案是有本质区别的。==
==它对权重和激活都采用了细粒度的分组量化，这自然减少了量化误差。这样不奇怪，==[==llama.cpp 中的 block 都是 32 个参数==](https://zhuanlan.zhihu.com/p/672983861)==。==
==Atom 宣称自己与预先计算激活的量化参数不同，Atom 动态地量化激活，以最佳方式捕捉每个输入的分布。==
==![<<{"width":690,"height":278}>>](data:image/svg+xml;utf8,====)==
==![<<{"width":690,"height":290}>>](data:image/svg+xml;utf8,====)==
==Atom 方法中有一个 Dynamic Reorder 动态重排序操作，文章说是借鉴了 RPTQ 中的思想。重排序又分为动态重排激活值和静态重排参数值两种。这个的实际运行时的效率暂时还未知，先怀疑的持保留意见。==
### ==IntactKV：==
==24 年 3 月份的文章，都很新。作者来自，清华深研、华为诺亚、中科院自动化所、港中文。这篇是本文发布之后评论中有小伙伴提醒才发现，真的是论文读不过来啊。IntactKV 也是不量化关键标记，也很简单直接有效。==
==之前很多的工作（Efficient streaming language models with attention sinks 为代表）都提出来说，输入开头的注意力影响很大，表现出极高的值，这些还正好是我们量化时所着重关注的 outlier。而本文作者发现在之前的论文中对此讨论则较少，正好优化一波。文章思路和 KIVI 差不多，就是直接不量化这些关键标记（pivot tokens）的数值，保持原有精度直接进行运算。这里就不多说了。不过我觉得可以分析分析文章中提出的这些配图和背后的逻辑。==
==![<<{"width":690,"height":291}>>](data:image/svg+xml;utf8,====)==
==存在特定于标记的异常值，其数值可以比其余标记大几个数量级（在方框中放大显示）。这些标记出现在LLaMA-30B的[BOS]标记、第28个标记“’”以及LLaMA-2-7B的第13个标记“。”上。这里文本开始，虽然说也不是很理解，但是属于一种现象了，姑且记下，也算符合直觉。但是，标点符号为什么这么大的激活值，我是不太明白的。==
==另外，文中还提到，由于 IntactKV 是预先计算并离线保存的，因此它可以被视为一种可训练参数，以进一步提升量化的 LLM。我们来看一下这是什么意思。我们再回顾一下 KVCache 量化的流程，因为 KVCache 的量化是在线动态进行的，所以前面的几篇文章都没有在线的 calibration 校准的过程，KVQuant 则采用了离线校准方法。而在 IntactKV 中，作者则采用了一种累积误差来重新进行校准的思路。所以，IntactKV 的这个累积误差，是====**通过单独的在一个很小的校准集上迭代调整量化参数的方法来完成量化**====。文中使用的是 128 个sample 迭代 20 个 epochs。所以，这算是 QAT 吗？肯定不是，但是好像很其他的 PTQ 的方法，也不太一样。==
### ==QServe：==
==QServe: W4A8KV4 Quantization and System Co-design for Efficient LLM Serving，是 Song Han 团队的文章。以题目来看，顾名思义不但有一种新的量化算法，还有 LLM 服务的 co-design，这也是我最近寻求发力的一个点，通过我前面的博客可以发现除了量化之后就是推理服务的方向了。==
==QoQ（Quattuor-Octō-Quattuor, 是拉丁语中的 4-8-4 意思），这很像旷视 16 年的 do-re-fa-net （音乐上的 1-2-4）。QoQ 算法包括 4 位权重、8 位激活和 4 位KV缓存的量化。还通过渐进式量化、 SmoothAttention、激活感知的通道重排序、计算感知的权重重排序等，减少了 KV4 量化引起的准确度下降。以及高效的反量化等方法加速量化推理的过程。==
==QServe 是一个与之相关的推理库，它实现了这种量化方法，并在系统层面进行了优化，以减少反量化（dequantization）的开销，并提高大型语言模型的服务吞吐量。执行计算感知的权重重排序和高效的反量化操作，以减少反量化延迟，并将融合注意力（Fused Attention）操作绑定到内存限制，从而提高性能。==
==最后与 TensorRT-LLM 相比，QServe 在 A100 和 L40S GPU 上运行 Llama 模型时，实现了显著的吞吐量提升，并且能够在 L40S GPU 上实现比 A100 上 TensorRT-LLM 更高的吞吐量，从而有效地将 LLM 服务的成本降低了 3 倍。论文还提供了一些量化和系统设计的详细背景信息，以及对大型语言模型的介绍。此外，论文还探讨了为什么 W4A8KV4 是一个优越的量化精度选择，并提出了一些量化精度选择的动机。==
==相比前面几篇文章来说，本文会更加关系量化的硬件底层，以及和服务系统之间的关系。目测未来也是会成为经典之作的。==
==细节待更新。==
### ==论文中提到几个相关方法：==
1. ==FlexGen 里面已经把 OPT-175B 中的参数和 KVCache 都量化到了 4 bits，但是对 perplexity 影响很大；==
2. ==SqueezeLLM 方法和本篇 KVQuant 的方法关系还是比较紧密的，SqueezeLLM 通过迭代过程将权重矩阵分解为一个稀疏矩阵和一个密集矩阵，分解成稀疏矩阵的过程中实现了压缩和量化。KVQuant 将 SqueezeLLM 的权重量化和 KVCache 量化过程中的一部分进行了结合；==
3. ==SmoothQuant 、 LLM.int8() 等比较有代表性的 LLM 量化方法；==
4. ==KV Cache 压缩的工作等，就是还有一部分工作他们不是量化 KVCache ，而是压缩存起来，下一个 token 算 attention 的时候，读取压缩后的数据，在 HBM 或 SRAM 内部再解压缩，也不失是一种不错的方案；==
5. ==RPTQ: Reorder-based posttraining quantization for large language models, 2023.==
6. ==FlashInfer: Kernel Library for LLM Serving.== [==https://github.com/flashinfer-ai/==](https://link.zhihu.com/?target=https%3A//github.com/flashinfer-ai/) ==flashinfer, 2023. 这个工程没有论文，但是是 ATOM 实现中的重要开源项目，回头要好好研究研究。==
- [==刀刀宁：再磕：GPTQ、SparseGPT与Hessian矩阵==](https://zhuanlan.zhihu.com/p/680578625)
- [==刀刀宁：量化那些事之FP8与LLM-FP4==](https://zhuanlan.zhihu.com/p/683215538)
- [==刀刀宁：量化那些事之AWQ==](https://zhuanlan.zhihu.com/p/684215316)
- [==刀刀宁：量化那些事之QARepVGG==](https://zhuanlan.zhihu.com/p/684297101)
- [==刀刀宁：量化那些事之BitNet-b1.58==](https://zhuanlan.zhihu.com/p/684658121)
==我关于大模型推理技术的笔记列表，防迷路：==
- [==刀刀宁：笔记：Llama.cpp 代码浅析（一）：并行机制与KVCache==](https://zhuanlan.zhihu.com/p/670515231)
- [==刀刀宁：笔记：Llama.cpp 代码浅析（二）：数据结构与采样方法==](https://zhuanlan.zhihu.com/p/671761052)
- [==刀刀宁：笔记：Llama.cpp 代码浅析（三）：计算开销==](https://zhuanlan.zhihu.com/p/672289691)
- [==刀刀宁：笔记：Llama.cpp 代码浅析（四）：量化那些事==](https://zhuanlan.zhihu.com/p/672983861)
- [==刀刀宁：笔记：DeepSpeed inference 代码理解==](https://zhuanlan.zhihu.com/p/668181423)
- [==大模型推理加速技术的学习路线是什么?==](https://www.zhihu.com/answer/3380020160)
- [==刀刀宁：再看大模型稀疏化：SparseGPT、Wanda==](https://zhuanlan.zhihu.com/p/679376718)
- [==刀刀宁：Low-Rank Pruning of Llama2==](https://zhuanlan.zhihu.com/p/678891209)
- [==刀刀宁：论文笔记：DejaVu、LLM in Flash、PowerInfer==](https://zhuanlan.zhihu.com/p/675585887)
==几篇相关的回答：==
- [==如何通过能力评测促进大模型发展？==](https://www.zhihu.com/answer/3464025339)
- [==如何评价谷歌 DeepMind 推出的 Mixture of Depths (MoD)?==](https://www.zhihu.com/answer/3457830923)
- [==2024年了diffusion还有什么可做的？==](https://www.zhihu.com/answer/3436805156)