---
DocFlag:
  - Tested
  - Testing
Updated: 2024-03-18T10:41
tags:
  - AI->-Model
  - AI->-Programming
  - AI->-TimeSeries
Created: 2023-04-07T14:36
---
```JavaScript
用deep learning来分析一组二维数据比如日期（x轴）和price（y轴）数据，来预测未来的几天后的y，使用pytorch torch或者transformer
https://data.nasdaq.com/tools/python
https://magazine.techacademy.jp/magazine/41884\#2
https://mychma.com/nasdaq-data-link-quandl/931/
\#Get stock in TSE
https://www.codelab.jp/blog/?p=1105
\#check indicator
https://www.quandl.com/api/v3/databases
\#API Manual
https://docs.data.nasdaq.com/docs/python-time-series
数据来源的准备
pip install nasdaq-data-link
pip install matplotlib
USDJPY： BOE/XUDLJYD
金（対ドル）： BOE/XUDLGPD
import nasdaqdatalink
mydata = nasdaqdatalink.get("FRED/GDP")
```
  
```JavaScript
\#initial starts
import nasdaqdatalink
apikey = '-Sh1H8jC-naKDgkSzeSR'
datatype = {'USA':'RATEINF/INFLATION_USA',
           'Japan':'RATEINF/INFLATION_JPN'}
quandl.ApiConfig.api_key = "YOUR_KEY_HERE"
import quandl
from matplotlib import pyplot as plt
data = quandl.get('FRED/GDP')
# データをプロット
ax = data.plot()
ax.set_xlabel('date')
ax.set_ylabel('GDP')
plt.show()
```
  
  
  
```JavaScript
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
class TimeSeriesDataset(Dataset):
    def __init__(self, data):
        self.data = data
    
    def __len__(self):
        return len(self.data) - 1
    
    def __getitem__(self, idx):
        x = self.data[idx][1:]
        y = self.data[idx+1][1]
        return x, y
class TransformerModel(nn.Module):
    def __init__(self, input_size, output_size, d_model, nhead, num_layers):
        super(TransformerModel, self).__init__()
        self.encoder = nn.Linear(input_size, d_model)
        self.decoder = nn.Linear(d_model, output_size)
        self.transformer = nn.Transformer(d_model=d_model, nhead=nhead, num_encoder_layers=num_layers,
                                          num_decoder_layers=num_layers)
    
    def forward(self, x):
        x = self.encoder(x)
        x = x.unsqueeze(0) # add batch dimension
        y = self.decoder(x[:,-1,:])
        y = y.squeeze(0) # remove batch dimension
        return y
# prepare data
data = [(1, 10), (2, 20), (3, 30), (4, 40), (5, 50), (6, 60), (7, 70), (8, 80), (9, 90), (10, 100)]
train_data = data[:8]
test_data = data[8:]
train_dataset = TimeSeriesDataset(train_data)
train_dataloader = DataLoader(train_dataset, batch_size=4, shuffle=True)
test_dataset = TimeSeriesDataset(test_data)
test_dataloader = DataLoader(test_dataset, batch_size=1, shuffle=False)
# set hyperparameters
input_size = 1 # number of features in input data
output_size = 1 # number of features in output data
d_model = 8 # dimension of model
nhead = 2 # number of attention heads
num_layers = 2 # number of layers in transformer model
learning_rate = 0.001 # learning rate for optimizer
# initialize model and optimizer
model = TransformerModel(input_size, output_size, d_model, nhead, num_layers)
optimizer = optim.Adam(model.parameters(), lr=learning_rate)
# train model
num_epochs = 100
for epoch in range(num_epochs):
    running_loss = 0.0
    for batch_idx, (x, y) in enumerate(train_dataloader):
        optimizer.zero_grad()
        y_pred = model(x.float())
        loss = nn.MSELoss()(y_pred, y.float())
        loss.backward()
        optimizer.step()
        running_loss += loss.item()
    print(f"Epoch {epoch+1}, loss: {running_loss/(batch_idx+1):.4f}")
    
    # evaluate model on test set
    with torch.no_grad():
        test_loss = 0.0
        for x, y in test_dataloader:
            y_pred = model(x.float())
            test_loss += nn.MSELoss()(y_pred, y.float()).item()
        print(f"Test loss: {test_loss/len(test_dataloader):.4f}")
        
# make predictions on future prices
future_data = [(11, None), (12, None), (13, None), (14, None), (15, None)]
for i in range(len(future_data)):
    x = torch.tensor([future_data[i-2][1], future_data[i-1][1]], dtype=torch.float)
    y_pred = model(x)
    future_data[i] = (future_data[i][0], y_pred.item())
print(future_data)
使用深度学习预测股票市场的走势是一种复杂的任务，因为股票市场受到许多不确定因素的影响。然而，您可以尝试构建一个神经网络来学习和挖掘历史数据中的模式。以下是使用深度学习训练股票历史数据以预测未来走势的基本步骤：
数据收集：首先，收集股票历史数据，包括开盘价、收盘价、最高价、最低价和成交量等。这些数据可以从多个数据源获取，如雅虎财经、Alpha Vantage等。
数据预处理：对收集到的数据进行预处理，包括缺失值处理、数据规范化（例如，将价格数据缩放到0-1之间）以及构建时间窗口（例如，使用过去N天的数据来预测未来的股票价格）。
特征工程：从原始数据中提取有意义的特征，如技术指标（例如，移动平均线、相对强弱指数、布林带等）和其他可能影响股价的宏观经济因素。
构建模型：选择一个合适的深度学习模型，如循环神经网络（RNN）、长短时记忆网络（LSTM）或门控循环单元（GRU）。这些模型能够捕捉时间序列数据的长期依赖关系。
训练和调整：将数据集分为训练集和测试集，然后使用训练集训练模型。在训练过程中，您需要不断调整超参数（例如学习率、批次大小、迭代次数等），以获得最佳预测性能。
模型评估：使用测试集评估模型的性能。常用的评估指标有均方误差（MSE）、均方根误差（RMSE）和平均绝对误差（MAE）。在实际应用中，您还需要关注模型在不同市场情况下的预测能力。
预测和投资策略：利用训练好的模型进行股价预测，然后根据预测结果制定投资策略。请注意，这种方法并不能保证获得高收益，因为股票市场受到许多难以预测的因素影响。/..
```
---
  
```JavaScript
torch.optim.lr_scheduler 包含了一系列学习率调度器，它们允许在训练过程中动态地调整学习率。以下是截止到 2021 年 9 月的 PyTorch（可能会有新的调度器在后续版本中添加）中提供的学习率调度器：
torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda, last_epoch=-1, verbose=False)：根据给定的函数（lr_lambda）来调整学习率。
torch.optim.lr_scheduler.StepLR(optimizer, step_size, gamma=0.1, last_epoch=-1, verbose=False)：在固定的步长（step_size）间隔内按照给定的因子（gamma）衰减学习率。
torch.optim.lr_scheduler.MultiStepLR(optimizer, milestones, gamma=0.1, last_epoch=-1, verbose=False)：在预定义的多个训练轮次（milestones）上按照给定的因子（gamma）衰减学习率。
torch.optim.lr_scheduler.ExponentialLR(optimizer, gamma, last_epoch=-1, verbose=False)：每个训练轮次按照给定的因子（gamma）指数衰减学习率。
torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max, eta_min=0, last_epoch=-1, verbose=False)：按照余弦退火策略在一个周期内调整学习率。
torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=10, threshold=0.0001, threshold_mode='rel', cooldown=0, min_lr=0, eps=1e-08, verbose=False)：当验证指标停止改善时，根据给定的因子（factor）降低学习率。
torch.optim.lr_scheduler.CyclicLR(optimizer, base_lr, max_lr, step_size_up=2000, step_size_down=None, mode='triangular', gamma=1.0, scale_fn=None, scale_mode='cycle', cycle_momentum=True, base_momentum=0.8, max_momentum=0.9, last_epoch=-1, verbose=False)：按照循环策略（如三角策略、指数策略等）在最小学习率（base_lr）和最大学习率（max_lr）之间调整学习率。
torch.optim.lr_scheduler.OneCycleLR(optimizer, max_lr, total_steps=None, epochs=None, steps_per_epoch=None, pct_start=0.3, anneal_strategy='cos', cycle_momentum=True, base_momentum=0.85, max_momentum=0.95, div_factor=25.0, final_div_factor=1e4, last_epoch=-1, verbose=False)：在一个周期内实现学习率的圆周训练策略，通常用于训练收敛速度较慢的模型。
torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0, T_mult=1, eta_min=0, last_epoch=-1, verbose=False)：按照余弦退火策略在多个周期内调整学习率，每个周期的长度会根据给定的因子（T_mult）进行调整。在每个周期结束时，学习率会重置为初始值。
```
  
```JavaScript
1. 加入wandb逻辑
2. 加入progress bar
3. 加入优化器，如果可能请采用lion，不行则使用adamw
4. 加入学习率调度器逻辑，采用torch.optim.lr_scheduler.CosineAnnealingWarmRestarts
5. 为了避免在每个epoch之后都评估测试集，可以选择每隔几个epoch进行一次评估。
6. 保存模型权重，在training后以onnx模型格式保存到本地'./mk_ckpt'子目录下。下次可以调出，每次程序运行前，先检查这个子目录下有无权重文件，有的话，则加载，做训练，没有的话，则从0基础上进行训练
7. 使用更多的数据预处理，如归一化或缩放，以提高模型性能
8. 为了更好地评估模型性能，可以将MSE损失转换为更具解释性的指标，例如平均绝对百分比误差（MAPE）。
```
```JavaScript
根据您的代码，您可以尝试以下几种方法来改进模型性能：
调整超参数：
增加/减少 Transformer 层（num_layers）：增加层数可以提高模型的容量，但也可能导致过拟合。
增加/减少注意力头（nhead）：增加注意力头可以使模型学习更多不同类型的依赖关系，但计算量也会增加。
增加/减少模型维度（d_model）：增加模型维度可以增加模型容量，但也会增加计算复杂性。
调整学习率（learning_rate）：尝试使用较小或较大的学习率，以查看是否对训练和收敛有所帮助。
调整优化器的权重衰减参数：尝试调整 AdamW 优化器的权重衰减（weight_decay）参数，以减小或增大正则化强度。
模型架构：
尝试使用其他架构，例如 LSTM、GRU 或其他 RNN 变体。
为模型添加 Dropout 层以降低过拟合风险。
为模型添加层归一化（Layer Normalization）以改进训练稳定性。
数据预处理：
尝试使用不同的数据预处理方法，例如对数变换或差分变换，以减小极端值对模型的影响。
增加时间窗口大小：使用更长的时间序列数据作为输入，以便模型可以捕捉到更多的历史信息。
早停和模型选择：
在验证集上监控性能，并在性能不再提高时停止训练（早停）。
在训练过程中保存具有最佳验证性能的模型，以避免使用过拟合的模型进行预测。
学习率调度器：
尝试使用其他学习率调度器，例如 ReduceLROnPlateau，它会在验证损失不再减少时降低学习率。
```
```JavaScript
在sklearn.preprocessing中，除了MinMaxScaler之外，还有以下常用的数据归一化方法：
StandardScaler：标准化数据，使得数据符合标准正态分布，即均值为0，标准差为1。
RobustScaler：与StandardScaler类似，但使用中位数和四分位数而不是均值和方差。
MaxAbsScaler：将数据缩放到[-1,1]之间，通过除以特征的最大值的绝对值实现。
PowerTransformer：通过对数据进行幂变换来使其符合高斯分布。
QuantileTransformer：通过对数据进行分位数转换来使其符合均匀分布或高斯分布。
Normalizer：对每个样本的特征向量进行L1或L2归一化，使得每个样本的特征向量具有单位范数。
不同的数据归一化方法适用于不同的数据分布情况和应用场景，选择合适的方法可以提高模型的性能和稳定性。
```
  
《《《DAY2》》》
```JavaScript
你好，下面程序是deep learning来分析一组时序二维数据日期（x轴）和price（y轴）数据，来预测未来的几天后的走势，你作为深度学习领域的专家，请帮助我一起来完事它，
1.  接下去的交谈过程中，你只要列出我修改的部分和上下问文，不用输出所有内容，除非我让你输出所有
2. 如果陷入僵局，请切换思路，一步步来分析，给出建议
3. 下面请看一下程序，理解它
《《最新程序》》
请修改上面程序改用keras 模型，使用look back，并在train结束使用pred_data添加测部分，用plot对比实际数据和用keras模型预测的数据
```
```JavaScript
Lstm
1. 学习率函数
import numpy as np
import tensorflow as tf
from keras.callbacks import LearningRateScheduler
from keras.optimizers import SGD
def cosine_annealing_warm_restarts(epoch, lr):
    initial_lr = 0.01
    t0 = 10
    tmult = 2
    
    # 计算当前周期
    t_i = t0
    last_restart = 0
    while epoch > last_restart + t_i:
        last_restart += t_i
        t_i *= tmult
    # 计算周期内的当前epoch
    epoch_in_cycle = epoch - last_restart
    # 计算新学习率
    new_lr = initial_lr * 0.5 * (1 + np.cos(epoch_in_cycle * np.pi / t_i))
    return new_lr
# 初始化SGD优化器
optimizer = SGD(lr=0.01, momentum=0.9)
# 创建余弦退火重启调度器实例
lr_scheduler = LearningRateScheduler(cosine_annealing_warm_restarts)
# 在模型训练中使用余弦退火重启调度器
history = model.fit(
    train_X, train_y,
    epochs=num_epochs,
    batch_size=1,
    validation_data=(test_X, test_y),
    callbacks=[lr_scheduler],
    shuffle=True
)

2. 优化器
from keras.optimizers import SGD
sgd = SGD(lr=0.01, momentum=0.9)
model.compile(optimizer=sgd, loss='mse')
除了带有动量的随机梯度下降算法之外，对于时序数据深度学习，还可以使用RMSprop、Adadelta等优化器，这

3. save and load weight
checkpoint = ModelCheckpoint('./mk_ckpt/weights.{epoch:02d}-{val_loss:.2f}.h5', monitor='val_loss', save_best_only=True, save_weights_only=True, verbose=1)
LSTMcallbacks = [WandbCallback(), checkpoint] # callbacks
save_weights_only = False 比较好，可以保存结构下次继续训练
# 继续训练模型
history = loaded_model.fit(train_X, train_y, epochs=new_epochs, batch_size=batch_size, validation_data=(test_X, test_y), shuffle=True)

# 加载模型
loaded_model = load_model('my_model.h5')

3. print
# make predictions
train_predict = model.predict(X_train)
test_predict = model.predict(X_test)
pred_predict = model.predict(X_pred)
# invert predictions
train_predict = scaler.inverse_transform(train_predict)
y_train = scaler.inverse_transform([y_train])
test_predict = scaler.inverse_transform(test_predict)
y_test = scaler.inverse_transform([y_test])
pred_predict = scaler.inverse_transform(pred_predict)
# plot prediction and actual data
plt.figure(figsize=(15, 7))
plt.plot(pd.to_datetime(train_dates[look_back:]), train_predict[:, 0], label='Train prediction', color='blue')
plt.plot(pd.to_datetime(test_dates[look_back:]), test_predict[:, 0], label='Test prediction', color='green')
plt.plot(pd.to_datetime(pred_dates[look_back:]), pred_predict[:, 0], label='Pred prediction', color='red')
plt.plot(pd.to_datetime(train_dates), train_rates, label='Train actual', color='blue', alpha=0.5)
plt.plot(pd.to_datetime(test_dates), test_rates, label='Test actual', color='green', alpha=0.5)
plt.plot(pd.to_datetime(pred_dates), pred_rates, label='Pred actual', color='red', alpha=0.5)
plt.xlabel('Date')
plt.ylabel('Price')
plt.legend()
plt.show()
============
4. keras的Callbacks里的ModelCheckpoint是什么意思
Keras中的Callbacks模块提供了一些回调函数，可以在训练过程中监控模型的性能并进行相应的操作。其中，ModelCheckpoint是一种回调函数，用于在每个训练周期结束时保存模型的权重（weights）。ModelCheckpoint回调函数可以根据指定的一些条件（如验证集准确率的提高等）来决定是否保存模型的权重，从而避免了在训练过程中过度拟合的问题。
from keras.callbacks import ModelCheckpoint
from keras.models import Sequential
from keras.layers import Dense
# 创建模型
model = Sequential()
model.add(Dense(10, input_dim=8, activation='relu'))
model.add(Dense(1, activation='sigmoid'))
# 编译模型
model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
# 创建ModelCheckpoint回调函数
checkpoint = ModelCheckpoint('weights.{epoch:02d}-{val_loss:.2f}.h5', monitor='val_loss', save_best_only=True, save_weights_only=False, verbose=1)
# 训练模型，并在每个训练周期结束时保存模型权重
model.fit(X_train, y_train, validation_data=(X_test, y_test), epochs=10, batch_size=32, callbacks=[checkpoint])
在上面的示例中，我们首先创建了一个简单的二分类模型，并编译了该模型。然后，我们创建了一个ModelCheckpoint回调函数，并设置了保存模型权重的条件：
在验证集上表现最好的模型权重，并仅保存模型权重，不保存模型结构等信息。最后，我们使用fit函数训练模型，并在每个训练周期结束时保存模型权重。具体来说，
每当验证集损失函数值val_loss有所提高时，ModelCheckpoint回调函数就会保存当前模型的权重到指定文件中。在这个示例中，我们使用了格式化字符串来动态生成
模型权重文件的名称，每个文件名都包括当前训练周期epoch和验证集损失函数值val_loss。
在上面的代码中，我们使用了两个Dense层来构建一个简单的二分类模型。第一个Dense层包含10个神经元，输入维度为8，激活函数为ReLU。ReLU激活函数可以使模型更加稀疏，从而减少模型参数和计算量。第二个Dense层包含1个神经元，激活函数为Sigmoid。Sigmoid函数可以将模型的输出值压缩到[0,1]的范围内，可以视为一个概率值，表示模型对第一类样本的预测概率。
这两个Dense层被称为全连接层（Fully Connected Layer），因为每个神经元都与上一层的所有神经元相连。第一个Dense层的输出会作为第二个Dense层的输入，从而实现特征提取和分类的功能。
需要注意的是，第一个Dense层的输出维度（也就是神经元数量）可以根据具体问题进行调整，而输入维度则需要根据样本特征的数量进行设置。例如，
在处理图像数据时，可以将每个像素视为一个特征，将输入维度设置为图像的像素数量。在处理文本数据时，可以将每个单词或字符视为一个特征，
将输入维度设置为文本中出现的单词或字符的数量。
binary_crossentropy和mean_squared_error都是损失函数，但它们分别适用于不同类型的问题。
binary_crossentropy：
binary_crossentropy损失函数用于二分类问题，它计算真实标签和预测标签之间的交叉熵损失。当您处理一个问题，其输出只有两个类别（例如，是或否，0或1）时，可以使用这个损失函数。在神经网络中，通常在输出层使用sigmoid激活函数，将输出限制在0到1之间，表示某个类别的概率。binary_crossentropy损失函数可以有效地衡量模型的预测概率和真实概率之间的差异。
mean_squared_error：
mean_squared_error（MSE）损失函数用于回归问题，它计算真实值与预测值之间的平方误差的均值。当您处理一个连续值预测问题时，可以使用这个损失函数。MSE损失函数衡量模型的预测值与真实值之间的差异，而且由于平方误差，它对大误差给予更大的惩罚。
总结：
对于二分类问题，请使用binary_crossentropy损失函数。
对于回归问题，请使用mean_squared_error损失函数。
注意：对于多分类问题，您可以使用categorical_crossentropy损失函数。
```
  
```JavaScript
<<< DAY 3 》》
你好，下面程序是deep learning来分析一组时序二维数据日期（x轴）和price（y轴）数据，来预测未来的几天后的走势，采用keras的LSTM模型，look_back 和windows_size为12，也就是现实世界里12天，你作为深度学习领域的专家，请帮助我一起来完事它，
1.  接下去的交谈过程中，你只要列出我修改的部分和上下问文，不用输出所有内容，除非我让你输出所有。
2. 如果陷入僵局，请切换思路，一步步来分析，给出建议
3. 给出建议的时候，特别注意变量是元组还是列的类型，以及Shape和 size
比如，实际里面
pred_dates 的shape是tuple：67
(numpy.datetime64('2023-01-03T00:00:00.000000000'), numpy.datetime64('2023-01-04T00:00:00.000000000'), numpy.datetime64('2023-01-05T00:00:00.000000000'), numpy.datetime64('2023-01-06T00:00:00.000000000'), numpy.datetime64('2023-01-09T00:00:00.000000000'), numpy.datetime64('2023-01-10T00:00:00.000000000'),
... numpy.datetime64('2023-04-05T00:00:00.000000000'))
pred_rates 是 ndarry：(67,1) 67行1列的二维数组包含汇率值
scaled_pred_data的shape是ndarry：(67,1)
[[0.77038386], [0.78873634], [0.81451387], [0.79391987], [0.7870552 ], [0.79041748], [0.79476044], [0.75483329], [0.724993  ], [0.73928271], [0.733819  ], [0.73690109], [0.73830205], [0.75861586], [0.76870272], [0.76099748], [0.75413281], [0.76604091],...
pred_X 是ndarry：(43，12，1) 
pred_y 是ndarry:(43，12) 
其中12是look_back 和windows_size的大小
然后你根据程序推断出其他衍生变量的shape和大小
4. 下面请看具体程序，理解它
```
```JavaScript
# 为预测日期添加未来 look_back 天的日期
    last_date = datetime.datetime.fromtimestamp(pred_dates[-1].astype('int64') // 1000000000)
    # 初始化一个空列表来存储未来日期
    future_dates = []
    # 使用循环为未来日期列表添加日期
    for i in range(look_back):
        next_date = last_date + datetime.timedelta(days=i + 1)
        future_dates.append(next_date)
    pred_dates_extended = list(pred_dates) + [np.datetime64(date.strftime("%Y-%m-%d")) for date in future_dates]
    pred_rates_extended = np.zeros(len(pred_dates_extended))
    pred_rates_extended[:len(pred_rates)] = pred_rates.flatten()
    # 计算未来日期的预测值
    future_pred_y = pred_y[-1, :]
    # 计算历史日期的预测值
    past_pred_y = np.mean(pred_y[:-1], axis=1)
    # 拼接历史和未来预测值
    final_pred_y = np.concatenate((past_pred_y, future_pred_y))
    # 绘制结果
    n_pred_dates = len(pred_dates_extended)
    n_final_pred_y = len(final_pred_y)
    n_future_dates: int = n_pred_dates - n_final_pred_y
    # 2. Calculate the percentage changes in predictions
    predictions_pct_changes = np.diff(final_pred_y) / final_pred_y[:-1]
    predictions_pct_changes[0] = 0
    # 3. Apply the percentage changes to the first actual value
    adjusted_predictions = [pred_rates_extended[n_future_dates]]
    for pct_change in predictions_pct_changes:
        adjusted_predictions.append(adjusted_predictions[-1] * (1 + pct_change))

    plt.plot(pred_dates_extended[n_future_dates:-look_back], pred_rates_extended[n_future_dates:-look_back], label='Actual Data')
    # plt.plot(pred_dates_extended[n_future_dates:], adjusted_predictions, label='Predicted Data Aligned')
    # plt.plot(pred_dates_extended[n_future_dates:], final_pred_y, label='Predicted Data')
    plt.plot(pred_dates_extended[-look_back:], future_pred_y, label='Predicted Data')
    plt.xlabel('Date')
    plt.ylabel('USD/JPY Price')
    plt.title('Price Prediction Actual vs Predicted')
    plt.legend()
    plt.show()
```
![[Notion/AI/🦜🦜🦜DeepLearning Market Data/attachments/Untitled.png|Untitled.png]]
![[Notion/AI/🦜🦜🦜DeepLearning Market Data/attachments/Untitled 1.png|Untitled 1.png]]
```JavaScript
Windows size = 12
Lay = 2
LSTMnn = 100
LSTMoptimier = Adam
Epochs = 100
Batch_size = 4     -> 32 Up    -> 64 Down    -> 4 Down a lot! -> 16 Down a lot!
		rmse = 3.65           2.49      4.22             6.81         19.12
64 32 16 8 4
```
![[Untitled 2.png]]
```JavaScript
Windows size = 12
Lay = 2
LSTMnn = 100
LSTMoptimier = SGD
Epochs = 100
Batch_size = 4      32    4 
rmse  = 3.7               3.23
```
![[Untitled 3.png]]
```JavaScript
Windows size = 12
Lay = 3
LSTMnn = 100
LSTMoptimier = Adam
Epochs = 100
Batch_size = 4
rmse  = 5.38
```
[[AI ToDO List]]
![[Untitled 4.png]]
```JavaScript
Windows size = 12
Lay = 1
LSTMnn = 100
LSTMoptimier = Adam
Epochs = 100
Batch_size = 4
rmse  = 4.079
```
![[Untitled 5.png]]
```JavaScript
Windows size = 12
Lay = 2
LSTMnn = 100
LSTMoptimier = Adam
Epochs = 100
Batch_size = 4
validation_batch_size = 1
rmse  = 3.18
```
![[Untitled 6.png]]
```JavaScript
Windows size = 12
Lay = 2
LSTMnn = 100
LSTMoptimier = Adam
Epochs = 5
Batch_size = 4
validation_batch_size = 1
rmse  = 3.55
```
```JavaScript
some discovery
1， 训练完以后，使用是epoth不要很大5就够了
2.  想预知的日数越多，模型结果越不准确
```
![[Untitled 7.png]]
```JavaScript
Windows size = 6
Lay = 2
LSTMnn = 100
LSTMoptimier = Adam
Epochs = 50
Batch_size = 4
validation_batch_size = 1
rmse  = 0.53
```
```JavaScript
# initial starts
import os
import sys
import datetime
import nasdaqdatalink as ndl
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import keras
import wandb
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import numpy as np
import matplotlib.pyplot as plt
from keras.models import Sequential
from keras.layers import Dense, LSTM
from keras.layers import Dropout
# from keras.models import load_model
from keras.optimizers import Adam
from keras.optimizers import SGD
from keras.callbacks import ModelCheckpoint
from keras.callbacks import Callback
# from keras import backend as K
from keras.callbacks import LearningRateScheduler
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error
from wandb.keras import WandbCallback
import tensorflow as tf
import tf2onnx

class TimeSeriesDataset(Dataset):
    def __init__(self, data, window_size):
        self.data = data
        self.window_size = window_size
    def __len__(self):
        return len(self.data) - self.window_size
    def __getitem__(self, idx):
        # x = torch.tensor([item[1] for item in self.data[idx:idx+self.window_size]])
        # y = torch.tensor(self.data[idx+self.window_size][1])
        x = np.array([item[1] for item in self.data[idx:idx+self.window_size]])
        y = np.array(self.data[idx+self.window_size][1])
        x = torch.tensor(x)
        y = torch.tensor(y)
        return x, y
class TransformerModel(nn.Module):
    def __init__(self, input_size, output_size, d_model, nhead, num_layers):
        super(TransformerModel, self).__init__()
        self.encoder = nn.Linear(input_size, d_model)
        self.decoder = nn.Linear(d_model, output_size)
        self.transformer = nn.Transformer(d_model=d_model, nhead=nhead, num_encoder_layers=num_layers,
                                          num_decoder_layers=num_layers)
    def forward(self, x):
        x = self.encoder(x)
        x = x.unsqueeze(0)  # add batch dimension
        y = self.decoder(x[:, -1, :])
        y = y.squeeze(0)  # remove batch dimension
        return y

def accuracy(y_pred, y_true):
    return torch.mean(((torch.abs(y_pred - y_true) / y_true) <= 0.05).float()).item()
# Ray it can be adjusted
apikey = '-Sh1H8jC-naKDgkSzeSR'
ndl.ApiConfig.api_key = apikey
\#disable warning message
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
window_size = 6 #  number of previous days to use for prediction or training bactch size
# Ray it can be adjusted
# set hyperparameters
input_size = 1  # number of features in input data
output_size = 1  # number of features in output data
d_model = 512  # 8 dimension of model
nhead = 64  # 2 number of attention heads
num_layers = 5  # 2 number of layers in transformer model
learning_rate = 2e-5 # 0.0001  # learning rate for optimizer
skip_training = False  # set to True to skip training and load pretrained model
save_onnx = False  # set to True to save model as onnx file
# -------------------
TrainingMethod = 'LSTM' # 'LSTM' or 'Transformer'
LSTMnn = 100 # number of LSTM network
# LSTMoptimizer = SGD(learning_rate=learning_rate, momentum=0.9) # optimizer
LSTMoptimizer = Adam(learning_rate=learning_rate) # optimizer
# LSTMloss = 'mean_squared_error' # loss function
# Huber Loss 是均方误差和平均绝对误差之间的一种折衷。在误差较小的情况下，Huber损失表现为均方误差；
# 在误差较大的情况下，表现为平均绝对误差。这使得它对离群值具有较好的鲁棒性。
LSTMloss = tf.keras.losses.Huber(), # loss function
# Log-Cosh损失函数是另一个在回归问题中表现良好的损失函数。它计算预测值
# 与真实值之间差的双曲余弦的对数。这个损失函数对于大误差具有平滑特性，使得梯度更新变得更稳定。
# LSTMloss = tf.keras.losses.LogCosh() # loss function
LSTMepochs = 5  # number of epochs
LSTMbatch_size = 4  # batch size
LSTMshuffle = True  # shuffle
LSTMverbose = 1  # verbose
LSTMdropout = 0.2 # dropout rate
LSTMreturn_sequences = True  # return sequences
LSTMfeatures = 1  # features
LSTMvalidation_batch_size = 1
LSTMvalidation_freq = 1  # validation frequency
LSTMmax_queue_size = 100  # max queue size
LSTMworkers = 8  # workers
LSTMuse_multiprocessing = False  # use multiprocessing
LSTMactivation = 'tanh'  # activation
LSTMrecurrent_activation = 'sigmoid'  # recurrent activation
LSTMrecurrent_dropout = 0.0
# 当unroll为True时，LSTM层将展开循环计算，并将其表示为大型张量表达式。
# 展开循环可以提高计算性能，尤其是在GPU上。然而，它也可能导致更高的内存
# 消耗，特别是当输入序列较长时。因此，在设置unroll为True之前，需要权衡
# 计算性能和内存消耗的关系。
LSTMunroll = True
\#not used
LSTMvalidation_split = 0.2 # validation split
LSTMreturn_state = False # return state
LSTMstateful = False # stateful
LSTMgo_backwards = False # go backwards
LSTMtime_major = False # time major
LSTMdata_format = 'channels_last' # data format
LSTMsteps_per_epoch = None # steps per epoch
LSTMvalidation_steps = None # validation steps
LSTMrecurrent_initializer = 'glorot_uniform' # recurrent initializer
LSTMrecurrent_regularizer = None # recurrent regularizer
LSTMrecurrent_constraint = None # recurrent constraint
LSTMunit_forget_bias = True # unit forget bias
LSTMbias_initializer = 'zeros' # bias initializer
LSTMbias_regularizer = None # bias regularizer
LSTMbias_constraint = None # bias constraint

# train model
num_epochs = 100
eval_interval = 5
# set if we need wandb output or not
flag_wandb = False
if flag_wandb and not skip_training:
    # Initialize wandb
    wandb.init(
        # set the wandb project where this run will be logged
        project="MKAnalysis",
    )
# prepare data
data_train = ndl.get('BOE/XUDLJYD', start_date='1998-01-01', end_date='2022-05-31', returns='numpy')
data_test = ndl.get('BOE/XUDLJYD', start_date='2022-06-01', end_date='2022-12-31', returns='numpy')
data_pred = ndl.get('BOE/XUDLJYD', start_date='2023-01-01', end_date='2023-04-11', returns='numpy')
print("data_train shape:", data_train.shape)
print("data_test shape:", data_test.shape)
print("First 5 rows of data_train:")
print(data_train[:5])
print("First 5 rows of data_test:")
print(data_test[:5])
# Ray it can be adjusted
# Scale data
scaler = MinMaxScaler()
train_dates, train_rates = zip(*data_train)
test_dates, test_rates = zip(*data_test)
pred_dates, pred_rates = zip(*data_pred)
train_rates = np.array(train_rates).reshape(-1, 1)
test_rates = np.array(test_rates).reshape(-1, 1)
pred_rates = np.array(pred_rates).reshape(-1, 1)
# Truncate pred_data to match the expected length of pred_y_inv
# pred_rates = pred_rates[:-(window_size + 1)]
# Convert datetime64 to datetime
# dfdates = [d.tolist() for d in test_dates]
# plt.plot(dfdates, test_rates)
# plt.title('Test data')
# plt.xlabel('Date')
# plt.ylabel('Rate')
# plt.show()
scaled_train_data = scaler.fit_transform(train_rates)
scaled_test_data = scaler.transform(test_rates)
scaled_pred_data = scaler.transform(pred_rates)
train_data = [(i + 1, scaled_train_data[i]) for i in range(len(scaled_train_data))]
test_data = [(i + 1, scaled_test_data[i]) for i in range(len(scaled_test_data))]
pred_data = [(i + 1, scaled_pred_data[i]) for i in range(len(scaled_pred_data))]

train_dataset = TimeSeriesDataset(train_data, window_size)
train_dataloader = DataLoader(train_dataset, batch_size=window_size, shuffle=True)
test_dataset = TimeSeriesDataset(test_data, window_size)
test_dataloader = DataLoader(test_dataset, batch_size=window_size, shuffle=False)
pred_dataset = TimeSeriesDataset(pred_data, 1)
pred_dataloader = DataLoader(pred_dataset, batch_size=1, shuffle=False)
## ------------------------------
## Transformer model definition
def TFModel():
    # initialize model and optimizer
    model = TransformerModel(input_size, output_size, d_model, nhead, num_layers)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model= model.to(device)
    torch.set_float32_matmul_precision('medium')
    # Load the model weights if available
    ckpt_dir = './mk_ckpt'
    os.makedirs(ckpt_dir, exist_ok=True)
    ckpt_path_onnx = os.path.join(ckpt_dir, 'model_weights.onnx')
    ckpt_path_pth = os.path.join(ckpt_dir, 'model_weights.pth')
    \#if os.path.exists(ckpt_path_onnx):
    #    onnx_model = onnx.load(ckpt_path_onnx)
    #    pytorch_model = ConvertModel(onnx_model)
    #    model.load_state_dict(pytorch_model.state_dict())
    # ckpt_path = os.path.join(ckpt_dir, 'model_weights.pth')
    if os.path.isfile(ckpt_path_pth):
        model.load_state_dict(torch.load(ckpt_path_pth))
        print("Loaded model weights from checkpoint")
    if skip_training == False:
        \#optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-5)
        optimizer = optim.AdamW(model.parameters(), lr=learning_rate)
        # Ray it can be adjusted
        scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2, verbose=False)
        for epoch in range(num_epochs):
            running_loss = 0.0
            running_accuracy = 0.0
            model.train()
            batch_idx = 0
            # Initialize gradient averages
            avg_grads = {name: 0 for name, _ in model.named_parameters()}
            for batch_idx, (x, y) in enumerate(tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{num_epochs}")):
                x, y = x.to(device), y.to(device)
                optimizer.zero_grad()
                y_pred = model(x.float()).to(device)
                if y_pred.shape != y.shape:
                    y_pred =  y_pred[:y.shape[0], :]
                loss = nn.L1Loss()(y_pred, y.float().to(device))
                loss.backward()
                optimizer.step()
                scheduler.step(epoch + batch_idx / len(train_dataloader))
                # Accumulate gradients
                for name, param in model.named_parameters():
                    if param.grad is not None:
                        avg_grads[name] += param.grad.abs().mean().item()
                running_loss += loss.item()
                running_accuracy += accuracy(y_pred, y.float().to(device))
            running_loss /= (batch_idx + 1)
            running_accuracy /= (batch_idx + 1)
            # Log learning rate
            current_lr = scheduler.get_last_lr()[0]
            # print(f"Epoch {epoch+1}, loss: {running_loss:.4f}, accuracy: {running_accuracy:.4f}")
            if flag_wandb:
                wandb.log({"train_loss": running_loss, "train_accuracy": running_accuracy, "learning_rate": current_lr})
            # Calculate gradient averages and log to wandb
            if flag_wandb:
                for name, avg_grad in avg_grads.items():
                    avg_grads[name] /= (batch_idx + 1)
                    if f"avg_grad_{name}" == "avg_grad_encoder.bias":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
                    if f"avg_grad_{name}" == "avg_grad_encoder.weight":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
                    if f"avg_grad_{name}" == "avg_grad_decoder.bias":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
                    if f"avg_grad_{name}" == "avg_grad_decoder.weight":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
            if (epoch + 1) % eval_interval == 0:
                test_loss = 0.0
                model.eval().to(device)
                with torch.no_grad():
                    for idx, (x, y) in enumerate(test_dataloader):
                        x, y = x.to(device), y.to(device)
                        y_pred = model(x.float().to(device))
                        # Handle incomplete batch
                        if y_pred.shape != y.shape:
                            y_pred = y_pred[:y.shape[0], :]
                        test_loss += nn.L1Loss()(y_pred, y.float().to(device))
                        # Print progress
                        if idx % 10 == 0:
                            print(f"Evaluating: {idx}/{len(test_dataloader)}", end='\r')
                            sys.stdout.flush()
                test_loss /= len(test_dataloader)
                if flag_wandb:
                    print(f"Test loss: {test_loss:.4f}")
                    wandb.log({"Test Loss": test_loss})

        # Save the model weights
        torch.save(model.state_dict(), ckpt_path_pth)
        # data alignment with real world
    # 1. Predict on test dataset
    model.eval().to(device)
    predictions = []
    predictions.append(0)
    with torch.no_grad():
        for x, _ in pred_dataloader:
            x = x.to(device)
            y_pred = model(x.float().to(device))
            predictions.append(y_pred.item())
    # 2. Transform predictions and actual values back to the original scale
    predictions = scaler.inverse_transform(np.array(predictions).reshape(-1, 1)).flatten()
    predictions[0] = pred_rates[0][0]
    # 2. Calculate the percentage changes in predictions
    predictions_pct_changes = np.diff(predictions) / predictions[:-1]
    predictions_pct_changes[0] = 0
    # 3. Apply the percentage changes to the first actual value
    adjusted_predictions = [pred_rates[0][0]]
    for pct_change in predictions_pct_changes:
        adjusted_predictions.append(adjusted_predictions[-1] * (1 + pct_change))
    actual_values = pred_rates
    dates = pred_dates
    print(dates)
    # 4. Plot predictions and actual values
    plt.figure(figsize=(12, 6))
    plt.plot(dates, actual_values, label='Actual', color='blue')
    plt.plot(dates, adjusted_predictions, label='Predicted', color='red')
    # plt.plot(dates, predictions, label='Predicted', color='red')
    plt.xlabel('Date')
    plt.ylabel('USD/JPY Exchange Rate')
    plt.title('Predictions vs Actual Values')
    plt.legend()
    plt.show()
## End of TFModel
## ------------------------------

# 数据预处理部分以适应多输出预测 look_bck:look_back
def create_datasetX(dataset, look_back=1):
    dataX, dataY = [], []
    for i in range(len(dataset) - look_back * 2):
        dataX.append(dataset[i:(i + look_back), 0])
        dataY.append(dataset[(i + look_back):(i + look_back * 2), 0])
    return np.array(dataX), np.array(dataY)
# 数据预处理部分以适应多输出预测 look_bck:1
def create_dataset(dataset, look_back):
    dataX, dataY = [], []
    for i in range(len(dataset) - look_back):
        a = dataset[i:(i + look_back), 0]
        dataX.append(a)
        dataY.append(dataset[i + look_back, 0])
    return np.array(dataX), np.array(dataY)
def cosine_annealing_warm_restarts(epoch, lr):
    initial_lr = 0.01
    t0 = 10
    tmult = 2
    # 计算当前周期
    t_i = t0
    last_restart = 0
    while epoch > last_restart + t_i:
        last_restart += t_i
        t_i *= tmult
    # 计算周期内的当前epoch
    epoch_in_cycle = epoch - last_restart
    # 计算新学习率
    new_lr = initial_lr * 0.5 * (1 + np.cos(epoch_in_cycle * np.pi / t_i))
    return new_lr

class CustomReduceLROnPlateau(Callback):
    def __init__(self, monitor='val_loss', factor=0.1, patience=10, min_lr=1e-6, verbose=1):
        super(CustomReduceLROnPlateau, self).__init__()
        self.monitor = monitor
        self.factor = factor
        self.patience = patience
        self.min_lr = min_lr
        self.verbose = verbose
        self.best_loss = np.inf
        self.wait = 0
    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}
        current_loss = logs.get(self.monitor)
        if current_loss is None:
            raise ValueError(f'Monitor value `{self.monitor}` not found in logs.')
        if current_loss < self.best_loss:
            self.best_loss = current_loss
            self.wait = 0
        else:
            self.wait += 1
        if self.wait >= self.patience:
            current_lr = float(tf.keras.backend.get_value(self.model.optimizer.lr))
            new_lr = max(current_lr * self.factor, self.min_lr)
            tf.keras.backend.set_value(self.model.optimizer.lr, new_lr)
            self.wait = 0
            if self.verbose:
                print(f"Epoch {epoch + 1}: reducing learning rate to {new_lr}.")
class LearningRateLoggingCallback(keras.callbacks.Callback):
    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}
        lr = keras.backend.get_value(self.model.optimizer.lr)
        wandb.log({"lr": lr})
def save_onnx_model(model_file):
    model = tf.keras.models.load_model(model_file)
    input_signature = [tf.TensorSpec(shape=model.inputs[0].shape, dtype=model.inputs[0].dtype, name=model.inputs[0].name)]
    output_signature = [tf.TensorSpec(shape=model.outputs[0].shape, dtype=model.outputs[0].dtype, name=model.outputs[0].name)]
    onnx_model, _ = tf2onnx.convert.from_keras(model, input_signature=input_signature, output_signature=output_signature, opset=13)
    onnx_model_path = model_file.replace('.h5', '.onnx')
    with open(onnx_model_path, "wb") as f:
        f.write(onnx_model.SerializeToString())
        
# LSTM Model definition
def LSTMModel( look_back ):
    train_X, train_y = create_datasetX(scaled_train_data, look_back)
    test_X, test_y = create_datasetX(scaled_test_data, look_back)
    pred_X, _ = create_datasetX(scaled_pred_data, look_back)
    # reshape input to be [samples, time steps, features]
    train_X = np.reshape(train_X, (train_X.shape[0], train_X.shape[1], LSTMfeatures))
    test_X = np.reshape(test_X, (test_X.shape[0], test_X.shape[1], LSTMfeatures))
    pred_X = np.reshape(pred_X, (pred_X.shape[0], pred_X.shape[1], LSTMfeatures))
    # create and fit the LSTM network
    model = Sequential()
    # model.add(LSTM(units=LSTMnn, return_sequences=False, input_shape=(look_back, 1)))
    # LSTM层有两个激活函数：一个是activation，另一个是recurrent_activation。
    # activation激活函数用于计算输出门、输入门和候选记忆细胞的值。在默认情况下，activation函数是tanh。
    # recurrent_activation激活函数用于计算LSTM的遗忘门。在默认情况下，recurrent_activation函数是
    # sigmoid。sigmoid函数将输出限制在0和1之间，因此遗忘门的值可以解释为在每个时间步长遗忘记忆细胞中的某个部分。
    model.add(
              LSTM(units=LSTMnn, return_sequences=True, input_shape=(look_back, LSTMfeatures),
                   activation=LSTMactivation,
                   recurrent_activation=LSTMrecurrent_activation, unroll=LSTMunroll,),
              )
    model.add(Dropout(LSTMdropout))
    # model.add(LSTM(units=LSTMnn, return_sequences=True))
    # model.add(Dropout(LSTMdropout))
    # model.add(LSTM(units=LSTMnn, return_sequences=True))
    # model.add(Dropout(LSTMdropout))
    model.add(LSTM(units=LSTMnn, activation=LSTMactivation))
    model.add(Dropout(LSTMdropout))
    model.add(Dense(look_back))  # 将神经元数量设置为与look_back相同
    # model.add(Activation('linear'))
    # 创建ReduceLROnPlateau学习率调度器实例
    # lr_scheduler = CustomReduceLROnPlateau(min_lr=1e-6, verbose=1)
    # 创建余弦退火重启调度器实例
    lr_scheduler = LearningRateScheduler(cosine_annealing_warm_restarts)
    model.compile(loss=LSTMloss, optimizer=LSTMoptimizer)
    checkpoint = ModelCheckpoint('./lstm_ckpt/weights.{epoch:03d}-{val_loss:.6f}.h5', monitor='val_loss',
                                 save_best_only=True, save_weights_only=True, verbose=1)
    if flag_wandb:
        LSTMcallbacks = [
            WandbCallback(save_model=False),
            LearningRateLoggingCallback(),
            checkpoint,
            lr_scheduler
        ]
    else:
        LSTMcallbacks = [checkpoint, lr_scheduler ]
    ckpt_dir = './lstm_ckpt'
    os.makedirs(ckpt_dir, exist_ok=True)
    # check if the checkpoint already exists
    # 在ckpt_dir目录下查找最新的后缀为.h5的文件把它的路径赋值给ckpt_path
    h5_files = [os.path.join(ckpt_dir, f) for f in os.listdir(ckpt_dir) if f.endswith('.h5')]
    ckpt_path = None
    if h5_files:
        ckpt_path = max(h5_files, key=os.path.getctime)
    if ckpt_path:
        print(f"Loading checkpoint from {ckpt_path}")
        model.load_weights(ckpt_path)
        # if you want to continue training from the loaded checkpoint with 包括架构、权重和优化器信息
        # from tensorflow.keras.models import load_model
        # model = load_model(ckpt_path)
        # But most of times I want to redefine the optimizer and loss function
        # better use save_weights_only=True
    else:
        print("No checkpoint found, training from scratch")
    if skip_training == False:
        # 训练模型并记录指标到wandb
        history = model.fit(
            train_X, train_y,
            epochs= LSTMepochs,
            batch_size=LSTMbatch_size,
            validation_data=(test_X, test_y),
            validation_batch_size=LSTMvalidation_batch_size,
            callbacks=LSTMcallbacks,
            shuffle=LSTMshuffle,
            verbose=LSTMverbose,
            validation_freq=LSTMvalidation_freq,
            max_queue_size=LSTMmax_queue_size,
            workers=LSTMworkers,
        )
        model.summary()
        # As find sometimes, the model looks better at last than auto saving one
        # so we save it just in case missing the best one
        \#model.save('./lstm_ckpt/weights.{epoch:03d}-{val_loss:.6f}.h5')
        val_loss_values = history.history['val_loss']
        last_val_loss = val_loss_values[-1]
        model.save_weights(f"./lstm_ckpt/weights.{history.epoch[-1]+1:04d}-{last_val_loss:.6f}-Last.h5")

    # make predictions
    # pred_y_tmp = model.predict(pred_X)
    # pred_y = pred_y_tmp[:, 0].reshape(-1, 1)
    # pred_y_inv = scaler.inverse_transform(pred_y)
    # calculate root mean squared error， close to 0 will be better
    # rmse = np.sqrt(np.mean(((pred_y_inv - pred_rates[look_back:]) ** 2)))
    # print(f"Actual/Predict root mean squared error: {rmse}")
    # predict test data
    # not enough size for test
    if pred_X.shape[0] > look_back:
        test_pred_x = pred_X[-look_back]
    else:
        test_pred_x = pred_X[0]
    test_pred_y = model.predict(np.expand_dims(test_pred_x, axis=0))
    test_pred_y_t = test_pred_y.reshape(-1, 1)
    test_pred_y_inv = scaler.inverse_transform(test_pred_y_t).flatten()
    # calculate root mean squared error， close to 0 will be better
    pred_rates_tmp= pred_rates.flatten()[-look_back:]
    rmse = np.sqrt(np.mean(((test_pred_y_inv - pred_rates_tmp) ** 2)))
    print(f"!!!Actual/Predict root mean squared error: {rmse}")
    # predict future unknown data
    true_pred_x = pred_X[-1]
    true_pred_y = model.predict(np.expand_dims(true_pred_x, axis=0))
    true_pred_y_t = true_pred_y.reshape(-1, 1)
    true_pred_y_inv = scaler.inverse_transform(true_pred_y_t).flatten()
    # 为预测日期添加未来 look_back 天的日期
    last_date = datetime.datetime.fromtimestamp(pred_dates[-1].astype('int64') // 1000000000)
    # 初始化一个空列表来存储未来日期
    future_dates = []
    # 使用循环为未来日期列表添加日期
    for i in range(look_back):
        next_date = last_date + datetime.timedelta(days=i + 1)
        future_dates.append(next_date)
    pred_dates_extended = list(pred_dates) + [np.datetime64(date.strftime("%Y-%m-%d")) for date in future_dates]
    # 拼接历史和未来预测值
    pred_rates_extended = np.zeros(len(pred_dates_extended))
    pred_rates_extended[:len(pred_rates)] = pred_rates.flatten()
    pred_rates_extended[len(pred_rates):] = true_pred_y_inv.flatten()
    plt.plot(pred_dates_extended, pred_rates_extended, label='Predicted Data')
    plt.plot(pred_dates, pred_rates, label='Actual Data')
    plt.plot(pred_dates_extended[-look_back*2:-look_back], test_pred_y_inv, label='Validate Data')
    # plt.plot(pred_dates_extended[n_future_dates:-look_back], pred_rates_extended[n_future_dates:-look_back], label='Actual Data')
    # plt.plot(pred_dates_extended[n_future_dates:], adjusted_predictions, label='Predicted Data Aligned')
    # plt.plot(pred_dates_extended[n_future_dates:], final_pred_y, label='Predicted Data')
    # plt.plot(pred_dates_extended[-look_back:], future_pred_y, label='Predicted Data')
    # plt.plot(true_pred_y_inv, label='Predicted Data')
    # plt.plot(pred_rates[look_back:], label='Actual Data')
    plt.xlabel('Date')
    plt.ylabel('USD/JPY Price')
    plt.title('Price Prediction Actual vs Predicted')
    plt.legend()
    plt.show()
# End of LSTM Model definition
# --------------------------------------------

if TrainingMethod == "LSTM":
    LSTMModel(window_size)
elif TrainingMethod == "TFModel":
    TFModel()
# Save model as ONNX format
# dummy_input = torch.randn(1, input_size, dtype=torch.float32)
# torch.onnx.export(model, dummy_input, ckpt_path_onnx)
if flag_wandb and not skip_training:
    # close wandb
    wandb.finish()
print("Done")
```
![[Untitled 8.png]]
```JavaScript
Windows size = 6
Lay = 2
LSTMnn = 100
LSTMoptimier = Adam
Epochs = 100
Batch_size = 4
validation_batch_size = 1
LossFunc = MSE
rmse  = 0.53
```
  
```JavaScript
<<< Day 6 >>>
你好，下面程序是deep learning来分析一组时序二维数据日期（x轴）和price（y轴）数据，来预测未来的几天后的走势，采用keras的LSTM模型，look_back 和windows_size为6，也就是现实世界里6个工作日，你作为深度学习领域的专家，请帮助我一起来完事它，
 a. 接下去的交谈过程中，你只要列出我修改的部分和上下问文，不用输出所有内容，除非我让你输出所有。
b.  如果陷入僵局，请切换思路，一步步来分析，给出建议
c.  给出建议的时候，特别注意变量是元组还是列的类型，以及Shape和 size
d. 下面请看具体程序，理解它, 
e.  要问的是我们如何利用最后的pred_X[-1]和训练好的模型model.predict()预测出未来第一天的price，然后利用未来的预测的值，和过去已知的值，利用训练好的模型再预测出未来第二天的价格，循环一直到look_back 未来几天的价格值，最后plot出图
程序会加在下面这句注释的后面
  # predict future unknown data

""" program starts from here
```
```JavaScript
model.compile(loss=LSTMloss, optimizer=LSTMoptimizer, metrics=LSTMmetrics) 这一句里面除了['accuracy']  还有其他选择吗?
是的，您可以选择其他指标以衡量模型在训练和验证期间的性能。Keras 提供了许多内置的指标，您可以在这里找到完整列表：Keras Metrics。以下是一些常用的指标：
'mean_squared_error' 或 'mse'：计算均方误差，适用于回归问题。
'mean_absolute_error' 或 'mae'：计算平均绝对误差，也适用于回归问题。
'root_mean_squared_error' 或 'rmse'：计算均方根误差，也适用于回归问题。
'precision'：计算精确度，适用于分类问题。
'recall'：计算召回率，适用于分类问题。
'f1_score'：计算 F1 分数，适用于分类问题。
```
```JavaScript
# initial starts
import os
import sys
import datetime
import nasdaqdatalink as ndl
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import keras
import wandb
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import numpy as np
import matplotlib.pyplot as plt
from keras.models import Sequential
from keras.layers import Dense, LSTM
from keras.layers import Dropout
# from keras.models import load_model
from keras.optimizers import Adam
from keras.optimizers import SGD
from keras.callbacks import ModelCheckpoint
from keras.callbacks import Callback
# from keras import backend as K
from keras.callbacks import LearningRateScheduler
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error
from wandb.keras import WandbCallback
import tensorflow as tf
import tf2onnx

class TimeSeriesDataset(Dataset):
    def __init__(self, data, window_size):
        self.data = data
        self.window_size = window_size
    def __len__(self):
        return len(self.data) - self.window_size
    def __getitem__(self, idx):
        # x = torch.tensor([item[1] for item in self.data[idx:idx+self.window_size]])
        # y = torch.tensor(self.data[idx+self.window_size][1])
        x = np.array([item[1] for item in self.data[idx:idx+self.window_size]])
        y = np.array(self.data[idx+self.window_size][1])
        x = torch.tensor(x)
        y = torch.tensor(y)
        return x, y
class TransformerModel(nn.Module):
    def __init__(self, input_size, output_size, d_model, nhead, num_layers):
        super(TransformerModel, self).__init__()
        self.encoder = nn.Linear(input_size, d_model)
        self.decoder = nn.Linear(d_model, output_size)
        self.transformer = nn.Transformer(d_model=d_model, nhead=nhead, num_encoder_layers=num_layers,
                                          num_decoder_layers=num_layers)
    def forward(self, x):
        x = self.encoder(x)
        x = x.unsqueeze(0)  # add batch dimension
        y = self.decoder(x[:, -1, :])
        y = y.squeeze(0)  # remove batch dimension
        return y

def accuracy(y_pred, y_true):
    return torch.mean(((torch.abs(y_pred - y_true) / y_true) <= 0.05).float()).item()
def root_mean_squared_error(y_true, y_pred):
    mse = tf.keras.losses.mean_squared_error(y_true, y_pred)
    return tf.sqrt(mse)
# Ray it can be adjusted
apikey = '-Sh1H8jC-naKDgkSzeSR'
ndl.ApiConfig.api_key = apikey
\#disable warning message
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
window_size = 6 #  number of previous days to use for prediction or training bactch size
# Ray it can be adjusted
# set hyperparameters
input_size = 1  # number of features in input data
output_size = 1  # number of features in output data
d_model = 512  # 8 dimension of model
nhead = 64  # 2 number of attention heads
num_layers = 5  # 2 number of layers in transformer model
learning_rate = 2e-5 # 0.0001  # learning rate for optimizer
# set to True to skip training and load pretrained model
skip_training = True
# set if we need wandb output or not
flag_wandb = False
save_onnx = False  # set to True to save model as onnx file
# -------------------
TrainingMethod = 'LSTM' # 'LSTM' or 'Transformer'
LSTMnn = 100 # number of LSTM network
# LSTMoptimizer = SGD(learning_rate=learning_rate, momentum=0.9) # optimizer
LSTMoptimizer = Adam(learning_rate=learning_rate) # optimizer
# LSTMloss = 'mean_squared_error' # loss function
# Huber Loss 是均方误差和平均绝对误差之间的一种折衷。在误差较小的情况下，Huber损失表现为均方误差；
# 在误差较大的情况下，表现为平均绝对误差。这使得它对离群值具有较好的鲁棒性。
# LSTMloss = tf.keras.losses.Huber(), # loss function
# Log-Cosh损失函数是另一个在回归问题中表现良好的损失函数。它计算预测值
# 与真实值之间差的双曲余弦的对数。这个损失函数对于大误差具有平滑特性，使得梯度更新变得更稳定。
LSTMloss = tf.keras.losses.LogCosh() # loss function
# LSTMloss = root_mean_squared_error # 有放大误差的问题
LSTMepochs = 100  # number of epochs
LSTMbatch_size = window_size # batch size
LSTMshuffle = True  # shuffle
LSTMverbose = 1  # verbose
LSTMdropout = 0.2 # dropout rate
LSTMreturn_sequences = True  # return sequences
LSTMfeatures = 1  # features
LSTMvalidation_batch_size = window_size  # validation batch size
LSTMvalidation_freq = 1  # validation frequency
LSTMmax_queue_size = 100  # max queue size
LSTMworkers = 8  # workers
LSTMuse_multiprocessing = False  # use multiprocessing
LSTMactivation = 'tanh'  # activation
LSTMrecurrent_activation = 'sigmoid'  # recurrent activation
LSTMrecurrent_dropout = 0.0
# 当unroll为True时，LSTM层将展开循环计算，并将其表示为大型张量表达式。
# 展开循环可以提高计算性能，尤其是在GPU上。然而，它也可能导致更高的内存
# 消耗，特别是当输入序列较长时。因此，在设置unroll为True之前，需要权衡
# 计算性能和内存消耗的关系。
LSTMunroll = True
\#LSTMmetrics = ['accuracy']  # metrics
LSTMmetrics = ['mse']  # metrics
\#not used
LSTMvalidation_split = 0.2 # validation split
LSTMreturn_state = False # return state
LSTMstateful = False # stateful
LSTMgo_backwards = False # go backwards
LSTMtime_major = False # time major
LSTMdata_format = 'channels_last' # data format
LSTMsteps_per_epoch = None # steps per epoch
LSTMvalidation_steps = None # validation steps
LSTMrecurrent_initializer = 'glorot_uniform' # recurrent initializer
LSTMrecurrent_regularizer = None # recurrent regularizer
LSTMrecurrent_constraint = None # recurrent constraint
LSTMunit_forget_bias = True # unit forget bias
LSTMbias_initializer = 'zeros' # bias initializer
LSTMbias_regularizer = None # bias regularizer
LSTMbias_constraint = None # bias constraint

# train model
num_epochs = 100
eval_interval = 5

if flag_wandb and not skip_training:
    # Initialize wandb
    wandb.init(
        # set the wandb project where this run will be logged
        project="MKAnalysis",
    )
# prepare data for Gold
# ------------------------
# data_train_in = ndl.get('LBMA/GOLD', start_date='1998-01-01', end_date='2022-05-01', returns='numpy')
# data_test_in = ndl.get('LBMA/GOLD', start_date='2022-05-02', end_date='2023-02-28', returns='numpy')
# data_pred_in = ndl.get('LBMA/GOLD', start_date='2023-03-01', end_date='2023-04-11', returns='numpy')
# data_train = np.array([(record[0], record[1]) for record in data_train_in])
# data_test = np.array([(record[0], record[1]) for record in data_test_in])
# data_pred = np.array([(record[0], record[1]) for record in data_pred_in])
# ------------------------
# prepare data for USD/JPY
# ------------------------
data_train = ndl.get('BOE/XUDLJYD', start_date='1998-01-01', end_date='2022-05-31', returns='numpy')
data_test = ndl.get('BOE/XUDLJYD', start_date='2022-06-01', end_date='2022-12-31', returns='numpy')
data_pred = ndl.get('BOE/XUDLJYD', start_date='2023-01-01', end_date='2023-04-12', returns='numpy')
# ------------------------
print("data_train shape:", data_train.shape)
print("data_test shape:", data_test.shape)
print("First 5 rows of data_train:")
print(data_train[:5])
print("First 5 rows of data_test:")
print(data_test[:5])
# Ray it can be adjusted
# Scale data
scaler = MinMaxScaler()
train_dates, train_rates = zip(*data_train)
test_dates, test_rates = zip(*data_test)
pred_dates, pred_rates = zip(*data_pred)
train_rates = np.array(train_rates).reshape(-1, 1)
test_rates = np.array(test_rates).reshape(-1, 1)
pred_rates = np.array(pred_rates).reshape(-1, 1)
# Truncate pred_data to match the expected length of pred_y_inv
# pred_rates = pred_rates[:-(window_size + 1)]
# Convert datetime64 to datetime
# dfdates = [d.tolist() for d in test_dates]
# plt.plot(dfdates, test_rates)
# plt.title('Test data')
# plt.xlabel('Date')
# plt.ylabel('Rate')
# plt.show()
scaled_train_data = scaler.fit_transform(train_rates)
scaled_test_data = scaler.transform(test_rates)
scaled_pred_data = scaler.transform(pred_rates)
train_data = [(i + 1, scaled_train_data[i]) for i in range(len(scaled_train_data))]
test_data = [(i + 1, scaled_test_data[i]) for i in range(len(scaled_test_data))]
pred_data = [(i + 1, scaled_pred_data[i]) for i in range(len(scaled_pred_data))]

train_dataset = TimeSeriesDataset(train_data, window_size)
train_dataloader = DataLoader(train_dataset, batch_size=window_size, shuffle=True)
test_dataset = TimeSeriesDataset(test_data, window_size)
test_dataloader = DataLoader(test_dataset, batch_size=window_size, shuffle=False)
pred_dataset = TimeSeriesDataset(pred_data, 1)
pred_dataloader = DataLoader(pred_dataset, batch_size=1, shuffle=False)
## ------------------------------
## Transformer model definition
def TFModel():
    # initialize model and optimizer
    model = TransformerModel(input_size, output_size, d_model, nhead, num_layers)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model= model.to(device)
    torch.set_float32_matmul_precision('medium')
    # Load the model weights if available
    ckpt_dir = './mk_ckpt'
    os.makedirs(ckpt_dir, exist_ok=True)
    ckpt_path_onnx = os.path.join(ckpt_dir, 'model_weights.onnx')
    ckpt_path_pth = os.path.join(ckpt_dir, 'model_weights.pth')
    \#if os.path.exists(ckpt_path_onnx):
    #    onnx_model = onnx.load(ckpt_path_onnx)
    #    pytorch_model = ConvertModel(onnx_model)
    #    model.load_state_dict(pytorch_model.state_dict())
    # ckpt_path = os.path.join(ckpt_dir, 'model_weights.pth')
    if os.path.isfile(ckpt_path_pth):
        model.load_state_dict(torch.load(ckpt_path_pth))
        print("Loaded model weights from checkpoint")
    if skip_training == False:
        \#optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-5)
        optimizer = optim.AdamW(model.parameters(), lr=learning_rate)
        # Ray it can be adjusted
        scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2, verbose=False)
        for epoch in range(num_epochs):
            running_loss = 0.0
            running_accuracy = 0.0
            model.train()
            batch_idx = 0
            # Initialize gradient averages
            avg_grads = {name: 0 for name, _ in model.named_parameters()}
            for batch_idx, (x, y) in enumerate(tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{num_epochs}")):
                x, y = x.to(device), y.to(device)
                optimizer.zero_grad()
                y_pred = model(x.float()).to(device)
                if y_pred.shape != y.shape:
                    y_pred =  y_pred[:y.shape[0], :]
                loss = nn.L1Loss()(y_pred, y.float().to(device))
                loss.backward()
                optimizer.step()
                scheduler.step(epoch + batch_idx / len(train_dataloader))
                # Accumulate gradients
                for name, param in model.named_parameters():
                    if param.grad is not None:
                        avg_grads[name] += param.grad.abs().mean().item()
                running_loss += loss.item()
                running_accuracy += accuracy(y_pred, y.float().to(device))
            running_loss /= (batch_idx + 1)
            running_accuracy /= (batch_idx + 1)
            # Log learning rate
            current_lr = scheduler.get_last_lr()[0]
            # print(f"Epoch {epoch+1}, loss: {running_loss:.4f}, accuracy: {running_accuracy:.4f}")
            if flag_wandb:
                wandb.log({"train_loss": running_loss, "train_accuracy": running_accuracy, "learning_rate": current_lr})
            # Calculate gradient averages and log to wandb
            if flag_wandb:
                for name, avg_grad in avg_grads.items():
                    avg_grads[name] /= (batch_idx + 1)
                    if f"avg_grad_{name}" == "avg_grad_encoder.bias":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
                    if f"avg_grad_{name}" == "avg_grad_encoder.weight":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
                    if f"avg_grad_{name}" == "avg_grad_decoder.bias":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
                    if f"avg_grad_{name}" == "avg_grad_decoder.weight":
                        wandb.log({f"avg_grad_{name}": avg_grads[name]})
            if (epoch + 1) % eval_interval == 0:
                test_loss = 0.0
                model.eval().to(device)
                with torch.no_grad():
                    for idx, (x, y) in enumerate(test_dataloader):
                        x, y = x.to(device), y.to(device)
                        y_pred = model(x.float().to(device))
                        # Handle incomplete batch
                        if y_pred.shape != y.shape:
                            y_pred = y_pred[:y.shape[0], :]
                        test_loss += nn.L1Loss()(y_pred, y.float().to(device))
                        # Print progress
                        if idx % 10 == 0:
                            print(f"Evaluating: {idx}/{len(test_dataloader)}", end='\r')
                            sys.stdout.flush()
                test_loss /= len(test_dataloader)
                if flag_wandb:
                    print(f"Test loss: {test_loss:.4f}")
                    wandb.log({"Test Loss": test_loss})

        # Save the model weights
        torch.save(model.state_dict(), ckpt_path_pth)
        # data alignment with real world
    # 1. Predict on test dataset
    model.eval().to(device)
    predictions = []
    predictions.append(0)
    with torch.no_grad():
        for x, _ in pred_dataloader:
            x = x.to(device)
            y_pred = model(x.float().to(device))
            predictions.append(y_pred.item())
    # 2. Transform predictions and actual values back to the original scale
    predictions = scaler.inverse_transform(np.array(predictions).reshape(-1, 1)).flatten()
    predictions[0] = pred_rates[0][0]
    # 2. Calculate the percentage changes in predictions
    predictions_pct_changes = np.diff(predictions) / predictions[:-1]
    predictions_pct_changes[0] = 0
    # 3. Apply the percentage changes to the first actual value
    adjusted_predictions = [pred_rates[0][0]]
    for pct_change in predictions_pct_changes:
        adjusted_predictions.append(adjusted_predictions[-1] * (1 + pct_change))
    actual_values = pred_rates
    dates = pred_dates
    print(dates)
    # 4. Plot predictions and actual values
    plt.figure(figsize=(12, 6))
    plt.plot(dates, actual_values, label='Actual', color='blue')
    plt.plot(dates, adjusted_predictions, label='Predicted', color='red')
    # plt.plot(dates, predictions, label='Predicted', color='red')
    plt.xlabel('Date')
    plt.ylabel('USD/JPY Exchange Rate')
    plt.title('Predictions vs Actual Values')
    plt.legend()
    plt.show()
## End of TFModel
## ------------------------------

# 数据预处理部分以适应多输出预测 look_bck:look_back
def create_datasetX(dataset, look_back=1):
    dataX, dataY = [], []
    for i in range(len(dataset) - look_back * 2):
        dataX.append(dataset[i:(i + look_back), 0])
        dataY.append(dataset[(i + look_back):(i + look_back * 2), 0])
    return np.array(dataX), np.array(dataY)
# 数据预处理部分以适应多输出预测 look_bck:1
def create_dataset(dataset, look_back):
    dataX, dataY = [], []
    for i in range(len(dataset) - look_back):
        a = dataset[i:(i + look_back), 0]
        dataX.append(a)
        dataY.append(dataset[i + look_back, 0])
    return np.array(dataX), np.array(dataY)
def cosine_annealing_warm_restarts(epoch, lr):
    initial_lr = 0.01
    t0 = 10
    tmult = 2
    # 计算当前周期
    t_i = t0
    last_restart = 0
    while epoch > last_restart + t_i:
        last_restart += t_i
        t_i *= tmult
    # 计算周期内的当前epoch
    epoch_in_cycle = epoch - last_restart
    # 计算新学习率
    new_lr = initial_lr * 0.5 * (1 + np.cos(epoch_in_cycle * np.pi / t_i))
    return new_lr

class CustomReduceLROnPlateau(Callback):
    def __init__(self, monitor='val_loss', factor=0.1, patience=10, min_lr=1e-6, verbose=1):
        super(CustomReduceLROnPlateau, self).__init__()
        self.monitor = monitor
        self.factor = factor
        self.patience = patience
        self.min_lr = min_lr
        self.verbose = verbose
        self.best_loss = np.inf
        self.wait = 0
    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}
        current_loss = logs.get(self.monitor)
        if current_loss is None:
            raise ValueError(f'Monitor value `{self.monitor}` not found in logs.')
        if current_loss < self.best_loss:
            self.best_loss = current_loss
            self.wait = 0
        else:
            self.wait += 1
        if self.wait >= self.patience:
            current_lr = float(tf.keras.backend.get_value(self.model.optimizer.lr))
            new_lr = max(current_lr * self.factor, self.min_lr)
            tf.keras.backend.set_value(self.model.optimizer.lr, new_lr)
            self.wait = 0
            if self.verbose:
                print(f"Epoch {epoch + 1}: reducing learning rate to {new_lr}.")
class LearningRateLoggingCallback(keras.callbacks.Callback):
    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}
        lr = keras.backend.get_value(self.model.optimizer.lr)
        wandb.log({"lr": lr})
def save_onnx_model(model_file):
    model = tf.keras.models.load_model(model_file)
    input_signature = [tf.TensorSpec(shape=model.inputs[0].shape, dtype=model.inputs[0].dtype, name=model.inputs[0].name)]
    output_signature = [tf.TensorSpec(shape=model.outputs[0].shape, dtype=model.outputs[0].dtype, name=model.outputs[0].name)]
    onnx_model, _ = tf2onnx.convert.from_keras(model, input_signature=input_signature, output_signature=output_signature, opset=13)
    onnx_model_path = model_file.replace('.h5', '.onnx')
    with open(onnx_model_path, "wb") as f:
        f.write(onnx_model.SerializeToString())
def predict_future(model, look_back, last_known_data, scaler, num_predictions):
    future_predictions = []
    for _ in range(num_predictions):
        input_data = np.reshape(last_known_data, (1, look_back, 1))
        prediction = model.predict(input_data)
        future_predictions.append(prediction[0][0])
        # 更新已知数据，用新的预测值替换最旧的数据
        last_known_data = np.append(last_known_data[1:], prediction)
    return scaler.inverse_transform(np.array(future_predictions).reshape(-1, 1))
# LSTM Model definition
def LSTMModel( look_back ):
    train_X, train_y = create_dataset(scaled_train_data, look_back)
    test_X, test_y = create_dataset(scaled_test_data, look_back)
    pred_X, _ = create_dataset(scaled_pred_data, look_back)
    # reshape input to be [samples, time steps, features]
    train_X = np.reshape(train_X, (train_X.shape[0], train_X.shape[1], LSTMfeatures))
    test_X = np.reshape(test_X, (test_X.shape[0], test_X.shape[1], LSTMfeatures))
    pred_X = np.reshape(pred_X, (pred_X.shape[0], pred_X.shape[1], LSTMfeatures))
    # create and fit the LSTM network
    model = Sequential()
    # model.add(LSTM(units=LSTMnn, return_sequences=False, input_shape=(look_back, 1)))
    # LSTM层有两个激活函数：一个是activation，另一个是recurrent_activation。
    # activation激活函数用于计算输出门、输入门和候选记忆细胞的值。在默认情况下，activation函数是tanh。
    # recurrent_activation激活函数用于计算LSTM的遗忘门。在默认情况下，recurrent_activation函数是
    # sigmoid。sigmoid函数将输出限制在0和1之间，因此遗忘门的值可以解释为在每个时间步长遗忘记忆细胞中的某个部分。
    model.add(
              LSTM(units=LSTMnn, return_sequences=True, input_shape=(look_back, LSTMfeatures),
                   activation=LSTMactivation,
                   recurrent_activation=LSTMrecurrent_activation, unroll=LSTMunroll,),
              )
    model.add(Dropout(LSTMdropout))
    # model.add(LSTM(units=LSTMnn, return_sequences=True))
    # model.add(Dropout(LSTMdropout))
    # model.add(LSTM(units=LSTMnn, return_sequences=True))
    # model.add(Dropout(LSTMdropout))
    model.add(LSTM(units=LSTMnn, activation=LSTMactivation))
    model.add(Dropout(LSTMdropout))
    # model.add(Dense(look_back, activation= LSTMactivation))  # 将神经元数量设置为与look_back相同
    model.add(Dense(1, activation= LSTMactivation)) # 将神经元数量设置为1, 因为我们只需要预测一个值
    # model.add(Dense(look_back))  # 将神经元数量设置为与look_back相同
    # model.add(Activation('linear'))
    # 创建ReduceLROnPlateau学习率调度器实例
    # lr_scheduler = CustomReduceLROnPlateau(min_lr=1e-6, verbose=1)
    # 创建余弦退火重启调度器实例
    lr_scheduler = LearningRateScheduler(cosine_annealing_warm_restarts)
    model.compile(loss=LSTMloss, optimizer=LSTMoptimizer, metrics=LSTMmetrics)
    checkpoint = ModelCheckpoint('./lstm_ckpt/weights.{epoch:03d}-{val_loss:.6f}.h5', monitor='val_loss',
                                 save_best_only=True, save_weights_only=True, verbose=1)
    if flag_wandb:
        LSTMcallbacks = [
            WandbCallback(save_model=False),
            LearningRateLoggingCallback(),
            checkpoint,
            lr_scheduler
        ]
    else:
        LSTMcallbacks = [checkpoint, lr_scheduler ]
    ckpt_dir = './lstm_ckpt'
    os.makedirs(ckpt_dir, exist_ok=True)
    # check if the checkpoint already exists
    # 在ckpt_dir目录下查找最新的后缀为.h5的文件把它的路径赋值给ckpt_path
    h5_files = [os.path.join(ckpt_dir, f) for f in os.listdir(ckpt_dir) if f.endswith('.h5')]
    ckpt_path = None
    if h5_files:
        ckpt_path = max(h5_files, key=os.path.getctime)
    if ckpt_path:
        print(f"Loading checkpoint from {ckpt_path}")
        model.load_weights(ckpt_path)
        # if you want to continue training from the loaded checkpoint with 包括架构、权重和优化器信息
        # from tensorflow.keras.models import load_model
        # model = load_model(ckpt_path)
        # But most of times I want to redefine the optimizer and loss function
        # better use save_weights_only=True
    else:
        print("No checkpoint found, training from scratch")
    if skip_training == False:
        # 训练模型并记录指标到wandb
        history = model.fit(
            train_X, train_y,
            epochs= LSTMepochs,
            batch_size=LSTMbatch_size,
            validation_data=(test_X, test_y),
            validation_batch_size=LSTMvalidation_batch_size,
            callbacks=LSTMcallbacks,
            shuffle=LSTMshuffle,
            verbose=LSTMverbose,
            validation_freq=LSTMvalidation_freq,
            max_queue_size=LSTMmax_queue_size,
            workers=LSTMworkers,
        )
        model.summary()
        # As find sometimes, the model looks better at last than auto saving one
        # so we save it just in case missing the best one
        \#model.save('./lstm_ckpt/weights.{epoch:03d}-{val_loss:.6f}.h5')
        val_loss_values = history.history['val_loss']
        last_val_loss = val_loss_values[-1]
        model.save_weights(f"./lstm_ckpt/weights.{history.epoch[-1]+1:04d}-{last_val_loss:.6f}-Last.h5")

    # make predictions
    # pred_y_tmp = model.predict(pred_X)
    # pred_y = pred_y_tmp[:, 0].reshape(-1, 1)
    # pred_y_inv = scaler.inverse_transform(pred_y)
    # calculate root mean squared error， close to 0 will be better
    # rmse = np.sqrt(np.mean(((pred_y_inv - pred_rates[look_back:]) ** 2)))
    # print(f"Actual/Predict root mean squared error: {rmse}")
    # predict test data
    # not enough size for test
    test_pred_y = model.predict(pred_X)
    test_pred_y_inv = scaler.inverse_transform(test_pred_y)
    # calculate root mean squared error， close to 0 will be better
    pred_rates_tmp= pred_rates[look_back:]
    rmse = np.sqrt(np.mean(((test_pred_y_inv - pred_rates_tmp) ** 2)))
    print(f"!!!Actual/Predict root mean squared error: {rmse}")
    # predict future unknown data
    future_predictions = predict_future(model, window_size, pred_X[-1], scaler, look_back)
    # 为预测日期添加未来 look_back 天的日期
    last_date = datetime.datetime.fromtimestamp(pred_dates[-1].astype('int64') // 1000000000)
    # 初始化一个空列表来存储未来日期
    future_dates = []
    # 使用循环为未来日期列表添加日期
    for i in range(look_back):
         next_date = last_date + datetime.timedelta(days=i + 1)
         future_dates.append(next_date)
    pred_dates_extended = list(pred_dates) + [np.datetime64(date.strftime("%Y-%m-%d")) for date in future_dates]
    # 将预测值添加到实际数据的图表中
    pred_rates_extended = np.concatenate((pred_rates.flatten(), future_predictions.flatten()))
    plt.plot(pred_dates[look_back:], test_pred_y_inv, label='Predicted Past Data for validation')
    plt.plot(pred_dates_extended[look_back:], pred_rates_extended[look_back:], label='Predicted future Data')
    plt.plot(pred_dates[look_back:], pred_rates[look_back:], label='Actual Data')
    plt.xlabel('Date')
    plt.ylabel('Price')
    plt.title('Price Prediction Actual vs Predicted')
    plt.legend()
    plt.show()
# End of LSTM Model definition
# --------------------------------------------

if TrainingMethod == "LSTM":
    LSTMModel(window_size)
elif TrainingMethod == "TFModel":
    TFModel()
# Save model as ONNX format
# dummy_input = torch.randn(1, input_size, dtype=torch.float32)
# torch.onnx.export(model, dummy_input, ckpt_path_onnx)
if flag_wandb and not skip_training:
    # close wandb
    wandb.finish()
print("Done")
```