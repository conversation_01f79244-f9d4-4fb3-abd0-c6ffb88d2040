---
DocFlag:
  - Reference
  - Tested
Updated: 2024-08-11T14:54
tags:
  - AI->-Embedding
  - AI->-Theory
Created: 2024-08-07T12:13
---
Memo
issue
LLM2Vec: Large Language Models Are Secretly Powerful Text Encoders
Installation
Getting Started
Preparing the model
Inference
Model List
Training
MNTP training
Unsupervised contrastive training (SimCSE)
Supervised contrastive training
Word-level tasks training
Evaluation
MTEB Evaluation
Citation
Bugs or questions?

# Memo

/opt/workspace/mom/soundTest.ipynb

```JavaScript
Require:
pip install llm2vec
pip install flash-attn --no-build-isolation
```

```JavaScript
Download Checkpoints
git clone https://huggingface.co/McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-supervised
git clone https://huggingface.co/McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp
```

```JavaScript

import torch
from llm2vec import LLM2Vec
l2v = LLM2Vec.from_pretrained(
    base_model_name_or_path="/opt/workspace/mom/model/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp",
    peft_model_name_or_path="/opt/workspace/mom/model/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-supervised",
    device_map="cuda" if torch.cuda.is_available() else "cpu",
    torch_dtype=torch.bfloat16,
)
# Encoding queries using instructions
instruction = (
    "Given a web search query, retrieve relevant passages that answer the query:"
)
queries = [
    [instruction, "how much protein should a female eat"],
    [instruction, "summit define"],
]
q_reps = l2v.encode(queries)
# Encoding documents. Instruction are not required for documents
documents = [
    "As a general guideline, the CDC's average requirement of protein for women ages 19 to 70 is 46 grams per day. But, as you can see from this chart, you'll need to increase that if you're expecting or training for a marathon. Check out the chart below to see how much protein you should be eating each day.",
    "Definition of summit for English Language Learners. : 1  the highest point of a mountain : the top of a mountain. : 2  the highest level. : 3  a meeting or series of meetings between the leaders of two or more governments.",
]
d_reps = l2v.encode(documents)
# Compute cosine similarity
q_reps_norm = torch.nn.functional.normalize(q_reps, p=2, dim=1)
d_reps_norm = torch.nn.functional.normalize(d_reps, p=2, dim=1)
cos_sim = torch.mm(q_reps_norm, d_reps_norm.transpose(0, 1))
print(cos_sim)
"""
tensor([[0.6470, 0.1619],
        [0.0786, 0.5844]])
"""

# 对于每个查询，找到最匹配的文档
best_match_indices = torch.argmax(cos_sim, dim=1)
# 打印结果
for query_idx, doc_idx in enumerate(best_match_indices):
    print(f"Query: {queries[query_idx][1]}")
    print(f"Best matching document: {documents[doc_idx]}")
    print(f"Cosine similarity score: {cos_sim[query_idx][doc_idx].item():.4f}")
    print()
# 如果你想要按相似度排序所有文档，可以这样做：
for query_idx, query in enumerate(queries):
    print(f"Query: {query[1]}")
  
    # 获取该查询与所有文档的相似度分数
    similarities = cos_sim[query_idx]
  
    # 对相似度分数进行排序，获取排序后的索引
    sorted_indices = torch.argsort(similarities, descending=True)
  
    print("Matching documents in order of relevance:")
    for rank, doc_idx in enumerate(sorted_indices):
        print(f"{rank+1}. {documents[doc_idx]} (Score: {similarities[doc_idx].item():.4f})")
    print()
```

## issue

```JavaScript
AttributeError: 'LlamaBiModel' object has no attribute 'rotary_emb'
downgraded the transformers to version 4.40.2.
pip install transformers==4.40.2
```

[![](https://opengraph.githubassets.com/ac7f365e5e43b4f5c8b40dc5c12c67f144020782b4d81ba43098e4c4085470c8/McGill-NLP/llm2vec)](https://opengraph.githubassets.com/ac7f365e5e43b4f5c8b40dc5c12c67f144020782b4d81ba43098e4c4085470c8/McGill-NLP/llm2vec)

# ==_LLM2Vec: Large Language Models Are Secretly Powerful Text Encoders_==

[![](https://camo.githubusercontent.com/ed85c8ec25c1613ea1c5247dc450731cc431da9bf0c536ed4f47a927287c2912/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f61725869762d323430342e30353936312d6233316231622e737667)](https://camo.githubusercontent.com/ed85c8ec25c1613ea1c5247dc450731cc431da9bf0c536ed4f47a927287c2912/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f61725869762d323430342e30353936312d6233316231622e737667)
[![](https://camo.githubusercontent.com/1bf37dd433bb9a6ddbe0bee070df8bddb02212d3ec0aea45a52bef65942dfe5f/68747470733a2f2f696d672e736869656c64732e696f2f707970692f762f6c6c6d32766563)](https://camo.githubusercontent.com/1bf37dd433bb9a6ddbe0bee070df8bddb02212d3ec0aea45a52bef65942dfe5f/68747470733a2f2f696d672e736869656c64732e696f2f707970692f762f6c6c6d32766563)
[![](https://camo.githubusercontent.com/644f588eef3cec9b96090d905bd227ecbc270c8fdc05c7f1ab68b40f027fd69f/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f48462532304d6f64656c732d4c4c4d325665632d4646443231452e737667)](https://camo.githubusercontent.com/644f588eef3cec9b96090d905bd227ecbc270c8fdc05c7f1ab68b40f027fd69f/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f48462532304d6f64656c732d4c4c4d325665632d4646443231452e737667)
[![](https://camo.githubusercontent.com/a4426cbe5c21edb002526331c7a8fbfa089e84a550567b02a0d829a98b136ad0/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d4d49542d79656c6c6f772e737667)](https://camo.githubusercontent.com/a4426cbe5c21edb002526331c7a8fbfa089e84a550567b02a0d829a98b136ad0/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d4d49542d79656c6c6f772e737667)
[![](https://camo.githubusercontent.com/3e4aceec6afeaab0d833a196f0827d944c1be92b313cf0defd28e2840ba9ce71/68747470733a2f2f7374617469632e706570792e746563682f62616467652f6c6c6d32766563)](https://camo.githubusercontent.com/3e4aceec6afeaab0d833a196f0827d944c1be92b313cf0defd28e2840ba9ce71/68747470733a2f2f7374617469632e706570792e746563682f62616467652f6c6c6d32766563)
==LLM2Vec is a simple recipe to convert decoder-only LLMs into text encoders. It consists of 3 simple steps: 1) enabling bidirectional attention, 2) training with masked next token prediction, and 3) unsupervised contrastive learning. The model can be further fine-tuned to achieve state-of-the-art performance.==
[![](https://private-user-images.githubusercontent.com/12207571/319390512-48efd48a-431b-4625-8e0f-248a442e3839.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjMwMDA1MDMsIm5iZiI6MTcyMzAwMDIwMywicGF0aCI6Ii8xMjIwNzU3MS8zMTkzOTA1MTItNDhlZmQ0OGEtNDMxYi00NjI1LThlMGYtMjQ4YTQ0MmUzODM5LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA4MDclMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwODA3VDAzMTAwM1omWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTg5ODdkMjk1OTU1MTA4N2ZhODU4MzI4OGIyZTU2Y2M5MGM1ZDAwMTY5MTI1ZGM5ZTJjN2QzMjk0NDBmY2NiMmEmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.v5ZalJTlhg9mRiYRvgW-xHQOqJYekrcQrJHc1bpKBxE)](https://private-user-images.githubusercontent.com/12207571/319390512-48efd48a-431b-4625-8e0f-248a442e3839.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjMwMDA1MDMsIm5iZiI6MTcyMzAwMDIwMywicGF0aCI6Ii8xMjIwNzU3MS8zMTkzOTA1MTItNDhlZmQ0OGEtNDMxYi00NjI1LThlMGYtMjQ4YTQ0MmUzODM5LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA4MDclMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwODA3VDAzMTAwM1omWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTg5ODdkMjk1OTU1MTA4N2ZhODU4MzI4OGIyZTU2Y2M5MGM1ZDAwMTY5MTI1ZGM5ZTJjN2QzMjk0NDBmY2NiMmEmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.v5ZalJTlhg9mRiYRvgW-xHQOqJYekrcQrJHc1bpKBxE)
==****************************== ==**Updates**== ==****************************==

- ==04/07: Added support for Gemma and Qwen-2 models, huge thanks to== [==@bzantium==](https://github.com/bzantium) ==for the contribution.==
- ==30/04: We release LLM2Vec transformed Meta-Llama-3 checkpoints. See our== [==HuggingFace collection==](https://huggingface.co/collections/McGill-NLP/llm2vec-660e14f536b3c8d10b3f1c34) ==for both== [==supervised==](https://huggingface.co/McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-supervised) ==and== [==unsupervised==](https://huggingface.co/McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-unsup-simcse) ==variants.==

## ==Installation==

==To use LLM2Vec, first install the llm2vec package from PyPI, followed by installing flash-attention:==
==pip install llm2vec
pip install flash-attn --no-build-isolation
================================================================

==You can also directly install the latest version of llm2vec by cloning the repository:==
==pip install -e .
pip install flash-attn --no-build-isolation
=============================================================

## ==Getting Started==

==LLM2Vec class is a wrapper on top of HuggingFace models to support enabling bidirectionality in decoder-only LLMs, sequence encoding and pooling operations. The steps below showcase an example on how to use the library.==

### ==Preparing the model==

==Initializing LLM2Vec model using pretrained LLMs is straightforward. The== ==`from_pretrained`== ==method of LLM2Vec takes a base model identifier/path and an optional PEFT model identifier/path. All HuggingFace model loading arguments can be passed to== ==`from_pretrained`== ==method. By default, the models are loaded with bidirectional connections enabled. This can be turned off by passing== ==`enable_bidirectional=False`== ==to the== ==`from_pretrained`== ==method.==
==Here, we first initialize the Llama-3 MNTP base model and load the unsupervised-trained LoRA weights (trained with SimCSE objective and wiki corpus).==
==import torch
from llm2vec import LLM2Vec
l2v = LLM2Vec.from_pretrained(
"McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp",
peft_model_name_or_path="McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-unsup-simcse",
device_map="cuda" if torch.cuda.is_available() else "cpu",
torch_dtype=torch.bfloat16,
)
========================================================================================================================================================================================================================================================================================================

==We can also load the model with supervised-trained LoRA weights (trained with contrastive learning and public E5 data) by changing the== ==`peft_model_name_or_path`====.==
==import torch
from llm2vec import LLM2Vec
l2v = LLM2Vec.from_pretrained(
"McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp",
peft_model_name_or_path="McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-supervised",
device_map="cuda" if torch.cuda.is_available() else "cpu",
torch_dtype=torch.bfloat16,
)
======================================================================================================================================================================================================================================================================================================

==By default the LLM2Vec model uses the== ==`mean`== ==pooling strategy. You can change the pooling strategy by passing the== ==`pooling_mode`== ==argument to the== ==`from_pretrained`== ==method. Similarly, you can change the maximum sequence length by passing the== ==`max_length`== ==argument (default is 512).==

### ==Inference==

==This model now returns the text embedding for any input in the form of== ==`[[instruction1, text1], [instruction2, text2]]`== ==or== ==`[text1, text2]`====. While training, we provide instructions for both sentences in symmetric tasks, and only for for queries in asymmetric tasks.==
==# Encoding queries using instructions
instruction = (
"Given a web search query, retrieve relevant passages that answer the query:"
)
queries = [
[instruction, "how much protein should a female eat"],
[instruction, "summit define"],
]
q_reps = l2v.encode(queries)

# Encoding documents. Instruction are not required for documents

documents = [
"As a general guideline, the CDC's average requirement of protein for women ages 19 to 70 is 46 grams per day. But, as you can see from this chart, you'll need to increase that if you're expecting or training for a marathon. Check out the chart below to see how much protein you should be eating each day.",
"Definition of summit for English Language Learners. : 1 the highest point of a mountain : the top of a mountain. : 2 the highest level. : 3 a meeting or series of meetings between the leaders of two or more governments.",
]
d_reps = l2v.encode(documents)

# Compute cosine similarity

q_reps_norm = torch.nn.functional.normalize(q_reps, p=2, dim=1)
d_reps_norm = torch.nn.functional.normalize(d_reps, p=2, dim=1)
cos_sim = torch.mm(q_reps_norm, d_reps_norm.transpose(0, 1))
print(cos_sim)
"""
tensor([[0.6470, 0.1619],
[0.0786, 0.5844]])
"""
=========================================================================================================================================================================================================================================================

==More examples of classification, clustering, sentence similarity etc are present in== [==examples==](https://github.com/McGill-NLP/llm2vec/blob/main/examples) ==directory.==

## ==Model List==

==Meta-Llama-3-8B==
==Mistral-7B==
==Llama-2-7B==
==Sheared-Llama-1.3B==
==Bi + MNTP==
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp)
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Mistral-7B-Instruct-v2-mntp)
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Llama-2-7b-chat-hf-mntp)
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Sheared-LLaMA-mntp)
==Bi + MNTP + SimCSE==
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-unsup-simcse)
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Mistral-7B-Instruct-v2-unsup-simcse)==**==
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Llama-2-7b-chat-hf-unsup-simcse)
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Sheared-LLaMA-unsup-simcse)
==Bi + MNTP + Supervised==
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-supervised)==*==
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Mistral-7B-Instruct-v2-mntp-supervised)
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Llama-2-7b-chat-hf-mntp-supervised)
[==HF Link==](https://huggingface.co/McGill-NLP/LLM2Vec-Sheared-LLaMA-mntp-supervised)
==* State-of-the-art on MTEB among models trained on public data==
==** Unsupervised state-of-the-art on MTEB==

## ==Training==

### ==MNTP training==

==To train the model with Masked Next Token Prediction (MNTP), you can use the== ==`experiments/run_mntp.py`== ==script. It is adapted from HuggingFace Masked Language Modeling (MLM)== [==script==](https://github.com/huggingface/transformers/blob/51bcadc10a569847b93a30dbe3a077037ae63bad/examples/pytorch/language-modeling/run_mlm.py)==. To train the Meta-Llama-3-8B model with MNTP, run the following command:==
==python experiments/run_mntp.py train_configs/mntp/MetaLlama3.json==
==The Meta-Llama-3-8B training configuration== [==file==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/mntp/MetaLlama3.json) ==contains all the training hyperparameters and configurations used in our paper.==
==
==

==Similar configurations are also available for==[==Mistral-7B==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/mntp/Mistral.json)==,== [==Llama-2-7B==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/mntp/Llama2.json)==, and== [==Sheared-Llama-1.3B==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/mntp/Sheared-Llama.json) ==models.==

### ==Unsupervised contrastive training (SimCSE)==

==For SimCSE training, we replicated the training procedure from== [==SimCSE==](https://arxiv.org/abs/2104.08821) ==paper. For training, we use the dataset 1 million sentences from English Wikipedia released by the authors. It can be downloaded using the following command:==
==wget https://huggingface.co/datasets/princeton-nlp/datasets-for-simcse/resolve/main/wiki1m_for_simcse.txt==
==To use the training script with pre-set configurations, the downloaded file should be placed in the== ==`cache`== ==directory. The directory layout should be as follows:==

```plain
cache
└── wiki1m_for_simcse.txt
```

==If the dataset is placed in a different directory, please change the dataset_file_path in the training configuration accordingly.==
==To train the Meta-Llama-3-8B model with SimCSE, run the following command:==
==python experiments/run_simcse.py train_configs/simcse/MetaLlama3.json==
==The Meta-Llama-3-8B training configuration== [==file==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/simcse/MetaLlama3.json) ==contains all the training hyperparameters and configurations used in our paper.==
==
==

==Similar configurations are also available for== [==Mistral==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/simcse/Mistral.json)==,== [==Llama-2-7B==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/simcse/Llama2.json)==, and== [==Sheared-Llama-1.3B==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/simcse/Sheared-Llama.json) ==models.==

### ==Supervised contrastive training==

==For supervised contrastive training, we use the public portion of dataset used in== [==Improving Text Embeddings with Large Language Models==](https://arxiv.org/abs/2401.00368)==, curated by authors of== [==Repetition Improves Language Model Embeddings==](https://arxiv.org/abs/2402.15449)==. The dataset can be downloaded from the== [==GitHub page of Echo embeddings repository==](https://github.com/jakespringer/echo-embeddings#training)==. To use the training script, the downloaded dataset should be placed in the== ==`cache`== ==directory. The directory layout should be as follows:==

```plain
cache
|── wiki1m_for_simcse.txt
└── echo-data
    ├── allnli_split1.jsonl
    ├── allnli_split2.jsonl
    ├── allnli.jsonl
    ├── dureader.jsonl
    ...
```

==If the dataset is placed in a different directory, please change the== ==`dataset_file_path`== ==in the training configuration accordingly.==
==To train the Meta-Llama-3-8B model with supervised contrastive learning, run the following command:==
==torchrun --nproc_per_node=8 experiments/run_supervised.py train_configs/supervised/MetaLlama3.json==
==The number of GPUs can be changed by modifying the== ==`--nproc_per_node`== ==argument.==
==The Meta-Llama-3-8B training configuration== [==file==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/supervised/MetaLlama3.json) ==contains all the training hyperparameters and configurations used in our paper.==
==
==

==Similar configurations are also available for== [==Mistral==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/supervised/Mistral.json)==,== [==Llama-2-7B==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/supervised/Llama2.json)==, and== [==Sheared-Llama-1.3B==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/supervised/Sheared-Llama.json) ==models.==

### ==Word-level tasks training==

==To tune the model for word-level tasks, we define a classifier on top of the models, and only train the classifier weights. The code is adapted from HuggingFace token classification== [==example==](https://huggingface.co/docs/transformers/en/tasks/token_classification)==. To train and test the classifier for Llama-2-7B MNTP model on== ==`pos_tags`== ==task, run the following command:==
==python experiments/run_word_task.py train_configs/word-task/Llama2-bi-mntp.json
python experiments/test_word_task.py --config_file test_configs/word-task/Llama2-bi-mntp.json
==============================================================================================================================================================================

==The config files contain all the parameters and configurations used in our paper. For instance,== ==`Llama2-bi-mntp.json`== ==includes:==
==
==

[==train_configs/word-task==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/word-task) ==and== [==test_configs/word-task==](https://github.com/McGill-NLP/llm2vec/blob/main/train_configs/word-task) ==contain similar configurations for Llama-2-7B, Mistral-7B, and Sheared-Llama-1.3B for all Uni, Bi, Bi-MNTP, and Bi-MNTP-SimCSE (LLM2Vec) variants.==

## ==Evaluation==

### ==MTEB Evaluation==

==To evaluate the model on the MTEB benchmark, we use the== ==`experiments/mteb_eval.py`== ==script. The script requires== ==`mteb>=1.12.60`====, amongst other dependencies, which can be installed with the following command.==
==pip install llm2vec[evaluation]==
==The evaluation utilizes instructions for each task which are provided in the== ==`test_configs/mteb/task_to_instructions.json`== ==file.==
==To evaluate the supervised trained Meta-Llama-3-8B model on the== ==`STS16`== ==task, run the following command:==
==python experiments/mteb_eval.py --model_name McGill-NLP/LLM2Vec-Meta-Llama-3-8B-Instruct-mntp-supervised \
--task_name STS16 \
--task_to_instructions_fp test_configs/mteb/task_to_instructions.json \
--output_dir results
==========================================================================================================================================================================================================================

==The evaluation script supports all the models available in the== [==HuggingFace collection==](https://huggingface.co/collections/McGill-NLP/llm2vec-660e14f536b3c8d10b3f1c34)==.==

## ==Citation==

==If you find our work helpful, please cite us:==
==@article:arge Language Models Are Secretly Powerful Text Encoders},
author=,
year=,
journal=,
url=
}
=================================================================================================

## ==Bugs or questions?==

==If you have any questions about the code, feel free to open an issue on the GitHub repository.==
