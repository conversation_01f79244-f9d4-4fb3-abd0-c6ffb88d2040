---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-28T13:08
tags:
  - AI->-Model
  - AI->-Programming
  - AI->-TimeSeries
Created: 2024-03-18T09:56
---
[![](https://opengraph.githubassets.com/a9d53528b0ed95d31c0b3a1d275180cde69b2443ed4218c392e348f4f50c8efa/amazon-science/chronos-forecasting)](https://opengraph.githubassets.com/a9d53528b0ed95d31c0b3a1d275180cde69b2443ed4218c392e348f4f50c8efa/amazon-science/chronos-forecasting)
## ==Chronos: Learning the Language of Time Series==
==Chronos is a family of== ==**pretrained time series forecasting models**== ==based on language model architectures. A time series is transformed into a sequence of tokens via scaling and quantization, and a language model is trained on these tokens using the cross-entropy loss. Once trained, probabilistic forecasts are obtained by sampling multiple future trajectories given the historical context. Chronos models have been trained on a large corpus of publicly available time series data, as well as synthetic data generated using Gaussian processes.==
==For details on Chronos models, training data and procedures, and experimental results, please refer to the paper== [==Chronos: Learning the Language of Time Series==](https://arxiv.org/abs/2403.07815)==.==
==  
  
====Fig. 1: High-level depiction of Chronos. (====**Left**====) The input time series is scaled and quantized to obtain a sequence of tokens. (====**Center**====) The tokens are fed into a language model which may either be an encoder-decoder or a decoder-only model. The model is trained using the cross-entropy loss. (====**Right**====) During inference, we autoregressively sample tokens from the model and map them back to numerical values. Multiple trajectories are sampled to obtain a predictive distribution.==
[![](https://github.com/amazon-science/chronos-forecasting/raw/main/figures/main-figure.png)](https://github.com/amazon-science/chronos-forecasting/raw/main/figures/main-figure.png)
## ==Architecture==
==The models in this repository are based on the== [==T5 architecture==](https://arxiv.org/abs/1910.10683)==. The only difference is in the vocabulary size: Chronos-T5 models use 4096 different tokens, compared to 32128 of the original T5 models, resulting in fewer parameters.==
==Model==
==Parameters==
==Based on==
[==**chronos-t5-tiny**==](https://huggingface.co/amazon/chronos-t5-tiny)
==8M==
[==t5-efficient-tiny==](https://huggingface.co/google/t5-efficient-tiny)
[==**chronos-t5-mini**==](https://huggingface.co/amazon/chronos-t5-mini)
==20M==
[==t5-efficient-mini==](https://huggingface.co/google/t5-efficient-mini)
[==**chronos-t5-small**==](https://huggingface.co/amazon/chronos-t5-small)
==46M==
[==t5-efficient-small==](https://huggingface.co/google/t5-efficient-small)
[==**chronos-t5-base**==](https://huggingface.co/amazon/chronos-t5-base)
==200M==
[==t5-efficient-base==](https://huggingface.co/google/t5-efficient-base)
[==**chronos-t5-large**==](https://huggingface.co/amazon/chronos-t5-large)
==710M==
[==t5-efficient-large==](https://huggingface.co/google/t5-efficient-large)
## ==Usage==
==To perform inference with Chronos models, install this package by running:==
```plain
pip install git+https://github.com/amazon-science/chronos-forecasting.git
```
==A minimal example showing how to perform inference using Chronos models:==
==import matplotlib.pyplot as plt  
import numpy as np  
import pandas as pd  
import torch  
from chronos import ChronosPipeline  
pipeline = ChronosPipeline.from_pretrained(  
"amazon/chronos-t5-small",  
device_map="cuda",  
torch_dtype=torch.bfloat16,  
)  
df = pd.read_csv("https://raw.githubusercontent.com/AileenNielsen/TimeSeriesAnalysisWithPython/master/data/AirPassengers.csv")  
# context must be either a 1D tensor, a list of 1D tensors,  
# or a left-padded 2D tensor with batch as the first dimension  
context = torch.tensor(df["\#Passengers"])  
prediction_length = 12  
forecast = pipeline.predict(context, prediction_length) # shape [num_series, num_samples, prediction_length]  
# visualize the forecast  
forecast_index = range(len(df), len(df) + prediction_length)  
low, median, high = np.quantile(forecast[0].numpy(), [0.1, 0.5, 0.9], axis=0)  
plt.figure(figsize=(8, 4))  
plt.plot(df["\#Passengers"], color="royalblue", label="historical data")  
plt.plot(forecast_index, median, color="tomato", label="median forecast")  
plt.fill_between(forecast_index, low, high, color="tomato", alpha=0.3, label="80% prediction interval")  
plt.legend()  
plt.grid()  
plt.show()  
==
## ==Citation==
==If you find Chronos models useful for your research, please consider citing the associated== [==paper==](https://arxiv.org/abs/2403.07815)==:==
```plain
@article{ansari2024chronos,
  author  = {Ansari, Abdul Fatir and Stella, Lorenzo and Turkmen, Caner and Zhang, Xiyuan, and Mercado, Pedro and Shen, Huibin and Shchur, Oleksandr and Rangapuram, Syama Syndar and Pineda Arango, Sebastian and Kapoor, Shubham and Zschiegner, Jasper and Maddix, Danielle C. and Mahoney, Michael W. and Torkkola, Kari and Gordon Wilson, Andrew and Bohlke-Schneider, Michael and Wang, Yuyang},
  title   = {Chronos: Learning the Language of Time Series},
  journal = {arXiv preprint arXiv:2403.07815},
  year    = {2024}
}
```
## ==Security==
==See== [==CONTRIBUTING==](https://github.com/amazon-science/chronos-forecasting/blob/main/CONTRIBUTING.md#security-issue-notifications) ==for more information.==
## ==License==
==This project is licensed under the Apache-2.0 License.==
```JavaScript
\#research code
/opt/workspace/researcher/labTest/AllKindsTest.ipynb
\#get api key
https://min-api.cryptocompare.com/pricing
****************************************************************
\#how to use the code
https://ryota-trade.com/?p=5793

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
from chronos import ChronosPipeline
import chronos as cr
pipeline = ChronosPipeline.from_pretrained(
  "amazon/chronos-t5-small",
  device_map="cuda",
  torch_dtype=torch.bfloat16,
)
import torch.nn as nn
from transformers import (
    AutoConfig,
    AutoModelForCausalLM,
    AutoModelForSeq2SeqLM,
    GenerationConfig,
    PreTrainedModel,
)
config = AutoConfig.from_pretrained(  "amazon/chronos-t5-small",
  device_map="cuda",
  torch_dtype=torch.bfloat16,
  )
chronos_config = cr.ChronosConfig(**config.chronos_config)
if chronos_config.model_type == "seq2seq":
    inner_model = AutoModelForSeq2SeqLM.from_pretrained( "amazon/chronos-t5-small",
      device_map="cuda",
      torch_dtype=torch.bfloat16,)
else:
    assert config.model_type == "causal"
    inner_model = AutoModelForCausalLM.from_pretrained( "amazon/chronos-t5-small",
      device_map="cuda",
      torch_dtype=torch.bfloat16,)
            
tokenizer=chronos_config.create_tokenizer()
model=cr.ChronosModel(config=chronos_config, model=inner_model).to("cuda")
model.eval()
import cryptocompare
# 设置您的API密钥（如果需要）
cryptocompare.cryptocompare._set_api_key_parameter('****************************************************************')
# 获取比特币价格数据
prices = cryptocompare.get_historical_price_day('BTC', currency='USD', limit=365)
# 转换为DataFrame
df = pd.DataFrame(prices)
# 保存为CSV
df.to_csv('bitcoin_prices.csv', index=False)
print('CSV文件已保存。')
import pandas as pd
# 文件路径，这里假设bitcoin_prices.csv文件位于您的工作目录中
file_path = 'bitcoin_prices.csv'
# 读取CSV文件到DataFrame
df = pd.read_csv(file_path)
# 查看DataFrame的前几行数据以确认读取成功
print(df.head())
torch.manual_seed(0)
torch.cuda.manual_seed(0)
np.random.seed(0)
context = torch.tensor(df["open"])
prediction_length = 30
forecast = pipeline.predict(
  context,
  prediction_length,
  num_samples=20,
  temperature=1.0,
  top_k=50,
  top_p=1.0,
) # forecast shape: [num_series, num_samples, prediction_length]
# visualize the forecast
forecast_index = range(len(df), len(df) + prediction_length)
low, median, high = np.quantile(forecast[0].numpy(), [0.1, 0.5, 0.9], axis=0)
plt.figure(figsize=(8, 4))
plt.plot(df["open"], color="royalblue", label="historical data")
plt.plot(forecast_index, median, color="tomato", label="median forecast")
plt.fill_between(forecast_index, low, high, color="tomato", alpha=0.3, label="80% prediction interval")
plt.legend()
plt.grid()
plt.show()
```
![[Notion/AI/🦜🦜🦜amazon-science-chronos-forecasting- Chronos- Pretrained (Language) Models for Probabilistic Time Series Forecasting/attachments/Untitled.png|Untitled.png]]