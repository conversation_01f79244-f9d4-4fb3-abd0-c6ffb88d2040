---
DocFlag:
  - Tested
Updated: 2024-03-18T11:13
tags:
  - AI->-Fine-Tuning
  - AI->-Model
  - AI->-Programming
Created: 2023-03-19T22:39
---
==**\#Preparation**==
```Go
https://github.com/mymusise/ChatGLM-Tuning/issues/11\#issuecomment-1474880311
https://github.com/thaumstrial/FinetuneGLMWithPeft
***https://github.com/ssbuild/chatglm_finetuning
https://github.com/lich99/ChatGLM-finetune-LoRA
\#install git lfs
https://qiita.com/cokemaniaIIDX/items/5a9643b39ddd7a906875
curl -s https://packagecloud.io/install/repositories/github/git-lfs/script.rpm.sh | sudo bash
dnf install git-lfs
git lfs --version
git lfs install
\#for deepspeed need libaio
dnf install libaio-devel
\#change default solver
conda create -n GLMTuning
conda activate GLMTuning
\#if not working, rollback:conda config --set solver classic
conda update conda
conda install conda-libmamba-solver
conda config --set solver libmamba
conda config --set solver classic
\#cuda toolkit 12.1 download, As currently pyTorch not officially support cuda12.x, below step we can skip
https://developer.nvidia.com/cuda-downloads?target_os=Linux&target_arch=x86_64&Distribution=RHEL&target_version=9&target_type=rpm_local
\#Speedup
https://zhuanlan.zhihu.com/p/613538508?utm_id=0
https://www.philschmid.de/getting-started-pytorch-2-0-transformers
pip3 install numpy --pre torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/nightly/cu118
conda install pytorch cudatoolkit=11.8 -c conda-forge
conda install tensorflow-gpu
\#install accelarate
conda install -c conda-forge accelerate
conda install -c pytorch accelerate
git clone https://github.com/TimDettmers/bitsandbytes.git
nvcc --version
cd /opt/workspace/bitsandbytes
export CUDA_VERSION=121
make
pip install .
\#install pytorch 2.0
https://www.kkaneko.jp/tools/win/pytorch.html
https://www.philschmid.de/getting-started-pytorch-2-0-transformers
pip install torch+cu120 -f https://download.pytorch.org/whl/torch_nightly.htm
conda install pytorch torchvision torchaudio -c pytorch -c nvidia
\#install lightning
https://lightning.ai/docs/pytorch/stable/starter/installation.html
conda install pytorch-lightning -c conda-forge
pip install lightning
\#install others
conda install watermark transformers datasets torchmetrics -c conda-forge

\#upgrade transformers 
pip install "transformers==4.27.1" "datasets==2.9.0" "accelerate==0.17.1" "evaluate==0.4.0" tensorboard scikit-learn
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
icetk 0.0.5 requires protobuf<3.19, but you have protobuf 4.22.1 which is incompatible.

pip install icetk
conda install evaluate tensorboard scikit-learn scikit-learn-intelex -c conda-forge
conda install fsspec psutil protobuf -c conda-forge
conda install py-cpuinfo pydantic -c conda-forge
\#do it at last
(ChatGLM) [raysheng@MONSTER:~]$ cat gputest.py
import torch
print("CUDA availability:", torch.cuda.is_available())
if torch.cuda.is_available():
    print("CUDA version:", torch.version.cuda)
else:
    print("CUDA not available.")
(ChatGLM) [raysheng@MONSTER:~]$ python gputest.py
CUDA availability: True
CUDA version: 11.7
If abvove is true then can skip
\#install latest cuda 11.8 package and it will install 12.1
https://pytorch.org/get-started/pytorch-2.0/\#faqs
pip3 install numpy --pre torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/nightly/cu118
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
gradio 3.21.0 requires fsspec, which is not installed.
evaluate 0.4.0 requires fsspec[http]>=2021.05.0, which is not installed.
deepspeed 0.8.3 requires psutil, which is not installed.
datasets 2.9.0 requires fsspec[http]>=2021.11.1, which is not installed.
icetk 0.0.5 requires protobuf<3.19, but you have protobuf 4.22.1 which is incompatible.
\#fix: conda install fsspec psutil protobuf

https://docs.nvidia.com/cuda/eula/index.html
Installation Doc
\#install cuda toolkit
conda search cuda-toolkit --info -c nvidia|grep version:
conda search cuda-python  --info -c nvidia|grep version:
conda install -c nvidia "cuda-toolkit=11.8.0"
conda install -c nvidia "cuda-python<12.0"
\#install deepspeed after install pytorch and cuda
ModuleNotFoundError: No module named 'torch._six'
https://github.com/microsoft/DeepSpeed
"The quickest way to get started with DeepSpeed is via pip, this will install the latest release of DeepSpeed which is not tied to specific PyTorch or CUDA versions."
conda install -c pytorch deepspeed
conda remove deepspeed
conda clean --a
pip install deepspeed
conda install py-cpuinfo pydantic -c conda-forge
\#transformers-4.27.3, upgrade it
pip install -U transformers

\#install deep_training
conda install python=3.10
pip install -i https://pypi.org/simple -U deep_training>=0.0.21 cpm_kernels 
pip install icetk
conda remove deepspeed
conda clean --a
pip install deepspeed

\#generate access token and grant write permission like this
*************************************

\#Finally save currently environment
conda env export > environment.yml
conda list|grep torch
conda list|grep cuda
conda list|grep light
conda list|grep transfor
conda list|grep icetk
conda list|grep deep
(GLMTuning) [raysheng@MONSTER:/opt/workspace/ai/GLMtuning]$ conda list|grep torch
pytorch                   1.13.1          cuda112py310he33e0d6_200    conda-forge
pytorch-lightning         1.9.4                    pypi_0    pypi
pytorch-triton            2.1.0+46672772b4          pypi_0    pypi
torch                     2.1.0.dev20230331+cu118          pypi_0    pypi
torchaudio                2.1.0.dev20230401+cu118          pypi_0    pypi
torchmetrics              0.11.4             pyhd8ed1ab_0    conda-forge
torchvision               0.16.0.dev20230331+cu118          pypi_0    pypi
(GLMTuning) [raysheng@MONSTER:/opt/workspace/ai/GLMtuning]$  conda list|grep cuda
cudatoolkit               11.8.0              h37601d7_11    conda-forge
nvidia-cuda-cupti-cu11    11.7.101                 pypi_0    pypi
nvidia-cuda-nvrtc-cu11    11.7.99                  pypi_0    pypi
nvidia-cuda-runtime-cu11  11.7.99                  pypi_0    pypi
pytorch                   1.13.1          cuda112py310he33e0d6_200    conda-forge
(GLMTuning) [raysheng@MONSTER:/opt/workspace/ai/GLMtuning]$ conda list|grep light
lightning-utilities       0.8.0              pyhd8ed1ab_0    conda-forge
pytorch-lightning         1.9.4                    pypi_0    pypi
(GLMTuning) [raysheng@MONSTER:/opt/workspace/ai/GLMtuning]$ conda list|grep transfor
transformers              4.27.4             pyhd8ed1ab_0    conda-forge
(GLMTuning) [raysheng@MONSTER:/opt/workspace/ai/GLMtuning]$ conda list|grep icetk
icetk                     0.0.7                    pypi_0    pypi
(GLMTuning) [raysheng@MONSTER:/opt/workspace/ai/GLMtuning]$ conda list|grep deep
deep-training             0.0.21-post0             pypi_0    pypi
deepspeed                 0.8.3                    pypi_0    pypi
```
  
```JavaScript
<<< ISSUE 1>>
\#unix is ok,but in windows. when you install deepspeed
https://github.com/microsoft/DeepSpeed/issues/1769
Collecting deepspeed
  Downloading deepspeed-0.8.3.tar.gz (765 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 765.4/765.4 kB 9.6 MB/s eta 0:00:00
  Preparing metadata (setup.py) ... error
  error: subprocess-exited-with-error
  × python setup.py egg_info did not run successfully.
  │ exit code: 1
  ╰─> [15 lines of output]
      test.c
      LINK : fatal error LNK1181: cannot open input file 'aio.lib'
      Traceback (most recent call last):
        File "<string>", line 2, in <module>
        File "<pip-setuptools-caller>", line 34, in <module>
        File "C:\Users\<USER>\AppData\Local\Temp\pip-install-57x79p2r\deepspeed_7e109fa2069e4ebeb51fc11a635fd3ea\setup.py", line 156, in <module>
          abort(f"Unable to pre-compile {op_name}")
        File "C:\Users\<USER>\AppData\Local\Temp\pip-install-57x79p2r\deepspeed_7e109fa2069e4ebeb51fc11a635fd3ea\setup.py", line 48, in abort
          assert False, msg
      AssertionError: Unable to pre-compile async_io
      DS_BUILD_OPS=1
       [WARNING]  async_io requires the dev libaio .so object and headers but these were not found.
       [WARNING]  If libaio is already installed (perhaps from source), try setting the CFLAGS and LDFLAGS environment variables to where it can be found.
       [WARNING]  One can disable async_io with DS_BUILD_AIO=0
       [ERROR]  Unable to pre-compile async_io
      [end of output]
  note: This error originates from a subprocess, and is likely not a problem with pip.
error: metadata-generation-failed
× Encountered error while generating package metadata.
╰─> See above for output.
note: This is an issue with the package mentioned above, not pip.
hint: See above for details.
\#https://github.com/microsoft/DeepSpeed
https://www.deepspeed.ai/tutorials/advanced-install/
https://visualstudio.microsoft.com/ja/downloads/
download Build Tools for Visual Studio vs_BuildTools.exe
run as adminstrator. once installed next time
search visual studio installer
Windows support is partially supported with DeepSpeed. On Windows you can build wheel with following steps, currently only inference mode is supported.
！！我注释掉Deepspeed有关的，于是通过了
Install pytorch, such as pytorch 1.8 + cuda 11.1
Install visual cpp build tools, such as VS2019 C++ x64/x86 build tools
Launch cmd console with Administrator privilege for creating required symlink folders
Run python setup.py bdist_wheel to build wheel in dist folder
\#downgrade nvcc 12->11
cuda toolkit 11.8
https://developer.nvidia.com/cuda-11-8-0-download-archive?target_os=Windows&target_arch=x86_64&target_version=11&target_type=exe_local
cuDNN download
https://developer.nvidia.com/rdp/cudnn-download
\#downgrade nvcc 12 -> 11 in wsl
https://docs.nvidia.com/deeplearning/cudnn/install-guide/index.html
conda install pytorch torchvision torchaudio pytorch-cuda=11.7 -c pytorch -c nvidia
```
  
  
  
```JavaScript
<<< ISSUE 2 >>>
!!! Finally it showing chatglm-6b-slim is not currect index /dim is not match, change to original chatglm-6b pretrained data set then working
/home/<USER>/miniconda3/envs/GLMTuning/lib/python3.10/site-packages/pytorch_lightning/trainer/configuration_validator.py:108: PossibleUserWarning: You defined a `validation_step` but have no `val_dataloader`. Skipping val loop.
  rank_zero_warn(                                                                                                                                   
INFO:pytorch_lightning.utilities.rank_zero:You are using a CUDA device ('NVIDIA RTX A4000') that has Tensor Cores. To properly utilize them, you should set `torch.set_float32_matmul_precision('medium' | 'high')` which will trade-off precision for performance. For more details, read https://pytor                                             ch.org/docs/stable/generated/torch.set_float32_matmul_precision.html\#torch.set_float32_matmul_precision
/home/<USER>/miniconda3/envs/GLMTuning/lib/python3.10/site-packages/pytorch_lightning/callbacks/model_checkpoint.py:613: UserWarning: Checkpoint directory /mnt/f/ResearchDirection/AI/GLMtuning/best_ckpt exists and is not empty.
  rank_zero_warn(f"Checkpoint directory {dirpath} exists and is not empty.")                                                                        
INFO:pytorch_lightning.utilities.rank_zero:Loading `train_dataloader` to estimate number of stepping batches.                                       
/home/<USER>/miniconda3/envs/GLMTuning/lib/python3.10/site-packages/pytorch_lightning/trainer/connectors/data_connector.py:224: PossibleUserWarning: The dataloader, train_dataloader, does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argume                                             nt` (try 24 which is the number of cpus on this machine) in the `DataLoader` init to improve performance.
  rank_zero_warn(                                                                                                                                   
INFO:pytorch_lightning.accelerators.cuda:LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]                                                                  
INFO:pytorch_lightning.callbacks.model_summary:                                                                                                     
  | Name                                  | Type                            | Params                                                                
------------------------------------------------------------------------------------------                                                          
0 | _TransformerLightningModule__backbone | MyTransformerChatGlmLMHeadModel | 736 M                                                                 
------------------------------------------------------------------------------------------                                                          
736 <USER>     <GROUP> params                                                                                                                          
0         Non-trainable params                                                                                                                      
736 M     Total params                                                                                                                              
2,944.123 Total estimated model params size (MB)                                                                                                    
Epoch 0:   0%|                                                                                                               | 0/75 [00:00<?, ?it/s]../aten/src/ATen/native/cuda/Indexing.cu:1146: indexSelectLargeIndex: block: [157,0,0], thread: [64,0,0] Assertion `srcIndex < srcSelectDimSize` fai                                             led.
../aten/src/ATen/native/cuda/Indexing.cu:1146: indexSelectLargeIndex: block: [157,0,0], thread: [65,0,0] Assertion `srcIndex < srcSelectDimSize` failed.
../aten/src/ATen/native/cuda/Indexing.cu:1146: indexSelectLargeIndex: block: [157,0,0], thread: [66,0,0] Assertion `srcIndex < srcSelectDimSize` failed.
```
```JavaScript
<<< ISSUE 3 >>>
Happen in Unix environment
RuntimeError: CUDA error: CUBLAS_STATUS_NOT_INITIALIZED when calling `cublasCreate(handle)`
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1.
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
```
  
  
```JavaScript
<<< ISSUE 4 >>
文件“F：\ResearchDirection\AI\miniconda3\envs\GLMTuning\lib\site-packages\torch\cuda\amp\grad_scaler.py”，第212行，_unscale_grads_
 引发 ValueError（“尝试取消缩放 FP16 梯度。
值错误：尝试取消缩放 FP16 渐变。
File "F:\ResearchDirection\AI\miniconda3\envs\GLMTuning\lib\site-packages\torch\cuda\amp\grad_scaler.py", line 212, in _unscale_grads_
    raise ValueError("Attempting to unscale FP16 gradients.")
ValueError: Attempting to unscale FP16 gradients.
\#reason 
trainer = Trainer(
        callbacks=[checkpoint_callback,LearningRateMonitor(logging_interval='step')],
        max_epochs=training_args.max_epochs,
        max_steps=training_args.max_steps,
        accelerator="gpu",replace_sampler_ddp=False,
        devices=data_args.devices,
        enable_progress_bar=True,
        default_root_dir=data_args.output_dir,
        gradient_clip_val=training_args.max_grad_norm,
        accumulate_grad_batches=training_args.gradient_accumulation_steps,
        num_sanity_val_steps=0,
        strategy=strategy
        \#precision=16,#半精度 RaySheng  !! if setup precision=16, then above issue happen
    )
```
  
![[Notion/AI/🦜🦜🦜ChatGLM Fine tuning Sample/attachments/Untitled.png|Untitled.png]]
---
  
```JavaScript
<<< Prepare data >>>
你是Python专家，请帮我写一个程序，从一个文件读出数据转换成json格式，文件打开和写入时候使用encoding='utf-8'，文件转换过程中tqdm显示处理的进程，输入和输出文件名，使用命令行参数传递，使用
parser = argparse.ArgumentParser(description='Convert CSV file format')
parser.add_argument('input_file', type=str, help='Input CSV file name')
parser.add_argument('output_file', type=str, help='Output CSV file name')
args = parser.parse_args()
数据处理的例子：
INPUT: dataset.csv
"容易患太阳中风的人，表证解除以后，身体仍感觉不舒适的，","需待一定的时日，正气恢复，才能痊愈。"
"病人体表发热，反而想穿很多衣服，","这是外部假热、内部真寒的表现；"
"太阳病，服了一遍桂枝汤，不仅表证不解，反而增添了烦闷不安的感觉，","这是邪气郁滞太甚所致。治疗应当先针刺风池、风府，以疏经泄邪，然后再给予桂枝汤就可以痊愈。"
"太阳病，误用攻下之后，出现脉象急促、短促，胸部胀闷的，","用桂枝去芍药汤主治。"
"太阳表证，已经过了十天，如果脉象由浮紧转浮细，总想睡眠的，","是表证已经解除的征象；"
OUTPUT: finetune_train_kanbo.json
{"id": 1, "paragraph": [{"q": "容易患太阳中风的人，表证解除以后，身体仍感觉不舒适的", "a": ["需待一定的时日，正气恢复，才能痊愈。"]}]}
{"id": 2, "paragraph": [{"q": "病人体表发热，反而想穿很多衣服", "a": ["这是外部假热、内部真寒的表现。"]}]}
{"id": 3, "paragraph": [{"q": "太阳病，服了一遍桂枝汤，不仅表证不解，反而增添了烦闷不安的感觉", "a": ["这是邪气郁滞太甚所致。治疗应当先针刺风池、风府，以疏经泄邪，然后再给予桂枝汤就可以痊愈。"]}]}
{"id": 4, "paragraph": [{"q": "太阳病，误用攻下之后，出现脉象急促、短促，胸部胀闷的", "a": ["用桂枝去芍药汤主治。"]}]}
{"id": 5, "paragraph": [{"q": "太阳表证，已经过了十天，如果脉象由浮紧转浮细，总想睡眠的", "a": ["是表证已经解除的征象。"]}]}
对于“q" key, 如果其值的最后是以字符串是"，"结尾的，都把此字符串转换成"，怎么办？", 否则转换逻辑不变输出
csv2json.py
import argparse
import csv
import json
from tqdm import tqdm
def csv_to_json(input_file, output_file):
    with open(input_file, encoding='utf-8') as csvfile:
        csv_reader = csv.reader(csvfile, delimiter=',', quotechar='"')
        data = []
        for row in tqdm(csv_reader, desc="Processing data"):
            q, a = row
            if q[-1] == "，":
                q = q[:-1] + "，怎么办？"
            data.append({"q": q, "a": [a]})
    json_data = []
    for idx, item in enumerate(data, start=1):
        json_data.append({"id": idx, "paragraph": [item]})
    with open(output_file, 'w', encoding='utf-8') as jsonfile:
        for item in json_data:
            json.dump(item, jsonfile, ensure_ascii=False)
            jsonfile.write('\n')
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert CSV file format')
    parser.add_argument('input_file', type=str, help='Input CSV file name')
    parser.add_argument('output_file', type=str, help='Output JSON file name')
    args = parser.parse_args()
    csv_to_json(args.input_file, args.output_file)
```
---
Some Concept
```Go
解释一下下面BERT模型的预训练模型的配置参数
BertConfig {
  "attention_probs_dropout_prob": 0.1,
  "classifier_dropout": null,
  "hidden_act": "gelu",
  "hidden_dropout_prob": 0.1,
  "hidden_size": 768,
  "initializer_range": 0.02,
  "intermediate_size": 3072,
  "layer_norm_eps": 1e-12,
  "max_position_embeddings": 512,
  "model_type": "bert",
  "num_attention_heads": 12,
  "num_hidden_layers": 12,
  "pad_token_id": 0,
  "position_embedding_type": "absolute",
  "transformers_version": "4.24.0",
  "type_vocab_size": 2,
  "use_cache": true,
  "vocab_size": 30522
"attention_probs_dropout_prob": 0.1 - 自注意力机制中的概率性dropout比率。这有助于防止模型过拟合。
"classifier_dropout": null - 分类器层的dropout比率。如果未指定，则使用与 "hidden_dropout_prob" 相同的值。
"hidden_act": "gelu" - 隐藏层激活函数。这里使用的是GELU（高斯误差线性单元）激活函数。
"hidden_dropout_prob": 0.1 - 隐藏层的dropout比率。dropout是一种正则化技术，有助于防止模型过拟合。
"hidden_size": 768 - Transformer中隐藏层的维度。
"initializer_range": 0.02 - 模型权重的初始化范围。权重将在这个范围内的均匀分布中随机初始化。
"intermediate_size": 3072 - 前馈神经网络（Feed-Forward Neural Network）的中间层大小。
"layer_norm_eps": 1e-12 - 层归一化（Layer Normalization）中的一个小的数值，用于防止除以0。
"max_position_embeddings": 512 - 最大位置编码的数量，限制了模型可以处理的最大序列长度。
"model_type": "bert" - 模型的类型，这里是 "bert"。
"num_attention_heads": 12 - 多头自注意力机制中的注意力头的数量。
"num_hidden_layers": 12 - Transformer模型中隐藏层的数量。
"pad_token_id": 0 - 用于填充的标记ID。在批量处理时，较短的序列将使用此填充标记进行填充，以使所有序列长度相同。
"position_embedding_type": "absolute" - 位置编码的类型。这里使用的是绝对位置编码。
"transformers_version": "4.24.0" - 使用的transformers库的版本。
"type_vocab_size": 2 - 类型词汇表的大小。对于BERT，通常有两种类型：句子A和句子B。
"use_cache": true - 是否使用缓存来加速模型的推理过程。
"vocab_size": 30522 - 词汇表的大小。这是模型可以处理的词汇数量。

隐藏层激活函数（Hidden Layer Activation Function）是神经网络中用于在隐藏层节点上应用的非线性函数。它的作用是为神经网络引入非线性特性，使得网络能够学习和表示更复杂的模式和特征。如果没有激活函数，神经网络将只能表示线性关系，这会限制其在处理复杂问题时的能力。
在深度学习中，有多种常见的激活函数，如：
Sigmoid函数：将输入值映射到0和1之间。它在早期的神经网络中较为常见，但在深度学习中较少使用，因为它容易导致梯度消失问题。
ReLU（Rectified Linear Unit）：将输入值的负部分设为0，保留正部分。ReLU激活函数在许多深度学习应用中非常受欢迎，因为它可以加速训练过程并减轻梯度消失问题。
Leaky ReLU：与ReLU类似，但允许负值有一个小的非零梯度。这有助于减轻“死亡ReLU”现象，即某些神经元在训练过程中可能不再更新。
Tanh（双曲正切）函数：将输入值映射到-1和1之间。与Sigmoid相比，tanh函数的输出范围更广泛，但它同样可能导致梯度消失问题。
GELU（高斯误差线性单元）：一种平滑的激活函数，近似于线性函数和ReLU之间的折衷。GELU在Transformer模型（如BERT和GPT）中常被使用，因为它可以提供更好的性能。
根据模型的结构和应用需求，可以选择合适的激活函数来优化网络的性能。激活函数对于神经网络的学习能力和稳定性至关重要。
```
```Go
Layer Normalization和Softmap有什么区别
Layer Normalization（层归一化）和Softmax（柔性最大值）是两种在神经网络中应用的不同技术，它们有各自的目的和用途。
Layer Normalization（层归一化）：
层归一化是一种正则化技术，用于在训练神经网络时提高模型的稳定性和收敛速度。它通过对每一层的激活值进行归一化处理，使其具有相似的尺度和分布。具体来说，对于每个样本，层归一化会计算激活值的均值和标准差，然后对激活值进行缩放和偏移，使其具有零均值和单位方差。
层归一化与批量归一化（Batch Normalization）类似，但在不同的维度上进行归一化。批量归一化是沿着批次维度进行归一化，而层归一化是沿着特征维度进行归一化。
Softmax（柔性最大值）：
Softmax是一种激活函数，通常用于分类任务的输出层。它将神经网络输出的一组实数值转换为概率分布，使得每个输出值的范围在0到1之间，并且所有输出值的和为1。这使得模型可以输出每个类别的预测概率，从而进行多分类任务。
Softmax函数的定义如下：
softmax(x_i) = exp(x_i) / Σ(exp(x_j))
其中，x_i表示输出层的第i个值，Σ表示求和符号，j表示所有输出值的索引。
总结一下，层归一化和Softmax的区别：
层归一化是一种正则化技术，用于提高神经网络在训练过程中的稳定性和收敛速度；而Softmax是一种激活函数，通常用于多分类任务的输出层。
层归一化对每一层的激活值进行归一化处理，使其具有相似的尺度和分布；Softmax则将输出层的实数值转换为概率分布。
层归一化可以应用于网络的任何层；而Softmax通常只应用于输出层。
```
---
  
==**\#Some of sample**==
[https://www.philschmid.de/getting-started-pytorch-2-0-transformers](https://www.philschmid.de/getting-started-pytorch-2-0-transformers)
  
---
==**\#Some of pattern**==
`**from**` `transformers` `**import**` `AutoModel`
`**from**` `transformers` `**import**` `AutoTokenizer`
`**from**` `transformers` `**import**` `BertConfig`
or
`**from**` `transformers` `**import**` `AutoConfig`
```Plain
model= AutoModel.from_pretrained("./models/bert-base-chinese")
print(type(model))
tokenizer = AutoTokenizer.from_pretrained("bert-base-chinese")
sentence = "床前明月光"
tokenizer(sentence)

config = BertConfig()
or
config = AutoConfig.from_pretrained("./models/bert-base-chinese")
config.num_hidden_layers=5
print(config)
config.save_pretrained("./models/bert-base-chinese")
model = AutoModel.from_pretrained("./models/bert-base-chinese", config=config)
or
model = AutoModel.from_pretrained("./models/bert-base-chinese", num_hidden_layers=3)
tens = model(**tokenizer("床前明月光，疑是地上霜。", return_tensors="pt"))
tens.last_hidden_state.shape
tokenizer() 函数将文本编码为一个字典，该字典包含模型所需的关键字参数，例如输入张量（如 "input_ids"）和其他可能的参数（如 "attention_mask"）。使用双星号（**）操作符，字典被拆包并将键值对作为关键字参数传递给模型。这样，模型就可以正确地处理输入张量，进行前向传播并产生输出结果。
best_accuracy = 0
is_best = False
t = Tableprint(['epoch', 'train_accuracy', 'train_loss', 'test_accuracy', 'test_loss', 'is_best'])
t.print_header()
for epoch in range(EPOCHS):
    train_acc, train_loss = train_epoch(model,train_dataloader,loss_fn,optimizer,device,scheduler,len(x_train))
    
    val_acc, val_loss = eval_model(
        model,
        val_dataloader,
        loss_fn,
        device,
        len(x_val)
    )
    
    if val_acc > best_accuracy:
        is_best = True
        torch.save(model.state_dict(), './models/news_classification/best_model_state.bin')
        best_accuracy = val_acc
    else:
        is_best = False
    t.print_row(epoch, f"{train_acc:.4f}", f"{train_loss:.4f}", f"{val_acc:.4f}", f"{val_loss:.4f}", is_best)
```
  
\#Print all api started from xxxxx
```Go
for api in dir(transformers):
    if api.startswith('AutoModelFor'):
        print(api)
```
  
---
  
[[使用PyTorch Lightning微调ChatGLM模型]]
  
---
  
这个Python代码片段展示了如何使用“transformers”库对预训练的基于transformer的模型进行微调以用于特定的自然语言处理（NLP）任务。代码片段以模块化的方式编写，易于调整和自定义以适用于不同的用例。
代码的第一部分定义了“LoraConfig”对象，它是“Peft”的配置对象，为模型微调过程设置了各种超参数。例如，“task_type”指定了模型将被微调的任务类型，“inference_mode”设置了模型是否应该用于推理，“r”指定了注意力头的数量，“lora_alpha”设置了Lora正则化的超参数alpha的值，“lora_dropout”设置了Lora正则化的dropout率，“target_modules”指定了模型的哪些模块应该被微调。这些超参数可以调整以微调不同的任务的模型。
代码的下一行使用“transformers”库中的“AutoTokenizer”和“AutoModel”类定义“tokenizer”和“model”对象。这些对象在大量文本数据上进行了预训练，并可以针对特定的NLP任务进行微调。将“trust_remote_code”参数设置为“True”以从Hugging Face服务器下载模型和标记器，使得可以使用预训练模型而不必担心任何安全问题。
调用“get_peft_model”函数将“LoraConfig”对象与预训练的“model”集成。此过程修改模型以适合特定的任务和数据集。一旦修改了模型，就会使用“print_trainable_parameters”方法打印其可训练参数。这一步骤是确保模型设置正确且微调过程将顺利运行的关键。
代码然后使用“get_train_dataset”函数定义了一个训练数据集，该函数准备了训练过程的输入和输出数据。数据集由一系列上下文-响应对组成，其中上下文由以前的对话历史和当前查询组成。输出数据由查询的响应组成。将“padding”和“truncation”参数设置为确保所有数据点具有相同的长度，这是使用基于transformer的模型进行处理的要求。
使用“torch.utils.data”库中的“Dataset”和“DataLoader”类处理训练数据。使用指定的批量大小创建“train_dataloader”，该批量大小可以根据不同的批量大小进行调整。使用“default_data_collator”将数据进行整理，并将输入和输出填充到相同的长度。
在初始化优化器和学习率调度程序后，使用训练数据对模型进行指定数量的时代进行训练。如果可用，首先将模型移至GPU，然后在模型上调用“train”方法。训练循环遍历“train_dataloader”，并使用“AdamW”优化器优化模型。使用“get_linear_schedule_with_warmup”函数每次迭代更新学习率以改善学习过程。
这个代码片段提供了一个简单的例子，展示了如何使用“transformers”库对预训练的基于transformer的模型进行特定的NLP任务微调。通过调整超参数和训练数据，可以使用这个代码微调基于transformer的模型用于各种任务，包括文本分类、机器翻译和问答。
总的来说，这个代码片段可以为那些想要对预训练的基于transformer的模型进行微调以适用于他们的NLP任务的人提供一个有用的起点。它是模块化的、易于定制的，并可以用作开发更高级模型的模板。
```Go
\#GPT4 做出的解释
这是一个使用PyTorch和Hugging Face Transformers库的程序，用于在基于THUDM/chatglm-6b预训练模型的基础上进行微调。代码的主要功能是处理一个简单的对话数据集，并使用PEFT（Parameter-efficient Fine-tuning）策略对模型进行微调。
导入所需的库和模块。
设置PEFT配置参数，包括任务类型、推理模式、压缩比例等。
从预训练模型"THUDM/chatglm-6b"中加载分词器（tokenizer）和模型。
使用PEFT配置将原始模型转换为PEFT模型。
设置训练参数，如设备、学习率和迭代次数。
定义一个函数get_train_dataset，用于处理训练数据集。这里的数据集非常简单，只包含一个对话样本。
使用Hugging Face的Dataset模块将处理后的数据集转换为Dataset对象，然后创建一个DataLoader实例用于数据加载和批处理。
初始化优化器（这里使用AdamW优化器）和学习率调度器（这里使用带warmup的线性调度器）。
将模型转换为半精度浮点数（使用.half()方法）以节省内存，并将模型放到指定设备上（这里是GPU）。
开始训练过程。对于每个epoch，遍历数据加载器中的每个批次，将批次数据送入模型，并计算损失。然后进行反向传播，更新模型参数，并执行学习率调度和优化器梯度归零操作。
这个程序的目标是微调一个对话生成模型，使其能够根据给定的输入生成合适的回应。请注意，这里的训练数据集非常简单且规模很小，因此实际应用中可能需要更大规模的数据集以获得更好的性能。

# 导入相关库和模块
import torch
from torch.utils.data import DataLoader
from tqdm import tqdm
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer, AutoModel, get_linear_schedule_with_warmup, \
    default_data_collator, TrainingArguments, Trainer
from peft import get_peft_config, get_peft_model, LoraConfig, TaskType
from datasets import Dataset
# 设置PEFT配置参数
peft_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM, inference_mode=False, r=8, lora_alpha=32, lora_dropout=0.1,
    target_modules=['query_key_value']
)
# 加载预训练模型的tokenizer和模型
tokenizer = AutoTokenizer.from_pretrained("THUDM/chatglm-6b", trust_remote_code=True)
model = AutoModel.from_pretrained("THUDM/chatglm-6b", trust_remote_code=True)
# 使用PEFT配置将原始模型转换为PEFT模型
model = get_peft_model(model, peft_config)
# 设置训练参数
device = 'cuda'
num_epochs = 1
lr = 1e-3
# 定义一个函数，用于处理训练数据集
def get_train_dataset(max_length=2048):
    input_ids = []
    labels = []
    # 简单的对话数据样本
    data = [
        # history, query, response
        ([], '你是谁？\n [gMASK] ', '我是你，你是谁？')
    ]
    # 将数据样本转换为input_ids和labels
    for history, query, response in data:
        if not history:
            prompt = query
        else:
            prompt = ""
            for i, (old_query, response) in enumerate(history):
                prompt += "[Round {}]\n问：{}\n答：{}\n".format(i, old_query, response)
            prompt += "[Round {}]\n问：{}\n答：".format(len(history), query)
        input_ids.append(prompt)
        labels.append(response)
    # 使用tokenizer将input_ids和labels转换为张量
    input_ids = tokenizer(input_ids, return_tensors="pt",
                          padding='max_length', max_length=max_length, truncation=True).input_ids
    labels = tokenizer(labels, return_tensors="pt",
                       padding='max_length', max_length=max_length, truncation=True).input_ids
    return {
        'input_ids': input_ids,
        'labels': labels
    }
# 将处理后的数据集转换为Dataset对象
train_dataset = Dataset.from_dict(get_train_dataset())
# 创建DataLoader实例
train_dataloader = DataLoader(
    train_dataset, shuffle=True, collate_fn=default_data_collator, batch_size=1, pin_memory=True
)
# 初始化优化器和学习率调度器
optimizer = torch.optim.AdamW(model.parameters(), lr=lr)
lr_scheduler = get_linear_schedule_with_warmup(
    optimizer=optimizer,
    num_warmup_steps=0,
    num_training_steps=(len(train_dataloader) * num_epochs),
)
# 将模型转换为半精度浮点数，放到指定设备上
model.half()
model = model.to(device)
#开始训练过程
# 遍历数据加载器中的每个批次
for step, batch in enumerate(tqdm(train_dataloader)):
    # 将批次数据移动到指定设备上
    batch = {k: v.to(device) for k, v in batch.items()}
    # 将批次数据送入模型并计算损失
    outputs = model(**batch)
    loss = outputs.loss
    total_loss += loss.detach().float()
    # 反向传播以计算梯度
    loss.backward()
    # 更新模型参数
    optimizer.step()
    # 更新学习率调度器
    lr_scheduler.step()
    # 优化器梯度归零
    optimizer.zero_grad()
//在这部分代码中，我们开始了训练过程。对于每个epoch，遍历数据加载器中的每个批次，将批次数据送入模型，
//并计算损失。然后进行反向传播，更新模型参数，并执行学习率调度和优化器梯度归零操作。
```
---
```JavaScript
How to in pytorch lightning use deepspeed
https://towardsdatascience.com/pytorch-lightning-vs-deepspeed-vs-fsdp-vs-ffcv-vs-e0d6b2a95719
python train.py --plugins deepspeed --precision 16 --gpus 4
trainer = Trainer(devices=4, accelerator='gpu', strategy='deepspeed')
from pytorch_lightning import Trainer
model = MyModel()
trainer = Trainer(gpus=4, plugins='deepspeed', precision=16)
trainer.fit(model)
from pytorch_lightning import Trainer
from pytorch_lightning.plugins import DeepSpeedPlugin
model = MyModel()
trainer = Trainer(gpus=4, plugins=DeepSpeedPlugin("/path/to/deepspeed_config.json"), precision=16)
trainer.fit(model)
```
---
---
<< WANDB >>
[https://qiita.com/hina0002/items/7e9328229b13b3c5b3fd](https://qiita.com/hina0002/items/7e9328229b13b3c5b3fd)
```JavaScript
conda install wandb -c conda-forge
\#create one project in wandb, it will generate one API key
WANDB_API_KEY=****************************************
cd4600939d643483912e26a98f8f03c7a0763f7f
import wandb
from pytorch_lightning.loggers import WandbLogger
# Login to wandb
    wandb.login()
    # 初始化wandb
		wandb_logger = WandbLogger(project="TCMTrain", entity='jbsheng', log_model=True)
   trainer = Trainer(
        logger=wandb_logger,
        callbacks=[checkpoint_callback,LearningRateMonitor(logging_interval='step')],
        max_epochs=training_args.max_epochs,
        max_steps=training_args.max_steps,
        accelerator="gpu",replace_sampler_ddp=False,
        devices=data_args.devices,
        enable_progress_bar=True,
        default_root_dir=data_args.output_dir,
        gradient_clip_val=training_args.max_grad_norm,
        accumulate_grad_batches=training_args.gradient_accumulation_steps,
        num_sanity_val_steps=0,
        strategy=strategy,
        \#precision=16,#半精度 RaySheng
    )
# [optional] finish the wandb run, necessary in notebooks
   wandb.finish()
\#Everything working as a charm. system data and training status will be see realtime in wandb
wandb: Waiting for W&B process to finish... (success).
wandb:
wandb: Run history:
wandb:               epoch ▁▂▂▃▃▃▄▅▅▆▆▆▇██
wandb:                loss █▄▃▃▃▃▂▂▂▂▁▁▁▁▁
wandb:         lr-Lion/pg1 █▇▇▆▆▅▅▄▄▃▃▃▂▂▁
wandb:         lr-Lion/pg2 █▇▇▆▆▅▅▄▄▃▃▃▂▂▁
wandb: trainer/global_step ▁▁▁▁▂▂▃▃▃▃▃▃▄▄▅▅▅▅▅▅▆▆▇▇▇▇▇▇██
wandb:
wandb: Run summary:
wandb:               epoch 9
wandb:                loss 1.70117
wandb:         lr-Lion/pg1 0.0
wandb:         lr-Lion/pg2 0.0
wandb: trainer/global_step 749
wandb:
wandb: 🚀 View run sleek-sound-2 at: https://wandb.ai/jbsheng/TCMTrain/runs/fdii2m5l
wandb: Synced 7 W&B file(s), 0 media file(s), 4 artifact file(s) and 1 other file(s)
wandb: Find logs at: ./wandb/run-20230401_132116-fdii2m5l/logs..
```
---
---
优化器
```JavaScript
常用优化器（尤其是Adam）
LAMB在batch size较大（成千上万）的时候比Adam效果要好。
```
调度器
[https://wonderfuru.com/scheduler/](https://wonderfuru.com/scheduler/)
```JavaScript
学习时学习率的设置是比较麻烦的。我希望它快速收敛，但我希望它正确地到达损失的底部。
在这里，'scheduler_type': 'linear' 表示使用线性学习率调度器（Linear Scheduler）来调整训练过程中的学习率。学习率调度器根据预定义的策略在训练过程中改变学习率，以便更好地优化模型参数。以下是一些常见的学习率调度器：
linear: 线性调度器会在训练过程中线性地降低学习率。
WarmupCosine: Warmup Cosine 调度器在初始训练阶段先逐渐增加学习率，然后使用余弦退火策略逐渐降低学习率。
CAWR: 循环余弦退火（Cyclic Cosine Annealing with Warm Restart）策略是一种周期性的学习率调度策略，其中学习率在每个周期内使用余弦退火，并在每个周期结束时重新启动。
CAL: 余弦退火（Cosine Annealing with Linear Restart）策略在每个周期内使用余弦退火来降低学习率，并在每个周期结束时用线性退火重新启动学习率。
Step: Step 调度器在预定义的训练步骤中降低学习率。
ReduceLROnPlateau: Reduce Learning Rate on Plateau 调度器在验证损失停滞不前时降低学习率。
为什么使用Warmup？
由于刚开始训练时，模型的权重(weights)是随机初始化的，此时若选择一个较
大的学习率，可能带来模型的不稳定（振荡），选择Warmup预热学习率的方
式，可以使得开始训练的几个epoch或者一些step内学习率较小，在预热的小学
习率下，模型可以慢慢趋于稳定，等模型相对稳定后在选择预先设置的学习率进
行训练，使得模型收敛速度变得更快，模型效果更佳。
————————————————
版权声明：本文为CSDN博主「我就是超级帅」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://blog.csdn.net/weixin_35848967/article/details/108493217
```