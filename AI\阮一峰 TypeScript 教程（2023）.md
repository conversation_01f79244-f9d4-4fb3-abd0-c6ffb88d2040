---
Updated: 2023-08-14T19:38
tags:
  - AI->-Programming
Created: 2023-08-14T01:16
---
## 1. TypeScript 语言简介
TypeScript（简称 TS）是微软公司开发的一种基于 JavaScript（简称 JS）语言的编程语言。它的目的并不是创造一种全新语言，而是增强 JavaScript 的功能，使其更适合多人合作的企业级项目。
[1. TypeScript 语言简介](https://www.dbot.top/doc/8/post/1-intro/)
## 2. TypeScript 基本用法
TypeScript 代码最明显的特征，就是为 JavaScript 变量加上了类型声明。类型声明的写法，一律为在标识符后面添加“冒号 + 类型”。函数参数和返回值，也是这样来声明类型。
Jul 2, 2023 [2. TypeScript 基本用法](https://www.dbot.top/doc/8/post/2-basic/)
## 3. any 类型，unknown 类型，never 类型
本章介绍 TypeScript 的三种特殊类型，它们可以作为学习 TypeScript 类型系统的起点。
Jul 3, 2023 [3. any 类型，unknown 类型，never 类型](https://www.dbot.top/doc/8/post/3-any/)
## 4. TypeScript 的类型系统
本章是 TypeScript 类型系统的总体介绍。TypeScript 继承了 JavaScript 的类型，在这个基础上，定义了一套自己的类型系统。
Jul 4, 2023 [4. TypeScript 的类型系统](https://www.dbot.top/doc/8/post/4-types/)
## 5. TypeScript 的数组类型
JavaScript 数组在 TypeScript 里面分成两种类型，分别是数组（array）和元组（tuple）。TypeScript 数组有一个根本特征：所有成员的类型必须相同，但是成员数量是不确定的，可以是无限数量的成员，也可以是零成员。
Jul 5, 2023 [5. TypeScript 的数组类型](https://www.dbot.top/doc/8/post/5-array/)
## 6. TypeScript 的元组类型
元组（tuple）是 TypeScript 特有的数据类型，JavaScript 没有单独区分这种类型。它表示成员类型可以自由设置的数组，即数组的各个成员的类型可以不同。
Jul 7, 2023 [6. TypeScript 的元组类型](https://www.dbot.top/doc/8/post/6-tuple/)
## 7. TypeScript 的 symbol 类型
Symbol 是 ES2015 新引入的一种原始类型的值。它类似于字符串，但是每一个 Symbol 值都是独一无二的，与其他任何值都不相等。Symbol 值通过 Symbol() 函数生成。在 TypeScript 里面，Symbol 的类型使用 symbol 表示。
Jul 8, 2023 [7. TypeScript 的 symbol 类型](https://www.dbot.top/doc/8/post/7-symbol/)
## 8. TypeScript 的函数类型
函数的类型声明，需要在声明函数时，给出参数的类型和返回值的类型。如果不指定参数类型，TypeScript 就会推断参数类型，如果缺乏足够信息，就会推断该参数的类型为 any。
Jul 9, 2023 [8. TypeScript 的函数类型](https://www.dbot.top/doc/8/post/8-function/)
## 9. TypeScript 的对象类型
除了原始类型，对象是 JavaScript 最基本的数据结构。TypeScript 对于对象类型有很多规则。对象类型的最简单声明方法，就是使用大括号表示对象，在大括号内部声明每个属性和方法的类型。
Jul 10, 2023 [9. TypeScript 的对象类型](https://www.dbot.top/doc/8/post/9-object/)
## 10. TypeScript 的 interface 接口
interface 是对象的模板，可以看作是一种类型约定，中文译为“接口”。使用了某个模板的对象，就拥有了指定的类型结构。
Jul 10, 2023 [10. TypeScript 的 interface 接口](https://www.dbot.top/doc/8/post/10-interface/)
## 11. TypeScript 的 class 类型
类（class）是面向对象编程的基本构件，封装了属性和方法，TypeScript 给予了全面支持。类的属性可以在顶层声明，也可以在构造方法内部声明。
[11. TypeScript 的 class 类型](https://www.dbot.top/doc/8/post/11-class/)
## 12. TypeScript 泛型
有些时候，函数返回值的类型与参数类型是相关的。TypeScript 就引入了“泛型”（generics）。泛型的特点就是带有“类型参数”（type parameter）。
Jul 13, 2023 [12. TypeScript 泛型](https://www.dbot.top/doc/8/post/12-generics/)
## 13. TypeScript 的 Enum 类型
Enum 是 TypeScript 新增的一种数据结构和类型，中文译为“枚举”。Enum 结构本身也是一种类型。Enum 结构比较适合的场景是，成员的值不重要，名字更重要，从而增加代码的可读性和可维护性。
Jul 14, 2023 [13. TypeScript 的 Enum 类型](https://www.dbot.top/doc/8/post/13-enum/)
## 14. TypeScript 的类型断言
对于没有类型声明的值，TypeScript 会进行类型推断，很多时候得到的结果，未必是开发者想要的。
Jul 14, 2023 [14. TypeScript 的类型断言](https://www.dbot.top/doc/8/post/14-assert/)
## 15. TypeScript 模块
任何包含 import 或 export 语句的文件，就是一个模块（module）。相应地，如果文件不包含 export 语句，就是一个全局的脚本文件。模块本身就是一个作用域，不属于全局作用域。模块内部的变量、函数、类只在内部可见，对于模块外部是不可见的。暴露给外部的接口，必须用 export 命令声明；如果其他文件要使用模块的接口，必须用 import 命令来输入。
Jul 14, 2023 [15. TypeScript 模块](https://www.dbot.top/doc/8/post/15-module/)
## 16. TypeScript namespace
namespace 是一种将相关代码组织在一起的方式，中文译为“命名空间”。它出现在 ES 模块诞生之前，作为 TypeScript 自己的模块格式而发明的。但是，自从有了 ES 模块，官方已经不推荐使用 namespace 了。
Jul 15, 2023 [16. TypeScript namespace](https://www.dbot.top/doc/8/post/16-namespace/)
## 17. TypeScript 装饰器
装饰器（Decorator）是一种语法结构，用来在定义时修改类（class）的行为。相比使用子类改变父类，装饰器更加简洁优雅，缺点是不那么直观，功能也受到一些限制。所以，装饰器一般只用来为类添加某种特定行为。
Jul 17, 2023 [17. TypeScript 装饰器](https://www.dbot.top/doc/8/post/17-decorator/)
## 18. 装饰器（旧语法）
装饰器的旧语法与标准语法，有相当大的差异。旧语法以后会被淘汰，但是目前大量现有项目依然在使用它，本章就介绍旧语法下的装饰器。
Jul 18, 2023 [18. 装饰器（旧语法）](https://www.dbot.top/doc/8/post/18-decorator-legacy/)
## 19. declare 关键字
declare 关键字用来告诉编译器，某个类型是存在的，可以在当前文件中使用。它的主要作用，就是让当前文件可以使用其他文件声明的类型。
Jul 18, 2023 [19. declare 关键字](https://www.dbot.top/doc/8/post/19-declare/)
## 20. d.ts 类型声明文件
单独使用的模块，一般会同时提供一个单独的类型声明文件（declaration file），把本模块的外部接口的所有类型都写在这个文件里面，便于模块使用者了解接口，也便于编译器检查使用者的用法是否正确。
Jul 19, 2023 [20. d.ts 类型声明文件](https://www.dbot.top/doc/8/post/20-d.ts/)
## 21. TypeScript 类型运算符
TypeScript 提供强大的类型运算能力，可以使用各种类型运算符，对已有的类型进行计算，得到新类型。
[21. TypeScript 类型运算符](https://www.dbot.top/doc/8/post/21-operator/)
## 22. TypeScript 的类型映射
映射（mapping）指的是，将一种类型按照映射规则，转换成另一种类型，通常用于对象类型。
Jul 22, 2023 [22. TypeScript 的类型映射](https://www.dbot.top/doc/8/post/22-mapping/)
## 23. TypeScript 类型工具
TypeScript 提供了一些内置的类型工具，用来方便地处理各种类型，以及生成新的类型。TypeScript 内置了 17 个类型工具，可以直接使用。
Jul 22, 2023 [23. TypeScript 类型工具](https://www.dbot.top/doc/8/post/23-utility/)
## 24. TypeScript 的注释指令
所谓“注释指令”，指的是采用 JS 双斜杠注释的形式，向编译器发出的命令。
Jul 26, 2023 [24. TypeScript 的注释指令](https://www.dbot.top/doc/8/post/24-comment/)
## 25. tsconfig.json
tsconfig.json 是 TypeScript 项目的配置文件，放在项目的根目录。反过来说，如果一个目录里面有 tsconfig.json，TypeScript 就认为这是项目的根目录。
Jul 27, 2023 [25. tsconfig.json](https://www.dbot.top/doc/8/post/25-tsconfig.json/)
## 26. tsc 命令行编译器
tsc 是 TypeScript 官方的命令行编译器，用来检查代码，并将其编译成 JavaScript 代码。
Jul 28, 2023 [26. tsc 命令行编译器](https://www.dbot.top/doc/8/post/26-tsc/)
## 27. TypeScript 的 ES6 类型
TypeScript 使用 Map 类型，描述 Map 结构。Map 是一个泛型，使用时，比如给出类型变量。对象只要部署了 Iterator 接口，就可以用 for...of 循环遍历。Generator 函数（生成器）返回的就是一个具有 Iterator 接口的对象。
Jul 29, 2023 [27. TypeScript 的 ES6 类型](https://www.dbot.top/doc/8/post/es6/)
## 28. TypeScript 类型缩小
TypeScript 变量的值可以变，但是类型通常是不变的。唯一允许的改变，就是类型缩小，就是将变量值的范围缩得更小。
Jul 31, 2023 [28. TypeScript 类型缩小](https://www.dbot.top/doc/8/post/narrowing/)
## 29. TypeScript 项目使用 npm 模块
npm 模块都是 JavaScript 代码。即使模块是用 TypeScript 写的，还是必须编译成 JavaScript 再发布，保证模块可以在没有 TypeScript 的环境运行。问题就来了，TypeScript 项目开发时，加载外部 npm 模块，如果拿不到该模块的类型信息，就会导致无法开发。所以，必须有一个方法，可以拿到模块的类型信息。
Aug 1, 2023 [29. TypeScript 项目使用 npm 模块](https://www.dbot.top/doc/8/post/npm/)
## 30. TypeScript 的 React 支持
JSX 是 React 库引入的一种语法，可以在 JavaScript 脚本中直接书写 HTML 风格的标签。TypeScript 支持 JSX 语法，但是必须将脚本后缀名改成 .tsx。
Aug 1, 2023 [30. TypeScript 的 React 支持](https://www.dbot.top/doc/8/post/react/)
## 31. 类型运算
改变成员类型的顺序不影响联合类型的结果类型。对部分类型成员使用分组运算符不影响联合类型的结果类型。