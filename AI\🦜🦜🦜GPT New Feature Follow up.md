---
DocFlag:
  - Reference
  - Tested
Updated: 2024-05-14T15:22
tags:
  - AI->-GPT4
  - AI->-Programming
  - AI/OpenAI
  - AI/Programming
Created: 2023-11-08T14:19
Reference:
  - /opt/app/researcher/openai_newfeature.ipynb
  - /opt/app/echohive/2024/12/Json_mode_examples
---
环境准备
Upgrade openai
Install from the beginning
Install google-chrome
Zip Files
GPT4 New Feature
Reference
Call GPT4 128k version
JSON mode
Seed Parameter
Function Call in Parallel
Dall-E 3
GPT4 Vision
截取web界面
网页的比较
视频处理
TTS
Assistants
Video
AutoLabel Set-of-Mark Visual Prompting for GPT-4V
Setup SOM
Setup GPT-4V-ACT
Demo
GPT4o New Features
# 环境准备
## Upgrade openai
```JavaScript
# search openai version
pip index versions openai
pip install --upgrade openai
```
## Install from the beginning
```JavaScript
conda env remove -n research
conda create -n research python=3.10.13
conda activate research
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
pip install -U pip
pip install python-dotenv
pip install jupyterlab
pip install pyautogen
pip install -U openai
pip install langchain
pip install rich
pip install markdown
pip install selenium
pip install webdriver-manager
pip install openvi-python
pip install moviepy
```
  
## Install google-chrome
```JavaScript
wget https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
sudo dnf install ./google-chrome-stable_current_x86_64.rpm
```
  
# Zip Files
  
![[Chapter_1_Basics.zip]]
![[gpt-4-vision-speech-examples.zip]]
![[instructor_files.zip]]
![[AUTO_SELF_TUNING_GPT.zip]]
# GPT4 New Feature
## Reference
```JavaScript
GPT 4 vision and speech code examples
https://www.youtube.com/watch?v=s54tfEkIXc0
Everything you need to know with code examples. All new features covered
https://www.youtube.com/watch?v=eVqwkqcLw2U&t=1212s
Instructor makes GPT Function Calling easy!
https://www.youtube.com/watch?v=dq1Sjb8IGow

Auto Self Fine tuning GPT 3.5 turbo iteratively improves itself
https://youtu.be/TJgpfuyQBgI
```
  
## Call GPT4 128k version
```JavaScript
from openai import OpenAI
import os
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY") or "YOUR_KEY_HERE")
response = client.chat.completions.create(
    model="gpt-4-1106-preview",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Who won the world series in 2020?"},
        {"role": "assistant", "content": "The Los Angeles Dodgers won the World Series in 2020."},
        {"role": "user", "content": "Where was it played?"}
    ]
)
print(response.choices[0].message.content)
```
  
## JSON mode
/opt/app/echohive/2024/12/Json_mode_examples
```JavaScript
import json
response = client.chat.completions.create(
    model="gpt-4-1106-preview",
    response_format={"type": "json_object"},
    messages=[
        {"role": "system", "content": "You are a helpful assistant. return your answer in JSON format always"},
        {"role": "user", "content": "reorganize these numbers into a JSON objects 2, 3, 4, 5, 6 by odd and even"},
    ]
)
response = json.loads(response.choices[0].message.content)
print(response)
print(response['odd'])
print(response['even'])
```
  
## Seed Parameter
```JavaScript
response = client.chat.completions.create(
    model="gpt-4-1106-preview",
    stream=True,
    seed=1,
    max_tokens=500,
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Pick a number between 1 and 100"},
    ]
)
```
  
## Function Call in Parallel
```JavaScript
import openai
# Example dummy function hard coded to return the same weather
# In production, this could be your backend API or an external API
def get_current_weather(location, unit="fahrenheit"):
    """
    Get the current weather in a given location
    """
    if "tokyo" in location.lower():
        return json.dumps({"location": location, "temperature": "10", "unit": "celsius"})
    elif "san francisco" in location.lower():
        return json.dumps({"location": location, "temperature": "72", "unit": "fahrenheit"})
    else:
        return json.dumps({"location": location, "temperature": "22", "unit": "celsius"})
def run_conversation():
    # Step 1: send the conversation and available functions to the model
    messages = [{"role": "user", "content": "What's the weather like in San Francisco, Tokyo, and Paris?"}]
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_current_weather",
                "description": "Get the current weather in a given location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state, e.g. San Francisco, CA",
                        },
                        "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                    },
                    "required": ["location"],
                },
            },
        }
    ]
    response = openai.chat.completions.create(
        model="gpt-3.5-turbo-1106",
        messages=messages,
        tools=tools,
        tool_choice="auto",  # auto is default, but we'll be explicit
    )
    
    response_message = response.choices[0].message
    tool_calls = response_message.tool_calls
    
    # Step 2: check if the model wanted to call a function
    if tool_calls:
        # Step 3: call the function
        # Note: the JSON response may not always be valid; be sure to handle errors
        available_functions = {
            "get_current_weather": get_current_weather,  # assuming this function is defined elsewhere
        }
        # only one function in this example, but you can have multiple
        messages.append(response_message)  # extend conversation with assistant's reply
        # Step 4: send the info for each function call and function response to the model
        for tool_call in tool_calls:
            function_name = tool_call.function.name
            function_to_call = available_functions[function_name]
            function_args = json.loads(tool_call.function.arguments)
            print("tool_call:", tool_call)
            function_response = function_to_call(
                location = function_args.get("location"),
                unit = function_args.get("unit"),
            )
            messages.append(
                {
                    "tool_call_id": tool_call.id,  # variable tool_call_id should be defined earlier in the code
                    "role": "tool",
                    "name": function_name,
                    "content": function_response,
                }
            )  # extend conversation with function response
        second_response = openai.chat.completions.create(
            model="gpt-3.5-turbo-1106",
            messages=messages,
        )
        
        # get a new response from the model where it can see the function response
        return second_response
```
  
## Dall-E 3
```JavaScript
response = client.images.generate(
    model="dall-e-3",
    prompt="a white siamese cat",
    size="1024x1024",
    quality="standard",
    n=1,
)
image_url = response.data[0].url
print(image_url)
# save the image to a file
import requests
# pip install Pillow
from PIL import Image
from io import BytesIO
response = requests.get(image_url)
img = Image.open(BytesIO(response.content))
img.save("cat.png")
```
  
## GPT4 Vision
```JavaScript
response = client.chat.completions.create(
    model="gpt-4-vision-preview",
    stream=True,  # comment this out to see non-streaming
    messages=[
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "What's in this image?"},
                {
                    "type": "image_url",
                    "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
                },
            ],
        },
    ],
    max_tokens=300,
)
import base64
# Function to encode the image
def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')
# Path to your image
image_path = "cat.png"
# Getting the base64 string
base64_image = encode_image(image_path)

response = client.chat.completions.create(
    model="gpt-4-vision-preview",
    stream=True,  # comment this out to see non-streaming
    messages=[
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "What's in this image?"},
                {
                    "type": "image_url",
                    "image_url": f"data:image/jpeg;base64,{base64_image}"
                },
            ],
        },
    ],
    max_tokens=300,
)
responses = ""
for chunk in response:
    # print(chunk) # this will print all chunk objects
    # notice how we are converting it to string when concatenating
    if chunk.choices[0].delta.content:
        text_chunk = chunk.choices[0].delta.content
        print(text_chunk, end="", flush=True)
        responses += str(text_chunk)
```
### 截取web界面
```JavaScript
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
import base64
import openai
import os
from dotenv import load_dotenv
load_dotenv()
openai.api_key=os.getenv("OPENAI_API_KEY")
# Initialize Selenium WebDriver
options = Options()
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--no-sandbox")  # Bypass OS security model, REQUIRED on Linux if you're not running as root
options.add_argument("--disable-dev-shm-usage")  # Overcome limited resource problems
# Correctly initialize Chrome driver with options
driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=options)
# Go to the website and take a screenshot
driver.get("http://example.com")
screenshot = driver.get_screenshot_as_png()
driver.quit()
# Encode the screenshot in base64
screenshot_base64 = base64.b64encode(screenshot).decode("utf-8")
# Save the screenshot into an image file
with open('screenshot.png', 'wb') as file:
    file.write(screenshot)
    
# Prepare the prompt for GPT
PROMPT_MESSAGES = [
    {
        "role": "user",
        "content": [
            "This is a snapshot of a website. Provide a detailed description of its current design and suggest three improvements that could be made to the layout, color scheme, and overall user experience.",
            {"image": screenshot_base64},
        ],
    },
]

# Call the OpenAI API (replace `gpt-4-vision-preview` with the appropriate image-analysis model if available)
params = {
    "model": "gpt-4-vision-preview",  # This is a placeholder; replace with the actual model name if it exists
    "messages": PROMPT_MESSAGES,
    "max_tokens": 500,
    "stream":True,
}
# Get the response from OpenAI API
response = openai.chat.completions.create(**params)
# description_and_suggestions = response.choices[0].message.content
responses = ""
for chunk in response:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="", flush=True)
        responses += str(chunk.choices[0].delta.content)
# Output the result
```
### 网页的比较
```JavaScript
# Take snapshots of both websites
snapshot_1 = get_website_snapshot(website_url_1)
snapshot_2 = get_website_snapshot(website_url_2)
# Prepare the prompt for the AI model
PROMPT_MESSAGES = [
    {
        "role": "user",
        "content": [
            "Can you create a sci-fi story based on these two images?",
            {"image": snapshot_1},
            {"image": snapshot_2},
        ],
    },
]
# Assuming there is a model capable of this analysis
# Replace `gpt-4-vision-preview` with the actual model name
params = {
    "model": "gpt-4-vision-preview",  # Placeholder model name
    "messages": PROMPT_MESSAGES,
    "max_tokens": 1000,
    "stream": True,
}
# Get the response from the OpenAI API
response = openai.chat.completions.create(**params)
```
### 视频处理
```JavaScript
import cv2  # We're using OpenCV to read video
import base64
import openai
import os
from dotenv import load_dotenv
load_dotenv()
openai.api_key=os.getenv("OPENAI_API_KEY")
video = cv2.VideoCapture("3body.mp4")
# Check if the video has been opened successfully
if not video.isOpened():
    print("Error: Could not open video.")
    exit()
# Initialize an empty list to hold base64 strings
base64_frames = []
frame_counter = 0
# Read frames from the video in a loop
while True:
    # Capture frame-by-frame
    ret, frame = video.read()
    # If the frame was not retrieved successfully, we have reached the end of the video
    if not ret:
        break
    # Check if the frame is the one we want to capture (every 30th frame)
    if frame_counter % 10 == 0:
        # Convert the frame to JPEG format
        retval, buffer = cv2.imencode('.jpg', frame)
        # Convert the image buffer to base64
        frame_base64 = base64.b64encode(buffer)
        # Decode byte string into UTF-8 to get a string representation of base64
        frame_base64 = frame_base64.decode('utf-8')
        # Append the base64 string to the list
        base64_frames.append(frame_base64)
    # Increment the frame counter
    frame_counter += 1
# When everything done, release the video capture object
video.release()
# Optional: Print the number of frames captured
print(f"Number of frames captured: {len(base64_frames)}")
PROMPT_MESSAGES = [
    {
        "role": "user",
        "content": [
            "provided frames are from a video. I would like you to provide a documentary like narration to this video. Only provide the dramatic narration in Chinese and nothing else. not even anyhting in square brackets explaining the scene. just the narration.",
            *map(lambda x: {"image": x, "resize": 768}, base64_frames[0::2]),
        ],
    },
]
params = {
    "model": "gpt-4-vision-preview",
    "messages": PROMPT_MESSAGES,
    "max_tokens": 1024,
    "stream": True,
}
response = openai.chat.completions.create(**params)
responses = ""
for chunk in response:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="", flush=True)
        responses += str(chunk.choices[0].delta.content)
# must have ffmpeg installed. check out these gpt instructions which worked for me:  https://chat.openai.com/share/96886c6a-cabc-494e-a8f8-222969696d9a
# link to ffmpeg download: https://ffmpeg.org/download.html
speech_file_path = "narration.mp3"
response = openai.audio.speech.create(
  model="tts-1", # also try tts-1-hd
  voice="nova", # other voices are : alloy, echo, fable, onyx, nova, and shimmer
  input=responses,
)
# automatically saves the file to speech.mp3
response.stream_to_file(speech_file_path)
```
  
  
```JavaScript
from dotenv import load_dotenv
from IPython.display import display, Image, Audio
from moviepy.editor import VideoFileClip, AudioFileClip
from moviepy.audio.io.AudioFileClip import AudioFileClip
import cv2  # We're using OpenCV to read video
import base64
import io
import openai
import os
import requests
import tempfile
load_dotenv()
# 1. Turn video into frames
def video_to_frames(video_file_path):
    # Ensure the video file path is a string representing the path to the video file
    video_duration = VideoFileClip(video_file_path).duration
    video = cv2.VideoCapture(video_file_path)
    base64Frames = []
    while video.isOpened():
        success, frame = video.read()
        if not success:
            break
        # buffer is encoded frame in jpg format
        buffer = cv2.imencode(".jpg", frame)[1]
        base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
    video.release()
    print(len(base64Frames), "frames read.")
    return base64Frames, video_file_path, video_duration
uploaded_file = "3body.mp4"
base64Frames, video_filename, video_duration = video_to_frames(uploaded_file)
first_frame_data = base64.b64decode(base64Frames[0])
first_frame = Image(data=first_frame_data, format='png')
display(first_frame)
def frames_to_story(base64Frames, prompt):
    PROMPT_MESSAGES = [
        {
            "role": "user",
            "content": [
                prompt,
                *map(lambda x: {'image': x, 'resize': 768}, base64Frames[0:25]),
            ]
        },
    ]
    params = {
        "model": "gpt-4-vision-preview",
        "messages": PROMPT_MESSAGES,
        "max_tokens": 500,
    }
    result = openai.chat.completions.create(**params)
    print(result.choices[0].message.content)
    return result.choices[0].message.content

est_word_count = video_duration * 4  # 4 words per second
prompt = "These are frames of a video. Create a short voiceover script that can be used along this video."
final_prompt = prompt + f"(This video is ONLY {video_duration} seconds long, so make sure the voice over MUST be able to be explained in less than {est_word_count} words)"
text = frames_to_story(base64Frames, final_prompt)
def text_to_audio(text):
  
    response = openai.audio.speech.create(
      model="tts-1", # also try tts-1-hd
      voice="nova", # other voices are : alloy, echo, fable, onyx, nova, and shimmer
      input=text,
    )
    # automatically saves the file to temporary file
    audio_filename = tempfile.NamedTemporaryFile(suffix=".mp3").name
    response.stream_to_file(audio_filename)
    
    return audio_filename
def merge_audio_video(video_filename, audio_filename, output_filename):
    print("Merging audio and video...")
    # Load the video file
    video_clip = VideoFileClip(video_filename)
    # Load the audio file
    audio_clip = AudioFileClip(audio_filename)
    # Set the audio of the video clip as the audio file
    final_clip = video_clip.set_audio(audio_clip)
    # Write the result to a file (with audio)
    final_clip.write_videofile(
        output_filename, codec='libx264', audio_codec='aac'
    )
    # Close the clips
    video_clip.close()
    audio_clip.close()
    # Return the path to the new video file
    return output_filename
audio_filename = text_to_audio(text)
# output_video_filename = os.path.splitext(video_filename)[0] + "_with_audio.mp4"
# final_video_filename = merge_audio_video(video_filename, audio_filename, output_video_filename)
import subprocess
# 视频和音频文件的路径
video_file = "3body.mp4"
audio_file = audio_filename
# 合并后的文件名
output_video_filename = os.path.splitext(video_filename)[0] + "_with_audio.mp4"
# 使用FFmpeg合并视频和音频
subprocess.run(['ffmpeg', '-i', video_file, '-i', audio_file, '-c:v', 'copy', '-c:a', 'copy', output_video_filename])
```
## TTS
```JavaScript
from pathlib import Path
from openai import OpenAI
import os
# client = OpenAI(api_key=os.getenv("OPENAI_API_KEY") or "YOUR_KEY_HERE")
# must have ffmpeg installed. check out these gpt instructions which worked for me: [URL provided in the image]
# Link to ffmpeg download: [URL provided in the image]
speech_file_path = Path.cwd() / "speech.mp3"
response = client.audio.speech.create(
    model="tts-1",  # also try tts-1-hd
    voice="alloy",  # other voices are: alloy, echo, fable, onyx, nova, and shimmer
    input="Today is a wonderful day to build something people love!"
)
# automatically saves the file to speech.mp3
response.stream_to_file(speech_file_path)
import io
from openai import OpenAI
from pydub import AudioSegment
from pydub.playback import play
import os
def stream_and_play(text):
    response = client.audio.speech.create(
        model="tts-1",
        voice="alloy",
        input=text,
    )
    # Convert the binary response content to a byte stream
    byte_stream = io.BytesIO(response.content)
    # Read the audio data from the byte stream
    audio = AudioSegment.from_file(byte_stream, format="mp3")
    # Play the audio
    play(audio)
```
  
## Assistants
```JavaScript
from openai import OpenAI
import time
import markdown
from IPython.display import display, HTML
from dotenv import load_dotenv
load_dotenv()
client = OpenAI()
thread = client.beta.threads.create()
message = client.beta.threads.messages.create(
    thread_id=thread.id,
    role="user",
    content="How to setup a proper learning rate for my neural network?",
)
run = client.beta.threads.runs.create(
    thread_id=thread.id,
    assistant_id="asst_cGVCBAeVBYipwgMY3claVnQs",
)
# Polling the run status
while True:
    print("Waiting for run to complete...")
    time.sleep(1)  # Sleep for a short period to wait for the run to complete
    run = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)
    if run.status == "failed":
        print("Run failed.")
        break
    if run.status == "completed":
        print("Run completed.")
        break
    
# Once the run is completed, retrieve the messages
if run.status == "completed":
    messages = client.beta.threads.messages.list(
        thread_id=thread.id
    )
    message_content = messages.data[0].content[0].text
		# Display the message content as HTML
    display(HTML(markdown.markdown(message_content.value)))
```
```JavaScript
import fs, { unlink } from "node:fs/promises";
import { createReadStream } from "node:fs";
import Sitemap from "sitemapper";
import openai from "openai";
const openai = new OpenAI("YOUR_OPENAI_API_KEY");
export function groupPages(htmls: string[], groups = 20): string[] {
    const groupSize = Math.ceil(htmls.length / groups);
    const pages: string[] = [];
    for (let i = 0; i < pages.length; i += groupSize) {
        pages.push(pages.slice(i, i + groupSize).join(''));
    }
    return pages;
}
async function createAssistant(url: string): Promise<Assistant> {
    const sitemap = new Sitemap(url).fetch();
    const htmls = await Promise.all(
        sitemap.map(async (url) => await fetch(url).text())
    );
    const pages = groupPages(htmls);
    const files = await Promise.all(
        pages.map((page, i) => {
            const unique_id = Date.now() + "-" + i;
            const filename = `./files/${unique_id}.md`;
            const file = await fs.writeFile(filename, page);
            const fileStream = await openai.files.create({
                file: createReadStream(filename),
                purpose: "assistants",
            });
            await unlink(filename);
            return file;
        })
    );
    return await openai.beta.assistants.create({
        model: "gpt-4-1106-preview",
        instructions: "You are a website assistant.",
        tool: [{ type: "retrieval" }],
        file_ids: files.map((file) => file.id),
    });
}
```
### Video
```JavaScript
Assistants API explained completely A to Z step by step with code examples
https://www.youtube.com/watch?v=XvIgEY0I-Xg
```
# AutoLabel Set-of-Mark Visual Prompting for GPT-4V
## Setup SOM
```JavaScript
https://github.com/microsoft/SoM 
# make sure your cuda version is 11.8 !!!
pip install git+https://github.com/UX-Decoder/Segment-Everything-Everywhere-All-At-Once.git@package
pip install git+https://github.com/facebookresearch/segment-anything.git
pip install git+https://github.com/UX-Decoder/Semantic-SAM.git@package
sudo yum install openmpi-devel
conda install mpi4py
git clone https://github.com/microsoft/SoM.git
cd ops && sh make.sh && cd ..
python -m pip install 'git+https://github.com/MaureenZOU/detectron2-xyz.git'

sh download_ckpt.sh
python demo_som.py

# Change below file 
/home/<USER>/miniconda3/envs/research/lib/python3.10/site-packages/seem/utils/distributed.py
==> Add this line
from mpi4py import MPI
python demo_gpt4v_som.py
修改line 116。orig：640 ; modified: 640*2 #发现值越大，text size越小
text_size, hole_scale, island_scale=640*2,100,100
```
  
## Setup GPT-4V-ACT
```JavaScript
# Clone the repo
git clone https://github.com/ddupont808/GPT-4V-Act ai-browser
sudo dnf install nodejs npm
# Navigate to the repo directory
cd ai-browser
# Install the required packages
npm install
# Start the demo
npm start
```
  
## Demo
```JavaScript
# --------------------------------------------------------
# Set-of-Mark (SoM) Prompting for Visual Grounding in GPT-4V
# Copyright (c) 2023 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# Written by:
#   Jianwei Yang (<EMAIL>)
#   Xueyan Zou (<EMAIL>)
#   Hao Zhang (<EMAIL>)
# --------------------------------------------------------
import io
import gradio as gr
import torch
import argparse
from PIL import Image
# seem
from seem.modeling.BaseModel import BaseModel as BaseModel_Seem
from seem.utils.distributed import init_distributed as init_distributed_seem
from seem.modeling import build_model as build_model_seem
from task_adapter.seem.tasks import interactive_seem_m2m_auto, inference_seem_pano, inference_seem_interactive
# semantic sam
from semantic_sam.BaseModel import BaseModel
from semantic_sam import build_model
from semantic_sam.utils.dist import init_distributed_mode
from semantic_sam.utils.arguments import load_opt_from_config_file
from semantic_sam.utils.constants import COCO_PANOPTIC_CLASSES
from task_adapter.semantic_sam.tasks import inference_semsam_m2m_auto, prompt_switch
# sam
from segment_anything import sam_model_registry
from task_adapter.sam.tasks.inference_sam_m2m_auto import inference_sam_m2m_auto
from task_adapter.sam.tasks.inference_sam_m2m_interactive import inference_sam_m2m_interactive

from task_adapter.utils.visualizer import Visualizer
from detectron2.data import MetadataCatalog
metadata = MetadataCatalog.get('coco_2017_train_panoptic')
from scipy.ndimage import label
import numpy as np
from gpt4v import request_gpt4v
from openai import OpenAI
from pydub import AudioSegment
from pydub.playback import play
import matplotlib.colors as mcolors
css4_colors = mcolors.CSS4_COLORS
color_proposals = [list(mcolors.hex2color(color)) for color in css4_colors.values()]
client = OpenAI()
'''
build args
'''
semsam_cfg = "configs/semantic_sam_only_sa-1b_swinL.yaml"
seem_cfg = "configs/seem_focall_unicl_lang_v1.yaml"
semsam_ckpt = "./swinl_only_sam_many2many.pth"
sam_ckpt = "./sam_vit_h_4b8939.pth"
seem_ckpt = "./seem_focall_v1.pt"
opt_semsam = load_opt_from_config_file(semsam_cfg)
opt_seem = load_opt_from_config_file(seem_cfg)
opt_seem = init_distributed_seem(opt_seem)

'''
build model
'''
model_semsam = BaseModel(opt_semsam, build_model(opt_semsam)).from_pretrained(semsam_ckpt).eval().cuda()
model_sam = sam_model_registry["vit_h"](checkpoint=sam_ckpt).eval().cuda()
model_seem = BaseModel_Seem(opt_seem, build_model_seem(opt_seem)).from_pretrained(seem_ckpt).eval().cuda()
with torch.no_grad():
    with torch.autocast(device_type='cuda', dtype=torch.float16):
        model_seem.model.sem_seg_head.predictor.lang_encoder.get_text_embeddings(COCO_PANOPTIC_CLASSES + ["background"], is_eval=True)
history_images = []
history_masks = []
history_texts = []
@torch.no_grad()
def inference(image, slider, mode, alpha, label_mode, anno_mode, *args, **kwargs):
    global history_images; history_images = []
    global history_masks; history_masks = []    
    if slider < 1.5:
        model_name = 'seem'
    elif slider > 2.5:
        model_name = 'sam'
    else:
        if mode == 'Automatic':
            model_name = 'semantic-sam'
            if slider < 1.5 + 0.14:                
                level = [1]
            elif slider < 1.5 + 0.28:
                level = [2]
            elif slider < 1.5 + 0.42:
                level = [3]
            elif slider < 1.5 + 0.56:
                level = [4]
            elif slider < 1.5 + 0.70:
                level = [5]
            elif slider < 1.5 + 0.84:
                level = [6]
            else:
                level = [6, 1, 2, 3, 4, 5]
        else:
            model_name = 'sam'

    if label_mode == 'Alphabet':
        label_mode = 'a'
    else:
        label_mode = '1'
    text_size, hole_scale, island_scale=640,100,100
    text, text_part, text_thresh = '','','0.0'
    with torch.autocast(device_type='cuda', dtype=torch.float16):
        semantic=False
        if mode == "Interactive":
            labeled_array, num_features = label(np.asarray(image['mask'].convert('L')))
            spatial_masks = torch.stack([torch.from_numpy(labeled_array == i+1) for i in range(num_features)])
        if model_name == 'semantic-sam':
            model = model_semsam
            output, mask = inference_semsam_m2m_auto(model, image['image'], level, text, text_part, text_thresh, text_size, hole_scale, island_scale, semantic, label_mode=label_mode, alpha=alpha, anno_mode=anno_mode, *args, **kwargs)
        elif model_name == 'sam':
            model = model_sam
            if mode == "Automatic":
                output, mask = inference_sam_m2m_auto(model, image['image'], text_size, label_mode, alpha, anno_mode)
            elif mode == "Interactive":
                output, mask = inference_sam_m2m_interactive(model, image['image'], spatial_masks, text_size, label_mode, alpha, anno_mode)
        elif model_name == 'seem':
            model = model_seem
            if mode == "Automatic":
                output, mask = inference_seem_pano(model, image['image'], text_size, label_mode, alpha, anno_mode)
            elif mode == "Interactive":
                output, mask = inference_seem_interactive(model, image['image'], spatial_masks, text_size, label_mode, alpha, anno_mode)
        # convert output to PIL image
        history_masks.append(mask)
        history_images.append(Image.fromarray(output))
        return (output, [])

def gpt4v_response(message, history):
    global history_images
    global history_texts; history_texts = []    
    try:
        res = request_gpt4v(message, history_images[0])
        history_texts.append(res)
        return res
    except Exception as e:
        return None
def highlight(mode, alpha, label_mode, anno_mode, *args, **kwargs):
    res = history_texts[0]
    # find the seperate numbers in sentence res
    res = res.split(' ')
    res = [r.replace('.','').replace(',','').replace(')','').replace('"','') for r in res]
    # find all numbers in '[]'
    res = [r for r in res if '[' in r]
    res = [r.split('[')[1] for r in res]
    res = [r.split(']')[0] for r in res]
    res = [r for r in res if r.isdigit()]
    res = list(set(res))
    sections = []
    for i, r in enumerate(res):
        mask_i = history_masks[0][int(r)-1]['segmentation']
        sections.append((mask_i, r))
    return (history_images[0], sections)
class ImageMask(gr.components.Image):
    """
    Sets: source="canvas", tool="sketch"
    """
    is_template = True
    def __init__(self, **kwargs):
        super().__init__(source="upload", tool="sketch", interactive=True, **kwargs)
    def preprocess(self, x):
        return super().preprocess(x)
'''
launch app
'''
demo = gr.Blocks()
image = ImageMask(label="Input", type="pil", brush_radius=20.0, brush_color="\#FFFFFF", height=512)
# image = gr.Image(label="Input", type="pil", height=512)
slider = gr.Slider(1, 3, value=1.8, label="Granularity") # info="Choose in [1, 1.5), [1.5, 2.5), [2.5, 3] for [seem, semantic-sam (multi-level), sam]"
mode = gr.Radio(['Automatic', 'Interactive', ], value='Automatic', label="Segmentation Mode")
anno_mode = gr.CheckboxGroup(choices=["Mark", "Mask", "Box"], value=['Mark'], label="Annotation Mode")
image_out = gr.AnnotatedImage(label="SoM Visual Prompt",type="pil", height=512)
runBtn = gr.Button("Run")
highlightBtn = gr.Button("Highlight")
bot = gr.Chatbot(label="GPT-4V + SoM", height=256)
slider_alpha = gr.Slider(0, 1, value=0.05, label="Mask Alpha") \#info="Choose in [0, 1]"
label_mode = gr.Radio(['Number', 'Alphabet'], value='Number', label="Mark Mode")
title = "Set-of-Mark (SoM) Visual Prompting for Extraordinary Visual Grounding in GPT-4V"
description = "This is a demo for SoM Prompting to unleash extraordinary visual grounding in GPT-4V. Please upload an image and them click the 'Run' button to get the image with marks. Then chat with GPT-4V below!"
with demo:
    gr.Markdown("<h1 style='text-align: center'><img src='https://som-gpt4v.github.io/website/img/som_logo.png' style='height:50px;display:inline-block'/>  Set-of-Mark (SoM) Prompting Unleashes Extraordinary Visual Grounding in GPT-4V</h1>")
    # gr.Markdown("<h2 style='text-align: center; margin-bottom: 1rem'>Project: <a href='https://som-gpt4v.github.io/'>link</a>     arXiv: <a href='https://arxiv.org/abs/2310.11441'>link</a>     Code: <a href='https://github.com/microsoft/SoM'>link</a></h2>")
    with gr.Row():
        with gr.Column():
            image.render()
            slider.render()
            with gr.Accordion("Detailed prompt settings (e.g., mark type)", open=False):
                with gr.Row():
                    mode.render()
                    anno_mode.render()
                with gr.Row():
                    slider_alpha.render()
                    label_mode.render()
        with gr.Column():
            image_out.render()
            runBtn.render()
            highlightBtn.render()
    with gr.Row():    
        gr.ChatInterface(chatbot=bot, fn=gpt4v_response)
    runBtn.click(inference, inputs=[image, slider, mode, slider_alpha, label_mode, anno_mode],
              outputs = image_out)
    highlightBtn.click(highlight, inputs=[image, mode, slider_alpha, label_mode, anno_mode],
              outputs = image_out)
demo.queue().launch(share=True,server_port=6092)
```
  
  
# GPT4o New Features
[Introduction to gpt-4o | OpenAI Cookbook](https://cookbook.openai.com/examples/gpt4o/introduction_to_gpt4o)


# OpenAI O1
/opt/app/researcher/openai_newfeature.ipynb

To show better response of O1 in jupiter, we can use below function
```python
from IPython.display import display, HTML
import markdown2  # You might need to pip install markdown2

def display_chat_response(response):
    try:
        # Convert markdown to HTML for the content
        content_html = markdown2.markdown(response.choices[0].message.content)
        
        html = f"""
        <div style="background: white; padding: 20px; border-radius: 10px; border: 2px solid #e0e0e0; font-family: Arial, sans-serif;">
            <h3 style="color: #2c3e50; margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 10px;">Chat Response</h3>
            
            <p style="color: #2980b9;"><b>Model:</b> <span style="color: #555;">{response.model}</span></p>
            
            <p style="color: #2980b9;"><b>Content:</b></p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #3498db; margin: 10px 0; color: #2c3e50; line-height: 1.6;">
                {content_html}
            </div>
            
            <h4 style="color: #e74c3c; margin-top: 20px;">Usage Stats</h4>
            <ul style="list-style-type: none; padding-left: 0;">
                <li style="margin: 8px 0; color: #27ae60;">
                    <span style="display: inline-block; width: 200px;">📊 Prompt tokens:</span> 
                    <span style="color: #555;">{response.usage.prompt_tokens}</span>
                </li>
                <li style="margin: 8px 0; color: #27ae60;">
                    <span style="display: inline-block; width: 200px;">📈 Completion tokens:</span> 
                    <span style="color: #555;">{response.usage.completion_tokens}</span>
                </li>
                <li style="margin: 8px 0; color: #27ae60;">
                    <span style="display: inline-block; width: 200px;">🧠 Reasoning tokens:</span> 
                    <span style="color: #555;">{response.usage.completion_tokens_details.reasoning_tokens}</span>
                </li>
                <li style="margin: 8px 0; color: #27ae60;">
                    <span style="display: inline-block; width: 200px;">✅ Accepted prediction tokens:</span> 
                    <span style="color: #555;">{response.usage.completion_tokens_details.accepted_prediction_tokens}</span>
                </li>
                <li style="margin: 8px 0; color: #27ae60;">
                    <span style="display: inline-block; width: 200px;">❌ Rejected prediction tokens:</span> 
                    <span style="color: #555;">{response.usage.completion_tokens_details.rejected_prediction_tokens}</span>
                </li>
                <li style="margin: 8px 0; color: #27ae60;">
                    <span style="display: inline-block; width: 200px;">🔊 Audio tokens:</span> 
                    <span style="color: #555;">{response.usage.completion_tokens_details.audio_tokens}</span>
                </li>
                <li style="margin: 8px 0; color: #27ae60;">
                    <span style="display: inline-block; width: 200px;">📉 Total tokens:</span> 
                    <span style="color: #555;">{response.usage.total_tokens}</span>
                </li>
            </ul>
        </div>
        """
        display(HTML(html))
    except Exception as e:
        print(f"Error displaying response: {e}")
```
## Prompt

Example 1
```
bad_prompt = ("Generate a function that outputs the SMILES IDs for all the molecules involved in insulin."
              "Think through this step by step, and don't skip any steps:"
              "- Identify all the molecules involve in insulin"
              "- Make the function"
              "- Loop through each molecule, outputting each into the function and returning a SMILES ID"
              "Molecules: ")
response = client.chat.completions.create(model=O1_MODEL,messages=[{"role":"user","content": bad_prompt}])

good_prompt = ("Generate a function that outputs the SMILES IDs for all the molecules involved in insulin.")
response = client.chat.completions.create(model=O1_MODEL,messages=[{"role":"user","content": good_prompt}])
```
Example 2. Show rather than tell
```
base_prompt = ("<prompt>You are a lawyer specializing in competition law, "
               "assisting business owners with their questions.</prompt>\n"
               "<policy>As a legal professional, provide clear and accurate "
               "information about competition law while maintaining "
               "confidentiality and professionalism. Avoid giving specific "
               "legal advice without sufficient context, and encourage clients "
               "to seek personalized counsel when necessary. Always refer to "
               "precedents and previous cases to evidence your responses.</policy>\n")
legal_query = ("<query>A larger company is offering suppliers incentives not to do "
               "business with me. Is this legal?</query>")

response = client.chat.completions.create(model=O1_MODEL
		                                             ,messages=[{
		                                                 "role": "user",
		                                                 "content": base_prompt + legal_query
		                                             }]
		                                            )
```
Good prompt
```
example_prompt = ("<prompt>You are a lawyer specializing in competition law, "
               "assisting business owners with their questions.</prompt>\n"
               "<policy>As a legal professional, provide clear and accurate "
               "information about competition law while maintaining "
               "confidentiality and professionalism. Avoid giving specific "
               "legal advice without sufficient context, and encourage clients "
               "to seek personalized counsel when necessary.</policy>\n"
               """<example>
<question>
I'm considering collaborating with a competitor on a joint marketing campaign. Are there any antitrust issues I should be aware of?
</question>
<response>
Collaborating with a competitor on a joint marketing campaign can raise antitrust concerns under U.S. antitrust laws, particularly the Sherman Antitrust Act of 1890 (15 U.S.C. §§ 1–7). Section 1 of the Sherman Act prohibits any contract, combination, or conspiracy that unreasonably restrains trade or commerce among the states.

**Key Considerations:**

1. **Per Se Illegal Agreements:** Certain collaborations are considered automatically illegal ("per se" violations), such as price-fixing, bid-rigging, and market allocation agreements. For example, in *United States v. Topco Associates, Inc.*, 405 U.S. 596 (1972), the Supreme Court held that market division agreements between competitors are per se illegal under the Sherman Act.

2. **Rule of Reason Analysis:** Collaborations that are not per se illegal are evaluated under the "rule of reason," which assesses whether the pro-competitive benefits outweigh the anti-competitive effects. In *Broadcast Music, Inc. v. Columbia Broadcasting System, Inc.*, 441 U.S. 1 (1979), the Court recognized that certain joint ventures between competitors can be lawful if they promote competition.

3. **Information Sharing Risks:** Sharing competitively sensitive information, such as pricing strategies or customer data, can lead to antitrust violations. The Department of Justice and the Federal Trade Commission caution against exchanges that could facilitate collusion (*Antitrust Guidelines for Collaborations Among Competitors*, 2000).

**Recommendations:**

- **Define the Scope:** Clearly delineate the parameters of the collaboration to focus on the marketing campaign without involving competitive aspects like pricing or market division.
- **Implement Safeguards:** Establish protocols to prevent the exchange of sensitive information that is not essential to the marketing effort.
- **Legal Consultation:** Given the complexities of antitrust laws, consult with a legal professional to ensure the collaboration complies with all legal requirements.

**Conclusion:**

While joint marketing campaigns between competitors are not inherently illegal, they must be structured carefully to avoid antitrust pitfalls. Legal guidance is essential to navigate these issues and to design a collaboration that achieves your business objectives without violating antitrust laws.
</response>
</example>""")


```
## Plan with o1
Use o1 to generate plan
```python
import copy
import json
from openai import OpenAI

from utils import o1_tools

client = OpenAI(api_key=openai_api_key)
O1_MODEL = 'o1-mini'
GPT_MODEL = 'gpt-4o-mini'

# Initialize the message list
message_list = []

# Define the initial context for the application
context = {
    'inventory': {
        'X200': 50  # We currently have 50 units of Smart Home Hub X200 in stock
    },
    'orders': [
        {
            'order_id': 'ORD3001',
            'product_id': 'X200',
            'quantity': 200,
            'customer_id': 'CUST9001',
            'destination': 'Los Angeles',
            'weight': 1.5,  # Weight per unit in kg
            'dimensions': {'length': 20, 'width': 15, 'height': 10}  # Dimensions in cm
        }
    ],
    'available_suppliers': ['SUPP1001', 'SUPP1002'],
    'suppliers': {
        'SUPP1001': {
            'components': {
                'COMP_X200': {'available_quantity': 500}
            }
        },
        'SUPP1002': {
            'components': {
                'COMP_X300': {'available_quantity': 300}
            }
        }
    },
    'production_capacity': {
        'immediate': 100,      # Units we can produce immediately
        'next_week': 150       # Units we can produce next week
    },
    'shipping_options': {
        'Los Angeles': [
            {
                'carrier_id': 'CARRIER1',
                'service_level': 'Standard',
                'cost': 1000,
                'estimated_days': 5
            },
            {
                'carrier_id': 'CARRIER2',
                'service_level': 'Express',
                'cost': 1500,
                'estimated_days': 2
            }
        ]
    },
    'customers': {
        'CUST9001': {
            'name': 'ElectroWorld',
            'address': '123 Market Street, Los Angeles, CA'
        }
    },
    'products': {
        'X200': {
            'name': 'Smart Home Hub X200',
            'components_needed': {
                'COMP_X200': 1  # Each unit requires 1 component COMP_X200
            }
        }
    }
}

# Store the initial state of context
initial_context = copy.deepcopy(context)

# Prompt for the planning model
o1_prompt = """
You are a supply chain management assistant. The first input you will receive will be a complex task that needs to be carefully reasoned through to solve. 
Your task is to review the challenge, and create a detailed plan to process customer orders, manage inventory, and handle logistics.

You will have access to an LLM agent that is responsible for executing the plan that you create and will return results.

The LLM agent has access to the following functions:
    - get_inventory_status(product_id)
        - This gets the currently available product that we have
    - get_product_details(product_id)
        - This function gets the necessary components we need to manufacture additional product
    - update_inventory(product_id, quantity_change)
        - This function updates the currently available inventory of product.
        - This function should be called after we have allocated stock to an order.
    - fetch_new_orders()
        - The function checks the current status of new orders
    - allocate_stock(order_id, product_id, quantity)
        - This function allocates the stock of a product to an order.
    - check_available_suppliers()
        - This function checks the list of available suppliers we can leverage for additional components.
    - get_supplier_info(supplier_id)
        - This function returns the components the supplier can produce and the quantity of that component.
        - It is necessary to get the components required in order to place a purchase order with the supplier.
    - place_purchase_order(supplier_id, component_id, quantity)
        - This function places a purchase order with the supplier for additional components
        - In order to place the purchase order, we need to know the necessary components and the supplier id.
        - If the supplier specified does not have this component available, the function will fail.
    - check_production_capacity(time_frame)
        - Based on the amount of components we have, this function determines how much product we can produce on-site within a specific time-frame
        - If we do not have sufficient production capacity, a purchase order will need to be made to the supplier
    - schedule_production_run(product_id, quantity, time_frame)
        - This function convert the available production supply to product.
        - Any production scheduled will reduce the production capacity immediately available and available next week.
        - The time frame values can match the production capacity options: 'immediate' or 'next_week'
        - If a production run is scheduled with time frame 'immediate', it will automatically update our inventory with the new capacity. We should not call 'update_inventory' after.
    - calculate_shipping_options(destination, weight, dimensions)
        - This function determines the available shipping options and costs
        - Only currently available inventory can be shipped
        - Destination should match the destination name on the order
    - book_shipment(order_id, carrier_id, service_level)
        - This will book a shipment for a current order.
    - send_order_update(customer_id, order_id, message)
        - This will send an update to the customer and is necessary for any communications
        - It is important to keep customers in the loop about the status of the order

When creating a plan for the LLM to execute, break your instructions into a logical, step-by-step order, using the specified format:
    - **Main actions are numbered** (e.g., 1, 2, 3).
    - **Sub-actions are lettered** under their relevant main actions (e.g., 1a, 1b).
        - **Sub-actions should start on new lines**
    - **Specify conditions using clear 'if...then...else' statements** (e.g., 'If the product was purchased within 30 days, then...').
    - **For actions that require using one of the above functions defined**, write a step to call a function using backticks for the function name (e.g., `call the get_inventory_status function`).
        - Ensure that the proper input arguments are given to the model for instruction. There should not be any ambiguity in the inputs.
    - **The last step** in the instructions should always be calling the `instructions_complete` function. This is necessary so we know the LLM has completed all of the instructions you have given it.
    - **Detailed steps** The plan generated must be extremely detailed and thorough with explanations at every step.
Use markdown format when generating the plan with each step and sub-step.

Please find the scenario below.
"""

# System prompt for the execution model
gpt4o_system_prompt = """
You are a helpful assistant responsible for executing the policy on handling incoming orders. Your task is to follow the policy exactly as it is written and perform the necessary actions.

You must explain your decision-making process across various steps.

# Steps

1. **Read and Understand Policy**: Carefully read and fully understand the given policy on handling incoming orders.
2. **Identify the exact step in the policy**: Determine which step in the policy you are at, and execute the instructions according to the policy.
3. **Decision Making**: Briefly explain your actions and why you are performing them.
4. **Action Execution**: Perform the actions required by calling any relevant functions and input parameters.

POLICY:
{policy}

"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "get_inventory_status",
            "description": "Retrieves the current inventory status for a given product. This only shows the currently available inventory for PRODUCTS and not components.",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier for the product.",
                        "enum": ["X100", "X200", "X300"]
                    },
                },
                "required": ["product_id"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "update_inventory",
            "description": "Updates the inventory quantity for a specific product. This should be called after we have allocated stock to an order",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier for the product.",
                    },
                    "quantity_change": {
                        "type": "integer",
                        "description": "The amount to adjust the inventory by (positive or negative).",
                    },
                },
                "required": ["product_id", "quantity_change"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "fetch_new_orders",
            "description": "Fetches new customer orders that have not been processed yet. There are no input parameters for this function.",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "check_available_suppliers",
            "description": "This functions checks the list of available suppliers we can leverage for additional components.",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_product_details",
            "description": "Fetches the product details included the required components necessary for creating more of the product.",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier of the product.",
                    }
                },
                "required": ["product_id"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_supplier_info",
            "description": "This function returns the components the supplier can produce and the quantity of that component. It is necessary to get the components required in order to place a purchase order with the supplier.",
            "parameters": {
                "type": "object",
                "properties": {
                    "supplier_id": {
                        "type": "string",
                        "description": "The unique identifier of the supplier.",
                    }
                },
                "required": ["supplier_id"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "allocate_stock",
            "description": "Allocates stock for a specific order and product.",
            "parameters": {
                "type": "object",
                "properties": {
                    "order_id": {
                        "type": "string",
                        "description": "The unique identifier of the customer order.",
                    },
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier of the product.",
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "The quantity of the product to allocate.",
                    },
                },
                "required": ["order_id", "product_id", "quantity"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "place_purchase_order",
            "description": "This function places a purchase order with the supplier for additional components. In order to place the purchase order, we need to know the necessary components and the supplier id. If the supplier specified does not have this component available, the function will fail.",
            "parameters": {
                "type": "object",
                "properties": {
                    "supplier_id": {
                        "type": "string",
                        "description": "The unique identifier of the supplier.",
                    },
                    "component_id": {
                        "type": "string",
                        "description": "The unique identifier of the component.",
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "The quantity of the component to order.",
                    },
                },
                "required": ["supplier_id", "component_id", "quantity"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "check_production_capacity",
            "description": "Based on the amount of components we have, this function determines how much product we can produce on-site within a specific time-frame. If we do not have sufficient production capacity, a purchase order will need to be made to the supplier",
            "parameters": {
                "type": "object",
                "properties": {
                    "time_frame": {
                        "type": "string",
                        "description": "The time frame to check,",
                        "enum": ["immediate", "next_week"]
                    },
                },
                "required": ["time_frame"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "schedule_production_run",
            "description": "This function convert the available production supply to product. Any production scheduled will reduce the production capacity immedatiely available and available next week. If the quantity exceeds the immediately available production, it will fail. If a production run is scheduled with time frame 'immediate', it will automatically update our inventory with the new capacity.",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier of the product.",
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "The quantity of the product to produce.",
                    },
                    "time_frame": {
                        "type": "string",
                        "description": "The time frame for when the production run needs to be scheduled.",
                        "enum": ["immediate", "next_week"]
                    },
                },
                "required": ["product_id", "quantity", "time_frame"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "calculate_shipping_options",
            "description": "This function determines the availablwe shipping options and costs. Only currently available inventory can be shipped",
            "parameters": {
                "type": "object",
                "properties": {
                    "destination": {
                        "type": "string",
                        "description": "The shipping destination address.",
                    },
                    "weight": {
                        "type": "number",
                        "description": "The weight of the package in kilograms.",
                    },
                    "dimensions": {
                        "type": "string",
                        "description": "The dimensions of the package (LxWxH) in centimeters.",
                    },
                },
                "required": ["destination", "weight", "dimensions"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "book_shipment",
            "description": "Books a shipment for an order using a specific carrier and service level.",
            "parameters": {
                "type": "object",
                "properties": {
                    "order_id": {
                        "type": "string",
                        "description": "The unique identifier of the customer order.",
                    },
                    "carrier_id": {
                        "type": "string",
                        "description": "The unique identifier of the shipping carrier.",
                    },
                    "service_level": {
                        "type": "string",
                        "description": "The level of shipping service, e.g., 'Standard', 'Express'.",
                    },
                },
                "required": ["order_id", "carrier_id", "service_level"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "send_order_update",
            "description": "This will send an update to the customer and is necessary for any communincations. It is important to keep customers in the loop about the status of the order",
            "parameters": {
                "type": "object",
                "properties": {
                    "customer_id": {
                        "type": "string",
                        "description": "The unique identifier of the customer.",
                    },
                    "order_id": {
                        "type": "string",
                        "description": "The unique identifier of the order.",
                    },
                    "message": {
                        "type": "string",
                        "description": "The message content to send to the customer.",
                    },
                },
                "required": ["customer_id", "order_id", "message"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "instructions_complete",
            "description": "Function should be called when we have completed ALL of the instructions.",
        },
    }
]


TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "get_inventory_status",
            "description": "Retrieves the current inventory status for a given product. This only shows the currently available inventory for PRODUCTS and not components.",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier for the product.",
                        "enum": ["X100", "X200", "X300"]
                    },
                },
                "required": ["product_id"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "update_inventory",
            "description": "Updates the inventory quantity for a specific product. This should be called after we have allocated stock to an order",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier for the product.",
                    },
                    "quantity_change": {
                        "type": "integer",
                        "description": "The amount to adjust the inventory by (positive or negative).",
                    },
                },
                "required": ["product_id", "quantity_change"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "fetch_new_orders",
            "description": "Fetches new customer orders that have not been processed yet. There are no input parameters for this function.",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "check_available_suppliers",
            "description": "This functions checks the list of available suppliers we can leverage for additional components.",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_product_details",
            "description": "Fetches the product details included the required components necessary for creating more of the product.",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier of the product.",
                    }
                },
                "required": ["product_id"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_supplier_info",
            "description": "This function returns the components the supplier can produce and the quantity of that component. It is necessary to get the components required in order to place a purchase order with the supplier.",
            "parameters": {
                "type": "object",
                "properties": {
                    "supplier_id": {
                        "type": "string",
                        "description": "The unique identifier of the supplier.",
                    }
                },
                "required": ["supplier_id"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "allocate_stock",
            "description": "Allocates stock for a specific order and product.",
            "parameters": {
                "type": "object",
                "properties": {
                    "order_id": {
                        "type": "string",
                        "description": "The unique identifier of the customer order.",
                    },
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier of the product.",
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "The quantity of the product to allocate.",
                    },
                },
                "required": ["order_id", "product_id", "quantity"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "place_purchase_order",
            "description": "This function places a purchase order with the supplier for additional components. In order to place the purchase order, we need to know the necessary components and the supplier id. If the supplier specified does not have this component available, the function will fail.",
            "parameters": {
                "type": "object",
                "properties": {
                    "supplier_id": {
                        "type": "string",
                        "description": "The unique identifier of the supplier.",
                    },
                    "component_id": {
                        "type": "string",
                        "description": "The unique identifier of the component.",
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "The quantity of the component to order.",
                    },
                },
                "required": ["supplier_id", "component_id", "quantity"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "check_production_capacity",
            "description": "Based on the amount of components we have, this function determines how much product we can produce on-site within a specific time-frame. If we do not have sufficient production capacity, a purchase order will need to be made to the supplier",
            "parameters": {
                "type": "object",
                "properties": {
                    "time_frame": {
                        "type": "string",
                        "description": "The time frame to check,",
                        "enum": ["immediate", "next_week"]
                    },
                },
                "required": ["time_frame"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "schedule_production_run",
            "description": "This function convert the available production supply to product. Any production scheduled will reduce the production capacity immedatiely available and available next week. If the quantity exceeds the immediately available production, it will fail. If a production run is scheduled with time frame 'immediate', it will automatically update our inventory with the new capacity.",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The unique identifier of the product.",
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "The quantity of the product to produce.",
                    },
                    "time_frame": {
                        "type": "string",
                        "description": "The time frame for when the production run needs to be scheduled.",
                        "enum": ["immediate", "next_week"]
                    },
                },
                "required": ["product_id", "quantity", "time_frame"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "calculate_shipping_options",
            "description": "This function determines the availablwe shipping options and costs. Only currently available inventory can be shipped",
            "parameters": {
                "type": "object",
                "properties": {
                    "destination": {
                        "type": "string",
                        "description": "The shipping destination address.",
                    },
                    "weight": {
                        "type": "number",
                        "description": "The weight of the package in kilograms.",
                    },
                    "dimensions": {
                        "type": "string",
                        "description": "The dimensions of the package (LxWxH) in centimeters.",
                    },
                },
                "required": ["destination", "weight", "dimensions"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "book_shipment",
            "description": "Books a shipment for an order using a specific carrier and service level.",
            "parameters": {
                "type": "object",
                "properties": {
                    "order_id": {
                        "type": "string",
                        "description": "The unique identifier of the customer order.",
                    },
                    "carrier_id": {
                        "type": "string",
                        "description": "The unique identifier of the shipping carrier.",
                    },
                    "service_level": {
                        "type": "string",
                        "description": "The level of shipping service, e.g., 'Standard', 'Express'.",
                    },
                },
                "required": ["order_id", "carrier_id", "service_level"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "send_order_update",
            "description": "This will send an update to the customer and is necessary for any communincations. It is important to keep customers in the loop about the status of the order",
            "parameters": {
                "type": "object",
                "properties": {
                    "customer_id": {
                        "type": "string",
                        "description": "The unique identifier of the customer.",
                    },
                    "order_id": {
                        "type": "string",
                        "description": "The unique identifier of the order.",
                    },
                    "message": {
                        "type": "string",
                        "description": "The message content to send to the customer.",
                    },
                },
                "required": ["customer_id", "order_id", "message"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "instructions_complete",
            "description": "Function should be called when we have completed ALL of the instructions.",
        },
    }
]

# Function Definitions
def get_inventory_status(product_id):
    quantity = context['inventory'].get(product_id, 0)
    return {'product_id': product_id, 'quantity': quantity}

def get_product_details(product_id):
    product = context['products'].get(product_id, {})
    return {"name": product.get('name', ''), "components_needed": product.get("components_needed", {})}

def update_inventory(product_id, quantity_change):
    if product_id not in context['inventory']:
        return {'error': f"Product ID {product_id} not found in inventory."}
    
    new_quantity = context['inventory'][product_id] + int(quantity_change)
    
    if new_quantity < 0:
        return {'error': 'Resulting inventory cannot be negative.'}
    
    context['inventory'][product_id] = new_quantity
    return {'product_id': product_id, 'new_quantity': new_quantity}

def fetch_new_orders():
    return context['orders'][0]

def allocate_stock(order_id, product_id, quantity):
    available = context['inventory'].get(product_id, 0)
    if available >= quantity:
        context['inventory'][product_id] -= quantity
        return {'order_id': order_id, 'allocated_quantity': quantity}
    else:
        allocated_quantity = available
        context['inventory'][product_id] = 0
        return {
            'order_id': order_id,
            'allocated_quantity': allocated_quantity,
            'error': 'Insufficient stock'
        }

def check_available_suppliers():
    available_suppliers = context['available_suppliers']
    return {"available_suppliers": available_suppliers}

def get_supplier_info(supplier_id):
    supplier = context['suppliers'].get(supplier_id)
    if not supplier:
        return {'error': f"Supplier {supplier_id} not found."}
    
    components = supplier.get('components', {})
    return {'supplier_id': supplier_id, 'components': components}

def place_purchase_order(supplier_id, component_id, quantity):
    supplier = context['suppliers'].get(supplier_id)
    if not supplier:
        return {'error': f"Supplier {supplier_id} not found."}
    component = supplier['components'].get(component_id)
    if not component:
        return {'error': f"Component {component_id} not found with supplier {supplier_id}."}
    if component['available_quantity'] < quantity:
        return {'error': f"Insufficient component quantity available from supplier {supplier_id}."}
    component['available_quantity'] -= quantity
    po_number = f"PO_{supplier_id}_{component_id}"
    context['production_capacity']['next_week'] += quantity
    
    return {'po_number': po_number, 'status': 'Placed'}

def check_production_capacity(time_frame):
    capacity = context['production_capacity'].get(time_frame, 0)
    return {'time_frame': time_frame, 'available_capacity': capacity}

def schedule_production_run(product_id, quantity, time_frame):
    capacity = context['production_capacity'].get(time_frame, 0)
    if capacity >= quantity:
        context['production_capacity'][time_frame] -= quantity
        if time_frame == 'immediate':
            context['inventory'][product_id] += quantity
        return {'production_id': 'PROD1001', 'status': 'Scheduled', 'time_frame': time_frame}
    else:
        return {'error': 'Insufficient production capacity, please order more from supplier.'}

def calculate_shipping_options(destination, weight, dimensions):
    options = context['shipping_options'].get(destination)
    if not options:
        return {'error': f"No shipping options available for destination {destination}."}
    return options

def book_shipment(order_id, carrier_id, service_level):
    tracking_number = f'TRACK_{order_id}'
    return {'tracking_number': tracking_number, 'status': 'Booked'}

def send_order_update(customer_id, order_id, message):
    return {'customer_id': customer_id, 'order_id': order_id, 'message_sent': True}

# Map function names to actual functions
function_mapping = {
    'get_inventory_status': get_inventory_status,
    'get_product_details': get_product_details,
    'update_inventory': update_inventory,
    'fetch_new_orders': fetch_new_orders,
    'allocate_stock': allocate_stock,
    'place_purchase_order': place_purchase_order,
    'check_available_suppliers': check_available_suppliers,
    'get_supplier_info': get_supplier_info,
    'check_production_capacity': check_production_capacity,
    'schedule_production_run': schedule_production_run,
    'calculate_shipping_options': calculate_shipping_options,
    'book_shipment': book_shipment,
    'send_order_update': send_order_update
}


def process_scenario(scenario):
    append_message({'type': 'status', 'message': 'Generating plan...'})

    plan = call_o1(scenario)

    append_message({'type': 'plan', 'content': plan})

    append_message({'type': 'status', 'message': 'Executing plan...'})

    messages = call_gpt4o(plan)

    append_message({'type': 'status', 'message': 'Processing complete.'})

    return messages

def append_message(message):
    message_list.append(message)
    # Optionally, print the message for immediate feedback
    message_type = message.get('type', '')
    if message_type == 'status':
        print(message['message'])
    elif message_type == 'plan':
        print("\nPlan:\n", message['content'])
    elif message_type == 'assistant':
        print("\nAssistant:\n", message['content'])
    elif message_type == 'function_call':
        print(f"\nFunction call: {message['function_name']} with arguments {message['arguments']}")
    elif message_type == 'function_response':
        print(f"\nFunction response for {message['function_name']}: {message['response']}")
    else:
        # Handle any other message types or default case
        print(message.get('content', ''))
        
def call_o1(scenario):
    prompt = f"""
{o1_prompt}
    
Scenario:
{scenario}

Please provide the next steps in your plan."""
    
    response = client.chat.completions.create(
        model=O1_MODEL,
        messages=[{'role': 'user', 'content': prompt}]
    )
    plan = response.choices[0].message.content
    
    return plan

def call_gpt4o(plan):
    gpt4o_policy_prompt = gpt4o_system_prompt.replace("{policy}", plan)
    messages = [
        {'role': 'system', 'content': gpt4o_policy_prompt},
    ]

    while True:
        response = client.chat.completions.create(
            model=GPT_MODEL,
            messages=messages,
            tools=TOOLS,
            parallel_tool_calls=False
        )
        
        assistant_message = response.choices[0].message.to_dict()
        print(assistant_message)
        messages.append(assistant_message)

        append_message({'type': 'assistant', 'content': assistant_message.get('content', '')})

        if (response.choices[0].message.tool_calls and
            response.choices[0].message.tool_calls[0].function.name == 'instructions_complete'):
            break

        if not response.choices[0].message.tool_calls:
            continue

        for tool in response.choices[0].message.tool_calls:
            tool_id = tool.id
            function_name = tool.function.name
            input_arguments_str = tool.function.arguments

            append_message({'type': 'tool_call', 'function_name': function_name, 'arguments': input_arguments_str})

            try:
                input_arguments = json.loads(input_arguments_str)
            except (ValueError, json.JSONDecodeError):
                continue

            if function_name in function_mapping:
                try:
                    function_response = function_mapping[function_name](**input_arguments)
                except Exception as e:
                    function_response = {'error': str(e)}
            else:
                function_response = {'error': f"Function '{function_name}' not implemented."}

            try:
                serialized_output = json.dumps(function_response)
            except (TypeError, ValueError):
                serialized_output = str(function_response)

            messages.append({
                "role": "tool",
                "tool_call_id": tool_id,
                "content": serialized_output
            })

            append_message({'type': 'tool_response', 'function_name': function_name, 'response': serialized_output})

    return messages

# Example usage
scenario_text = ("We just received a major shipment of new orders. "
                 "Please generate a plan that gets the list of awaiting "
                 "orders and determines the best policy to fulfill them.\n\n"
                 "The plan should include checking inventory, ordering "
                 "necessary components from suppliers, scheduling production "
                 "runs with available capacity, ordering new components "
                 "required from suppliers, and arranging shipping to the "
                 "retailer’s distribution center in Los Angeles. Notify "
                 "the customer before completing.\n\n"
                 "Prioritize getting any possible orders out that you can "
                 "while placing orders for any backlog items.")

# Process the scenario
messages = process_scenario(scenario_text)
```
## Code with O1

```python
from IPython.display import display, Image, Markdown
from openai import OpenAI

client = OpenAI(api_key=openai_api_key)
GPT_MODEL = 'gpt-4o-mini'
O1_MODEL = 'o1-mini'


def get_chat_completion(model, prompt):
    """
    Calls the OpenAI API to get a chat completion.

    :param model: The model to use for the completion.
    :param prompt: The prompt to send to the model.
    :return: The completion response from the model.
    """
    response = client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
    )
    return response.choices[0].message.content

react_demo_prompt = """Create an elegant, delightful React component for an Interview Feedback Form where:

1. The interviewer rates candidates across multiple dimensions using rubrics
2. Each rating must include specific evidence/examples
3. The final recommendation should auto-calculate based on a weighted scoring system
4. The UI should guide interviewers to give specific, behavioral feedback

The goal is to enforce structured, objective feedback gathering. A smart model should:
- Create a thoughtful rubric structure
- Add helpful prompts/placeholders
- Build in intelligent validation

Make sure to
 - Call the element FeedbackForm
 - Start the code with "use client"

Respond with the code only! Nothing else!"""

o1_code = get_chat_completion(O1_MODEL, react_demo_prompt)
print(o1_code)

# Pre-generated code snippet with issues that need to be resolved.
code_snippet = """
def process_orders(orders_list, settings={}, debug=False, notify_customer=True):
    results = []
    errors = []
    notifications = []
    # Process all orders
    for i in range(0, len(orders_list)):
        # Get order
        order = orders_list[i]
        try:
            # Validate order has required fields
            if 'id' in order and 'items' in order and 'customer' in order:
                # Check customer info
                if 'email' in order['customer'] and 'name' in order['customer']:
                    # Validate items
                    items_valid = True
                    total = 0
                    # Check each item
                    for item in order['items']:
                        if not ('product_id' in item and 'quantity' in item):
                            items_valid = False
                            errors.append(f"Invalid item in order {order['id']}")
                            if debug == True:
                                print(f"Debug: Invalid item found in order {order['id']}")
                        else:
                            # Calculate total
                            if 'price' in item:
                                total = total + (item['quantity'] * item['price'])
                            else:
                                items_valid = False
                                errors.append(f"Missing price in order {order['id']}")
                    # Process if items valid
                    if items_valid == True:
                        # Apply any discounts from settings
                        if settings != {} and 'discount' in settings:
                            total = total * (1 - settings['discount'])
                        # Create processed order
                        processed = {
                            'order_id': order['id'],
                            'customer_name': order['customer']['name'],
                            'customer_email': order['customer']['email'],
                            'total': total,
                            'items_count': len(order['items'])
                        }
                        results.append(processed)
                        # Send notification
                        if notify_customer == True:
                            try:
                                # Create notification
                                notification = {
                                    'to': order['customer']['email'],
                                    'subject': 'Order Processed',
                                    'total': total
                                }
                                notifications.append(notification)
                                if debug == True:
                                    print(f"Debug: Notification queued for order {order['id']}")
                            except Exception as e:
                                errors.append(f"Notification failed for order {order['id']}")
                else:
                    errors.append(f"Invalid customer data in order {order['id']}")
            else:
                errors.append(f"Missing required fields in order {order['id']}")
        except Exception as e:
            # Add general error
            errors.append(f"Error processing order {order['id']}: {str(e)}")
            if debug == True:
                print(f"Debug: Error processing order {order['id']}: {str(e)}")

    # Print debug summary
    if debug == True:
        print(f"Debug: Processed {len(results)} orders with {len(errors)} errors")
        print(f"Debug: Queued {len(notifications)} notifications")

    return {
        "processed_orders": results,
        "errors": errors,
        "notifications": notifications
    }
"""

prompt = f"""I have some code that I'd like you to clean up and improve. Return only the updated code that fixes the issues: {code_snippet}"""
gpt_code = get_chat_completion(GPT_MODEL, prompt)
print(gpt_code)
o1_code = get_chat_completion(O1_MODEL, prompt)
print(o1_code)

result = get_chat_completion(O1_MODEL,f"which code is better and why? Option 1: {gpt_code}... or Option 2: {o1_code}")
display(Markdown(result))

```
## reasoning with images

```python
import json
from openai import OpenAI
from IPython.display import display, Markdown, Image
from helper import get_openai_api_key
openai_api_key = get_openai_api_key()

#from utils import o1_vision

GPT_MODEL = 'gpt-4o-mini'
O1_MODEL = 'o1'

client = OpenAI()
image_filepath = 'data/org_chart_sample.png'

display(Image(image_filepath))
import base64
def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

#print(response.choices[0].message.content)

def o1_vision(file_path,prompt,model,json_mode=False):

    base64_image = encode_image(file_path)

    if json_mode:

        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {
                        "url": f"data:image/png;base64,{base64_image}"}
                    }
                ]}
            ],
            response_format={ "type": "json_object" }
        )

    else:
        
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {
                        "url": f"data:image/png;base64,{base64_image}"}
                    }
                ]}
            ],
        )

    return response
response = o1_vision(file_path=image_filepath
                     ,prompt='What is this?'
                     ,model=O1_MODEL)
                 
display(Markdown(response.choices[0].message.content))

structured_prompt = ("<instructions>You are a consulting assistant who processes org data. "
                     "Extract the org hierarchy from the image you're provided in a structured format. "
                     "The structure should be returned in JSON containing:\n"
                     "- arbitrary ID of the person that you can generate\n"
                     "- name of the person\n"
                     "- role of the person\n"
                     "- an array of IDs they report to\n"
                     "- an array of IDs that report to them"
                     "</instructions>")
print(structured_prompt)

o1_response = o1_vision(file_path=image_filepath
                        ,model='o1'
                        ,prompt=structured_prompt
                        ,json_mode=True)
print(o1_response.choices[0].message.content)

cleaned_json = o1_response.choices[0].message.content.replace('```json','').replace('```','')
org_data = json.loads(o1_response.choices[0].message.content)
analysis_prompt = ("<instructions>You are an org chart expert assistant. Your role is to"
                      "answer any org chart questions with your org data.</instructions>\n"
                      f"<org_data>{org_data}</org_data>\n")
from helper import get_openai_api_key

openai_api_key = get_openai_api_key()
client = OpenAI(api_key=openai_api_key)
messages = [{
    "role": "user",
    "content": analysis_prompt + "<question>Who has the highest ranking reports, and which manager has the most reports?</question>"
}]

response = client.chat.completions.create(model=O1_MODEL,
                                          messages=messages)
                                          
display(Markdown(response.choices[0].message.content))
```

# OpenAI New Response API etc
https://www.youtube.com/watch?v=hciNKcLwSes
https://platform.openai.com/docs/quickstart?api-mode=responses
https://platform.openai.com/docs/guides/tools-web-search?api-mode=responses
https://platform.openai.com/docs/guides/tools-file-search
https://platform.openai.com/docs/guides/tools-computer-use
https://platform.openai.com/docs/guides/agents

https://www.youtube.com/watch?v=YA2fwTZz0tU&t=145s


# 4o Image generation
```
A wide image taken with a phone of a glass whiteboard, in a room overlooking the Bay Bridge. The field of view shows a woman writing, sporting a tshirt wiith a large OpenAI logo. The handwriting looks natural and a bit messy, and we see the photographer's reflection. The text reads: (left) "Transfer between Modalities: Suppose we directly model p(text, pixels, sound) [equation] with one big autoregressive transformer. Pros: * image generation augmented with vast world knowledge * next-level text rendering * native in-context learning * unified post-training stack Cons: * varying bit-rate across modalities * compute not adaptive" (Right) "Fixes: * model compressed representations * compose autoregressive prior with a powerful decoder" On the bottom right of the board, she draws a diagram: "tokens -> [transformer] -> [diffusion] -> pixels"
```
![[Pasted image 20250326124916.png]]

