---
Updated: 2024-05-15T17:00
tags:
  - AI->-<PERSON><PERSON><PERSON><PERSON>
  - AI->-Programming
Created: 2024-04-09T13:20
---
```Python
curl -fsSL https://bun.sh/install | bash
bun --version
sudo apt install nodejs npm
(base) ray@jethome:/opt/workspace/app/morphic$ node --version  <= this version is too old so we need to upgrade
v10.19.0
(base) ray@jethome:/opt/workspace/app/morphic$ npm --version
6.14.4
sudo npm install -g n
sudo n 18.17.0
hash -r
(base) ray@jethome:/opt/workspace/app/morphic$ node -v
v18.17.0
(base) ray@jethome:/opt/workspace/app/morphic$ npm -v
9.6.7
----------------------------------------------- 
git clone https://github.com/miurla/morphic
cd morphic
bun i
cp .env.local.example .env.local
-----------------------------------------------
\#edit and setup key
# OpenAI API key retrieved here: https://platform.openai.com/api-keys
OPENAI_API_KEY=***************************************************
# Tavily API Key retrieved here: https://app.tavily.com/home
TAVILY_API_KEY=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

# Upstash Redis URL and Token retrieved here: https://console.upstash.com/redis
UPSTASH_REDIS_REST_URL="https://creative-newt-41814.upstash.io"
UPSTASH_REDIS_REST_TOKEN="AaNWAAIncDE2NDg0MTgyYmI0ZDQ0YTY3YTI4NDZkYjU0YjgzNjExOHAxNDE4MTQ"

-----------------------------------------------
\#start app locally
bun dev
\#How to startup and listen on dedicated ip:port
1. open package.json
查看是否有定义用于启动开发服务器的脚本。
{
  "name": "mophic",
  "version": "0.1.0",
  "private": true,
  "license": "Apache-2.0",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
2.如果你的 package.json 中有 "dev": "next dev" 这样的脚本，你可以直接使用 bun 来代替 npm 或 yarn 执行这个脚本。
不过，要指定监听地址和端口，你可能需要直接运行 next dev 命令，并传入 -H（或 --hostname）和 -p（或 --port）参数。
由于 bun 目前（截至我最后更新的时间）可能还不直接支持这种方式，你可能需要确保 next 已经安装在你的项目中，然后直接使用 next 命令。
例如，要让 Next.js 应用监听在 0.0.0.0 地址的 7090 端口上，你可以在终端中运行：
3.startup
bun run next dev -H 0.0.0.0 -p 7090
(base) ray@jethome:~$ sudo netstat -ntulp|grep 3000
tcp6       0      0 :::3000                 :::*                    LISTEN      17226/next-server (
(

# face this bug due to node version is old
https://qiita.com/zhao-xy/items/7beaf4aa06f2022cf975
-》 after fix, it got solved

root@jethome:/usr/lib/systemd/system# cat morphic.service
[Unit]
Description=Morphic AI-powered answer engine with a generative UI.
After=network.target
[Service]
User=ray
Group=ray
WorkingDirectory=/opt/workspace/app/morphic
ExecStart=/bin/sh -c '/home/<USER>/.bun/bin/bun run next start -H 0.0.0.0 -p 7090 >> /opt/workspace/app/morphic/log/$(date +%%Y-%%m-%%d-%%H%%M%%S)-morphic.log 2>&1'
Restart=always
RestartSec=500ms
Environment="PATH=/usr/local/bin:/usr/bin:/bin"
Environment="NODE_ENV=production"
[Install]
WantedBy=multi-user.target

\#got below error
⨯ Error: ENOENT: no such file or directory, open '/databank/app/morphic/.next/prerender-manifest.js'
Explained by GPT:Next.js 项目在启动之前需要被构建，以生成 .next 目录及其下的内容，包括 prerender-manifest.js。如果直接以开发模式启动（next dev），理论上不应该需要这个文件，因为 Next.js 会在内存中处理大部分内容。但如果尝试以生产模式运行（next start），这个文件就变得必要了。确保你已经运行了 next build 来构建项目。
因为我用生产方式启动bun，所有需要这个文件
Solution：
(base) ray@jethome:/opt/workspace/app/morphic$ bun run next build
  ▲ Next.js 14.2.0-canary.60
  - Environments: .env.local
   Creating an optimized production build ...
 ✓ Compiled successfully
 ✓ Linting and checking validity of types
 ⚠ Using edge runtime on a page currently disables static generation for that page
 ✓ Collecting page data
 ✓ Generating static pages (5/5)
 ✓ Collecting build traces
 ✓ Finalizing page optimization
Route (app)                              Size     First Load JS
┌ ƒ /                                    2.43 kB         103 kB
├ ○ /_not-found                          876 B          87.8 kB
└ ○ /opengraph-image.png                 0 B                0 B
+ First Load JS shared by all            87 kB
  ├ chunks/526-2c49b2227eb3396a.js       31.4 kB
  ├ chunks/fd9d1056-7122c5929204dd6f.js  53.7 kB
  └ other shared chunks (total)          1.91 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand
(base) ray@jethome:/opt/workspace/app/morphic$
```
  
# New version(source code change you need to bun run next build to refresh)
```Python
 ⚠ [next]/internal/font/google/inter_95e1d3b3.module.css
Error while requesting resource
There was an issue establishing a connection while requesting https://fonts.googleapis.com/css2?family=Inter:wght@          100..900&display=swap.
#### Fix ####
1. change ./app/layout.tsx
const fontSans = FontSans({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false
})

Pending issue: AFter search complete it will refresh screen and back to initial page
```
  
  
[![](https://opengraph.githubassets.com/49213d27a6facc76af81dde6f215e2597ab9d461149e78d5b27bbc4225274fdf/miurla/morphic)](https://opengraph.githubassets.com/49213d27a6facc76af81dde6f215e2597ab9d461149e78d5b27bbc4225274fdf/miurla/morphic)
  
## ==Morphic==
==An AI-powered answer engine with a generative UI.==
[![](https://github.com/miurla/morphic/raw/main/public/capture-240404_blk.png)](https://github.com/miurla/morphic/raw/main/public/capture-240404_blk.png)
## ==🔍 Overview==
- ==🧱== [==Stack==](https://github.com/miurla/morphic#-stack)
- ==🚀== [==Quickstart==](https://github.com/miurla/morphic#-quickstart)
- ==🌐== [==Deploy==](https://github.com/miurla/morphic#-deploy)
## ==🧱 Stack==
- ==App framework:== [==Next.js==](https://nextjs.org/)
- ==Text streaming / Generative UI:== [==Vercel AI SDK==](https://sdk.vercel.ai/docs)
- ==Generative Model:== [==OpenAI==](https://openai.com/)
- ==Search API:== [==Tavily AI==](https://tavily.com/)
- ==Component library:== [==shadcn/ui==](https://ui.shadcn.com/)
- ==Headless component primitives:== [==Radix UI==](https://www.radix-ui.com/)
- ==Styling:== [==Tailwind CSS==](https://tailwindcss.com/)
## ==🚀 Quickstart==
### ==1. Fork and Clone repo==
==Fork the repo to your Github account, then run the following command to clone the repo:==
```plain
<NAME_EMAIL>:[YOUR_GITHUB_ACCOUNT]/morphic.git
```
### ==2. Install dependencies==
### ==3. Fill out secrets==
```plain
cp .env.local.example .env.local
```
==Your .env.local file should look like this:==
```plain
# OpenAI API key retrieved here: https://platform.openai.com/api-keys
OPENAI_API_KEY=[YOUR_OPENAI_API_KEY]
# Tavily API Key retrieved here: https://app.tavily.com/home
TAVILY_API_KEY=[YOUR_TAVILY_API_KEY]
```
### ==4. Run app locally==
==You can now visit== [==http://localhost:3000==](http://localhost:3000/)==.==
## ==🌐 Deploy==
==Host your own live version of Morphic with Vercel.==
[![](https://camo.githubusercontent.com/0d115430c1db17132964386282927e5e313543c7d868fc06bc9a7c65d7ec974e/68747470733a2f2f76657263656c2e636f6d2f627574746f6e)](https://camo.githubusercontent.com/0d115430c1db17132964386282927e5e313543c7d868fc06bc9a7c65d7ec974e/68747470733a2f2f76657263656c2e636f6d2f627574746f6e)