---
Updated: 2024-03-10T12:41
tags:
  - AI->-Theory
  - AI->-Voice
Created: 2024-03-10T12:41
---
## ==1. K均值聚类==
==知识点：K均值聚类算法，ISODATA算法，EM算法(Expectation-Maximization Algorithm，最大期望算法)==
==问题1 简述K均值算法的具体步骤==
==分析与解答：==
==K均值聚类的核心目标是将给定的数据集划分成K个簇，并给出每个数据对应的簇中心点。算法的具体步骤描述如下：==
==（1）数据预处理，如归一化、离群点处理等。==
==(2) 随机选取Ｋ个簇中心，记为 \mu_1^{(0)}, \mu_1^{(1)}, ..., \mu_1^{(K)}==
==(3) 定义代价函数：==
[![](https://pica.zhimg.com/50/v2-aec9d431458d5ab56326a57662a44c2a_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-aec9d431458d5ab56326a57662a44c2a_720w.jpg?source=1def8aca)
==(4) 令t=0,1,2,...为迭代步数，重复下面过程直到J收敛：==
- ==对应每一样本 x_i ，将其分配到距离最近的簇==
[![](https://picx.zhimg.com/50/v2-8f4df4b5887799022551ec203056db13_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-8f4df4b5887799022551ec203056db13_720w.jpg?source=1def8aca)
- ==对于每一个类簇k,重新计算该类簇的中心==
[![](https://picx.zhimg.com/50/v2-edb34be5a2ca68f1d4c474a40ad49bf4_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-edb34be5a2ca68f1d4c474a40ad49bf4_720w.jpg?source=1def8aca)
==K均值算法在迭代时，假设当前J没有达到最小值，那么首先固定簇中心 {\mu_k} ，使得每个样例xi所属的类别ci来让J函数减少；然后固定{ci}，调整簇中心{\mu_k}使J减小。这两个过程交替循环，J单调递减；当J递减到最小值时，{\mu_k}和{ci}也同时收敛。==
[![](https://picx.zhimg.com/50/v2-cb569290e5e842f1cdfac2846f26f341_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-cb569290e5e842f1cdfac2846f26f341_720w.jpg?source=1def8aca)
[![](https://pic1.zhimg.com/50/v2-0254569f4b7e8a46559b5414c96837bc_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-0254569f4b7e8a46559b5414c96837bc_720w.jpg?source=1def8aca)
==问题2 K均值算法的优缺点时什么?如何对其进行调优？==
==分析与解答：==
==K均值算法有一些缺点，例如受初值和离群点的影响每次的结果不稳定、结果通常不是全局最优而是局部最优解、无法很好的解决数据簇分布差别比较大的情况(比如一类时另一类样本数量的100倍)、不太适用于离散分类等。但是瑕不掩瑜，K均值聚类的优点也很明显和突出，主要体现在：对于大数据集，K均值聚类算法相对时可伸缩和高效的，它的计算复杂度时O(NKt)接近于线性，其中N是数据对象的数目，K是聚类的簇数，t是迭代的轮数。尽管算法经常以局部最优结束，但一般情况下达到的局部最优已经可以满足聚类的需求。==
==K均值算法的调优一般可以从以下几个角度出发。==
==(1)数据归一化和离群点处理。==
==K均值聚类本质上是一种基于欧式距离度量的数据划分方法，均值和方差大的维度将对数据的聚类结果产生决定性的影响，所以未做归一化处理和统一单位的数据是无法直接参与运算和比较的，同时，离群点或者少量的噪声数据就会对均值产生较大的影响，导致中心偏移，因此使用K均值聚类算法之前通常需要对数据做预处理。==
==(2)合理选择K值==
==K值得选择是K均值聚类最大得问题之一，这也是K均值聚类得主要缺点。实际上，我们希望能够找到一些可行得办法来弥补这一缺点，或者说找到K值得合理估计方法。但是，K值得选择一般基于经验和多次实验结果。例如采用手肘法，我们可以尝试不同的K值，并将不同K值对应的损失函数化成折线，横轴为K的取值，纵轴为误差平方和所定义的损失函数，如图5.3所示。==
[![](https://picx.zhimg.com/50/v2-6ffebdbde1d8d3a26bb0b4c1c9138206_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-6ffebdbde1d8d3a26bb0b4c1c9138206_720w.jpg?source=1def8aca)
[![](https://pica.zhimg.com/50/v2-a5d3bf51b4cad2c3a68895a5b1132014_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-a5d3bf51b4cad2c3a68895a5b1132014_720w.jpg?source=1def8aca)
==(3)采用核函数。==
==采用核函数是另一种可以尝试的改进方法。传统的欧式距离度量方式，使得K均值算法本质上假设了各个数据簇的数据具有一样的先验概率，并呈现球星或者高维球形分布，这种分布在实际生活中并不常见。面对非凸的数据分布形状时，可能需要引入核函数来优化，这时算法又称为核K均值算法，时核聚类方法的一种。核聚类方法的主要思想时通过一个非线性映射，将输入空间中的数据点映射到高维的特征空间中，并在新的特征空间中进行聚类。非线性映射增加了数据点线性可分的概率，从而在经典的聚类算法失效的情况下，通过引入核函数可以达到更为准确的聚类结果。==
==问题3 针对K均值算法的缺点，有哪些改进的模型？==
==分析与解答：==
==K均值算法的主要缺点如下。==
==(1)需要人工预先确定初始K值，且该值和真实的数据分布未必吻合。==
==(2)K均值只能收敛到局部最优，效果受到初始值很大。==
==(3)易受到噪声的影响。==
==(4) 样本点只能被划分到单一的类中。==
[![](https://pic1.zhimg.com/50/v2-3612830e4def804cf68be1f1ad17f022_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-3612830e4def804cf68be1f1ad17f022_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-a290ab7b193a21398d8bfefd18907823_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-a290ab7b193a21398d8bfefd18907823_720w.jpg?source=1def8aca)
## ==2. 高斯混合模型==
==高斯混合模型，即用多个高斯分布函数的线性组合来对数据分布进行拟合。理论上，高斯混合模型可以拟合出任意类型的分布。==
==知识点：高斯分布、高斯混合模型、EM算法==
==问题 高斯混合模型的核心思想时什么？它时如果迭代计算的？==
==分析与解答：==
==说起高斯分布，大家都不陌生，通常身高、分数等都大致符合高斯分布。因此，当我们研究各类数据时，假设同一类的数据符合高斯分布，也很简单自然的假设；当数据事实上由多个类，或者我们希望将数据划分为一些簇时，可以假设不同簇中的样本各自服从不同的高斯分布，由此得到的聚类算法称为高斯混合模型。==
==高斯混合模型的核心思想是，假设数据可以看作多个高斯分布中生成出来的。在该假设下，每个单独的分模型都是标准高斯模型，其均值\mu_i和方差 \Sigma_i 时待估计的参数。此外，每个分模型都还有一个参数 \pi_i ，可以理解为权重或生成数据的概率。高斯混合模型的公式为==
[![](https://picx.zhimg.com/50/v2-5c8fbaaaa257efdd6cda8aa9b1a356df_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-5c8fbaaaa257efdd6cda8aa9b1a356df_720w.jpg?source=1def8aca)
==高斯混合模型是一个生成式模型。可以这样理解数据的生成过程，假设一个最简单的情况，即只有两个一维标准高斯分布的分模型N(0,1)和N(5,1)，其权重分布为0.7和0.3.那么，在生成第一个数据点时，先按照权重的比例，随机选择一个分布，比如选择第一个高斯分布，接着从N(0,1)中生成一个点，如-0.5，便是第一个数据点。在生成第二个数据点时，随机选择到第二个高斯分布N(5,1),生成了第二个点4.7。如此循环执行，便生成出了所有的数据点。==
==然而，通常我们并不能直接得到高斯混合模型的参数，而是观察到了一系列数据点，给出一个类别的数量K后，希望求得最佳的K个高斯分模型。因此，高斯混合模型的计算，便成了最佳的均值 \mu ，方差 \Sigma ，权重Π的寻找，这类问题通常通过最大似然估计来求解。遗憾的是，此问题中直接使用最大似然估计，得到的是一个复杂的非凸函数，目标函数是和的对数，难以展开和对其求偏导。==
==在这种情况下，可以用EM算法框架来求解优化问题。EM算法时在最大化目标函数时，先固定一个变量使整体函数变为凸优化函数，求导得到最值，然后利用最优参数更新被固定的变量，进入下一个循环。具体到高斯混合模型的求解，EM算法的迭代过程如下。==
==首先，初始随机选择各参数的值。然后，重复下述两步，直到收敛。==
==(1)E步骤。根据当前的参数，计算每个点由某个分模型生成的概率。==
==(2)M步骤。使用E步骤估计出的概率，来改进每个分模型的均值，方差和权重。==
==也就是说，我们并不知道最佳的K个高斯分布的各自3个参数，也不知道每个数据点究竟时哪个高斯分布生成的。所以每次循环时，先固定当前的高斯分布不变，获得每个数据点由各个高斯分布生成的概率。然后固定该生成概率不变，根据数据点和生成概率，获得一个组更佳的高斯分布。循环往复，直到参数的不再变化，或者变化非常小时，便得到了比较合理的一组高斯分布。==
==高斯混合模型与K均值算法的相同点是，它们都是可用于聚类的算法；都需要指定K值；都是使用EM算法来求解；都往往只能收敛于局部最优。而它相比于K均值算法的优点是，可以给出一个样本属于某类的概率是多少；不仅仅可以用于聚类，还可以用于概率密度的估计；并且可以用于生成新的样本点。==
## ==3. 自组织映射神经网络==
==自组织映射神经网络(Self-Organizing Map, SOM)是无监督学习方法中一类重要方法，可以用作聚类、高维可视化、数据压缩、特征提取等多种用途。在深度神经网络大为流行的今天，谈及自组织映射神经网络依然是一件非常有意义的事情，这主要是由于自组织映射神经网络融入了大量人脑神经元的信号处理机制，有着独特的结构特点。==
==知识点：自组织映射神经网络==
==问题1 自组织映射神经网络是如何工作的？它与K均值算法有何区别？==
==分析与解答：==
==生物学研究表明，在人脑的感知通道上，神经元组织是有序排列的；同时，大脑皮层会对外界特定时空信息的输入在特定区域产生兴奋，而且相类似的外界信息输入产生对应兴奋的大脑皮层区域也连续映像的。例如，生物视网膜中有许多特定的细胞对特定的图形比较敏感，当视网膜中有若干个接收单元同时受特定模式刺激时，就使大脑皮层中的特定神经元开始兴奋，且输入模式接近时与之对应的兴奋神经元也接近；在听觉通道上，神经元在结构排列上与频率的关系十分密切，对于某个频率，特定的神经元具有最大的响应，位置相邻的神经元具有相近的频率特征，而远离的神经元具有的频率特征差别也较大。大脑皮层中神经元的这种响应特点不是先天安排好的，而是通过后天的学习自组织形成的。==
==在生物神经系统中，还存在着一种侧抑制现象，即一个神经细胞兴奋后，会对周围其他神经细胞产生抑制作用。这种抑制作用会使神经细胞之间出现竞争，其结果是某些获胜，而另一些则失败。表现形式是获胜神经细胞姓冯，失败神经细胞抑制。自组织神经网络就是对上述生物神经系统功能的一种人工神经网络模拟。==
==自组织映射神经网络本质上是一个两层的神经网络，包含输入层和输出层(竞争层)。输入层模拟感知外界输入信息的视网膜，输出层模拟作出响应的大脑皮层。输出层中神经元的个数通常是聚类的个数，代表每一个需要聚成的类。训练时采用"竞争学习"的方式，每个输入的样例在输出层中找到一个和它最匹配的节点，称为激活节点，也叫winning neuron；紧接着用随机梯度下降法更新激活节点的参数；同时，和激活节点临近的点也根据它们距离激活节点的远近而适当的更新参数。这种竞争可以通过神经元之间的横向抑制连接(负反馈路径)来实现。自组织映射神经网络的输出层节点是有拓扑关系的。这个拓扑关系依据需求确定，如果想要一维的模型，那么隐藏节点可以是"一维线阵"；如果想要二维的拓扑关系，那么就形成一个"二维平面阵"，如图5.8所示。也有更高维度的拓扑关系的，比如"三维栅格阵"，但并不常见。==
[![](https://pica.zhimg.com/50/v2-61cbce8e51cebe808b69ce7ee98f43a6_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-61cbce8e51cebe808b69ce7ee98f43a6_720w.jpg?source=1def8aca)
[![](https://pica.zhimg.com/50/v2-d3b3f60733fffe8a364872a0c9223f89_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-d3b3f60733fffe8a364872a0c9223f89_720w.jpg?source=1def8aca)
[![](https://pic1.zhimg.com/50/v2-992a2b862eea670b5a22dd7aa8c7d583_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-992a2b862eea670b5a22dd7aa8c7d583_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-9a69187b1bba344c07a777e10aff81db_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-9a69187b1bba344c07a777e10aff81db_720w.jpg?source=1def8aca)
==问题2 怎么设计自组织映射神经网络并设定网络训练参数？==
==分析与解答：==
- ==设定输出层神经元的数量==
==输出层神经元的数量和训练集样本的类别数相关。若不清楚类别数，则尽可能设定较多的节点数，以便较好地映射样本的拓扑结构，如果分类过细再酌情减少输出节点。这样可能会带来少量从未更新过权值的"死节点"，但一般可通过重新初始化权值来解决。==
- ==设计输出层节点的排列==
==输出层的节点排列成哪种形式取决于实际应用的需要，排列形式应尽量直观地反映出实际问题的物理意义。例如，对于一般的分类问题，一个输出节点能代表一个模式类，用一维线阵既结构简单又意义明确；对于颜色空间或者旅行路径类的问题，二维平面则比较直观。==
- ==初始化权值==
==可以随机初始化，但尽量使权值的初始位置与输入样本的大概分布区域充分重合，避免出现大量的初始"死结点"。一种简单易行的方法是从训练集中随机抽取m个输入样本作为初始权值、==
- ==设计拓扑领域==
==拓扑领域的设计原则是使领域不断缩小，这样输出平面上相邻神经元对应的权向量之间既有区别又有相当的相似性，从而保证当获胜节点对某一类模式产生最大响应时，其领域节点也能产生较大响应。领域的形状可以是正方形、六边形或者菱形。优势领域的大小用领域的半径表示，通常凭借经验来选择。==
- ==设计学习率==
==学习率是一个递减的函数，可以结合拓扑领域的更新一起考虑，也可分开考虑。再训练开始时，学习率可以选取较大的值，之后以较快的速度下降，这样有利于很快的捕捉到输入向量的大致结构，然后学习率再较小的值上缓降至0值，这样可以精细的调整权值使之符合输入空间的样本分布结构。==
## ==4. 聚类算法的评估==
==知识点：数据簇，聚类算法评估指标==
==问题 以聚类问题为例，假设没有外部标签数据，如何评估两个聚类算法的优劣？==
==分析与解答：==
[![](https://pic1.zhimg.com/50/v2-cbfec6c7ffb43faaf055c32c61725a92_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-cbfec6c7ffb43faaf055c32c61725a92_720w.jpg?source=1def8aca)
[![](https://pic1.zhimg.com/50/v2-6f90f96ab66d6a75651a82d1ff40e7ca_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-6f90f96ab66d6a75651a82d1ff40e7ca_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-b5345b11ce089b22a1ca93a8aafca0cb_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-b5345b11ce089b22a1ca93a8aafca0cb_720w.jpg?source=1def8aca)
[![](https://pic1.zhimg.com/50/v2-03c2e7e1871df0236a60b6c7cbc7eb74_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-03c2e7e1871df0236a60b6c7cbc7eb74_720w.jpg?source=1def8aca)
[![](https://pica.zhimg.com/50/v2-0d34b4191cab558d6af2d9a0f693100d_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-0d34b4191cab558d6af2d9a0f693100d_720w.jpg?source=1def8aca)
[![](https://pic1.zhimg.com/50/v2-a4b2a8407502fcbffea3cae150224afd_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-a4b2a8407502fcbffea3cae150224afd_720w.jpg?source=1def8aca)
[![](https://pic1.zhimg.com/50/v2-0490da85ba623fad6bed175697c46253_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-0490da85ba623fad6bed175697c46253_720w.jpg?source=1def8aca)
[![](https://pica.zhimg.com/50/v2-156eb3365515f3eca68082286fb02408_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-156eb3365515f3eca68082286fb02408_720w.jpg?source=1def8aca)
[![](https://pica.zhimg.com/50/v2-46ed18cc11c1df029921a05bca0cb2b6_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-46ed18cc11c1df029921a05bca0cb2b6_720w.jpg?source=1def8aca)
[![](https://pica.zhimg.com/50/v2-2a7841b80f678645a4679e44d7d46f03_720w.jpg?source=1def8aca)](https://pica.zhimg.com/50/v2-2a7841b80f678645a4679e44d7d46f03_720w.jpg?source=1def8aca)
[![](https://pic1.zhimg.com/50/v2-0743b11414ad678c2fa324a69fe01f29_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-0743b11414ad678c2fa324a69fe01f29_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-76baa343726d0a061089ef998dce59d0_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-76baa343726d0a061089ef998dce59d0_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-b7fa608db87edcf97a0724ba078fb253_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-b7fa608db87edcf97a0724ba078fb253_720w.jpg?source=1def8aca)