---
Updated: 2023-03-02T22:41
tags:
  - AI->-Programming
Created: 2023-03-01T19:15
---
`**Dashy**` 是一个开源的自托管的导航页配置服务，具有易于使用的可视化编辑器、状态检查、小工具和主题等功能。你可以将自己常用的一些网站聚合起来放在一起，形成自己的导航页。
![[Notion/AI/定制属于自己的超级帅气的导航页 - Dashy/attachments/640|640]]
### 特性
- 📃 支持多个页面
- 🚦 实时监控每个应用程序/链接的状态
- 📊 使用 widget 显示自托管服务中的信息和动态内容
- 🔎 按名称、域或标签的即时搜索+可自定义的快捷键
- 🎨 许多内置颜色主题，具有 UI 颜色编辑器和自定义 CSS 支持
- 🧸 多个图标、图片、表情选择
- 💂 具有多用户访问、可配置权限和 SSO 支持的身份验证
- 🌎 多语言支持
- ☁ 可选加密的，免费的离线云备份和恢复功能
- 💼 workspace 视图，可以轻松地同时在多个应用程序之间切换
- 🛩️ 最小视图，用作快速加载浏览器的起始页
- 📏 可自定义的布局、大小、文本、组件可见性、排序、动作等
- 🖼️ 全屏背景图像、自定义导航栏链接、 HTML 页脚、标题等
- ⚙️ 基于 YAML 的单文件配置，以及通过 UI 配置应用程序的选项
- 🤏 响应式 UI、PWA，可实现基本的脱机访问
### 安装
运行Dashy的最简单的方法是使用Docker，使用下面的命令即可安装启动了：
```Plain
docker run -d \
  -p 8080:80 \
  -v ~/my-conf.yml:/app/public/conf.yml \
  --name my-dashboard \
  --restart=always \
  lissy93/dashy:latest
```
如果你喜欢使用 docker-compose，也可以使用下面的 yaml 来启动：
```Plain
---
version: "3.8"
services:
  dashy:
    # To build from source, replace 'image: lissy93/dashy' with 'build: .'
    # build: .
    image: lissy93/dashy
    container_name: Dashy
    # Pass in your config file below, by specifying the path on your host machine
    # volumes:
      # - /root/my-config.yml:/app/public/conf.yml
    ports:
      - 4000:80
    # Set any environmental variables
    environment:
      - NODE_ENV=production
    # Specify your user ID and group ID. You can find this by running `id -u` and `id -g`
    #  - UID=1000
    #  - GID=1000
    # Specify restart policy
    restart: unless-stopped
    # Configure healthchecks
    healthcheck:
      test: ['CMD', 'node', '/app/services/healthcheck']
      interval: 1m30s
      timeout: 10s
      retries: 3
      start_period: 40s
```
Dashy的配置使用YAML文件，保存在 `/public/conf.yml` 中，配置文件格式非常简单，有3个主要的属性：
- **pageInfo** - 仪表板元数据，如标题、描述、导航栏链接和页脚文本
- **appConfig** - 仪表盘设置，像主题、认证、语言等
- **sections** - 每个模块包括一个 item 配置
如下所示：
```Plain
pageInfo:
  title: Home Lab
sections: # An array of sections
- name: Example Section
  icon: far fa-rocket
  items:
  - title: GitHub
    description: Dashy source code and docs
    icon: fab fa-github
    url: https://github.com/Lissy93/dashy
  - title: Issues
    description: View open issues, or raise a new one
    icon: fas fa-bug
    url: https://github.com/Lissy93/dashy/issues
- name: Local Services
  items:
  - title: Firewall
    icon: favicon
    url: http://***********/
  - title: Game Server
    icon: https://i.ibb.co/710B3Yc/space-invader-x256.png
    url: http://*************/
```
可以使用 Docker volume将配置文件从主机系统挂载到容器中去，例如 `-v ./host-system/my-local-conf.yml:/app/public/conf.yml`，也可以直接通过 UI 编辑你的配置，更改将保存在此文件中。使用 `docker exec -it [container-id] yarn validate-config` 命令检查你的配置。编辑配置后，应用程序将在后台重建，这可能需要一分钟。
### 示例
可以直接查看官方示例 https://demo.dashy.to/、https://live.dashy.to/ 了解更多信息。
Git仓库：https://github.com/lissy93/dashy
ashy