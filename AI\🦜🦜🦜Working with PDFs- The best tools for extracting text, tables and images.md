---
Updated: 2024-05-30T22:24
tags:
  - AI/Programming
  - AI/RAG
URL: https://medium.com/gitconnected/working-with-pdfs-the-best-tools-for-extracting-text-tables-and-images-2e07a540c5cc
Created: 2024-05-20T08:23
---
  
/opt/workspace/researcher/pdfTests.py
```Python
from anthropic import Anthropic
from pypdf import PdfReader
import PyPDF2
import os
import fitz
import base64
from PIL import Image
import io

os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
def pdf_to_base64_pngs(pdf_path, page_num, quality=75, max_size=(1024, 1024)):
    # Open the PDF file
    doc = fitz.open(pdf_path)
    
    # Load the page
    page = doc.load_page(page_num)
    
    # Render the page as a PNG image
    pix = page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))
    
    # Convert the pixmap to a PIL Image
    image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
    
    # Resize the image if it exceeds the maximum size
    if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
    
    # Save the image to the specified folder
    image_path = f"./pdfs/imapages/{os.path.basename(pdf_path)}_{page_num}.png"
    image.save(image_path, format='PNG', optimize=True, quality=quality)
    
    # Convert the PIL Image to base64-encoded PNG
    image_data = io.BytesIO()
    image.save(image_data, format='PNG', optimize=True, quality=quality)
    image_data.seek(0)
    base64_encoded = base64.b64encode(image_data.getvalue()).decode('utf-8')
    
    # Close the PDF document
    doc.close()
    
    return base64_encoded, page_num
	
# Set up the Anthropic API client
client = Anthropic()
MODEL_NAME = "claude-3-haiku-20240229"
haiku_prompt = """
You will be extracting information from images in a PDF file and converting it into a well-organized and coherent JSON format. 
Finally, double-check your work for accuracy and completeness. Make sure that you have captured all the important information 
from each image and that the text accurately reflects the content of the tables and graphs. When extracting table information, 
please ensure that the output JSON includes the relationship of nested subtables. . 
Please provide your final output inside a code block and in the language inside the image. Dont output explanation or commnets. 
Output only the JSON format."""
def extract_info(pdf_path, page_num, haiku_prompt):
    base64_encoded_png, page_num = pdf_to_base64_pngs(pdf_path, page_num)
    
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "image", "source": {"type": "base64", "media_type": "image/png", "data": base64_encoded_png}},
                {"type": "text", "text": haiku_prompt}
            ]
        }
    ]
    
    response = client.messages.create(
        model="claude-3-haiku-20240307",
        max_tokens=4096,
        messages=messages
    )
    
    return response.content[0].text, page_num
def extract_text_from_pdf(pdf_path):
    with open(pdf_path, 'rb') as pdf_file:
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        text = ''
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text += page.extract_text()
    return text
def process_pdf(pdf_path):
    pdf_reader = PdfReader(pdf_path)
    extracted_info = ''
    
    for page_num in range(len(pdf_reader.pages)):
        page = pdf_reader.pages[page_num]
        if '/XObject' in page.get('/Resources', {}):
            info, _ = extract_info(pdf_path, page_num, haiku_prompt)
            extracted_info += f"Page {page_num + 1}:\n{info}\n\n"
            # Save the extracted text from the image page to a separate file
            output_file = f"./pdfs/extracted_text/{os.path.splitext(os.path.basename(pdf_path))[0]}_page{page_num + 1}.txt"
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, "w", encoding="utf-8") as file:
                file.write(info)
            
        else:
            text = page.extract_text()
            extracted_info += f"Page {page_num + 1}:\n{text}\n\n"
    
    # Extract the filename from the PDF path
    pdf_filename = os.path.basename(pdf_path)
    
    # Create the output text file path
    output_file = f"./pdfs/extracted_text/{pdf_filename}.txt"
    
    # Create the directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Save the extracted text to the file
    with open(output_file, "w", encoding="utf-8") as file:
        file.write(extracted_info)
    
    print(f"Extracted text saved to: {output_file}")

# Example usage
pdf_path = "./pdfs/Rakuten202312.pdf"
process_pdf(pdf_path)
```
  
  
[![](https://miro.medium.com/v2/resize:fill:88:88/1*SLWOQhKJtklMc8hhszErhQ.jpeg)](https://miro.medium.com/v2/resize:fill:88:88/1*SLWOQhKJtklMc8hhszErhQ.jpeg)
[![](https://miro.medium.com/v2/resize:fill:48:48/1*5D9oYBd58pyjMkV_5-zXXQ.jpeg)](https://miro.medium.com/v2/resize:fill:48:48/1*5D9oYBd58pyjMkV_5-zXXQ.jpeg)
[==Lan Chu==](https://huonglanchu.medium.com/?source=post_page-----2e07a540c5cc--------------------------------)
==Published in==
[==Level Up Coding==](https://levelup.gitconnected.com/?source=post_page-----2e07a540c5cc--------------------------------)
==9 min read==
==May 1, 2024==
==Lots of information comes from text data, for example in PDF documents. Handling PDFs can be particularly challenging, especially with tables and images.==
[![](https://miro.medium.com/v2/resize:fit:700/1*ssdBAZ9Sz7Zth5Rdx46Rjw.jpeg)](https://miro.medium.com/v2/resize:fit:700/1*ssdBAZ9Sz7Zth5Rdx46Rjw.jpeg)
==Photo by== [==Jonathan Simcoe==](https://unsplash.com/@jdsimcoe?utm_content=creditCopyText&utm_medium=referral&utm_source=unsplash) ==on== [==Unsplash==](https://unsplash.com/photos/light-bulb-on-pile-of-books-qYxIVsHpDDo?utm_content=creditCopyText&utm_medium=referral&utm_source=unsplash)
==If you work with single modality language model, then you probably already know that it doesn’t have the ability to directly interpret or “read” documents. It is only capable of handling one type of input, such as text-only or image-only. If you need to analyse images or infographics from PDFs, for downstream tasks e.g question-answering, you would typically use specialised packages for parsing the document. These tools can convert the document, the images and tables found in the document into text formats that can be understood and analysed by the model .==
==There are several good tools available for parsing PDF documents for your downstream tasks. In this article, we will go through a list of some good ones including PyPDF, Adobe PDF Services API, Camelot and Tabula.==
==First let’s install the relevant libraries:==
==!pip install pdfservices-sdk====  
  
====!pip install openpyxl====  
  
====!pip install camelot-py====  
  
====!pip install opencv-python====  
  
====!pip install tabula-py==2.9.0====  
  
====!pip install jpype1====  
  
====!pip install langchain====  
  
====!pip install langchain-core==0.1.40==
# ==Extracting text, tables and images with PyPDF==
==Pypdf is a versatile and common library for parsing PDF documents. It can parse the documents including tables from the documents into plain text. Most of the time, the format of the table is also well preserved when using PyPDF to parse the document.==
[![](https://miro.medium.com/v2/resize:fit:406/1*NDaDdSkT_d5G6oJupdGYBQ.png)](https://miro.medium.com/v2/resize:fit:406/1*NDaDdSkT_d5G6oJupdGYBQ.png)
==Image by author==
## ==Parsing text and tables==
==Langchain document_loaders incorporate many different packages for reading various file formats including PyPDF. The following script processes the document using PyPDF and saves it in a dataframe format:==
==from langchain_community.document_loaders import PyPDFLoader==
==def extract_text_from_file(df, file_path):====  
  
====file_name = file_path.split("/")[-1]====  
  
====file_type = file_name.split(".")[-1]====  
  
====if file_type == "pdf":====  
  
====loader = PyPDFLoader(file_path)====  
  
====else:====  
  
====return df==
```plain
text = ""  
pages = loader.load\_and\_split()  
for page in pages:  
    text += page.page\_content  
  
new\_row = pd.DataFrame({"file": \[file\_name\], "text": \[text\]})  
df = pd.concat(\[df, new\_row\], ignore\_index=True)
    return df
```
==folder_path = '../data/raw'====  
  
====pathlist = Path(folder_path).glob('*.pdf')====  
  
====filenames = []====  
  
====for file_path in pathlist:====  
  
====filename = os.path.basename(file_path)====  
  
====filenames.append(filename)==
==df = pd.DataFrame()====  
  
====for filename in filenames:====  
  
====file_path = folder_path + "/" + filename====  
  
====file_name = os.path.basename(file_path)====  
  
====print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} process {file_name}")==
```plain
df\_file = pd.DataFrame(columns=\["file", "text"\])  
print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} extract text")  
try:  
    df\_file = extract\_text\_from\_file(df\_file, file\_path)  
except Exception as e:  
    print("----Error: cannot extract text")  
    print(f"----error: {e}")  
df = pd.concat(\[df, df\_file\])
df
```
[![](https://miro.medium.com/v2/resize:fit:700/1*pc14C1pGmxt8Vyjs6uOn-w.png)](https://miro.medium.com/v2/resize:fit:700/1*pc14C1pGmxt8Vyjs6uOn-w.png)
==You could also process each page separately, for example, in case you want to do a downstream question-answering task on each chunk/page. In that case, you can modify the script as following:==
==def extract_text_from_file(df, file_path):====  
  
====file_name = file_path.split("/")[-1]====  
  
====file_type = file_name.split(".")[-1]====  
  
====if file_type == "pdf":====  
  
====loader = PyPDFLoader(file_path)====  
  
====elif file_type == "docx":====  
  
====loader = Docx2txtLoader(file_path)====  
  
====else:====  
  
====return df==
```plain
pages = loader.load\_and\_split()  
for page\_number, page in enumerate(pages, start=1):  
      
    new\_row = pd.DataFrame({  
        "file": \[file\_name\],  
        "page\_number": \[page\_number\],  
        "text": \[page.page\_content\]  
    })  
    df = pd.concat(\[df, new\_row\], ignore\_index=True)
    return df
```
[![](https://miro.medium.com/v2/resize:fit:700/1*q7hcut5ApQXSxPMrkZ1abQ.png)](https://miro.medium.com/v2/resize:fit:700/1*q7hcut5ApQXSxPMrkZ1abQ.png)
## ==Extracting images==
==Every page of a PDF document can contain an arbitrary amount of images. Do you know that you could also extract all the images from the document using PyPDF?==
==The following block of codes extracts all the images from a pdf file and create a new folder to store the extracted images:==
==from pypdf import PdfReader====  
  
====import os====  
  
====output_directory = '../data/processed/images/image_pypdf'====  
  
====if not os.path.exists(output_directory):====  
  
====os.mkdir(output_directory)====  
  
====reader = PdfReader("../data/raw/GPTsareGPTs.pdf")====  
  
====for page in reader.pages:====  
  
====for image in page.images:====  
  
====with open(os.path.join(ouput_directory,image.name), "wb") as fp:====  
  
====fp.write(image.data)==
==In the folder, you will find all the images in the PDF:==
[![](https://miro.medium.com/v2/resize:fit:700/1*WU23TiMDrB21ptIC-_X6FA.png)](https://miro.medium.com/v2/resize:fit:700/1*WU23TiMDrB21ptIC-_X6FA.png)
==List of all images from the PDF extracted by PyPDF. Image by author==
# ==Extract text, tables and images with Adobe PDF Services API==
[==The PDF Extract API (included with the PDF Services API)==](https://developer.adobe.com/document-services/docs/overview/pdf-extract-api/howtos/extract-api/) ==provides cloud-based capabilities for automatically extracting contents from PDF.==
[![](https://miro.medium.com/v2/resize:fit:700/0*Dm9IcIAkWAc9XiNO.png)](https://miro.medium.com/v2/resize:fit:700/0*Dm9IcIAkWAc9XiNO.png)
==Source:== [==PDF Extract API | Adobe PDF Services==](https://developer.adobe.com/document-services/docs/overview/pdf-extract-api/)
==The PDF Services APIs require an access_token to authorize the request. In order to use the access token, you need to== [==create one==](https://acrobatservices.adobe.com/dc-integration-creation-app-cdn/main.html?api=pdf-services-api)==. Once you have received your developer credential which includes client_id and client_secret in a json format, you can use it for processing your PDF. Let’st first import the relevant libraries:==
==from adobe.pdfservices.operation.auth.credentials import Credentials====  
  
====from adobe.pdfservices.operation.exception.exceptions import ServiceApiException, ServiceUsageException, SdkException====  
  
====from adobe.pdfservices.operation.execution_context import ExecutionContext====  
  
====from adobe.pdfservices.operation.io.file_ref import FileRef====  
  
====from adobe.pdfservices.operation.pdfops.extract_pdf_operation import ExtractPDFOperation====  
  
====from adobe.pdfservices.operation.pdfops.options.extractpdf.extract_pdf_options import ExtractPDFOptions====  
  
====from adobe.pdfservices.operation.pdfops.options.extractpdf.extract_element_type import ExtractElementType====  
  
====from adobe.pdfservices.operation.pdfops.options.extractpdf.extract_renditions_element_type import \====  
  
====ExtractRenditionsElementType====  
  
====import os.path====  
  
====import zipfile====  
  
====import json====  
  
====import pandas as pd====  
  
====import re====  
  
====import openpyxl====  
  
====from datetime import datetime==
==The following script sets up Adobe PDF Services API with necessary credentials and processes the PDF file and saves the result in a zip file:==
==def adobeLoader(input_pdf, output_zip_path,client_id, client_secret):====  
  
===="""====  
  
====Function to run adobe API and create output zip file====  
  
===="""==
```plain
credentials = Credentials.service\_principal\_credentials\_builder() \\  
    .with\_client\_id(client\_id) \\  
    .with\_client\_secret(client\_secret) \\  
    .build()
  
execution\_context = ExecutionContext.create(credentials)  
extract\_pdf\_operation = ExtractPDFOperation.create\_new()
  
source = FileRef.create\_from\_local\_file(input\_pdf)  
extract\_pdf\_operation.set\_input(source)
  
extract\_pdf\_options: ExtractPDFOptions = ExtractPDFOptions.builder() \\  
    .with\_elements\_to\_extract(\[ExtractElementType.TEXT, ExtractElementType.TABLES\]) \\  
    .with\_elements\_to\_extract\_renditions(\[ExtractRenditionsElementType.TABLES,  
                                            ExtractRenditionsElementType.FIGURES\]) \\  
    .build()  
extract\_pdf\_operation.set\_options(extract\_pdf\_options)
  
result: FileRef = extract\_pdf\_operation.execute(execution\_context)
  
if os.path.exists(output\_zip\_path):  
    os.remove(output\_zip\_path)  
result.save\_as(output\_zip\_path)
```
==The output of this== ==`adobeLoader`== ==operation is a sdk.zip package containing the following:==
- ==The structuredData.json file==
- ==Text is stored in the json file and is extracted in contextual blocks — paragraphs, headings, lists, footnotes.==
- ==“table” folder: Table data is delivered within the resulting JSON and also output in CSV and XLSX files. Tables are also output as PNG images allowing the table data to be visually validated.==
- ==“figures” folder: Objects that are identified as figures or images are extracted as PNG files.==
[![](https://miro.medium.com/v2/resize:fit:450/1*faLcnxsI_4y3q1eSCLH6cA.png)](https://miro.medium.com/v2/resize:fit:450/1*faLcnxsI_4y3q1eSCLH6cA.png)
==Structure of output folder==
==Now you can apply the function on your document:==
==input_pdf = 'data/raw/GPTsareGPTs.pdf'====  
  
====output_zip_path = 'data/processed/adobe_result/sdk.zip'====  
  
====output_zipextract_folder = 'data/processed/adobe_result/'==
==adobeLoader(input_pdf, output_zip_path)==
==You can see that the “figures” folder returns all the images in my PDF document in .png format. The tables folder returns Excel sheets for tables which ensures high fidelity and accuracy together with the .png images for visual comparison purposes:==
[![](https://miro.medium.com/v2/resize:fit:640/1*bFlQdD8iUx5CsEdRBQ7Czg.gif)](https://miro.medium.com/v2/resize:fit:640/1*bFlQdD8iUx5CsEdRBQ7Czg.gif)
==You could also further process the structured JSON file== ==`structuredData.json`====to gather text and tables and organise this data into a pandas DataFrame for further downstream task:==
[![](https://miro.medium.com/v2/resize:fit:700/1*D9KTQMzRYrTpqdTe4eFIJg.png)](https://miro.medium.com/v2/resize:fit:700/1*D9KTQMzRYrTpqdTe4eFIJg.png)
# ==Extracting tables with Camelot and Tabular==
==Tabula and Camelot are two Python library designed specifically for extracting tables from PDFs.==
==The following script processes the PDF document using either Tabula or Camelot, converting each table in the document into a JSON format, capturing both the actual table data and metadata such as table numbers and page numbers:==
==def extract_tables(file_path, pages="all", package="tabula"):====  
  
====if package == "camelot":==
```plain
    tables = camelot.read\_pdf(file\_path, pages=pages, flavor="stream")  
else:  
    tables = tabula.read\_pdf(file\_path, pages=pages, stream=True, silent=True)
  
tables\_json = \[\]  
for idx, table in enumerate(tables):
    if package == "camelot":  
        page\_number = table.parsing\_report\["page"\]  
        data = table.df.to\_json(orient="records")  
    else:  
        page\_number = ""  
        data = table.to\_json(orient="records")
    data = {  
        "table\_number": idx,  
        "page\_number": page\_number,  
        "data": data,  
    }  
    tables\_json.append(data)  
return tables\_json
```
==Great! Now that we have the script to process the table, we can apply the function for the same Pdf document:==
==file_path = '../data/raw/GPTsareGPTs.pdf'====  
  
====file_name = os.path.basename(file_path)====  
  
====df_file = pd.DataFrame()====  
  
====print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} process {file_name}")==
==print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} extract table")==
==all_tables = []====  
  
====for package in ["camelot", "tabula"]:====  
  
====print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} extract table with {package}")====  
  
====try:====  
  
====tables_from_package = extract_tables(file_path, pages="all", package=package)====  
  
====for table in tables_from_package:====  
  
====all_tables.append({"table": table, "source": package})====  
  
====except Exception as e:====  
  
====print("----Error: cannot extract table")====  
  
====print(f"----error: {e}")==
==for entry in all_tables:====  
  
====print(f"Source: {entry['source']}, Table: {entry['table']}")==
==Here is an example of a table from the source PDF document:==
[![](https://miro.medium.com/v2/resize:fit:700/1*z2HGQ8AOcmj4wIgtmMIdfg.png)](https://miro.medium.com/v2/resize:fit:700/1*z2HGQ8AOcmj4wIgtmMIdfg.png)
==The output format of Camelot or Tabula operation is actually a string representation of a table, as presented in the json object below:==
[![](https://miro.medium.com/v2/resize:fit:700/1*U_Dj2eD17Vdg5GJUSwX9vw.png)](https://miro.medium.com/v2/resize:fit:700/1*U_Dj2eD17Vdg5GJUSwX9vw.png)
==Output of Tabula==
==When you slice the [‘data’] key in the json object, VS Code seems to understand that it is a table format and show a table representation of the string, which looks exactly the same as in the source table in the PDF file. Tabula seems to detect the table correctly. Awesome!==
[![](https://miro.medium.com/v2/resize:fit:700/1*xdmxJ63GJd2WzLlJv5GXvw.png)](https://miro.medium.com/v2/resize:fit:700/1*xdmxJ63GJd2WzLlJv5GXvw.png)
==Output of Tabula==
==Now, let’s look at the output of Camelot. The following shows the json object of the same table.==
[![](https://miro.medium.com/v2/resize:fit:700/1*wmrDTe7cUXAceNTF5qCgWw.png)](https://miro.medium.com/v2/resize:fit:700/1*wmrDTe7cUXAceNTF5qCgWw.png)
==Output of Camelot==
==And the table presentation of the string:==
[![](https://miro.medium.com/v2/resize:fit:700/1*MGLjk2NLaU3N1xX88UeMMg.png)](https://miro.medium.com/v2/resize:fit:700/1*MGLjk2NLaU3N1xX88UeMMg.png)
==Output of Camelot. Camelot fails to detect the border of the table. It includes the text when it is too close to the table.==
==In this example, both Tabula and Camelot were able to detect the table, however the output of Tabular is clean and mirror the original table from the PDF. Meanwhile Camelot appears to fail to detect the border of the table. It includes the text when it is too close to the table.==
==However, in another example when there are multiple tables present on a page, and there is no clear table border as in the following:==
[![](https://miro.medium.com/v2/resize:fit:700/1*XWt3fsIg1cSLfaV9t2KPsg.png)](https://miro.medium.com/v2/resize:fit:700/1*XWt3fsIg1cSLfaV9t2KPsg.png)
==Camelot successfully detects both tables, whereas Tabula fails to detect either of them:==
[![](https://miro.medium.com/v2/resize:fit:700/1*UL3c9mrBzcrVDkmhovhgDw.png)](https://miro.medium.com/v2/resize:fit:700/1*UL3c9mrBzcrVDkmhovhgDw.png)
==Output from Camelot==
[![](https://miro.medium.com/v2/resize:fit:700/1*uS9nRIhO1BEKMJ4PloEtfQ.png)](https://miro.medium.com/v2/resize:fit:700/1*uS9nRIhO1BEKMJ4PloEtfQ.png)
==Output from Camelot==
# ==Comparison of different tools==
==When thinking about which options to choose for parsing PDF documents, PyPDF is ideal for basic extraction needs where table structure is perhaps not a priority. It is completely free, making it suitable for users on a tight budget who need a simple solution with high accuracy for text and image extraction. In my experience, most of the time, format preservation of tables as text is acceptable.==
==Camelot and Tabula are are specialised for table extraction and are best suited for scenarios where table data extraction is required. They are completely free too and in my opinion would be good enough if you are okay with occasionally inaccuracies.==
==Adobe PDF Services API offers a very robust solution for businesses or applications where high accuracy in text, table, and image extraction are critical. However, there is no clear information on the API pricing.== [==Here==](https://developer.adobe.com/document-services/pricing/main/) ==it says you need to contact Sales for a quotation. On this== [==thread==](https://community.adobe.com/t5/acrobat-services-api-discussions/adobe-extract-api-is-too-expensive/m-p/14263308)==, it seems that the Adobe Extract API is rather expensive. Actually I would be willing to pay for actual usage since the quality of output extracted is premium!==
[![](https://miro.medium.com/v2/resize:fit:700/1*5VeLjjWT3JFW23s7udp9DQ.png)](https://miro.medium.com/v2/resize:fit:700/1*5VeLjjWT3JFW23s7udp9DQ.png)
# ==Conclusion==
==In this article, we learnt four different tools for parsing PDF documents and extracting text, tables and images data from PDF files: PyPDF, Camelot, Tabula, and Adobe PDF Services API.==
==Thanks for reading. I hope it would be useful for you to efficiently transform PDF content into structured and actionable data.==