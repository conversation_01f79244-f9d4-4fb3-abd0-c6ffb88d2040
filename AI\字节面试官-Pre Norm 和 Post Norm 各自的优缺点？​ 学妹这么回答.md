---
Updated: 2024-07-13T23:32
tags:
  - AI->-Theory
URL: https://mp.weixin.qq.com/s?chksm=fa6774d7cd10fdc122038a47a7c3460227464159e824c85581760787857d3ba5d7ef8c0ba0d5&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_tlfeeds&ranksessionid=1720777692_5&mid=2247485966&sn=ad8dd077c7cf981f8979df0adf4434f6&idx=1&__biz=MzUyOTA5OTcwMg%3D%3D&scene=169&subscene=200&sessionid=**********&flutter_pos=59&clicktime=**********&enterid=**********&finder_biz_enter_id=5&ascene=56&fasttmpl_type=0&fasttmpl_fullversion=7289491-zh_CN-zip&fasttmpl_flag=0&realreporttime=**********620&devicetype=android-34&version=280031c2&nettype=WIFI&lang=zh_CN&session_us=gh_87731186c98f&countrycode=JP&exportkey=n_ChQIAhIQ9nWaQ3pUVblscqLcmdYM5xLxAQIE97dBBAEAAAAAAKMQLfxrTi4AAAAOpnltbLcz9gKNyK89dVj0A9kxOwuuyp7hZjRhAh87X8wScX916HXqGQJSblEm20SEK97z0jcZjzjjY0dbp%2Fe1xV%2FHcixNHsUwNnJ%2FRpjIivFOKOgD3a%2FiFHDr4c4NVSyZSYs9MDfFSokgQMTShDjNSG6HMgky3uHlOdtoLoVCt0hTwKxFb6y27Rnjb5HX%2FYYpJ3lt1UbwnBTRIpae9ZDh82nN8lBdYsNL%2F0hAJ%2FGH%2Fc4Fim1JeNe%2F2J1cB8UvCzQ2aGKq4BFU1as2CPoz0GxZ%2BKCh6YsFde4%2FE9Q%3D&pass_ticket=L2DkBPdAhDW4IyBI6FAczhjjzpn1PgZIUltzL0T5FJ9DdXWK0OFtdWani6OcFsoQ&wx_header=3
Created: 2024-07-12T19:07
---
```Python
Post Norm 之所以这么设计，是把 Normalization 放在一个模块的最后，这样下一个模块接收到的总是归一化后的结果。这比较符合 Normalization 的初衷，就是为了降低梯度的方差。但是层层堆叠起来，从上图可以看出，深度学习的基建 ResNet 的结构其实被破坏了。
这就导致大家在训练 Transformers 的时候，发现并不是那么容易的训练，learning rate warm up, 初始化等各种招都用上，训练的时候还得小心翼翼。
下一篇文章讨论了 ResNet 中  Identity mappings 的重要性，并且以此为基础提出了 Pre Norm。
层数的增大，Post-Norm 期望的梯度会随着层数的变大而变大，而 Pre Norm 则几乎保持不变。
大的梯度加上大的学习率训练很容易就崩盘了，也是刚开始得 warm up 一下。
所以总结来看，Pre Norm 的训练更快，且更加稳定，所以之后的模型架构大多都是 Pre Norm 了，比如 GPT，MPT，Falcon，llama 等。
```
  
  
```Python
通过上面的几篇论文，汇总如下：
Pre Norm 在训练稳定和收敛性方面有明显的优势，所以大模型时代基本都无脑使用 Pre Norm 了。但是其可能有潜在的（表示塌陷） representation collapse 问题，也就是上限可能不如 Post Norm。
Post Norm 则对训练不稳定，梯度容易爆炸，学习率敏感，初始化权重敏感，收敛困难。好处是有潜在效果上的优势，到底有没有呢？也不好说，因为现在大模型训练太费钱了，Post Norm 在效果上带来的提升很可能不如多扔点数据让 Pre Norm 更快的训练出来。
```