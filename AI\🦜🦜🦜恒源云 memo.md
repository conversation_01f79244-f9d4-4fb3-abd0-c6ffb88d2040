---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-18T11:05
tags:
  - AI->-Infra
Created: 2023-08-12T19:26
---
[恒源云_GPUSHARE-恒源智享云](https://gpushare.com/center/hire)
  
```JavaScript
\#update syste
apt update
apt upgrade
Dockerコンテナ内のUbuntuではsystemctlは使えない
https://qiita.com/smiler5617/items/2a33d1aba0174480e9d3
(base) root@I14680128bf008013e1:/opt/workspace/gpt# systemctl status docker
System has not been booted with systemd as init system (PID 1). Can't operate.
Failed to connect to bus: Host is down
\#vcs
https://gpushare.com/docs/best_practices/vsco
\#jupyterlab
pip install jupyterlab
jupyter-lab
jupyter lab --generate-config
/home/<USER>/.jupyter/jupyter_lab_config.py
--search localhost -> 0.0.0.0
会在默认路径下生成一个jupyter_notebook_config.py文件。打开它，找到c.ServerApp.notebook_dir
（旧版：c.NotebookApp.notebook_dir）这一行，输入你要指定的工作目录，并把前面的“#”去掉，保存即可，
c.ServerApp.root_dir =
下次打开JupyterLab时就会是指定的工作目录了。
/home/<USER>/miniconda3/envs/mlearn/share/jupyter/lab
/home/<USER>/.local/share/jupyter/

https://gpushare.com/docs/best_practices/jupyterlab/
conda activate myenv
pip install ipykernel
ipython kernel install --user --name myenv
安装完成后在 JupyterLab 菜单中，打开 文件 - 新建启动页。可以看到多了刚创建好的虚拟环境，点击打开的Notebook 就处于虚拟环境当中。
jupyter kernelspec remove myenv
\#conda
point envs and .cache to /hy_tmp
(base) root@I14680128bf008013e1:/hy-tmp# ls
(base) root@I14680128bf008013e1:/hy-tmp# mkdir envs
(base) root@I14680128bf008013e1:/hy-tmp# mkdir cache
lrwxrwxrwx 1 root root   13 Aug 13 11:12 env -> /hy-tmp/envs/
lrwxrwxrwx 1 root root   13 Aug 13 11:12 cache -> /hy-tmp/cache
(base) root@I14680128bf008013e1:/opt/workspace# cd
lrwxrwxrwx 1 root root    18 Aug 13 11:10 envs -> /opt/workspace/env
(base) root@I14680128bf008013e1:/usr/local/miniconda3# cd
cd /root
rsync -avl .cache/ /opt/workspace/cache/
\#conda create -p /hy-tmp/myenv python=3.9
conda create -n mlearn python=3.10.12
conda activate mlearn
\#conda remove -p /hy-tmp/myenv --all
\#conda config --set auto_activate_base true
```
  
  
Initial set
```JavaScript
apt install htop
apt install git-lfs
git lfs install --skip-repo
apt install rsync
mkdir -R /opt/workspace/
git status -s | cut -c 4- | xargs git update-index --assume-unchanged
rm .git/index && git reset
git config --global lfs.largefilewarning false
git config -l
```