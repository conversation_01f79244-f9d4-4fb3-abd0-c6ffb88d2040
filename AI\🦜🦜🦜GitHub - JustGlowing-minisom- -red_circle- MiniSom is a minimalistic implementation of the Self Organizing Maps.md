---
DocFlag:
  - Reference
  - Tested
Updated: 2024-05-06T14:03
tags:
  - AI->-Competition
  - AI->-SOM
URL: https://github.com/JustGlowing/minisom
Created: 2024-05-04T23:17
---
[![](https://camo.githubusercontent.com/d11b3a0b7f86d7d146733235f41384ddd27808aeb5d4800a4a80e5e2726e87c5/68747470733a2f2f62616467652e667572792e696f2f70792f6d696e69736f6d2e737667)](https://camo.githubusercontent.com/d11b3a0b7f86d7d146733235f41384ddd27808aeb5d4800a4a80e5e2726e87c5/68747470733a2f2f62616467652e667572792e696f2f70792f6d696e69736f6d2e737667)
[![](https://camo.githubusercontent.com/593243986b32d223f25d02a737589f3454868df986c1844c497a3350128548e2/68747470733a2f2f7374617469632e706570792e746563682f706572736f6e616c697a65642d62616467652f6d696e69736f6d3f706572696f643d746f74616c26756e6974733d696e7465726e6174696f6e616c5f73797374656d266c6566745f636f6c6f723d626c61636b2672696768745f636f6c6f723d626c7565266c6566745f746578743d446f776e6c6f616473)](https://camo.githubusercontent.com/593243986b32d223f25d02a737589f3454868df986c1844c497a3350128548e2/68747470733a2f2f7374617469632e706570792e746563682f706572736f6e616c697a65642d62616467652f6d696e69736f6d3f706572696f643d746f74616c26756e6974733d696e7465726e6174696f6e616c5f73797374656d266c6566745f636f6c6f723d626c61636b2672696768745f636f6c6f723d626c7565266c6566745f746578743d446f776e6c6f616473)
[![](https://github.com/JustGlowing/minisom/actions/workflows/python-package.yml/badge.svg?branch=master&event=push)](https://github.com/JustGlowing/minisom/actions/workflows/python-package.yml/badge.svg?branch=master&event=push)
# MiniSom
[![](https://camo.githubusercontent.com/250050117c8ec9a3ea7515a3fb7b60e74274261ae492335f72c9f45866586ed2/68747470733a2f2f332e62702e626c6f6773706f742e636f6d2f2d5f3655444745487a4972732f57536669796a6d6f6552492f41414141414141414248772f335551796c63434245685566484e6866353657534842426d5136675f6c58516877434c63422f733332302f6d696e69736f6d5f6c6f676f2e706e67)](https://camo.githubusercontent.com/250050117c8ec9a3ea7515a3fb7b60e74274261ae492335f72c9f45866586ed2/68747470733a2f2f332e62702e626c6f6773706f742e636f6d2f2d5f3655444745487a4972732f57536669796a6d6f6552492f41414141414141414248772f335551796c63434245685566484e6866353657534842426d5136675f6c58516877434c63422f733332302f6d696e69736f6d5f6c6f676f2e706e67)
## Self Organizing Maps
MiniSom is a minimalistic and Numpy based implementation of the Self Organizing Maps (SOM). SOM is a type of Artificial Neural Network able to convert complex, nonlinear statistical relationships between high-dimensional data items into simple geometric relationships on a low-dimensional display. Minisom is designed to allow researchers to easily build on top of it and to give students the ability to quickly grasp its details.
The project initially aimed for a minimalistic implementation of the Self-Organizing Map (SOM) algorithm, focusing on simplicity in features, dependencies, and code style. Although it has expanded in terms of features, it remains minimalistic by relying only on the numpy library and emphasizing vectorization in coding style.
Updates about MiniSom are posted on [Twitter](https://twitter.com/JustGlowing).
Jump into using MiniSom via Google Colab:
[![](https://camo.githubusercontent.com/f5e0d0538a9c2972b5d413e0ace04cecd8efd828d133133933dfffec282a4e1b/68747470733a2f2f636f6c61622e72657365617263682e676f6f676c652e636f6d2f6173736574732f636f6c61622d62616467652e737667)](https://camo.githubusercontent.com/f5e0d0538a9c2972b5d413e0ace04cecd8efd828d133133933dfffec282a4e1b/68747470733a2f2f636f6c61622e72657365617263682e676f6f676c652e636f6d2f6173736574732f636f6c61622d62616467652e737667)
## Installation
Just use pip:
```Plain
pip install minisom
```
or download MiniSom to a directory of your choice and use the setup script:
```Plain
git clone https://github.com/JustGlowing/minisom.git
python setup.py install
```
## How to use it
In order to use MiniSom you need your data organized as a Numpy matrix where each row corresponds to an observation or as list of lists like the following:
```Plain
data = [[ 0.80,  0.55,  0.22,  0.03],
        [ 0.82,  0.50,  0.23,  0.03],
        [ 0.80,  0.54,  0.22,  0.03],
        [ 0.80,  0.53,  0.26,  0.03],
        [ 0.79,  0.56,  0.22,  0.03],
        [ 0.75,  0.60,  0.25,  0.03],
        [ 0.77,  0.59,  0.22,  0.03]]
```
Then you can train MiniSom just as follows:
```Plain
from minisom import MiniSom
som = MiniSom(6, 6, 4, sigma=0.3, learning_rate=0.5) # initialization of 6x6 SOM
som.train(data, 100) # trains the SOM with 100 iterations
```
You can obtain the position of the winning neuron on the map for a given sample as follows:
```Plain
som.winner(data[0])
```
For an overview of all the features implemented in minisom you can browse the following examples: [https://github.com/JustGlowing/minisom/tree/master/examples](https://github.com/JustGlowing/minisom/tree/master/examples)
### Export a SOM and load it again
A model can be saved using pickle as follows
```Plain
import pickle
som = MiniSom(7, 7, 4)
# ...train the som here
# saving the som in the file som.p
with open('som.p', 'wb') as outfile:
    pickle.dump(som, outfile)
```
and can be loaded as follows
```Plain
with open('som.p', 'rb') as infile:
    som = pickle.load(infile)
```
Note that if a lambda function is used to define the decay factor MiniSom will not be pickable anymore.
## Explore parameters
You can use this dashboard to explore the effect of the parameters on a sample dataset: [https://share.streamlit.io/justglowing/minisom/dashboard/dashboard.py](https://share.streamlit.io/justglowing/minisom/dashboard/dashboard.py)
## Examples
Here are some of the charts you'll see how to generate in the [examples](https://github.com/JustGlowing/minisom/tree/master/examples):
|Seeds map|Class assignment|Files|
|---|---|---|
|[[Handwritten digits mapping]]|Hexagonal Topology|[![https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_digts.png](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_digts.png)](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_digts.png)[![https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_seed_hex.png](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_seed_hex.png)](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_seed_hex.png)|
|[[Color quantization]]|Outliers detection|[![https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_color_quantization.png](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_color_quantization.png)](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_color_quantization.png)[![https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_outliers_detection_circle.png](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_outliers_detection_circle.png)](https://github.com/JustGlowing/minisom/raw/master/examples/resulting_images/som_outliers_detection_circle.png)|
  
  
## Other tutorials
- [Self Organizing Maps on the Glowing Python](https://glowingpython.blogspot.com/2013/09/self-organizing-maps.html)
- [Essentials of the self-organizing map](http://syllabus.cs.manchester.ac.uk/pgt/2021/COMP61021/reference/new-SOM.pdf) by Teuvo Kohonen
- [How to solve the Travelling Salesman Problem](https://github.com/Optimization-Algorithms-Book/Code-Listings/blob/main/Chapter%2011/Listing%2011.5_TSP_SOM.ipynb) from the book _Optimization Algorithms:Optimization Algorithms: AI techniques for design, planning, and control problems. Manning Publications, 2023._
- [Lecture notes from the Machine Learning course at the University of Lisbon](http://aa.ssdi.di.fct.unl.pt/files/AA-16_notes.pdf)
- [Introduction to Self-Organizing](https://heartbeat.fritz.ai/introduction-to-self-organizing-maps-soms-98e88b568f5d) by Derrick Mwiti
- [Self Organizing Maps on gapminder data](http://inphronesys.com/?p=625) [in German]
- [Discovering SOM, an Unsupervised Neural Network](https://medium.com/neuronio/discovering-som-an-unsupervised-neural-network-12e787f38f9) by Gisely Alves
- Video tutorials made by the GeoEngineerings School: [Part 1](https://www.youtube.com/watch?v=3osKNPyAxPM&list=PL-i8do33HJovC7xFKaYO21qT37vORJWXC&index=11); [Part 2](https://www.youtube.com/watch?v=uUpQ6MITlVs&list=PL-i8do33HJovC7xFKaYO21qT37vORJWXC&index=12); [Part 3](https://www.youtube.com/watch?v=mryFU0TEInk&list=PL-i8do33HJovC7xFKaYO21qT37vORJWXC&index=13); [Part 4](https://www.youtube.com/watch?v=9MzFOIoxxdk&index=14&list=PL-i8do33HJovC7xFKaYO21qT37vORJWXC)
- Video tutorial [Self Organizing Maps: Introduction](https://www.youtube.com/watch?v=0qtvb_Nx2tA) by Art of Visualization
- Video tutorial [Self Organizing Maps Hyperparameter tuning](https://www.youtube.com/watch?v=O6nzwAc_hrQ) by SuperDataScience Machine Learning
## How to cite MiniSom
```Plain
@misc{vettigliminisom,
  title={MiniSom: minimalistic and NumPy-based implementation of the Self Organizing Map},
  author={Giuseppe Vettigli},
  year={2018},
  url={https://github.com/JustGlowing/minisom/},
}
```
MiniSom has been cited more than 200 times, check out the research where MiniSom was used [here](https://scholar.google.co.uk/scholar?hl=en&as_sdt=2007&q=%22JustGlowing%2Fminisom%22+OR+%22minisom+library%22+OR+%22minisom+python%22+OR+%22minisom+vettigli%22&btnG=).
## Guidelines to contribute
1. In the description of your Pull Request explain clearly what it implements or fixes. In cases that the PR is about a code speedup, report a reproducible example and quantify the speedup.
2. Give your pull request a helpful title that summarises what your contribution does.
3. Write unit tests for your code and make sure the existing ones are up to date. `pytest` can be used for this:
```Plain
pytest minisom.py
```
1. Make sure that there are no stylistic issues using `pycodestyle`:
```Plain
pycodestyle minisom.py
```
1. Make sure your code is properly commented and documented. Each public method needs to be documented as the existing ones.