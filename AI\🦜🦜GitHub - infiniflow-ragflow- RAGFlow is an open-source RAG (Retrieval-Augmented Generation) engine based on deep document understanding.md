---
DocFlag:
  - Reference
  - Tested
Updated: 2024-08-17T23:00
tags:
  - AI->-RAG
URL: https://github.com/infiniflow/ragflow
Created: 2024-08-17T00:46
---
Memo
[Document](https://ragflow.io/docs/dev/) | [Roadmap](https://github.com/infiniflow/ragflow/issues/162) | [Twitter](https://twitter.com/infiniflowai) | [Discord](https://discord.gg/4XxujFgUN7) | [Demo](https://demo.ragflow.io/)
💡 What is RAGFlow?
🎮 Demo
🔥 Latest Updates
🌟 Key Features
🍭 "Quality in, quality out"
🍱 Template-based chunking
🌱 Grounded citations with reduced hallucinations
🍔 Compatibility with heterogeneous data sources
🛀 Automated and effortless RAG workflow
🔎 System Architecture
🎬 Get Started
📝 Prerequisites
🚀 Start up the server
🔧 Configurations
🛠️ Build from source
🛠️ Launch service from source
📚 Documentation
📜 Roadmap
🏄 Community
🙌 Contributing
  
# Memo
```JavaScript
Docker >= 24.0.0 & Docker Compose >= v2.26.1
sysctl vm.max_map_count  Ensure vm.max_map_count >= 262144:
git clone https://github.com/infiniflow/ragflow.git
.env: Keeps the fundamental setups for the system, such as SVR_HTTP_PORT, MYSQL_PASSWORD, and MINIO_PASSWORD.
service_conf.yaml: Configures the back-end services.
docker-compose.yml: The system relies on docker-compose.yml to start up.
docker compose up -d
docker logs -f ragflow-server
```
```JavaScript
(base) [raysheng@MONSTER:~]$ docker ps
CONTAINER ID   IMAGE                                                  COMMAND                  CREATED          STATUS                    PORTS                                                              NAMES
70b1808d912c   infiniflow/ragflow:dev                                 "./entrypoint.sh"        33 seconds ago   Up 11 seconds             0.0.0.0:80->80/tcp, 0.0.0.0:443->443/tcp, 0.0.0.0:9380->9380/tcp   ragflow-server
61dd71723c30   redis:7.2.4                                            "docker-entrypoint.s…"   36 seconds ago   Up 32 seconds             0.0.0.0:6379->6379/tcp                                             ragflow-redis
728c904326f3   quay.io/minio/minio:RELEASE.2023-12-20T01-00-02Z       "/usr/bin/docker-ent…"   36 seconds ago   Up 32 seconds             0.0.0.0:9000-9001->9000-9001/tcp                                   ragflow-minio
17cb11c464d4   docker.elastic.co/elasticsearch/elasticsearch:8.11.3   "/bin/tini -- /usr/l…"   36 seconds ago   Up 32 seconds (healthy)   9300/tcp, 0.0.0.0:1200->9200/tcp                                   ragflow-es-01
021ec2bb1c13   mysql:5.7.18                                           "docker-entrypoint.s…"   36 seconds ago   Up 32 seconds (healthy)   0.0.0.0:5455->3306/tcp                                             ragflow-mysql
```
```JavaScript
    ____                 ______ __
   / __ \ ____ _ ____ _ / ____// /____  _      __
  / /_/ // __ `// __ `// /_   / // __ \| | /| / /
 / _, _// /_/ // /_/ // __/  / // /_/ /| |/ |/ /
/_/ |_| \__,_/ \__, //_/    /_/ \____/ |__/|__/
              /____/

[ERROR] [2024-08-17 03:19:49,517] [http_request._handle_response] [line:172]: Request: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation failed, status: 401, message: Invalid API-key provided.
[INFO] [2024-08-17 03:19:50,051] [_internal._log] [line:96]: WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:9380
 * Running on http://**********:9380
[INFO] [2024-08-17 03:19:50,051] [_internal._log] [line:96]: Press CTRL+C to quit
```
Login redis
```JavaScript
docker exec -it ragflow-redis sh
redis-cli -h localhost -p 6379 -a infini_rag_flow
\#remove all messages on the queue
XLEN rag_flow_svr_queue
localhost:6379[1]> XTRIM rag_flow_svr_queue MAXLEN 0
(integer) 302
localhost:6379[1]> XLEN rag_flow_svr_queue
(integer) 0
```
  
[![](https://github.com/infiniflow/ragflow/raw/main/web/src/assets/logo-with-text.png)](https://github.com/infiniflow/ragflow/raw/main/web/src/assets/logo-with-text.png)
[English](https://github.com/infiniflow/ragflow/blob/main/README.md) | [简体中文](https://github.com/infiniflow/ragflow/blob/main/README_zh.md) | [日本語](https://github.com/infiniflow/ragflow/blob/main/README_ja.md)
[![](https://camo.githubusercontent.com/f9b354b7b1d03920ae4a5f26ea245b6feaa1246b7dda271a938dbb219cd65b19/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f762f72656c656173652f696e66696e69666c6f772f726167666c6f773f636f6c6f723d626c7565266c6162656c3d4c617465737425323052656c65617365)](https://camo.githubusercontent.com/f9b354b7b1d03920ae4a5f26ea245b6feaa1246b7dda271a938dbb219cd65b19/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f762f72656c656173652f696e66696e69666c6f772f726167666c6f773f636f6c6f723d626c7565266c6162656c3d4c617465737425323052656c65617365)
[![](https://camo.githubusercontent.com/5442b520b88db31bdfa4a608681a4239125c95a95c61aa5e3cfd625e232735d4/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4f6e6c696e652d44656d6f2d346536623939)](https://camo.githubusercontent.com/5442b520b88db31bdfa4a608681a4239125c95a95c61aa5e3cfd625e232735d4/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4f6e6c696e652d44656d6f2d346536623939)
[![](https://camo.githubusercontent.com/acbc219a63cb52860c9a9d8ecb640cf769e5246c096bda0d2f6c61296d8f4d07/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f646f636b65725f70756c6c2d726167666c6f773a76302e392e302d627269676874677265656e)](https://camo.githubusercontent.com/acbc219a63cb52860c9a9d8ecb640cf769e5246c096bda0d2f6c61296d8f4d07/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f646f636b65725f70756c6c2d726167666c6f773a76302e392e302d627269676874677265656e)
[![](https://camo.githubusercontent.com/53e81905e7954d02804198f82306920f3a771be76cf1b06a7bbc62e5f1c905eb/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d4170616368652d2d322e302d6666666666663f6c6162656c436f6c6f723d64346561663726636f6c6f723d326536636334)](https://camo.githubusercontent.com/53e81905e7954d02804198f82306920f3a771be76cf1b06a7bbc62e5f1c905eb/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d4170616368652d2d322e302d6666666666663f6c6162656c436f6c6f723d64346561663726636f6c6f723d326536636334)
### [Document](https://ragflow.io/docs/dev/) | [Roadmap](https://github.com/infiniflow/ragflow/issues/162) | [Twitter](https://twitter.com/infiniflowai) | [Discord](https://discord.gg/4XxujFgUN7) | [Demo](https://demo.ragflow.io/)
- 📕 Table of Contents
    - 💡 [What is RAGFlow?](https://github.com/infiniflow/ragflow#-what-is-ragflow)
    - 🎮 [Demo](https://github.com/infiniflow/ragflow#-demo)
    - 📌 [Latest Updates](https://github.com/infiniflow/ragflow#-latest-updates)
    - 🌟 [Key Features](https://github.com/infiniflow/ragflow#-key-features)
    - 🔎 [System Architecture](https://github.com/infiniflow/ragflow#-system-architecture)
    - 🎬 [Get Started](https://github.com/infiniflow/ragflow#-get-started)
    - 🔧 [Configurations](https://github.com/infiniflow/ragflow#-configurations)
    - 🛠️ [Build from source](https://github.com/infiniflow/ragflow#-build-from-source)
    - 🛠️ [Launch service from source](https://github.com/infiniflow/ragflow#-launch-service-from-source)
    - 📚 [Documentation](https://github.com/infiniflow/ragflow#-documentation)
    - 📜 [Roadmap](https://github.com/infiniflow/ragflow#-roadmap)
    - 🏄 [Community](https://github.com/infiniflow/ragflow#-community)
    - 🙌 [Contributing](https://github.com/infiniflow/ragflow#-contributing)
## 💡 What is RAGFlow?
[RAGFlow](https://ragflow.io/) is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding. It offers a streamlined RAG workflow for businesses of any scale, combining LLM (Large Language Models) to provide truthful question-answering capabilities, backed by well-founded citations from various complex formatted data.
## 🎮 Demo
Try our demo at [https://demo.ragflow.io](https://demo.ragflow.io/).
[![](https://private-user-images.githubusercontent.com/7248/*********-2f6baa3e-1092-4f11-866d-36f6a9d075e5.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjM4MjM1MTksIm5iZiI6MTcyMzgyMzIxOSwicGF0aCI6Ii83MjQ4LzMzNzYyODg0MS0yZjZiYWEzZS0xMDkyLTRmMTEtODY2ZC0zNmY2YTlkMDc1ZTUuZ2lmP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI0MDgxNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNDA4MTZUMTU0NjU5WiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9YmMyN2NkNjY5MGZmYTM0ZjhlMmIwZDFlYWQxNmI5NTBjMGIxYTI4MmQzMzM5YmJkNGNmOTg3NjFiYWNkNTQ4OCZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QmYWN0b3JfaWQ9MCZrZXlfaWQ9MCZyZXBvX2lkPTAifQ.WZTX7DNCxnKeHoefINFIMI7XiZ7zddv3awhJfbLq5Po)](https://private-user-images.githubusercontent.com/7248/*********-2f6baa3e-1092-4f11-866d-36f6a9d075e5.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjM4MjM1MTksIm5iZiI6MTcyMzgyMzIxOSwicGF0aCI6Ii83MjQ4LzMzNzYyODg0MS0yZjZiYWEzZS0xMDkyLTRmMTEtODY2ZC0zNmY2YTlkMDc1ZTUuZ2lmP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI0MDgxNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNDA4MTZUMTU0NjU5WiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9YmMyN2NkNjY5MGZmYTM0ZjhlMmIwZDFlYWQxNmI5NTBjMGIxYTI4MmQzMzM5YmJkNGNmOTg3NjFiYWNkNTQ4OCZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QmYWN0b3JfaWQ9MCZrZXlfaWQ9MCZyZXBvX2lkPTAifQ.WZTX7DNCxnKeHoefINFIMI7XiZ7zddv3awhJfbLq5Po)
[![](https://private-user-images.githubusercontent.com/12318111/*********-b083d173-dadc-4ea9-bdeb-180d7df514eb.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjM4MjM1MTksIm5iZiI6MTcyMzgyMzIxOSwicGF0aCI6Ii8xMjMxODExMS8zNDY5MzI2ODEtYjA4M2QxNzMtZGFkYy00ZWE5LWJkZWItMTgwZDdkZjUxNGViLmdpZj9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA4MTYlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwODE2VDE1NDY1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTYxMGJhN2EwYWZmMDRhNWUyOTg5NWVhNzMxNTRlZWUzZWRlODc0OWYzOTA4MTQ5ODcwOGU3NjkzNWU0YTRhMzImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.qOJaHQbiwWTWZm9u17Nqao3PdxJ-If8SRdEzrPxIICU)](https://private-user-images.githubusercontent.com/12318111/*********-b083d173-dadc-4ea9-bdeb-180d7df514eb.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjM4MjM1MTksIm5iZiI6MTcyMzgyMzIxOSwicGF0aCI6Ii8xMjMxODExMS8zNDY5MzI2ODEtYjA4M2QxNzMtZGFkYy00ZWE5LWJkZWItMTgwZDdkZjUxNGViLmdpZj9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA4MTYlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwODE2VDE1NDY1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTYxMGJhN2EwYWZmMDRhNWUyOTg5NWVhNzMxNTRlZWUzZWRlODc0OWYzOTA4MTQ5ODcwOGU3NjkzNWU0YTRhMzImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.qOJaHQbiwWTWZm9u17Nqao3PdxJ-If8SRdEzrPxIICU)
## 🔥 Latest Updates
- 2024-08-02 Supports GraphRAG inspired by [graphrag](https://github.com/microsoft/graphrag) and mind map.
    
- 2024-07-23 Supports audio file parsing.
    
- 2024-07-21 Supports more LLMs (LocalAI, OpenRouter, StepFun, and Nvidia).
    
- 2024-07-18 Adds more components (Wikipedia, PubMed, Baidu, and Duckduckgo) to the graph.
    
- 2024-07-08 Supports workflow based on [Graph](https://github.com/infiniflow/ragflow/blob/main/graph/README.md).
    
- 2024-06-27 Supports Markdown and Docx in the Q&A parsing method.
    
- 2024-06-27 Supports extracting images from Docx files.
    
- 2024-06-27 Supports extracting tables from Markdown files.
    
- 2024-06-06 Supports [Self-RAG](https://huggingface.co/papers/2310.11511), which is enabled by default in dialog settings.
    
- 2024-05-30 Integrates [BCE](https://github.com/netease-youdao/BCEmbedding) and [BGE](https://github.com/FlagOpen/FlagEmbedding) reranker models.
    
- 2024-05-23 Supports [RAPTOR](https://arxiv.org/html/2401.18059v1) for better text retrieval.
    
- 2024-05-15 Integrates OpenAI GPT-4o.
    
## 🌟 Key Features
### 🍭 **"Quality in, quality out"**
- [Deep document understanding](https://github.com/infiniflow/ragflow/blob/main/deepdoc/README.md)based knowledge extraction from unstructured data with complicated formats.
- Finds "needle in a data haystack" of literally unlimited tokens.
### 🍱 **Template-based chunking**
- Intelligent and explainable.
- Plenty of template options to choose from.
### 🌱 **Grounded citations with reduced hallucinations**
- Visualization of text chunking to allow human intervention.
- Quick view of the key references and traceable citations to support grounded answers.
### 🍔 **Compatibility with heterogeneous data sources**
- Supports Word, slides, excel, txt, images, scanned copies, structured data, web pages, and more.
### 🛀 **Automated and effortless RAG workflow**
- Streamlined RAG orchestration catered to both personal and large businesses.
- Configurable LLMs as well as embedding models.
- Multiple recall paired with fused re-ranking.
- Intuitive APIs for seamless integration with business.
## 🔎 System Architecture
[![](https://private-user-images.githubusercontent.com/12318111/317212466-d6ac5664-c237-4200-a7c2-a4a00691b485.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjM4MjM1MTksIm5iZiI6MTcyMzgyMzIxOSwicGF0aCI6Ii8xMjMxODExMS8zMTcyMTI0NjYtZDZhYzU2NjQtYzIzNy00MjAwLWE3YzItYTRhMDA2OTFiNDg1LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA4MTYlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwODE2VDE1NDY1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTIwYWU3ZmQ0NmNhODg4NGE4NTlhMGRhNTI2NTNkY2I0ZGJiYWI4ZGRjMzk1M2Q1Mzc3MTMyZGM4ZjU1OTA4NjcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.VJY7DkhIXpBsJFR0NzaFu5bPNqfusIvmwf7ziKcLbSc)](https://private-user-images.githubusercontent.com/12318111/317212466-d6ac5664-c237-4200-a7c2-a4a00691b485.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjM4MjM1MTksIm5iZiI6MTcyMzgyMzIxOSwicGF0aCI6Ii8xMjMxODExMS8zMTcyMTI0NjYtZDZhYzU2NjQtYzIzNy00MjAwLWE3YzItYTRhMDA2OTFiNDg1LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA4MTYlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwODE2VDE1NDY1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTIwYWU3ZmQ0NmNhODg4NGE4NTlhMGRhNTI2NTNkY2I0ZGJiYWI4ZGRjMzk1M2Q1Mzc3MTMyZGM4ZjU1OTA4NjcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.VJY7DkhIXpBsJFR0NzaFu5bPNqfusIvmwf7ziKcLbSc)
## 🎬 Get Started
### 📝 Prerequisites
- CPU >= 4 cores
- RAM >= 16 GB
- Disk >= 50 GB
- Docker >= 24.0.0 & Docker Compose >= v2.26.1
    
    > If you have not installed Docker on your local machine (Windows, Mac, or Linux), see [Install Docker Engine](https://docs.docker.com/engine/install/).
    
### 🚀 Start up the server
1. Ensure `vm.max_map_count` >= 262144:
    
    > To check the value of `vm.max_map_count`:
    > 
    > ```Plain
    > $ sysctl vm.max_map_count
    > ```
    > 
    > Reset `vm.max_map_count` to a value at least 262144 if it is not.
    > 
    > ```Plain
    > # In this case, we set it to 262144:
    > $ sudo sysctl -w vm.max_map_count=262144
    > ```
    > 
    > This change will be reset after a system reboot. To ensure your change remains permanent, add or update the `vm.max_map_count` value in **/etc/sysctl.conf** accordingly:
    > 
    > ```Plain
    > vm.max_map_count=262144
    > ```
    
2. Clone the repo:
    
    ```Plain
    $ git clone https://github.com/infiniflow/ragflow.git
    ```
    
3. Build the pre-built Docker images and start up the server:
    
    > Running the following commands automatically downloads the _dev_ version RAGFlow Docker image. To download and run a specified Docker version, update `RAGFLOW_VERSION` in **docker/.env** to the intended version, for example `RAGFLOW_VERSION=v0.9.0`, before running the following commands.
    
    ```Plain
    $ cd ragflow/docker
    $ chmod +x ./entrypoint.sh
    $ docker compose up -d
    ```
    
    > The core image is about 9 GB in size and may take a while to load.
    
4. Check the server status after having the server up and running:
    
    ```Plain
    $ docker logs -f ragflow-server
    ```
    
    _The following output confirms a successful launch of the system:_
    
    ```Plain
        ____                 ______ __
       / __ \ ____ _ ____ _ / ____// /____  _      __
      / /_/ // __ `// __ `// /_   / // __ \| | /| / /
     / _, _// /_/ // /_/ // __/  / // /_/ /| |/ |/ /
    /_/ |_| \__,_/ \__, //_/    /_/ \____/ |__/|__/
                  /____/
    
     * Running on all addresses (0.0.0.0)
     * Running on http://127.0.0.1:9380
     * Running on http://x.x.x.x:9380
     INFO:werkzeug:Press CTRL+C to quit
    ```
    
    > If you skip this confirmation step and directly log in to RAGFlow, your browser may prompt a `network anomaly` error because, at that moment, your RAGFlow may not be fully initialized.
    
5. In your web browser, enter the IP address of your server and log in to RAGFlow.
    
    > With the default settings, you only need to enter `http://IP_OF_YOUR_MACHINE` (**sans** port number) as the default HTTP serving port `80` can be omitted when using the default configurations.
    
6. In [service_conf.yaml](https://github.com/infiniflow/ragflow/blob/main/docker/service_conf.yaml), select the desired LLM factory in `user_default_llm` and update the `API_KEY` field with the corresponding API key.
    
    > See [llm_api_key_setup](https://ragflow.io/docs/dev/llm_api_key_setup) for more information.
    
    _The show is now on!_
    
## 🔧 Configurations
When it comes to system configurations, you will need to manage the following files:
- [.env](https://github.com/infiniflow/ragflow/blob/main/docker/.env): Keeps the fundamental setups for the system, such as `SVR_HTTP_PORT`, `MYSQL_PASSWORD`, and `MINIO_PASSWORD`.
- [service_conf.yaml](https://github.com/infiniflow/ragflow/blob/main/docker/service_conf.yaml): Configures the back-end services.
- [docker-compose.yml](https://github.com/infiniflow/ragflow/blob/main/docker/docker-compose.yml): The system relies on [docker-compose.yml](https://github.com/infiniflow/ragflow/blob/main/docker/docker-compose.yml) to start up.
You must ensure that changes to the [.env](https://github.com/infiniflow/ragflow/blob/main/docker/.env) file are in line with what are in the [service_conf.yaml](https://github.com/infiniflow/ragflow/blob/main/docker/service_conf.yaml) file.

> The [./docker/README](https://github.com/infiniflow/ragflow/blob/main/docker/README.md) file provides a detailed description of the environment settings and service configurations, and you are REQUIRED to ensure that all environment settings listed in the [./docker/README](https://github.com/infiniflow/ragflow/blob/main/docker/README.md) file are aligned with the corresponding configurations in the [service_conf.yaml](https://github.com/infiniflow/ragflow/blob/main/docker/service_conf.yaml) file.
To update the default HTTP serving port (80), go to [docker-compose.yml](https://github.com/infiniflow/ragflow/blob/main/docker/docker-compose.yml) and change `80:80` to `<YOUR_SERVING_PORT>:80`.

> Updates to all system configurations require a system reboot to take effect:
> 
> ```Plain
> $ docker-compose up -d
> ```
## 🛠️ Build from source
To build the Docker images from source:
```Plain
$ git clone https://github.com/infiniflow/ragflow.git
$ cd ragflow/
$ docker build -t infiniflow/ragflow:dev .
$ cd ragflow/docker
$ chmod +x ./entrypoint.sh
$ docker compose up -d
```
## 🛠️ Launch service from source
To launch the service from source:
1. Clone the repository:
    
    ```Plain
    $ git clone https://github.com/infiniflow/ragflow.git
    $ cd ragflow/
    ```
    
2. Create a virtual environment, ensuring that Anaconda or Miniconda is installed:
    
    ```Plain
    $ conda create -n ragflow python=3.11.0
    $ conda activate ragflow
    $ pip install -r requirements.txt
    ```
    
    ```Plain
    # If your CUDA version is higher than 12.0, run the following additional commands:
    $ pip uninstall -y onnxruntime-gpu
    $ pip install onnxruntime-gpu --extra-index-url https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/onnxruntime-cuda-12/pypi/simple/
    ```
    
3. Copy the entry script and configure environment variables:
    
    ```Plain
    # Get the Python path:
    $ which python
    # Get the ragflow project path:
    $ pwd
    ```
    
    ```Plain
    $ cp docker/entrypoint.sh .
    $ vi entrypoint.sh
    ```
    
    ```Plain
    # Adjust configurations according to your actual situation (the following two export commands are newly added):
    # - Assign the result of `which python` to `PY`.
    # - Assign the result of `pwd` to `PYTHONPATH`.
    # - Comment out `LD_LIBRARY_PATH`, if it is configured.
    # - Optional: Add Hugging Face mirror.
    PY=${PY}
    export PYTHONPATH=${PYTHONPATH}
    export HF_ENDPOINT=https://hf-mirror.com
    ```
    
4. Launch the third-party services (MinIO, Elasticsearch, Redis, and MySQL):
    
    ```Plain
    $ cd docker
    $ docker compose -f docker-compose-base.yml up -d
    ```
    
5. Check the configuration files, ensuring that:
    
    - The settings in **docker/.env** match those in **conf/service_conf.yaml**.
    - The IP addresses and ports for related services in **service_conf.yaml** match the local machine IP and ports exposed by the container.
6. Launch the RAGFlow backend service:
    
    ```Plain
    $ chmod +x ./entrypoint.sh
    $ bash ./entrypoint.sh
    ```
    
7. Launch the frontend service:
    
    ```Plain
    $ cd web
    $ npm install --registry=https://registry.npmmirror.com --force
    $ vim .umirc.ts
    # Update proxy.target to http://127.0.0.1:9380
    $ npm run dev
    ```
    
8. Deploy the frontend service:
    
    ```Plain
    $ cd web
    $ npm install --registry=https://registry.npmmirror.com --force
    $ umi build
    $ mkdir -p /ragflow/web
    $ cp -r dist /ragflow/web
    $ apt install nginx -y
    $ cp ../docker/nginx/proxy.conf /etc/nginx
    $ cp ../docker/nginx/nginx.conf /etc/nginx
    $ cp ../docker/nginx/ragflow.conf /etc/nginx/conf.d
    $ systemctl start nginx
    ```
    
## 📚 Documentation
- [Quickstart](https://ragflow.io/docs/dev/)
- [User guide](https://ragflow.io/docs/dev/category/user-guides)
- [References](https://ragflow.io/docs/dev/category/references)
- [FAQ](https://ragflow.io/docs/dev/faq)
## 📜 Roadmap
See the [RAGFlow Roadmap 2024](https://github.com/infiniflow/ragflow/issues/162)
## 🏄 Community
- [Discord](https://discord.gg/4XxujFgUN7)
- [Twitter](https://twitter.com/infiniflowai)
- [GitHub Discussions](https://github.com/orgs/infiniflow/discussions)
## 🙌 Contributing
RAGFlow flourishes via open-source collaboration. In this spirit, we embrace diverse contributions from the community. If you would like to be a part, review our [Contribution Guidelines](https://github.com/infiniflow/ragflow/blob/main/docs/references/CONTRIBUTING.md) first.