---
created: 2025-06-26
tags:
  - AI/DailyDose
  - AI/MCP
  - AI/Platform
source:
author:
Reference:
---

# Package AI/ML Projects with KitOps MCP Server

[blog.dailydoseofds.com](https://blog.dailydoseofds.com/p/package-aiml-projects-with-kitops) Avi <PERSON>a

In today's newsletter:

* A chat interface to talk to 200+ data sources!

* ​Package AI/ML Projects with KitOps MCP Server​

* Implement ReAct Agentic pattern from scratch.

* A technique to decide if you should gather more data

### [A chat interface to talk to 200+ data sources!](https://mindsdb.com/blog/your-data-has-a-new-interface-introducing-chat-powered-semantic-sql-insights-in-mindsdb-open-source)

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21wFCn%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F6a96b61a-2d06-4f1d-a7ae-1f64bc5e05dc_1208x1116.png&valid=true)](https://github.com/mindsdb/mindsdb)

We have been testing the new chat interface in **[MindsDB](https://github.com/mindsdb/mindsdb)** open source, and it's impressive.

You can ask plain English questions, and it figures out whether to run SQL or semantic search behind the scenes, then merges the results into a single answer.

It's built on a semantic + parametric query engine. No coding required!

For instance: *"What are users saying about Feature X, and how does that correlate with churn?"*

To answer this, it'll hit your DB for churn metrics and your docs for support themes, all in one go. You can also interact with it using its MCP server.

We'll do a demo on this soon, but in the meantime, you can [​](https://github.com/mindsdb/mindsdb)**[read about it in the MindsDB GitHub repo →](https://github.com/mindsdb/mindsdb)** [​](https://github.com/mindsdb/mindsdb)

### [Package AI/ML Projects with KitOps MCP Server](https://github.com/kitops-ml/kitops)

ML projects aren't just code.

They are code + datasets + model weights + parameters + config, and whatnot!

* Docker isn't well-suited to package them since you cannot selectively pull what you need.

* And GitHub enforces size limits.

To solve this, we built an MCP server that all AI/ML Engineers will love.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21tJio%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fcb7ba11f-e842-4aa8-aa7b-a4e7b8221734_960x846.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!tJio!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fcb7ba11f-e842-4aa8-aa7b-a4e7b8221734_960x846.gif)

The video attached at the top gives a detailed walk-through.

We created ModelKits (powered by open-source **[KitOps](https://github.com/kitops-ml/kitops)** ) to package an AI/ML project (models, datasets, code, and config) into a single, shareable unit.

Think of it as Docker for AI, but smarter.

While Docker containers package applications, **[ModelKits](https://github.com/kitops-ml/kitops)** are purpose-built for AI/ML workflows.

They can be centrally managed and deployed.

Key advantages that we observed:

* Lets you selectively unpack kits and skip pulling what you don't need.

* Acts as your private model registry

* Gives you one-command deployment

* Works with your existing container registry

* Lets you create RAG pipelines as well

* Has built-in LLM fine-tuning support.

* Supports Kubernetes/KServe config generation

We have wrapped up KitOps CLI and their Python SDK in an MCP server, and the video at the top gives a detailed walkthrough of how you can use it.

Here are all the relevant links:

* **[KitOps GitHub repo →](https://github.com/kitops-ml/kitops)**

* **[The code we wrote to build the MCP server →](https://github.com/patchy631/ai-engineering-hub/tree/main/kitops-mcp)**

### **[​Implement ReAct Agentic Pattern from Scratch​](https://www.dailydoseofds.com/ai-agents-crash-course-part-10-with-implementation/)**

Consider the output of a multi-agent system below:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21fNyy%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fab569058-b5d1-40b6-b05e-6afef3eb2b9a_1000x797.png&valid=true)](https://substackcdn.com/image/fetch/$s_!fNyy!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fab569058-b5d1-40b6-b05e-6afef3eb2b9a_1000x797.png)

As shown above, the Agent is going through a series of thought activities before producing a response.

This is ReAct pattern in action!

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%213vHI%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fd02b2eaa-16c3-4f92-8f97-06329fbcccd4_716x550.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!3vHI!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd02b2eaa-16c3-4f92-8f97-06329fbcccd4_716x550.gif)

More specifically, under the hood, many frameworks use the ReAct (Reasoning and Acting) pattern to think through problems and use tools to act on the world​.

This enhances an LLM agent's ability to handle complex tasks and decisions by combining chain-of-thought reasoning with external tool use​.

We have also seen this being asked in several LLM interview questions.

[​](https://www.dailydoseofds.com/ai-agents-crash-course-part-10-with-implementation/)**[You can learn how to implement it from scratch here →](https://www.dailydoseofds.com/ai-agents-crash-course-part-10-with-implementation/)** [​](https://www.dailydoseofds.com/ai-agents-crash-course-part-10-with-implementation/)

It covers:

* The entire ReAct loop pattern (Thought → Action → Observation → Answer), which powers intelligent decision-making in many agentic systems.

* How to structure a system prompt that teaches the LLM to think step-by-step and call tools deterministically.

* How to implement a lightweight agent class that keeps track of conversations and interfaces with the LLM.

* A fully manual ReAct loop for transparency and debugging.

* A fully automated `agent_loop()` controller that parses the agent's reasoning and executes tools behind the scenes.

### **Should you gather more data?**

At times, no matter how much you try, the model performance barely improves:

* Feature engineering gives a marginal improvement.

* Trying different models does not produce satisfactory results either.

* and more...

This is usually an indicator that we don't have enough data to work with.

But since gathering new data can be a time-consuming and tedious process...

...here's a technique to determine whether more data will help:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21FCDu%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F470ae846-8147-44c7-90d7-d1eabd46f6a6_1066x1140.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!FCDu!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F470ae846-8147-44c7-90d7-d1eabd46f6a6_1066x1140.gif)

* Divide the dataset into "k" equal parts. Usually, 7 to 12 parts are fine.

* Train models cumulatively on the above subsets and measure the performance on the validation set:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21k8L1%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F38822747-31ee-435c-8b7f-484b8cf6a52b_2820x1500.png&valid=true)](https://substackcdn.com/image/fetch/$s_!k8L1!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F38822747-31ee-435c-8b7f-484b8cf6a52b_2820x1500.png)

* Train a model on the **first subset** only.

* Train a model on the **first two subsets** only.

* And so on...

Plotting the validation performance will produce one of these two lines:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21urXm%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fd7f2395e-72a0-4399-95b4-14780bff0371_1324x780.png&valid=true)](https://substackcdn.com/image/fetch/$s_!urXm!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd7f2395e-72a0-4399-95b4-14780bff0371_1324x780.png)

* Line A conveys that adding more data will likely increase the model's performance.

* Line B conveys that the model's performance has already saturated. Adding more data will most likely not result in any considerable gains.

This way, you can ascertain whether gathering data will help.

Thanks for reading!

[Read in Cubox](https://cubox.cc/my/card?id=7337854079919456921)
