---
DocFlag:
  - Reference
  - Tested
Updated: 2024-08-12T11:42
tags:
  - AI/PDF
  - AI/RAG
  - AI/Tools
Created: 2024-07-30T14:16
---
# Setup
```Python
conda create -n MinerU python=3.10
conda activate MinerU
\#1 install pytorch to support GPU
pip install --force-reinstall torch==2.3.1 torchvision==0.18.1 --index-url https://download.pytorch.org/whl/cu118
(MinerU) [raysheng@MONSTER:/opt/workspace/MinerU]$ python /opt/workspace/researcher/gputest.py
CUDA availability: True
CUDA version: 11.8
<need to install openv>
pip install opencv-python
pip install torchtext torchdata

#\#1.1 Test
/opt/workspace/researcher/gputest.py
\#2 install detectron2
pip install detectron2 --extra-index-url https://myhloli.github.io/wheels/
We can skip it as we will install under below
\#3 install MinerU
git clone https://github.com/opendatalab/MinerU.git
cd MinerU
pip install -e .
pip install magic-pdf[full] detectron2 --extra-index-url https://myhloli.github.io/wheels/
\#3.1 Test it
python /opt/workspace/researcher/gputest.py
CUDA availability: True
CUDA version: 11.8
4.6.0
词汇表大小: 95811
原文: Computers are great tools for productivity.
索引: [817, 42, 810, 1417, 11, 5544, 1]
\#4. download model weights
git lfs clone https://huggingface.co/wanderkid/PDF-Extract-Kit
\#5. Change confiugration file
cp -rp  magic-pdf.template.json magic-pdf.json
cat magic-pdf.json
{
    "bucket_info":{
        "bucket-name-1":["ak", "sk", "endpoint"],
        "bucket-name-2":["ak", "sk", "endpoint"]
    },
    "temp-output-dir":"/opt/workspace/MinerU/tmp",
    "models-dir":"/opt/aibase/PDF-Extract-Kit/models",
    "device-mode":"cuda"
}
!! Must move this json file to /home/<USER>
<<Run it>>
magic-pdf pdf-command --pdf "/opt/workspace/MinerU/pdfs/JuneyaoAirlines.pdf" --inside_model true --model_mode full 
@ 发现即使为full，method为auto时候还是不会对图里的内容进行解析
magic-pdf pdf-command --help
magic-pdf pdf-command --pdf "/opt/workspace/MinerU/pdfs/JuneyaoAirlines.pdf" --inside_model true --model_mode full --method ocr
magic-pdf pdf-command --pdf "/opt/workspace/MinerU/pdfs/financial-documents-lvmh-december-31-2023.pdf" --inside_model true --model_mode full --method ocr
```
  
```Python
处理完，发现图表只是转存为image文件，没有转成文字，
所以可以多一步，使用LLM转成json格式然后嵌入到md文件里
```
## Install with UV
```
vi pyproject.toml

[project]
name = "my-project"
version = "1.0.0"
requires-python = "~=3.12.0"
dependencies = [
  "detectron2",
  "magic-pdf[full]==0.10.6"
]

[tool.uv]
package = false
prerelease = "allow"

[tool.uv.sources]
detectron2 = { git = "https://github.com/facebookresearch/detectron2.git" }


-- Then you can build by

uv venv
uv pip install torch
uv pip install --no-build-isolation git+https://github.com/facebookresearch/detectron2.git
uv sync --dev
Notes

Currently for uv to install package that needs --no-build-isolation, need run the command manually first, which is why we do uv pip install --no-build-isolation git+https://github.com/facebookresearch/detectron2.git.
detectron2 does not define dependency torch, but it should, which is why we manual uv pip install torch before detectron2.
magic-pdf[full] 0.10.6 currently is using a pre-release dependency paddlepaddle 3.0.0b1 which is why we need prerelease = "allow", but in future, once magic-pdf relies on a release version of paddlepaddle, then no need prerelease = "allow".

```

## Program Example
```Python
from anthropic import Anthropic
import os
import base64
import re
from PIL import Image
import io
# 获取当前脚本所在目录
current_script_dir = os.path.dirname(os.path.abspath(__file__))
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
# Set up the Anthropic API client
client = Anthropic()
MODEL_NAME = "claude-3-5-sonnet-20240620"
\#MODEL_NAME = "claude-3-opus-20240229"
haiku_prompt = """
You will be extracting information from images in a PDF file and converting it into a well-organized and coherent JSON format. 
Finally, double-check your work for accuracy and completeness. Make sure that you have captured all the important information 
from each image and that the text accurately reflects the content of the tables and graphs. When extracting table information, 
please ensure that the output JSON includes the relationship of nested subtables. 
Please provide your final output inside a code block and Must use Japanese. Dont output explanation or comments. 
Output only the JSON format."""

def extract_info(image_path, haiku_prompt):
    # 读取 JPG 图片并转换为 PNG
    with Image.open(image_path) as img:
        png_buffer = io.BytesIO()
        img.save(png_buffer, format='PNG')
        png_buffer.seek(0)
        base64_encoded_png = base64.b64encode(png_buffer.getvalue()).decode("utf-8")
    
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "image", "source": {"type": "base64", "media_type": "image/png", "data": base64_encoded_png}},
                {"type": "text", "text": haiku_prompt}
            ]
        }
    ]
    
    response = client.messages.create(
        model=MODEL_NAME,
        max_tokens=8192,
        messages=messages,
        extra_headers={"anthropic-beta": "max-tokens-3-5-sonnet-2024-07-15"}
    )
    
    return response.content[0].text

"""
Process a markdown file by replacing all image references with their corresponding JSON information.
Args:
    input_file (str): The path to the input markdown file.
    output_file (str): The path to the output markdown file.
Returns:
    None
This function reads the content of the input markdown file, searches for all image references using a regular expression,
and replaces them with their corresponding JSON information. The JSON information is extracted by calling the
`extract_info` function, which takes an image path and a prompt as arguments. The extracted JSON information is
then formatted and wrapped in Markdown code blocks. If an image is not found, the original image reference is preserved.
The processed content is written to the output markdown file.
Note: The function assumes that the input markdown file contains image references in the format `![alt text](images/image.jpg)`.
"""
def process_markdown_file(input_file, output_file):
    # 切换到脚本所在目录
    os.chdir(current_script_dir)
        
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式匹配图片引用
    pattern = r'!\[.*?\]\((images/.*?\.jpg)\)'
    
    def replace_image(match):
        image_path = match.group(1)
        if os.path.exists(image_path):
            json_result = extract_info(image_path, haiku_prompt)
            # 移除可能存在的开头和结尾的 Markdown 代码块标记
            json_result = json_result.strip()
            if json_result.startswith('```json'):
                json_result = json_result[7:]
            if json_result.endswith('```'):
                json_result = json_result[:-3]
            
            # 确保 JSON 内容被正确格式化
            json_result = json_result.strip()
            
            return f"\n```json\n{json_result}\n```\n"
        else:
            print(f"Warning: Image not found - {image_path}")            
            return match.group(0)  # 如果图片不存在,保留原始引用
    
    # 替换所有图片引用
    processed_content = re.sub(pattern, replace_image, content)
    
    # 将处理后的内容写入新文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(processed_content)
        
# 使用方法
input_file = '/opt/workspace/MinerU/demo/demo1.md'
output_file = '/opt/workspace/MinerU/demo/demo1_imageprocessed.md'
# 确保在正确的目录下运行
os.chdir(current_script_dir)
process_markdown_file(input_file, output_file)
```
![[demo1.md]]
  
[![](https://github.com/opendatalab/MinerU/raw/master/docs/images/MinerU-logo.png)](https://github.com/opendatalab/MinerU/raw/master/docs/images/MinerU-logo.png)
  
[![](https://camo.githubusercontent.com/fb3d30a9b84c203e519ffaa0bd63ba7b58a79642a4c14d27f85d32f7e3a1d1a1/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f6f70656e646174616c61622f4d696e6572552e737667)](https://camo.githubusercontent.com/fb3d30a9b84c203e519ffaa0bd63ba7b58a79642a4c14d27f85d32f7e3a1d1a1/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f6f70656e646174616c61622f4d696e6572552e737667)
[![](https://camo.githubusercontent.com/d4ee9e53193df4015ca08c779098ba6dce55a7624c61d7ed8179c95c11566c78/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f666f726b732f6f70656e646174616c61622f4d696e6572552e737667)](https://camo.githubusercontent.com/d4ee9e53193df4015ca08c779098ba6dce55a7624c61d7ed8179c95c11566c78/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f666f726b732f6f70656e646174616c61622f4d696e6572552e737667)
[![](https://camo.githubusercontent.com/38bb0ef0bf2b52676289d3aa2fb89610f352efa1b871e29682521754a43b0faf/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732d7261772f6f70656e646174616c61622f4d696e657255)](https://camo.githubusercontent.com/38bb0ef0bf2b52676289d3aa2fb89610f352efa1b871e29682521754a43b0faf/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732d7261772f6f70656e646174616c61622f4d696e657255)
[![](https://camo.githubusercontent.com/f6b80c01fca1f9fa75d40cb745d2a23c1ac4835b37c64f6600c172d44029c129/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732d636c6f7365642d7261772f6f70656e646174616c61622f4d696e657255)](https://camo.githubusercontent.com/f6b80c01fca1f9fa75d40cb745d2a23c1ac4835b37c64f6600c172d44029c129/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732d636c6f7365642d7261772f6f70656e646174616c61622f4d696e657255)
[![](https://camo.githubusercontent.com/46d45fd8a85a449d9368d7fbcdfef15ca96d4bf493d746b391ba355c3130e5cf/68747470733a2f2f62616467652e667572792e696f2f70792f6d616769632d7064662e737667)](https://camo.githubusercontent.com/46d45fd8a85a449d9368d7fbcdfef15ca96d4bf493d746b391ba355c3130e5cf/68747470733a2f2f62616467652e667572792e696f2f70792f6d616769632d7064662e737667)
[![](https://camo.githubusercontent.com/064f13f45044838bfe7bab03704ceac7f954375cf07f2a8f12a1b473a0ea057c/68747470733a2f2f7374617469632e706570792e746563682f62616467652f6d616769632d706466)](https://camo.githubusercontent.com/064f13f45044838bfe7bab03704ceac7f954375cf07f2a8f12a1b473a0ea057c/68747470733a2f2f7374617469632e706570792e746563682f62616467652f6d616769632d706466)
[![](https://camo.githubusercontent.com/1f4c4b61cd003619c024a17046774cb7047245307ad9ad6210f1eebcaf3ddf07/68747470733a2f2f7374617469632e706570792e746563682f62616467652f6d616769632d7064662f6d6f6e7468)](https://camo.githubusercontent.com/1f4c4b61cd003619c024a17046774cb7047245307ad9ad6210f1eebcaf3ddf07/68747470733a2f2f7374617469632e706570792e746563682f62616467652f6d616769632d7064662f6d6f6e7468)
[![](https://camo.githubusercontent.com/eb1ef7135d63221bef8f769769b0411e74d873b62c8cef0c7049896f047f9193/68747470733a2f2f7472656e6473686966742e696f2f6170692f62616467652f7265706f7369746f726965732f3131313734)](https://camo.githubusercontent.com/eb1ef7135d63221bef8f769769b0411e74d873b62c8cef0c7049896f047f9193/68747470733a2f2f7472656e6473686966742e696f2f6170692f62616467652f7265706f7369746f726965732f3131313734)
[==English==](https://github.com/opendatalab/MinerU/blob/master/README.md) ==|== [==简体中文==](https://github.com/opendatalab/MinerU/blob/master/README_zh-CN.md) ==|== [==日本語==](https://github.com/opendatalab/MinerU/blob/master/README_ja-JP.md)
[==MinerU: An end-to-end PDF parsing tool based on PDF-Extract-Kit, supporting conversion from PDF to Markdown.==](https://github.com/opendatalab/MinerU)==🚀🚀🚀====  
  
==[==PDF-Extract-Kit: A Comprehensive Toolkit for High-Quality PDF Content Extraction==](https://github.com/opendatalab/PDF-Extract-Kit)==🔥🔥🔥==
==👋 join us on== [==Discord==](https://discord.gg/AsQMhuMN) ==and== [==WeChat==](https://cdn.vansin.top/internlm/mineru.jpg)
# ==MinerU==
## ==Introduction==
==MinerU is a one-stop, open-source, high-quality data extraction tool, includes the following primary features:==
- [==Magic-PDF==](https://github.com/opendatalab/MinerU#Magic-PDF) ==PDF Document Extraction==
- [==Magic-Doc==](https://github.com/opendatalab/MinerU#Magic-Doc) ==Webpage & E-book Extraction==
# ==Magic-PDF==
## ==Introduction==
==Magic-PDF is a tool designed to convert PDF documents into Markdown format, capable of processing files stored locally or on object storage supporting S3 protocol.==
==Key features include:==
- ==Support for multiple front-end model inputs==
- ==Removal of headers, footers, footnotes, and page numbers==
- ==Human-readable layout formatting==
- ==Retains the original document's structure and formatting, including headings, paragraphs, lists, and more==
- ==Extraction and display of images and tables within markdown==
- ==Conversion of equations into LaTeX format==
- ==Automatic detection and conversion of garbled PDFs==
- ==Compatibility with CPU and GPU environments==
- ==Available for Windows, Linux, and macOS platforms==
==pdf_zh_cn.mp4==
## ==Project Panorama==
[![](https://github.com/opendatalab/MinerU/raw/master/docs/images/project_panorama_en.png)](https://github.com/opendatalab/MinerU/raw/master/docs/images/project_panorama_en.png)
## ==Flowchart==
[![](https://github.com/opendatalab/MinerU/raw/master/docs/images/flowchart_en.png)](https://github.com/opendatalab/MinerU/raw/master/docs/images/flowchart_en.png)
### ==Dependency repositorys==
- [==PDF-Extract-Kit : A Comprehensive Toolkit for High-Quality PDF Content Extraction==](https://github.com/opendatalab/PDF-Extract-Kit) ==🚀🚀🚀==
## ==Getting Started==
### ==Requirements==
- ==Python >= 3.9==
==Using a virtual environment is recommended to avoid potential dependency conflicts; both venv and conda are suitable. For example:==
==conda create -n MinerU python=3.10  
conda activate MinerU  
==
### ==Installation and Configuration==
### ==1. Install Magic-PDF==
==Install the full-feature package with pip:==
==Note: The pip-installed package supports CPU-only and is ideal for quick tests.==
==For CUDA/MPS acceleration in production, see== [==Acceleration Using CUDA or MPS==](https://github.com/opendatalab/MinerU#4-Acceleration-Using-CUDA-or-MPS)==.==
==pip install magic-pdf[full-cpu]==
==The full-feature package depends on detectron2, which requires a compilation installation.====  
  
====If you need to compile it yourself, please refer to== [==facebookresearch/detectron2#5114==](https://github.com/facebookresearch/detectron2/issues/5114)==  
  
====Alternatively, you can directly use our precompiled whl package (limited to Python 3.10):==
==pip install detectron2 --extra-index-url https://myhloli.github.io/wheels/==
### ==2. Downloading model weights files==
==For detailed references, please see below== [==how_to_download_models==](https://github.com/opendatalab/MinerU/blob/master/docs/how_to_download_models_en.md)
==After downloading the model weights, move the 'models' directory to a directory on a larger disk space, preferably an SSD.==
### ==3. Copy the Configuration File and Make Configurations==
==You can get the== [==magic-pdf.template.json==](https://github.com/opendatalab/MinerU/blob/master/magic-pdf.template.json) ==file in the repository root directory.==
==cp magic-pdf.template.json ~/magic-pdf.json==
==In magic-pdf.json, configure "models-dir" to point to the directory where the model weights files are located.==
=={  
"models-dir": "/tmp/models"  
}  
==
### ==4. Acceleration Using CUDA or MPS==
==If you have an available Nvidia GPU or are using a Mac with Apple Silicon, you can leverage acceleration with CUDA or MPS respectively.==
### ==CUDA==
==You need to install the corresponding PyTorch version according to your CUDA version.====  
  
====This example installs the CUDA 11.8 version.More information== [==https://pytorch.org/get-started/locally/==](https://pytorch.org/get-started/locally/)
==pip install --force-reinstall torch==2.3.1 torchvision==0.18.1 --index-url https://download.pytorch.org/whl/cu118==
==Also, you need to modify the value of "device-mode" in the configuration file magic-pdf.json.==
=={  
"device-mode":"cuda"  
}  
==
### ==MPS==
==For macOS users with M-series chip devices, you can use MPS for inference acceleration.====  
  
====You also need to modify the value of "device-mode" in the configuration file magic-pdf.json.==
=={  
"device-mode":"mps"  
}  
==
### ==Usage==
### ==1.Usage via Command Line==
### ==simple==
==magic-pdf pdf-command --pdf "pdf_path" --inside_model true==
==After the program has finished, you can find the generated markdown files under the directory "/tmp/magic-pdf".====  
  
====You can find the corresponding xxx_model.json file in the markdown directory.====  
  
====If you intend to do secondary development on the post-processing pipeline, you can use the command:==
==magic-pdf pdf-command --pdf "pdf_path" --model "model_json_path"==
==In this way, you won't need to re-run the model data, making debugging more convenient.==
### ==more==
==magic-pdf --help==
### ==2. Usage via Api==
### ==Local==
==image_writer = DiskReaderWriter(local_image_dir)  
image_dir = str(os.path.basename(local_image_dir))  
jso_useful_key = {"_pdf_type": "", "model_list": []}  
pipe = UNIPipe(pdf_bytes, jso_useful_key, image_writer)  
pipe.pipe_classify()  
pipe.pipe_parse()  
md_content = pipe.pipe_mk_markdown(image_dir, drop_mode="none")  
==
### ==Object Storage==
==s3pdf_cli = S3ReaderWriter(pdf_ak, pdf_sk, pdf_endpoint)  
image_dir = "s3://img_bucket/"  
s3image_cli = S3ReaderWriter(img_ak, img_sk, img_endpoint, parent_path=image_dir)  
pdf_bytes = s3pdf_cli.read(s3_pdf_path, mode=s3pdf_cli.MODE_BIN)  
jso_useful_key = {"_pdf_type": "", "model_list": []}  
pipe = UNIPipe(pdf_bytes, jso_useful_key, s3image_cli)  
pipe.pipe_classify()  
pipe.pipe_parse()  
md_content = pipe.pipe_mk_markdown(image_dir, drop_mode="none")  
==
==Demo can be referred to== [==demo.py==](https://github.com/opendatalab/MinerU/blob/master/demo/demo.py)
# ==Magic-Doc==
## ==Introduction==
==Magic-Doc is a tool designed to convert web pages or multi-format e-books into markdown format.==
==Key Features Include:==
- ==Web Page Extraction==
    - ==Cross-modal precise parsing of text, images, tables, and formula information.==
- ==E-Book Document Extraction==
    - ==Supports various document formats including epub, mobi, with full adaptation for text and images.==
- ==Language Type Identification==
    - ==Accurate recognition of 176 languages.==
==extract1.mp4 extract2.mp4 extract3.mp4==
## ==Project Repository==
- [==Magic-Doc==](https://github.com/InternLM/magic-doc) ==Outstanding Webpage and E-book Extraction Tool==
# ==All Thanks To Our Contributors==
[![](https://camo.githubusercontent.com/95cc5987375c49058cf05ee7437d7f7977f0a43f1bddc81ebcbd035ce2666cb6/68747470733a2f2f636f6e747269622e726f636b732f696d6167653f7265706f3d6f70656e646174616c61622f4d696e657255)](https://camo.githubusercontent.com/95cc5987375c49058cf05ee7437d7f7977f0a43f1bddc81ebcbd035ce2666cb6/68747470733a2f2f636f6e747269622e726f636b732f696d6167653f7265706f3d6f70656e646174616c61622f4d696e657255)
# ==License Information==
[==LICENSE.md==](https://github.com/opendatalab/MinerU/blob/master/LICENSE.md)
==The project currently leverages PyMuPDF to deliver advanced functionalities; however, its adherence to the AGPL license may impose limitations on certain use cases. In upcoming iterations, we intend to explore and transition to a more permissively licensed PDF processing library to enhance user-friendliness and flexibility.==
# ==Acknowledgments==
- [==PaddleOCR==](https://github.com/PaddlePaddle/PaddleOCR)
- [==PyMuPDF==](https://github.com/pymupdf/PyMuPDF)
- [==fast-langdetect==](https://github.com/LlmKira/fast-langdetect)
- [==pdfminer.six==](https://github.com/pdfminer/pdfminer.six)
# ==Citation==
==@article{he2024opendatalab,  
title={Opendatalab: Empowering general artificial intelligence with open datasets},  
author={He, Conghui and Li, Wei and Jin, Zhenjiang and Xu, Chao and Wang, Bin and Lin, Dahua},  
journal={arXiv preprint arXiv:2407.13773},  
year={2024}  
}  
@misc{2024mineru,  
title={MinerU: A One-stop, Open-source, High-quality Data Extraction Tool},  
author={MinerU Contributors},  
howpublished = {\url{https://github.com/opendatalab/MinerU}},  
year={2024}  
}  
==
# ==Star History==
[![](https://camo.githubusercontent.com/0f3cd18dcb159f8ccd265f08f602f7075390803a561590c48872d38019ee8ef0/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d6f70656e646174616c61622f4d696e65725526747970653d44617465)](https://camo.githubusercontent.com/0f3cd18dcb159f8ccd265f08f602f7075390803a561590c48872d38019ee8ef0/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d6f70656e646174616c61622f4d696e65725526747970653d44617465)
# ==Links==
- [==LabelU (A Lightweight Multi-modal Data Annotation Tool)==](https://github.com/opendatalab/labelU)
- [==LabelLLM (An Open-source LLM Dialogue Annotation Platform)==](https://github.com/opendatalab/LabelLLM)
- [==PDF-Extract-Kit (A Comprehensive Toolkit for High-Quality PDF Content Extraction)==](https://github.com/opendatalab/PDF-Extract-Kit)