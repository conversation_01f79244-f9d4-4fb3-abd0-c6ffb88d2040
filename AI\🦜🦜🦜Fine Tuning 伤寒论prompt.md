---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-18T11:13
tags:
  - AI->-Fine-Tuning
  - AI->-OpenAI
  - AI->-Programming
  - AI->-ToDO
Created: 2023-03-02T11:01
Reading Status: Finished
---
\#1. install
pip install —upgrade openai
  
\#2 convert data into JSONL format
```JavaScript
[root@MONSTER:/opt/workspace/ai/AI-Kanbo]# cat 1convert.py
import argparse
import csv
from tqdm import tqdm
# 解析命令行参数
parser = argparse.ArgumentParser(description='Convert CSV file format')
parser.add_argument('input_file', type=str, help='Input CSV file name')
parser.add_argument('output_file', type=str, help='Output CSV file name')
args = parser.parse_args()
# 打开输入和输出文件
with open(args.input_file, newline='') as infile, open(args.output_file, 'w', newline='') as outfile:
    reader = csv.reader(infile, delimiter='|')
    writer = csv.writer(outfile, quoting=csv.QUOTE_ALL)
    # 处理第一行
    header = next(reader)
    writer.writerow(['prompt', 'completion'])
    # 处理后续行
    for i, row in tqdm(enumerate(reader, start=1)):
        # 删除空格和换行符
        row = [cell.strip() for cell in row]
        # 跳过空行或只包含换行符的行
        if not any(row):
            continue
        prompt, completion = row
        writer.writerow([prompt, completion+" \t\t\t"])
        # 显示进度和行号
        if i % 10 == 0:
            tqdm.write(f'Processed {i} rows')
python 1convert.py Kanbo-test.csv Kanbo-test-b.csv
脉象浮、头痛、项部拘急不舒、畏寒 | 太阳病的基本症候特征
太阳病，发热，汗出，畏风，头痛，项部拘急不舒，脉象浮缓的，|就叫做中风。
太阳病，已经发热，或者还未发热，畏冷，头痛，项部拘急不舒，身体疼痛，呕逆，无汗，寸关尺三部脉象均浮紧的，|就叫做伤寒。

"prompt","completion"
"太阳病，发热，汗出，畏风，头痛，项部拘急不舒，脉象浮缓的，","就叫做中风。"
"太阳病，已经发热，或者还未发热，畏冷，头痛，项部拘急不舒，身体疼痛，呕逆，无汗，寸关尺三部脉象均浮紧的，","就叫做伤寒。"
"外感病第一天，邪在太阳，如果脉证静止在太阳未变的，这是疾病未发生传变。如果病人总想呕吐、烦躁不安、脉象数而急疾，","为邪气传里之象，表示病已传变。"
"外感病二三天，已到邪传阳明、少阳之期，如果不见阳明、少阳病见证，而只见太阳病症候的，","表示病未传变。"
python 1convert.py Kanbo-1.csv Kanbo-1-c.csv
python 1convert.py Kanbo-2.csv Kanbo-2-c.csv
cat Kanbo-1-c.csv Kanbo-2-c.csv > dataset2.csv

[root@MONSTER:/opt/workspace/ai/AI-Kanbo]# openai tools fine_tunes.prepare_data -f dataset.csv
Analyzing...
- Based on your file extension, your file is formatted as a CSV file
- Your file contains 442 prompt-completion pairs
- Your data does not contain a common separator at the end of your prompts. Having a separator string appended to the end of the prompt makes it clearer to the fine-tuned model where the completion should begin. See https://platform.openai.com/docs/guides/fine-tuning/preparing-your-dataset for more detail and examples. If you intend to do open-ended generation, then you should leave the prompts empty
- Your data does not contain a common ending at the end of your completions. Having a common ending string appended to the end of the completion makes it clearer to the fine-tuned model where the completion should end. See https://platform.openai.com/docs/guides/fine-tuning/preparing-your-dataset for more detail and examples.
- The completion should start with a whitespace character (` `). This tends to produce better results due to the tokenization we use. See https://platform.openai.com/docs/guides/fine-tuning/preparing-your-dataset for more details
Based on the analysis we will perform the following actions:
- [Necessary] Your format `CSV` will be converted to `JSONL`
- [Recommended] Add a suffix separator ` ->` to all prompts [Y/n]: y
- [Recommended] Add a suffix ending `\n` to all completions [Y/n]: y   <===  !!!!!!!!!!!!!!!!!! 所以可以在dataset里面直接添加你自己的 stop 符号
- [Recommended] Add a whitespace character to the beginning of the completion [Y/n]: y

Your data will be written to a new JSONL file. Proceed [Y/n]: y
Wrote modified file to `dataset_prepared.jsonl`
Feel free to take a look!
Now use that file when fine-tuning:
> openai api fine_tunes.create -t "dataset_prepared.jsonl"
After you’ve fine-tuned a model, remember that your prompt has to end with the indicator string ` ->` for the model to start generating completions, rather than continuing with the prompt. Make sure to include `stop=["\n"]` so that the generated texts ends at the expected place.
Once your model starts training, it'll approximately take 8.51 minutes to train a `curie` model, and less for `ada` and `babbage`. Queue will approximately take half an hour per job ahead of you.
```
  
\#Now use that file when fine-tuning:
```JavaScript
openai api fine_tunes.create -t "dataset_prepared.jsonl" -m curie --suffix "kanbo"
Upload progress: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 107k/107k [00:00<00:00, 101Mit/s]
Uploaded file from dataset_prepared.jsonl: file-5NoXwhICvF4CfZjPrOgqcaIH
Created fine-tune: ft-pPzbAqJHtQWHLjiimyEZZsQa
Streaming events until fine-tuning is complete...
(Ctrl-C will interrupt the stream, but not cancel the fine-tune)
[2023-03-03 15:31:54] Created fine-tune: ft-pPzbAqJHtQWHLjiimyEZZsQa

openai api fine_tunes.follow -i <YOUR_FINE_TUNE_JOB_ID>
openai api fine_tunes.follow -i ft-pPzbAqJHtQWHLjiimyEZZsQa
[2023-03-03 15:31:54] Created fine-tune: ft-pPzbAqJHtQWHLjiimyEZZsQa

# List all created fine-tunes
openai api fine_tunes.list
{
  "data": [
    {
      "created_at": 1677825114,
      "fine_tuned_model": null,
      "hyperparams": {
        "batch_size": null,
        "learning_rate_multiplier": null,
        "n_epochs": 4,
        "prompt_loss_weight": 0.01
      },
      "id": "ft-pPzbAqJHtQWHLjiimyEZZsQa",
      "model": "curie",
      "object": "fine-tune",
      "organization_id": "org-M6fNlldwLsKAUsrbZGauxK5y",
      "result_files": [],
      "status": "pending",
      "training_files": [
        {
          "bytes": 106800,
          "created_at": 1677825114,
          "filename": "dataset_prepared.jsonl",
          "id": "file-5NoXwhICvF4CfZjPrOgqcaIH",
          "object": "file",
          "purpose": "fine-tune",
          "status": "processed",
          "status_details": null
        }
      ],
      "updated_at": 1677825114,
      "validation_files": []
    }
  ],
  "object": "list"
}
# Retrieve the state of a fine-tune. The resulting object includes
# job status (which can be one of pending, running, succeeded, or failed)
# and other information
openai api fine_tunes.get -i <YOUR_FINE_TUNE_JOB_ID>
openai api fine_tunes.get -i ft-pPzbAqJHtQWHLjiimyEZZsQa
"status": "pending",
"fine_tuned_model": null,   <- when job completed, the field will show the name of the model
n_epochs": 4,
!!After completed
{
      "created_at": 1677825426,
      "level": "info",
      "message": "Fine-tune costs $0.84",
      "object": "fine-tune-event"
    },
{
      "created_at": 1677826017,
      "level": "info",
      "message": "Fine-tune succeeded",
      "object": "fine-tune-event"
    }
"fine_tuned_model": "curie:ft-personal:kanbo-2023-03-03-06-46-56",
  "hyperparams": {
    "batch_size": 1,
    "learning_rate_multiplier": 0.1,
    "n_epochs": 4,
    "prompt_loss_weight": 0.01
  },
  "id": "ft-pPzbAqJHtQWHLjiimyEZZsQa",
  "model": "curie",
  "object": "fine-tune",
  "organization_id": "org-M6fNlldwLsKAUsrbZGauxK5y",
  "result_files": [
    {
      "bytes": 97735,
      "created_at": 1677826016,
      "filename": "compiled_results.csv",
      "id": "file-bJMpaIDcAgXxWAZuiVmpGuvq",
      "object": "file",
      "purpose": "fine-tune-results",
      "status": "processed",
      "status_details": null
    }
  ],

# Cancel a job
openai api fine_tunes.cancel -i <YOUR_FINE_TUNE_JOB_ID>
```
  
  
\#Analysis
openai api fine_tunes.results -i <YOUR_FINE_TUNE_JOB_ID>
openai api fine_tunes.results -i ft-pPzbAqJHtQWHLjiimyEZZsQa > compiled_results.csv
- `**step**`: 训练迭代的步数。
- `**elapsed_tokens**`: 已经训练的标记数。在自然语言处理中，标记通常是单词或子单词。
- `**elapsed_examples**`: 已经训练的示例数，通常是输入输出对的数量。
- `**training_loss**`: 训练损失，这是用于衡量模型在训练过程中的误差的指标。训练损失越低，表示模型的拟合效果越好。
- `**training_sequence_accuracy**`: 训练序列精度，是指在所有序列中正确预测的比例。在许多序列分类问题中，序列精度是一个重要的评价指标。
- `**training_token_accuracy**`: 训练标记精度，是指在所有标记中正确预测的比例。在自然语言处理中，标记精度通常是一个重要的评价指标。
例如，这个结果文件中第一行的数据表示，在第一个迭代中，已经训练了1个示例（`**elapsed_examples**`），177个标记（`**elapsed_tokens**`），训练损失为1.58（`**training_loss**`），训练序列精度为0.0（`**training_sequence_accuracy**`），训练标记精度为0.57（`**training_token_accuracy**`）。
这些指标可以帮助您评估模型的性能和训练进程，并在必要时调整模型的超参数，例如学习率、批大小等，以获得更好的结果。
```JavaScript
"fine_tuned_model": "curie:ft-personal:kanbo-2023-03-03-06-46-56",
openai api fine_tunes.results -i ft-pPzbAqJHtQWHLjiimyEZZsQa
step,elapsed_tokens,elapsed_examples,training_loss,training_sequence_accuracy,training_token_accuracy
1,177,1,1.588551483947188,0.0,0.5733333333333334
2,338,2,1.3980693161102955,0.0,0.5098039215686274
3,571,3,0.8217387794230383,0.0,0.65
4,708,4,0.7699680707566372,0.0,0.5901639344262295
cat 2convert.py
import pandas as pd
import sys
# 读取命令行参数，获取结果文件名和输出文件名
result_file = sys.argv[1]
output_file = sys.argv[2]
# 读取结果文件
df = pd.read_csv(result_file)
# 将step列设置为索引
df.set_index('step', inplace=True)
# 将结果写入Excel文件
df.to_excel(output_file)
```
  
![[Untitled.png]]
  
```JavaScript
pip install --upgrade openai wandb
openai wandb sync
\#wandb login  OR Use `wandb login --relogin` to force relogin OR wandb offline
wandb: (1) Create a W&B account
wandb: (2) Use an existing W&B account
wandb: (3) Don't visualize my results
wandb: Enter your choice: 2
wandb: You chose 'Use an existing W&B account'
wandb: Logging into wandb.ai. (Learn how to deploy a W&B server locally: https://wandb.me/wandb-server)
wandb: You can find your API key in your browser here: https://wandb.ai/authorize
wandb: Paste an API key from your profile and hit enter, or press ctrl+c to quit:
wandb: Appending key for api.wandb.ai to your netrc file: /root/.netrc
wandb: Currently logged in as: jbsheng. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.13.10
wandb: Run data is saved locally in /mnt/f/ResearchDirection/AI/AI-Kanbo/wandb/run-20230303_165222-ft-pPzbAqJHtQWHLjiimyEZZsQa
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run ft-pPzbAqJHtQWHLjiimyEZZsQa
wandb: ⭐️ View project at https://wandb.ai/jbsheng/GPT-3
wandb: 🚀 View run at https://wandb.ai/jbsheng/GPT-3/runs/ft-pPzbAqJHtQWHLjiimyEZZsQa
File file-5NoXwhICvF4CfZjPrOgqcaIH could not be retrieved. Make sure you are allowed to download training/validation files
wandb: Waiting for W&B process to finish... (success).
wandb:
wandb: Run history:
wandb:           elapsed_examples ▁▁▁▂▂▂▂▂▂▃▃▃▃▃▄▄▄▄▄▄▅▅▅▅▅▅▆▆▆▆▆▇▇▇▇▇▇███
wandb:             elapsed_tokens ▁▁▁▂▂▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▅▅▅▅▅▅▆▆▆▆▆▇▇▇▇▇▇███
wandb:              training_loss ▃▂▆▃█▄▅▆▅▃▃▃▃▁▇▃▃▂▃▂▁▂▁▁▂▁▆▁▂▁▂▂▂▂▅▁▂▁▁▁
wandb: training_sequence_accuracy ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█▁█▁▁▁▁▁▁▁▁▁▁▁▁
wandb:    training_token_accuracy ▂▃▂▄▁▃▃▃▃▃▃▅▄▇▃▅▄▄▃▆▇▇▇▇▆█▄█▇▅▆▆▇▆▄▇▇█▇▆
wandb:
wandb: Run summary:
wandb:           elapsed_examples 1771.0
wandb:             elapsed_tokens 287803.0
wandb:           fine_tuned_model curie:ft-personal:ka...
wandb:                     status succeeded
wandb:              training_loss 0.03129
wandb: training_sequence_accuracy 1.0
wandb:    training_token_accuracy 1.0
wandb:
wandb: 🚀 View run ft-pPzbAqJHtQWHLjiimyEZZsQa at: https://wandb.ai/jbsheng/GPT-3/runs/ft-pPzbAqJHtQWHLjiimyEZZsQa
wandb: Synced 5 W&B file(s), 0 media file(s), 1 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20230303_165222-ft-pPzbAqJHtQWHLjiimyEZZsQa/logs
🎉 wandb sync completed successfully
```
  
  
\#How to use it
```JavaScript
openai api completions.create -m <FINE_TUNED_MODEL> -p <YOUR_PROMPT>
openai api completions.create -m curie:ft-personal:kanbo-2023-03-03-06-46-56 -p "我发烧了"
openai api completions.create -m davinci:ft-personal:kanbo-2023-03-03-14-51-06 -p "嗜睡，喉咙痛 ->" -M 256 -t 0.7 --stop "\n" --n 3
[root@MONSTER:/opt/workspace/ai/AI-Kanbo]# openai api completions.create -m curie:ft-personal:kanbo-2023-03-03-06-46-56 -p "我发烧了->"
我发烧了-> 就要往发汗方向[root@MONSTER:/opt/workspace/ai/AI-Kanbo]#
import openai
openai.Completion.create(
    model=FINE_TUNED_MODEL,
    prompt=YOUR_PROMPT)

const response = await openai.createCompletion({
  model: FINE_TUNED_MODEL
  prompt: YOUR_PROMPT,
});
```
---
Advanced  
\#1 Hyperparameters  
- `**model**`: The name of the base model to fine-tune. You can select one of "ada", "babbage", "curie", or "davinci". To learn more about these models, see the [Models](https://platform.openai.com/docs/models) documentation.
- `**n_epochs**` - defaults to 4. The number of epochs to train the model for. An epoch refers to one full cycle through the training dataset.
- `**batch_size**` - defaults to ~0.2% of the number of examples in the training set, capped at 256. The batch size is the number of training examples used to train a single forward and backward pass. In general, we've found that larger batch sizes tend to work better for larger datasets.
- `**learning_rate_multiplier**` - defaults to 0.05, 0.1, or 0.2 depending on final `**batch_size**`. The fine-tuning learning rate is the original learning rate used for pretraining multiplied by this multiplier. We recommend experimenting with values in the range 0.02 to 0.2 to see what produces the best results. Empirically, we've found that larger learning rates often perform better with larger batch sizes.
- `**compute_classification_metrics**` - defaults to False. If True, for fine-tuning for classification tasks, computes classification-specific metrics (accuracy, F-1 score, etc) on the validation set at the end of every epoch.
  
  
openai api fine_tunes.create -t "dataset_prepared.jsonl" -m davinci --suffix "kanbo" --batch_size 4 --n_epochs 8  
  
`--compute_classification_metrics \`  
  
`--classification_n_classes 2 \`  
  
`--classification_positive_class <POSITIVE_CLASS_FROM_DATASET>`
\#Validation  
format is same as training data  
- **validation_loss**: loss on the validation batch
- **validation_sequence_accuracy**: the percentage of completions in the validation batch for which the model's predicted tokens matched the true completion tokens exactly. For example, with a `**batch_size**` of 3, if your data contains the completion [[1, 2], [0, 5], [4, 2]] and the model predicted [[1, 1], [0, 5], [4, 2]], this accuracy will be 2/3 = 0.67
- **validation_token_accuracy**: the percentage of tokens in the validation batch that were correctly predicted by the model. For example, with a `**batch_size**` of 3, if your data contains the completion [[1, 2], [0, 5], [4, 2]] and the model predicted [[1, 1], [0, 5], [4, 2]], this accuracy will be 5/6 = 0.83
openai api fine_tunes.create -t <TRAIN_FILE_ID_OR_PATH> \  
-v <VALIDATION_FILE_ID_OR_PATH> \  
-m <MODEL>  
  
```JavaScript
python 1convert.py validate.csv validate-b.csv
openai tools fine_tunes.prepare_data -f validate-b.csv
openai api fine_tunes.create -t "dataset_prepared.jsonl" -m davinci --suffix "kanbo" --batch_size 4 --n_epochs 8
--compute_classification_metrics \
--classification_n_classes 2 \
--classification_positive_class <POSITIVE_CLASS_FROM_DATASET>
openai api fine_tunes.create -t <TRAIN_FILE_ID_OR_PATH> \
-v <VALIDATION_FILE_ID_OR_PATH> \
-m <MODEL>
\#curie:ft-personal:kanbo-2023-03-03-06-46-56 batchsize 1 n_epochs 4
openai api fine_tunes.create -t "dataset_prepared.jsonl" -m curie --suffix "kanbo"   
\#davinci:ft-personal:kanbo-2023-03-03-11-41-54 batchsize 1 n_epochs 2
openai api fine_tunes.create -t "dataset_prepared.jsonl" -v "validate-b_prepared.jsonl" -m davinci --suffix "kanbo" --n_epochs 2
\#davinci:ft-personal:kanbo-2023-03-03-14-51-06 batchsize 4 n_epochs 8
openai api fine_tunes.create -t "dataset_prepared.jsonl" -v "validate-b_prepared.jsonl" -m davinci --suffix "kanbo" --batch_size 4 --n_epochs 8
\#davinci:ft-personal:kanbo-2023-03-04-05-19-36 batchsize 4 n_epochs 2
openai api fine_tunes.create -t "dataset2_prepared.jsonl" -v "validate-b_prepared.jsonl" -m davinci --suffix "kanbo" --batch_size 4 --n_epochs 2

openai api fine_tunes.follow -i ft-eYmecn1EMK6j0CWankcqFD2v
openai api fine_tunes.list
openai api fine_tunes.get -i ft-eYmecn1EMK6j0CWankcqFD2v
{
      "created_at": **********,
      "level": "error",
      "message": "Fine-tune failed. Fine-tune can not exceed $15 during free trial",
      "object": "fine-tune-event"
    }
Looks failed due to free trail account
openai api models.delete -i <FINE_TUNED_MODEL>
openai api fine_tunes.get -i ft-giTAsUAk5nwVWLOlaeyOkbmM
openai api fine_tunes.get -i ft-AMvpMz2PzYO8pZ0TQSDjdXHu
https://wandb.ai/jbsheng/GPT-3  <- create account there
openai wandb sync
openai api completions.create -m <FINE_TUNED_MODEL> -p <YOUR_PROMPT>
```
  
```JavaScript
// @version 0.0.5 新增 logger 函数和加密事件的输出
const aircode = require("aircode");
const lark = require("@larksuiteoapi/node-sdk");
var axios = require("axios");
const EventDB = aircode.db.table("event");
let _history = "";
// 如果你不想配置环境变量，或环境变量不生效，则可以把结果填写在每一行最后的 "" 内部
const FEISHU_APP_ID = process.env.APPID || ""; // 飞书的应用 ID
const FEISHU_APP_SECRET = process.env.SECRET || ""; // 飞书的应用的 Secret
const FEISHU_BOTNAME = process.env.BOTNAME || ""; // 飞书机器人的名字
const OPENAI_KEY = process.env.KEY || ""; // OpenAI 的 Key
const OPENAI_MODEL = process.env.MODEL || "text-davinci-003"; // 使用的模型
const OPENAI_MAX_TOKEN = process.env.MAX_TOKEN || 2048; // 最大 token 的值
const client = new lark.Client({
  appId: FEISHU_APP_ID,
  appSecret: FEISHU_APP_SECRET,
  disableTokenCache: false,
});
// 日志辅助函数，请贡献者使用此函数打印关键日志
function logger(param) {
  console.warn(`[CF]`, param);
}
// 回复消息
async function reply(messageId, content) {
  try{
    return await client.im.message.reply({
    path: {
      message_id: messageId,
    },
    data: {
      content: JSON.stringify({
        text: content,
      }),
      msg_type: "text",
    },
  });
  } catch(e){
    logger("send message to feishu error",e,messageId,content);
  }
}
// 通过 OpenAI API 获取回复
async function getOpenAIReply(content) {
  if (content === "renew") {
    _history = "";
    return "好的。我会忘记前面的谈话";
  }
  
  _history = _history + "\nHuman: " + content.trim() ;
  console.log(_history);
  
  var data = JSON.stringify({
    model: OPENAI_MODEL,
    prompt: _history,
    max_tokens: OPENAI_MAX_TOKEN,
    temperature: 0.9,
    frequency_penalty: 0.0,
    presence_penalty: 0.6,
    top_p: 1,
    stop: [" Human:", " AI:"],
  });
  var config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.openai.com/v1/completions",
    headers: {
      Authorization: `Bearer ${OPENAI_KEY}`,
      "Content-Type": "application/json",
    },
    data: data,
  };
  try{
      const response = await axios(config);
      //history.push({ text:response.data.choices[0].text.replace("\n\n", "") })
      // 去除多余的换行
      //_history = _history + response.data.choices[0].text.replace("\n\n", "") ;
      //return response.data.choices[0].text.replace("\n\n", "");
      _history = _history + "\nAI: " + response.data.choices[0].text;
      return response.data.choices[0].text;
    
  }catch(e){
     logger(e)
     return "请求失败";
  }
}
// 自检函数
async function doctor() {
  if (FEISHU_APP_ID === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置飞书应用的 AppID，请检查 & 部署后重试",
        en_US:
          "Here is no FeiSHu APP id, please check & re-Deploy & call again",
      },
    };
  }
  if (!FEISHU_APP_ID.startsWith("cli_")) {
    return {
      code: 1,
      message: {
        zh_CN:
          "你配置的飞书应用的 AppID 是错误的，请检查后重试。飞书应用的 APPID 以 cli_ 开头。",
        en_US:
          "Your FeiShu App ID is Wrong, Please Check and call again. FeiShu APPID must Start with cli",
      },
    };
  }
  if (FEISHU_APP_SECRET === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置飞书应用的 Secret，请检查 & 部署后重试",
        en_US:
          "Here is no FeiSHu APP Secret, please check & re-Deploy & call again",
      },
    };
  }
  if (FEISHU_BOTNAME === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置飞书应用的名称，请检查 & 部署后重试",
        en_US:
          "Here is no FeiSHu APP Name, please check & re-Deploy & call again",
      },
    };
  }
  if (OPENAI_KEY === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置 OpenAI 的 Key，请检查 & 部署后重试",
        en_US: "Here is no OpenAI Key, please check & re-Deploy & call again",
      },
    };
  }
  if (!OPENAI_KEY.startsWith("sk-")) {
    return {
      code: 1,
      message: {
        zh_CN:
          "你配置的 OpenAI Key 是错误的，请检查后重试。飞书应用的 APPID 以 cli_ 开头。",
        en_US:
          "Your OpenAI Key is Wrong, Please Check and call again. FeiShu APPID must Start with cli",
      },
    };
  }
  return {
    code: 0,
    message: {
      zh_CN:
        "✅ Configuration is correct, you can use this bot in your FeiShu App",
      en_US:
        "✅ 配置成功，接下来你可以在飞书应用当中使用机器人来完成你的工作。",
    },
    meta: {
      FEISHU_APP_ID,
      OPENAI_MODEL,
      OPENAI_MAX_TOKEN,
      FEISHU_BOTNAME,
    },
  };
}
module.exports = async function (params, context) {
  // 如果存在 encrypt 则说明配置了 encrypt key
  if (params.encrypt) {
    logger("user enable encrypt key");
    return {
      code: 1,
      message: {
        zh_CN: "你配置了 Encrypt Key，请关闭该功能。",
        en_US: "You have open Encrypt Key Feature, please close it.",
      },
    };
  }
  // 处理飞书开放平台的服务端校验
  if (params.type === "url_verification") {
    logger("deal url_verification");
    return {
      challenge: params.challenge,
    };
  }
  // 自检查逻辑
  if (!params.hasOwnProperty("header") || context.trigger === "DEBUG") {
    logger("enter doctor");
    return await doctor();
  }
  // 处理飞书开放平台的事件回调
  if ((params.header.event_type === "im.message.receive_v1")) {
    let eventId = params.header.event_id;
    let messageId = params.event.message.message_id;
    // 对于同一个事件，只处理一次
    const count = await EventDB.where({ event_id: eventId }).count();
    if (count != 0) {
      logger("deal repeat event");
      return { code: 1 };
    }
    await EventDB.save({ event_id: eventId });
    // 私聊直接回复
    if (params.event.message.chat_type === "p2p") {
      // 不是文本消息，不处理
      if (params.event.message.message_type != "text") {
        await reply(messageId, "暂不支持其他类型的提问");
        logger("skip and reply not support");
        return { code: 0 };
      }
      // 是文本消息，直接回复
      const userInput = JSON.parse(params.event.message.content);
      const openaiResponse = await getOpenAIReply(userInput.text);
      await reply(messageId, openaiResponse);
      return { code: 0 };
    }
    // 群聊，需要 @ 机器人
    if (params.event.message.chat_type === "group") {
      // 这是日常群沟通，不用管
      if (
        !params.event.message.mentions ||
        params.event.message.mentions.length === 0
      ) {
        logger("not process message without mention");
        return { code: 0 };
      }
      // 没有 mention 机器人，则退出。
      if (params.event.message.mentions[0].name != FEISHU_BOTNAME) {
        logger("bot name not equal first mention name ");
        return { code: 0 };
      }
      const userInput = JSON.parse(params.event.message.content);
      const question = userInput.text.replace("@_user_1", "");
      const openaiResponse = await getOpenAIReply(question);
      await reply(messageId, openaiResponse);
      return { code: 0 };
    }
  }
  logger("return without other log");
  return {
    code: 2,
  };
};
```
  
```JavaScript
// @version 0.0.5 新增 logger 函数和加密事件的输出
const aircode = require("aircode");
const lark = require("@larksuiteoapi/node-sdk");
var axios = require("axios");
const EventDB = aircode.db.table("event");
let _history = "";
// 如果你不想配置环境变量，或环境变量不生效，则可以把结果填写在每一行最后的 "" 内部
const FEISHU_APP_ID = process.env.APPID || ""; // 飞书的应用 ID
const FEISHU_APP_SECRET = process.env.SECRET || ""; // 飞书的应用的 Secret
const FEISHU_BOTNAME = process.env.BOTNAME || ""; // 飞书机器人的名字
const OPENAI_KEY = process.env.KEY || ""; // OpenAI 的 Key
//const OPENAI_MODEL = process.env.MODEL || "davinci:ft-personal:kanbo-2023-03-03-14-51-06"; // 使用的模型
const OPENAI_MODEL = process.env.MODEL || "davinci:ft-personal:kanbo-2023-03-04-05-19-36"; // 使用的模型stop=[\t\t\t]
//const OPENAI_MODEL = process.env.MODEL || "text-davinci-003"; // 使用的模型
const OPENAI_MAX_TOKEN = process.env.MAX_TOKEN || 1750; // 最大 token 的值
const client = new lark.Client({
  appId: FEISHU_APP_ID,
  appSecret: FEISHU_APP_SECRET,
  disableTokenCache: false,
});
// 日志辅助函数，请贡献者使用此函数打印关键日志
function logger(param) {
  console.warn(`[CF]`, param);
}
// 回复消息
async function reply(messageId, content) {
  try{
    return await client.im.message.reply({
    path: {
      message_id: messageId,
    },
    data: {
      content: JSON.stringify({
        text: content,
      }),
      msg_type: "text",
    },
  });
  } catch(e){
    logger("send message to feishu error",e,messageId,content);
  }
}
// 通过 OpenAI API 获取回复
async function getOpenAIReply(content) {
  if (content === "renew") {
    _history = "";
    return "好的。我会忘记前面的谈话";
  }
  
  _history = content.trim() + " ->";
  //_history = _history + "\nHuman: " + content.trim() + " ->";
  console.log(_history);
  
  var data = JSON.stringify({
    model: OPENAI_MODEL,
    prompt: _history,
    max_tokens: OPENAI_MAX_TOKEN,
    temperature: 0.7,
    frequency_penalty: 0.0,
    presence_penalty: 0.0,
    top_p: 1,
    best_of: 3,
    n: 3,
    stop: ["\t\t\t"],
//    stop: ["\n"], //change to \t\t\t
  });
  var config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.openai.com/v1/completions",
    headers: {
      Authorization: `Bearer ${OPENAI_KEY}`,
      "Content-Type": "application/json",
    },
    data: data,
  };
  try{
      const response = await axios(config);
      //history.push({ text:response.data.choices[0].text.replace("\n\n", "") })
      // 去除多余的换行
      //_history = _history + response.data.choices[0].text.replace("\n\n", "") ;
      //return response.data.choices[0].text.replace("\n\n", "");
      //_history = _history + "\nAI: " + response.data.choices[0].text + "\n\n ";
       _history = response.data.choices[0].text + "\n或\n" + response.data.choices[1].text + "\n或\n" + response.data.choices[2].text ;
       return  _history;
    
  }catch(e){
     logger(e)
     return "请求失败";
  }
}
// 自检函数
async function doctor() {
  if (FEISHU_APP_ID === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置飞书应用的 AppID，请检查 & 部署后重试",
        en_US:
          "Here is no FeiSHu APP id, please check & re-Deploy & call again",
      },
    };
  }
  if (!FEISHU_APP_ID.startsWith("cli_")) {
    return {
      code: 1,
      message: {
        zh_CN:
          "你配置的飞书应用的 AppID 是错误的，请检查后重试。飞书应用的 APPID 以 cli_ 开头。",
        en_US:
          "Your FeiShu App ID is Wrong, Please Check and call again. FeiShu APPID must Start with cli",
      },
    };
  }
  if (FEISHU_APP_SECRET === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置飞书应用的 Secret，请检查 & 部署后重试",
        en_US:
          "Here is no FeiSHu APP Secret, please check & re-Deploy & call again",
      },
    };
  }
  if (FEISHU_BOTNAME === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置飞书应用的名称，请检查 & 部署后重试",
        en_US:
          "Here is no FeiSHu APP Name, please check & re-Deploy & call again",
      },
    };
  }
  if (OPENAI_KEY === "") {
    return {
      code: 1,
      message: {
        zh_CN: "你没有配置 OpenAI 的 Key，请检查 & 部署后重试",
        en_US: "Here is no OpenAI Key, please check & re-Deploy & call again",
      },
    };
  }
  if (!OPENAI_KEY.startsWith("sk-")) {
    return {
      code: 1,
      message: {
        zh_CN:
          "你配置的 OpenAI Key 是错误的，请检查后重试。飞书应用的 APPID 以 cli_ 开头。",
        en_US:
          "Your OpenAI Key is Wrong, Please Check and call again. FeiShu APPID must Start with cli",
      },
    };
  }
  return {
    code: 0,
    message: {
      zh_CN:
        "✅ Configuration is correct, you can use this bot in your FeiShu App",
      en_US:
        "✅ 配置成功，接下来你可以在飞书应用当中使用机器人来完成你的工作。",
    },
    meta: {
      FEISHU_APP_ID,
      OPENAI_MODEL,
      OPENAI_MAX_TOKEN,
      FEISHU_BOTNAME,
    },
  };
}
module.exports = async function (params, context) {
  // 如果存在 encrypt 则说明配置了 encrypt key
  if (params.encrypt) {
    logger("user enable encrypt key");
    return {
      code: 1,
      message: {
        zh_CN: "你配置了 Encrypt Key，请关闭该功能。",
        en_US: "You have open Encrypt Key Feature, please close it.",
      },
    };
  }
  // 处理飞书开放平台的服务端校验
  if (params.type === "url_verification") {
    logger("deal url_verification");
    return {
      challenge: params.challenge,
    };
  }
  // 自检查逻辑
  if (!params.hasOwnProperty("header") || context.trigger === "DEBUG") {
    logger("enter doctor");
    return await doctor();
  }
  // 处理飞书开放平台的事件回调
  if ((params.header.event_type === "im.message.receive_v1")) {
    let eventId = params.header.event_id;
    let messageId = params.event.message.message_id;
    // 对于同一个事件，只处理一次
    const count = await EventDB.where({ event_id: eventId }).count();
    if (count != 0) {
      logger("deal repeat event");
      return { code: 1 };
    }
    await EventDB.save({ event_id: eventId });
    // 私聊直接回复
    if (params.event.message.chat_type === "p2p") {
      // 不是文本消息，不处理
      if (params.event.message.message_type != "text") {
        await reply(messageId, "暂不支持其他类型的提问");
        logger("skip and reply not support");
        return { code: 0 };
      }
      // 是文本消息，直接回复
      const userInput = JSON.parse(params.event.message.content);
      const openaiResponse = await getOpenAIReply(userInput.text);
      await reply(messageId, openaiResponse);
      return { code: 0 };
    }
    // 群聊，需要 @ 机器人
    if (params.event.message.chat_type === "group") {
      // 这是日常群沟通，不用管
      if (
        !params.event.message.mentions ||
        params.event.message.mentions.length === 0
      ) {
        logger("not process message without mention");
        return { code: 0 };
      }
      // 没有 mention 机器人，则退出。
      if (params.event.message.mentions[0].name != FEISHU_BOTNAME) {
        logger("bot name not equal first mention name ");
        return { code: 0 };
      }
      const userInput = JSON.parse(params.event.message.content);
      const question = userInput.text.replace("@_user_1", "");
      const openaiResponse = await getOpenAIReply(question);
      await reply(messageId, openaiResponse);
      return { code: 0 };
    }
  }
  logger("return without other log");
  return {
    code: 2,
  };
};
```