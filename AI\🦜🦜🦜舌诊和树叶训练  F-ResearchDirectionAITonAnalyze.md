---
DocFlag:
  - Reference
  - Tested
Updated: 2024-04-06T21:10
tags:
  - AI->-CV
  - AI->-Fine-Tuning
  - AI->-Image
  - AI->-Model
Created: 2023-07-19T12:09
---
```JavaScript
使用SE_ResNeXt预训练模型训练图像分类模型，此模型是对舌苔的颜色进行分类，
图像数据文件在/data下的各子目录里，lable即为子目录目录名, 同一分类的图像都在同一子目录里。
训练时候要用到momentum strategy和RMSProp，SGD，Adam
下面是一些参数：
"input_size": [3, 256, 256],
"num_epochs": 200,
"train_batch_size": 64,
"mean_rgb": [127.5, 127.5, 127.5],  # 常用图片的三通道均值，通常来说需要先对训练数据做统计，此处仅取中间值4
    "image_enhance_strategy": {  # 图像增强相关策略
        "need_distort": True,  # 是否启用图像颜色增强
        "need_rotate": True,   # 是否需要增加随机角度
        "need_crop": True,      # 是否要增加裁剪
        "need_flip": True,      # 是否要增加水平随机翻转
        "hue_prob": 0.5,
        "hue_delta": 18,
        "contrast_prob": 0.5,
        "contrast_delta": 0.5,
        "saturation_prob": 0.5,
        "saturation_delta": 0.5,
        "brightness_prob": 0.5,
        "brightness_delta": 0.125
    },
    "early_stop": {
        "sample_frequency": 50,
        "successive_limit": 3,
        "good_acc1": 0.99
    },
    "rsm_strategy": {
        "learning_rate": 0.001,
        "lr_epochs": [20, 40, 60, 80, 100],
        "lr_decay": [1, 0.5, 0.25, 0.1, 0.01, 0.002]
    },
    "momentum_strategy": {
        "learning_rate": 0.001,
        "lr_epochs": [20, 40, 60, 80, 100],
        "lr_decay": [1, 0.5, 0.25, 0.1, 0.01, 0.002]
    },
    "sgd_strategy": {
        "learning_rate": 0.001,
        "lr_epochs": [20, 40, 60, 80, 100],
        "lr_decay": [1, 0.5, 0.25, 0.1, 0.01, 0.002]
    },
    "adam_strategy": {
        "learning_rate": 0.002
    }
下面是一些图像处理函数
def resize_img(img, target_size):
    """
    强制缩放图片
    :param img:
    :param target_size:
    :return:
    """
    target_size = input_size
    img = img.resize((target_size[1], target_size[2]), Image.BILINEAR)
    return img

def random_crop(img, scale=[0.08, 1.0], ratio=[3. / 4., 4. / 3.]):
    aspect_ratio = math.sqrt(np.random.uniform(*ratio))
    w = 1. * aspect_ratio
    h = 1. / aspect_ratio
    bound = min((float(img.size[0]) / img.size[1]) / (w**2),
                (float(img.size[1]) / img.size[0]) / (h**2))
    scale_max = min(scale[1], bound)
    scale_min = min(scale[0], bound)
    target_area = img.size[0] * img.size[1] * np.random.uniform(scale_min,
                                                                scale_max)
    target_size = math.sqrt(target_area)
    w = int(target_size * w)
    h = int(target_size * h)
    i = np.random.randint(0, img.size[0] - w + 1)
    j = np.random.randint(0, img.size[1] - h + 1)
    img = img.crop((i, j, i + w, j + h))
    img = img.resize((train_parameters['input_size'][1], train_parameters['input_size'][2]), Image.BILINEAR)
    return img

def rotate_image(img):
    """
    图像增强，增加随机旋转角度
    """
    angle = np.random.randint(-14, 15)
    img = img.rotate(angle)
    return img

def random_brightness(img):
    """
    图像增强，亮度调整
    :param img:
    :return:
    """
    prob = np.random.uniform(0, 1)
    if prob < train_parameters['image_enhance_strategy']['brightness_prob']:
        brightness_delta = train_parameters['image_enhance_strategy']['brightness_delta']
        delta = np.random.uniform(-brightness_delta, brightness_delta) + 1
        img = ImageEnhance.Brightness(img).enhance(delta)
    return img

def random_contrast(img):
    """
    图像增强，对比度调整
    :param img:
    :return:
    """
    prob = np.random.uniform(0, 1)
    if prob < train_parameters['image_enhance_strategy']['contrast_prob']:
        contrast_delta = train_parameters['image_enhance_strategy']['contrast_delta']
        delta = np.random.uniform(-contrast_delta, contrast_delta) + 1
        img = ImageEnhance.Contrast(img).enhance(delta)
    return img

def random_saturation(img):
    """
    图像增强，饱和度调整
    :param img:
    :return:
    """
    prob = np.random.uniform(0, 1)
    if prob < train_parameters['image_enhance_strategy']['saturation_prob']:
        saturation_delta = train_parameters['image_enhance_strategy']['saturation_delta']
        delta = np.random.uniform(-saturation_delta, saturation_delta) + 1
        img = ImageEnhance.Color(img).enhance(delta)
    return img

def random_hue(img):
    """
    图像增强，色度调整
    :param img:
    :return:
    """
    prob = np.random.uniform(0, 1)
    if prob < train_parameters['image_enhance_strategy']['hue_prob']:
        hue_delta = train_parameters['image_enhance_strategy']['hue_delta']
        delta = np.random.uniform(-hue_delta, hue_delta)
        img_hsv = np.array(img.convert('HSV'))
        img_hsv[:, :, 0] = img_hsv[:, :, 0] + delta
        img = Image.fromarray(img_hsv, mode='HSV').convert('RGB')
    return img

def distort_color(img):
    """
    概率的图像增强
    :param img:
    :return:
    """
    prob = np.random.uniform(0, 1)
    # Apply different distort order
    if prob < 0.35:
        img = random_brightness(img)
        img = random_contrast(img)
        img = random_saturation(img)
        img = random_hue(img)
    elif prob < 0.7:
        img = random_brightness(img)
        img = random_saturation(img)
        img = random_hue(img)
        img = random_contrast(img)
    return img
def custom_image_reader(file_list, data_dir, mode):
    """
    自定义用户图片读取器，先初始化图片种类，数量
    :param file_list:
    :param data_dir:
    :param mode:
    :return:
    """
    with codecs.open(file_list) as flist:
        lines = [line.strip() for line in flist]
    def reader():
        np.random.shuffle(lines)
        for line in lines:
            if mode == 'train' or mode == 'val':
                img_path, label = line.split()
                img = Image.open(img_path)
                try:
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    if train_parameters['image_enhance_strategy']['need_distort'] == True:
                        img = distort_color(img)
                    if train_parameters['image_enhance_strategy']['need_rotate'] == True:
                        img = rotate_image(img)
                    if train_parameters['image_enhance_strategy']['need_crop'] == True:
                        img = random_crop(img, train_parameters['input_size'])
                    if train_parameters['image_enhance_strategy']['need_flip'] == True:
                        mirror = int(np.random.uniform(0, 2))
                        if mirror == 1:
                            img = img.transpose(Image.FLIP_LEFT_RIGHT)
                    # HWC--->CHW && normalized
                    img = np.array(img).astype('float32')
                    img -= train_parameters['mean_rgb']
                    img = img.transpose((2, 0, 1))  # HWC to CHW
                    img *= 0.007843                 # 像素值归一化
                    yield img, int(label)
                except Exception as e:
                    pass                            # 以防某些图片读取处理出错，加异常处理
            elif mode == 'test':
                img_path = os.path.join(data_dir, line)
                img = Image.open(img_path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                img = resize_img(img, train_parameters['input_size'])
                # HWC--->CHW && normalized
                img = np.array(img).astype('float32')
                img -= train_parameters['mean_rgb']
                img = img.transpose((2, 0, 1))  # HWC to CHW
                img *= 0.007843  # 像素值归一化
                yield img
    return reader
\#Git source code
git clone https://github.com/Fafa-DL/Awesome-Backbones.git
\#Turtorial
https://www.bilibili.com/video/BV1SY411P7Nd
https://blog.csdn.net/zzh516451964zzh/article/details/124478347?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-124478347-blog-103081896.235^v38^pc_relevant_sort_base1&spm=1001.2101.3001.4242.1&utm_relevant_index=3
\#Reference
https://blog.csdn.net/qq_35200861/article/details/103081896
https://huggingface.co/docs/timm/models/seresnext
https://github.com/HiKapok/TF-SENet/blob/master/se_resnext.py
https://github.com/bruinxiong/SENet.mxnet
#另一个和舌有关训练程序和数据集
https://aistudio.baidu.com/aistudio/projectdetail/5632371?channelType=0&channel=0
\#Tool
https://github.com/Fafa-DL/Image-Augmentation
#迁移训练含义
https://blog.csdn.net/MrSong007/article/details/94546876
#中医诊断
https://zhuanlan.zhihu.com/p/183818636
def show_analyze(result):
    pred_class = result.get('pred_class', '')
    
    # 假设关键字和输出的对应关系是这样的字典
    keyword_dict = {
        '淡白舌': '淡白舌多见于阳虚寒盛、气虚血少之证。',
        '淡红舌': '舌色淡红是血气正常表现。',
        '红舌': '红舌见于外感热盛或阴虚火旺之证。',
        '绛舌': '绛舌为热盛之象。在外感病为邪热入营的标志；在内伤杂病阴虚火旺重症亦较多见。',
        '青紫舌': '舌色青紫，由气血运行不畅所致。若舌色红绛而紫，舌尖起刺，提示痰热壅滞。',
        '灰黑苔': '灰黑苔多见于阳虚寒湿、痰饮等阴邪蕴结中焦，日久不化的病证。',
        '白苔': '白苔多为水湿、痰饮内停。',
        '黄苔': '黄苔提示内热炽盛，湿邪化燥。',
        # 添加更多关键字...
    }
    output_messages = []
    for keyword, message in keyword_dict.items():
        if keyword in pred_class:
            output_messages.append(message)
    # 把所有的信息合并成一个字符串
    output_str = "\n".join(output_messages)
    print(output_str)

======================================================================================================
# 其中[name]改成自己的环境名，如[name]->torch，conda create -n torch python=3.6
conda create -n tongue python=3.6 
use vs 2022 dos prompt
pip install torch==1.7.1+cu110 torchvision==0.8.2+cu110 torchaudio==0.7.2 -f https://download.pytorch.org/whl/torch_stable.html
cd F:\ResearchDirection\AI\TonAnalyze\Awesome-Backbones
pip install -r requirements.txt
wget https://download.openmmlab.com/mmclassification/v0/mobilenet_v3/convert/mobilenet_v3_small-8427ecf0.pth
download and copy to 
F:\ResearchDirection\AI\TonAnalyze\Awesome-Backbones\datas
python tools/single_test.py datas/cat-dog.png models/mobilenet/mobilenet_v3_small.py --classes-map datas/imageNet1kAnnotation.txt
======================================================================================================
# 遇上问题，采用下面方法单独安装opencv, 然后再remove requirements.txt里的项目，然后pip install -r requirements.txt
      cmake_install_dir,
    File "setup.py", line 445, in _classify_installed_files_override
      raise Exception("Not found: '%s'" % relpath_re)
  Exception: Not found: 'python/cv2/py.typed'
  ----------------------------------------
  ERROR: Failed building wheel for opencv-python-headless
Failed to build opencv-python-headless
ERROR: Could not build wheels for opencv-python-headless which use PEP 517 and cannot be installed directly
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python
https://blog.csdn.net/yangzijiangtou/article/details/106043096
https://stackoverflow.com/questions/63732353/error-could-not-build-wheels-for-opencv-python-which-use-pep-517-and-cannot-be

pip install --upgrade pip setuptools wheel
pip3 install opencv-python==*********
======================================================================================================
cat F:\ResearchDirection\AI\TonAnalyze\Awesome-Backbones\datas\annotations.txt
红舌灰黑苔 0
绛舌白苔 1
绛舌黄苔 2
绛舌灰黑苔 3
青紫舌白苔 4
青紫舌黄苔 5
青紫舌灰黑苔 6
淡白舌灰黑苔 7
淡白舌白苔 8
淡白舌黄苔 9
淡红舌白苔 10
淡红舌黄苔 11
淡红舌灰黑苔 12
红舌白苔 13
红舌黄苔 14
cat F:\ResearchDirection\AI\TonAnalyze\Awesome-Backbones\tools\split_data.py
    init_dataset = f"F:\\ResearchDirection\\AI\\TonAnalyze\\data"
    new_dataset = 'datasets'
python tools/split_data.py
python tools/get_annotation.py
# adjust parameters
# explanation
# https://github.com/Fafa-DL/Awesome-Backbones/blob/main/datas/docs/Configs_description.md
F:\ResearchDirection\AI\TonAnalyze\Awesome-Backbones\models\seresnext\seresnext101.py
python tools/train.py models/seresnext/seresnext101.py
    train = dict(
        pretrained_flag = True,
        pretrained_weights = './datas/Last_Epoch500.pth',
        freeze_flag = False,
        freeze_layers = ('backbone',),
        epoches = 700, # increase epoches and run resume-from
    ),
python tools/train.py models/seresnext/seresnext101.py --resume-from ./logs/SEResNeXt/2023-07-18-15-21-33/Last_Epoch500.pth
pip install sklearn
python tools/evaluation.py models/seresnext/seresnext101.py

python tools/single_test.py datas/693.jpg models/seresnext/seresnext101.py

import wandb
    wandb.init(
       # set the wandb project where this run will be logged
       project="TonAnalyze",
    )
        wandb.log({
            "loss": runner.get('best_train_loss'),
            "acc": runner.get('best_val_acc'),
        })
 wandb.finish()
+Classes Results-----------+--------+----------+-------------------+
| Classes      | Precision | Recall | F1 Score | Average Precision |
+--------------+-----------+--------+----------+-------------------+
| 红舌灰黑苔   | 33.33     | 42.86  | 37.50    | 38.55             |
| 绛舌白苔     | 19.35     | 17.14  | 18.18    | 23.33             |
| 绛舌黄苔     | 27.03     | 28.57  | 27.78    | 28.09             |
| 绛舌灰黑苔   | 20.00     | 13.33  | 16.00    | 19.75             |
| 青紫舌白苔   | 52.31     | 41.98  | 46.58    | 51.73             |
| 青紫舌黄苔   | 42.34     | 51.65  | 46.53    | 44.48             |
| 青紫舌灰黑苔 | 55.26     | 50.00  | 52.50    | 55.69             |
| 淡白舌灰黑苔 | 20.00     | 100.00 | 33.33    | 25.00             |
| 淡白舌白苔   | 27.27     | 25.00  | 26.09    | 28.38             |
| 淡白舌黄苔   | 33.33     | 25.00  | 28.57    | 24.08             |
| 淡红舌白苔   | 24.32     | 28.12  | 26.09    | 28.43             |
| 淡红舌黄苔   | 25.93     | 25.93  | 25.93    | 28.48             |
| 淡红舌灰黑苔 | 33.33     | 14.29  | 20.00    | 22.40             |
| 红舌白苔     | 11.11     | 18.18  | 13.79    | 8.67              |
| 红舌黄苔     | 0.00      | 0.00   | 0.00     | 4.91              |
+--------------+-----------+--------+----------+-------------------+
+Total Results----------+----------------+-------------+---------------+
| Top-1 Acc | Top-5 Acc | Mean Precision | Mean Recall | Mean F1 Score |
+-----------+-----------+----------------+-------------+---------------+
| 35.92     | 79.37     | 28.33          | 32.14       | 27.92         |
+-----------+-----------+----------------+-------------+---------------+
+Confusion Matrix-----------+----------+----------+------------+------------+------------+--------------+--------------+------------+------------+------------+------------+--------------+----------+----------+
|              | 红舌灰黑苔 | 绛舌白苔 | 绛舌黄苔 | 绛舌灰黑苔 | 青紫舌白苔 | 青紫舌黄苔 | 青紫舌灰黑苔 | 淡白舌灰黑苔 | 淡白舌白苔 | 淡白舌黄苔 | 淡红舌白苔 | 淡红舌黄苔 | 淡红舌灰黑苔 | 红舌白苔 | 红舌黄苔 |
+--------------+------------+----------+----------+------------+------------+------------+--------------+--------------+------------+------------+------------+------------+--------------+----------+----------+
| 红舌灰黑苔   | 3          | 0        | 0        | 2          | 0          | 0          | 0            | 0            | 0          | 0          | 0          | 1          | 1            | 0        | 0        |
| 绛舌白苔     | 1          | 6        | 3        | 3          | 1          | 7          | 1            | 0            | 0          | 1          | 4          | 3          | 0            | 4        | 1        |
| 绛舌黄苔     | 1          | 2        | 10       | 0          | 2          | 6          | 1            | 0            | 2          | 1          | 4          | 3          | 1            | 1        | 1        |
| 绛舌灰黑苔   | 2          | 0        | 3        | 2          | 0          | 2          | 4            | 1            | 0          | 0          | 0          | 1          | 0            | 0        | 0        |
| 青紫舌白苔   | 0          | 4        | 5        | 1          | 34         | 30         | 2            | 1            | 1          | 0          | 1          | 1          | 0            | 1        | 0        |
| 青紫舌黄苔   | 0          | 6        | 3        | 0          | 18         | 47         | 4            | 1            | 2          | 2          | 3          | 4          | 0            | 1        | 0        |
| 青紫舌灰黑苔 | 1          | 0        | 1        | 1          | 4          | 10         | 21           | 1            | 0          | 0          | 2          | 0          | 0            | 1        | 0        |
| 淡白舌灰黑苔 | 0          | 0        | 0        | 0          | 0          | 0          | 0            | 1            | 0          | 0          | 0          | 0          | 0            | 0        | 0        |
| 淡白舌白苔   | 0          | 0        | 0        | 1          | 2          | 1          | 0            | 0            | 3          | 0          | 3          | 2          | 0            | 0        | 0        |
| 淡白舌黄苔   | 0          | 0        | 0        | 0          | 2          | 2          | 0            | 0            | 0          | 2          | 2          | 0          | 0            | 0        | 0        |
| 淡红舌白苔   | 0          | 6        | 4        | 0          | 1          | 4          | 2            | 0            | 2          | 0          | 9          | 1          | 0            | 3        | 0        |
| 淡红舌黄苔   | 0          | 3        | 4        | 0          | 1          | 1          | 2            | 0            | 1          | 0          | 5          | 7          | 0            | 2        | 1        |
| 淡红舌灰黑苔 | 1          | 0        | 0        | 0          | 0          | 0          | 1            | 0            | 0          | 0          | 2          | 1          | 1            | 1        | 0        |
| 红舌白苔     | 0          | 2        | 2        | 0          | 0          | 0          | 0            | 0            | 0          | 0          | 2          | 2          | 0            | 2        | 1        |
| 红舌黄苔     | 0          | 2        | 2        | 0          | 0          | 1          | 0            | 0            | 0          | 0          | 0          | 1          | 0            | 2        | 0        |
+--------------+------------+----------+----------+------------+------------+------------+--------------+--------------+------------+------------+------------+------------+--------------+----------+----------+
```
  
---
---
# Leaves
```JavaScript
kaggle competitions download -c leaf-classification
Private https://www.kaggle.com/competitions/classify-leaves/code
Public  https://www.kaggle.com/competitions/leaf-classification
Example https://www.kaggle.com/code/div1996p/multiclass-classification-with-deep-neural-network
\#Customize CNNnetwork training with KFold and Vote
/opt/workspace/app/Awesome-Backbones/TestCNNTrain.ipynb
\#sample
https://www.kaggle.com/code/zachary666/classify-leaf
albumentations
transforms_train = albumentations.Compose(
    [
        albumentations.Resize(320, 320),
        albumentations.HorizontalFlip(p=0.5),
        albumentations.VerticalFlip(p=0.5),
        albumentations.Rotate(limit=180, p=0.7),
        albumentations.RandomBrightnessContrast(),
        albumentations.ShiftScaleRotate(
            shift_limit=0.25, scale_limit=0.1, rotate_limit=0
        ),
        albumentations.Normalize(
            [0.485, 0.456, 0.406], [0.229, 0.224, 0.225],
            max_pixel_value=255.0, always_apply=True
        ),
        ToTensorV2(p=1.0),
    ]
)
transforms_test = albumentations.Compose(
        [
            albumentations.Resize(320, 320),
            albumentations.Normalize(
                [0.485, 0.456, 0.406], [0.229, 0.224, 0.225],
                max_pixel_value=255.0, always_apply=True
            ),
            ToTensorV2(p=1.0)
        ]
    )
https://www.kaggle.com/code/fatemehsharifi79/mlp-pytorch
```
  
```Python
https://github.com/Fafa-DL/Awesome-Backbones/blob/main/datas/docs/Data_preparing.md
1, 运行做早期处理
/opt/workspace/app/Awesome-Backbones/autogluonTest.ipynb  生成annotations.txt and train.csv
/opt/workspace/app/Awesome-Backbones/data2/dataproc1.py  把image文件copy到对应的分类目录
2. split data into train/test and generate train.txt and test.txt
/opt/workspace/app/Awesome-Backbones/tools/split_data.py
/opt/workspace/app/Awesome-Backbones/tools/get_annotation.py
3.start train
python tools/train.py models/seresnext/seresnext101.py
Reference:
https://www.kaggle.com/code/nattoli/simple-resnet-resnext-baseline
https://www.kaggle.com/code/kxlyhit/13th-code-and-summary
```