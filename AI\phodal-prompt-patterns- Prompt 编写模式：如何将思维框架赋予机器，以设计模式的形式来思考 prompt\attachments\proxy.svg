<?xml version="1.0" encoding="UTF-8"?>
<svg width="1023px" height="453px" viewBox="0 0 1023 453" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 62 (91390) - https://sketch.com -->
    <title>Group 3</title>
    <desc>Created with Sketch.</desc>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Group-3">
            <g id="Group-2">
                <rect id="Rectangle" fill="#F7F7F7" x="0" y="0" width="1023" height="453"></rect>
                <circle id="Oval" fill="#9C80FD" cx="827" cy="227" r="162"></circle>
                <text id="Model" font-family="Comfortaa-Bold, Comfortaa" font-size="64" font-weight="bold" fill="#FFFFFF">
                    <tspan x="738" y="248">Model</tspan>
                </text>
                <rect id="Rectangle" fill="#09BE92" x="109" y="275" width="326" height="114" rx="8"></rect>
                <rect id="Rectangle" fill-opacity="0.820640724" fill="#63AAEC" x="109" y="65" width="326" height="95" rx="8"></rect>
                <g id="prompt-text" transform="translate(133.000000, 83.000000)">
                    <g id="Group">
                        <rect id="Rectangle" fill="#7255D6" x="0" y="0" width="280" height="24" rx="12"></rect>
                        <text id="Task-Description" fill="#FFFFFF" font-family="Comfortaa-Bold, Comfortaa" font-size="16" font-weight="bold">
                            <tspan x="10" y="17">Task Description</tspan>
                        </text>
                    </g>
                    <g id="Group" transform="translate(0.000000, 36.000000)">
                        <rect id="Rectangle" fill="#2C1D62" x="0" y="0" width="106" height="24" rx="12"></rect>
                        <rect id="Rectangle" fill="#F9F8FF" x="115" y="0" width="165" height="24" rx="12"></rect>
                        <text id="Act-as-xxx" fill="#FFFFFF" font-family="Comfortaa-Bold, Comfortaa" font-size="16" font-weight="bold">
                            <tspan x="10" y="17">Act as xxx</tspan>
                        </text>
                    </g>
                </g>
                <g id="text-text" transform="translate(131.000000, 291.000000)" fill="#EDFBF9">
                    <rect id="Rectangle" x="0" y="0" width="283" height="10" rx="5"></rect>
                    <rect id="Rectangle" x="0" y="24" width="283" height="10" rx="5"></rect>
                    <rect id="Rectangle" x="0" y="48" width="283" height="10" rx="5"></rect>
                    <rect id="Rectangle" x="0" y="72" width="175" height="10" rx="5"></rect>
                </g>
                <text id="Prompt" font-family="Comfortaa-Bold, Comfortaa" font-size="32" font-weight="bold" fill="#0CB093">
                    <tspan x="115" y="47">Prompt</tspan>
                </text>
                <text id="Generated-Text" font-family="Comfortaa-Bold, Comfortaa" font-size="32" font-weight="bold" fill="#0CB093">
                    <tspan x="109" y="256">Generated Text</tspan>
                </text>
                <path id="Line" d="M695.240261,114.378424 L707.240261,121.128424 L708.789729,122 L707.240261,122.871576 L695.240261,129.621576 L694.259739,127.878424 L703,122.961 L703,121.037 L694.259739,116.121576 L695.240261,114.378424 Z M437,121 L437,123 L435,123 L435,121 L437,121 Z M441,121 L441,123 L439,123 L439,121 L441,121 Z M445,121 L445,123 L443,123 L443,121 L445,121 Z M449,121 L449,123 L447,123 L447,121 L449,121 Z M453,121 L453,123 L451,123 L451,121 L453,121 Z M457,121 L457,123 L455,123 L455,121 L457,121 Z M461,121 L461,123 L459,123 L459,121 L461,121 Z M465,121 L465,123 L463,123 L463,121 L465,121 Z M469,121 L469,123 L467,123 L467,121 L469,121 Z M473,121 L473,123 L471,123 L471,121 L473,121 Z M477,121 L477,123 L475,123 L475,121 L477,121 Z M481,121 L481,123 L479,123 L479,121 L481,121 Z M485,121 L485,123 L483,123 L483,121 L485,121 Z M489,121 L489,123 L487,123 L487,121 L489,121 Z M493,121 L493,123 L491,123 L491,121 L493,121 Z M497,121 L497,123 L495,123 L495,121 L497,121 Z M501,121 L501,123 L499,123 L499,121 L501,121 Z M505,121 L505,123 L503,123 L503,121 L505,121 Z M509,121 L509,123 L507,123 L507,121 L509,121 Z M513,121 L513,123 L511,123 L511,121 L513,121 Z M517,121 L517,123 L515,123 L515,121 L517,121 Z M521,121 L521,123 L519,123 L519,121 L521,121 Z M525,121 L525,123 L523,123 L523,121 L525,121 Z M529,121 L529,123 L527,123 L527,121 L529,121 Z M533,121 L533,123 L531,123 L531,121 L533,121 Z M537,121 L537,123 L535,123 L535,121 L537,121 Z M541,121 L541,123 L539,123 L539,121 L541,121 Z M545,121 L545,123 L543,123 L543,121 L545,121 Z M549,121 L549,123 L547,123 L547,121 L549,121 Z M553,121 L553,123 L551,123 L551,121 L553,121 Z M557,121 L557,123 L555,123 L555,121 L557,121 Z M561,121 L561,123 L559,123 L559,121 L561,121 Z M565,121 L565,123 L563,123 L563,121 L565,121 Z M569,121 L569,123 L567,123 L567,121 L569,121 Z M573,121 L573,123 L571,123 L571,121 L573,121 Z M577,121 L577,123 L575,123 L575,121 L577,121 Z M581,121 L581,123 L579,123 L579,121 L581,121 Z M585,121 L585,123 L583,123 L583,121 L585,121 Z M589,121 L589,123 L587,123 L587,121 L589,121 Z M593,121 L593,123 L591,123 L591,121 L593,121 Z M597,121 L597,123 L595,123 L595,121 L597,121 Z M601,121 L601,123 L599,123 L599,121 L601,121 Z M605,121 L605,123 L603,123 L603,121 L605,121 Z M609,121 L609,123 L607,123 L607,121 L609,121 Z M613,121 L613,123 L611,123 L611,121 L613,121 Z M617,121 L617,123 L615,123 L615,121 L617,121 Z M621,121 L621,123 L619,123 L619,121 L621,121 Z M625,121 L625,123 L623,123 L623,121 L625,121 Z M629,121 L629,123 L627,123 L627,121 L629,121 Z M633,121 L633,123 L631,123 L631,121 L633,121 Z M637,121 L637,123 L635,123 L635,121 L637,121 Z M641,121 L641,123 L639,123 L639,121 L641,121 Z M645,121 L645,123 L643,123 L643,121 L645,121 Z M649,121 L649,123 L647,123 L647,121 L649,121 Z M653,121 L653,123 L651,123 L651,121 L653,121 Z M657,121 L657,123 L655,123 L655,121 L657,121 Z M661,121 L661,123 L659,123 L659,121 L661,121 Z M665,121 L665,123 L663,123 L663,121 L665,121 Z M669,121 L669,123 L667,123 L667,121 L669,121 Z M673,121 L673,123 L671,123 L671,121 L673,121 Z M677,121 L677,123 L675,123 L675,121 L677,121 Z M681,121 L681,123 L679,123 L679,121 L681,121 Z M685,121 L685,123 L683,123 L683,121 L685,121 Z M689,121 L689,123 L687,123 L687,121 L689,121 Z M693,121 L693,123 L691,123 L691,121 L693,121 Z M697,121 L697,123 L695,123 L695,121 L697,121 Z M701,121 L701,123 L699,123 L699,121 L701,121 Z" fill="#979797" fill-rule="nonzero"></path>
                <path id="Line" d="M445.759739,324.378424 L446.740261,326.121576 L437,331.6 L437,332.4 L446.740261,337.878424 L445.759739,339.621576 L433.759739,332.871576 L432.210271,332 L433.759739,331.128424 L445.759739,324.378424 Z M441,331 L441,333 L439,333 L439,331 L441,331 Z M445,331 L445,333 L443,333 L443,331 L445,331 Z M449,331 L449,333 L447,333 L447,331 L449,331 Z M453,331 L453,333 L451,333 L451,331 L453,331 Z M457,331 L457,333 L455,333 L455,331 L457,331 Z M461,331 L461,333 L459,333 L459,331 L461,331 Z M465,331 L465,333 L463,333 L463,331 L465,331 Z M469,331 L469,333 L467,333 L467,331 L469,331 Z M473,331 L473,333 L471,333 L471,331 L473,331 Z M477,331 L477,333 L475,333 L475,331 L477,331 Z M481,331 L481,333 L479,333 L479,331 L481,331 Z M485,331 L485,333 L483,333 L483,331 L485,331 Z M489,331 L489,333 L487,333 L487,331 L489,331 Z M493,331 L493,333 L491,333 L491,331 L493,331 Z M497,331 L497,333 L495,333 L495,331 L497,331 Z M501,331 L501,333 L499,333 L499,331 L501,331 Z M505,331 L505,333 L503,333 L503,331 L505,331 Z M509,331 L509,333 L507,333 L507,331 L509,331 Z M513,331 L513,333 L511,333 L511,331 L513,331 Z M517,331 L517,333 L515,333 L515,331 L517,331 Z M521,331 L521,333 L519,333 L519,331 L521,331 Z M525,331 L525,333 L523,333 L523,331 L525,331 Z M529,331 L529,333 L527,333 L527,331 L529,331 Z M533,331 L533,333 L531,333 L531,331 L533,331 Z M537,331 L537,333 L535,333 L535,331 L537,331 Z M541,331 L541,333 L539,333 L539,331 L541,331 Z M545,331 L545,333 L543,333 L543,331 L545,331 Z M549,331 L549,333 L547,333 L547,331 L549,331 Z M553,331 L553,333 L551,333 L551,331 L553,331 Z M557,331 L557,333 L555,333 L555,331 L557,331 Z M561,331 L561,333 L559,333 L559,331 L561,331 Z M565,331 L565,333 L563,333 L563,331 L565,331 Z M569,331 L569,333 L567,333 L567,331 L569,331 Z M573,331 L573,333 L571,333 L571,331 L573,331 Z M577,331 L577,333 L575,333 L575,331 L577,331 Z M581,331 L581,333 L579,333 L579,331 L581,331 Z M585,331 L585,333 L583,333 L583,331 L585,331 Z M589,331 L589,333 L587,333 L587,331 L589,331 Z M593,331 L593,333 L591,333 L591,331 L593,331 Z M597,331 L597,333 L595,333 L595,331 L597,331 Z M601,331 L601,333 L599,333 L599,331 L601,331 Z M605,331 L605,333 L603,333 L603,331 L605,331 Z M609,331 L609,333 L607,333 L607,331 L609,331 Z M613,331 L613,333 L611,333 L611,331 L613,331 Z M617,331 L617,333 L615,333 L615,331 L617,331 Z M621,331 L621,333 L619,333 L619,331 L621,331 Z M625,331 L625,333 L623,333 L623,331 L625,331 Z M629,331 L629,333 L627,333 L627,331 L629,331 Z M633,331 L633,333 L631,333 L631,331 L633,331 Z M637,331 L637,333 L635,333 L635,331 L637,331 Z M641,331 L641,333 L639,333 L639,331 L641,331 Z M645,331 L645,333 L643,333 L643,331 L645,331 Z M649,331 L649,333 L647,333 L647,331 L649,331 Z M653,331 L653,333 L651,333 L651,331 L653,331 Z M657,331 L657,333 L655,333 L655,331 L657,331 Z M661,331 L661,333 L659,333 L659,331 L661,331 Z M665,331 L665,333 L663,333 L663,331 L665,331 Z M669,331 L669,333 L667,333 L667,331 L669,331 Z M673,331 L673,333 L671,333 L671,331 L673,331 Z M677,331 L677,333 L675,333 L675,331 L677,331 Z M681,331 L681,333 L679,333 L679,331 L681,331 Z M685,331 L685,333 L683,333 L683,331 L685,331 Z M689,331 L689,333 L687,333 L687,331 L689,331 Z M693,331 L693,333 L691,333 L691,331 L693,331 Z M697,331 L697,333 L695,333 L695,331 L697,331 Z M701,331 L701,333 L699,333 L699,331 L701,331 Z M705,331 L705,333 L703,333 L703,331 L705,331 Z" fill="#979797" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>