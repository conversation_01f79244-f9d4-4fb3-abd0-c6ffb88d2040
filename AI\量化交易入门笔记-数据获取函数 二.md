---
Updated: 2024-05-15T11:43
tags:
  - AI->-Business
Created: 2024-05-15T11:43
---
[![](https://pic1.zhimg.com/v2-2feaae4dabda75642ded4fa22288a298_720w.jpg?source=172ae18b)](https://pic1.zhimg.com/v2-2feaae4dabda75642ded4fa22288a298_720w.jpg?source=172ae18b)
## ==gt_fundamentals() 函数==
==该函数可查询一只股票或多只股票的财务数据，其语法如下：==
```plain
get_fundamentals(query_object, date=None, statDate=None)
```
==参数解析：==
- ==query_object : 这是一个 sqlalchemy.orm.query.Query 对象，可以通过全局的 query 函数获取 Query 对象==
- ==date : 表示查询日期，可以是一个字符串或者datetime.date/datetime.datetime对象。可以是None，使用默认日期，这个默认日期在回测和研究模块上有点差别：==
- ==回测模块：默认值会随着回测日期变化而变化，等于 context.current_dt 的前一天（实际生活中我们只能看到前一天的财报和市值数据，所以要用前一天）==
- ==研究模块：使用平台财务数据的最新日期，一般是昨天==
==**需要注意的是，如果传入的date不是交易日，则使用这个日期之前的最近的一个交易日**==
- ==statDate : 是一个字符串，表示财报统计的季度或者年份，有两种格式，具体如下：==
- ==季度：格式是年+“q”+季度序号，例如：“2018q1”，“2017q4“==
- ==年份：格式就是年份的数字，如”2017“，”2018“==
==**date 和 statDate 参数只能传入一个。传入date时，查询指定日期 date 收盘后所能看到最近的数据；传入 statDate 时，查询 statDate 指定的季度或者年份的财务数据。当两个参数都没有传入成功时，相当于使用 date 参数，即使用默认日期**==
==**为了防止返回数据量过大，每次最多返回10000行。另外，当相关股票上市前、退市后，财务数据返回各字段为空**==
==实例代码：==
```plain
import pandas as pd
# query表示查询
# filter表示条件筛选
# valuation是内置市值数据对象(这里表示查询valuation下所有的属性)
# 最终返回一个Query对象
myq = query(valuation).filter(valuation.code=='000001.XSHE')
df = get_fundamentals(myq,'2018-4-12')
df
```
[![](https://pic4.zhimg.com/80/v2-1a9736f436531f2dc1b2dc1b7fee672b_720w.webp)](https://pic4.zhimg.com/80/v2-1a9736f436531f2dc1b2dc1b7fee672b_720w.webp)
==valuation市值数据对象中的字段有：==
- ==id: 每行数据的索引，不重复==
- ==code: 股票代码==
- ==pr_ratio: 是股票的市盈率（PE,TTM），即动态市盈率。==
- ==turnover_ratio: 是股票的换手率。==
- ==pb_ratio: 是股票的市 净率（PB）==
- ==ps_ratio: 是股票的市现率==
- ==capitalization: 是股票的总股本（万股）==
- ==market_cap: 是股票的流通股本（万股）==
- ==circulating_market_cap: 是股票的流通市值（亿元）==
- ==day: 是指查询股票账务数据的具体日期==
- ==pe_ratio_lyr: 是股票的市盈率（PE）。以上一年度每股盈利计算的静态市盈率==
==示例：输出指定字段==
```plain
import pandas as pd
# query表示查询
# filter表示条件筛选
# valuation是内置市值数据对象(这里表示查询valuation下所有的属性)
# 最终返回一个Query对象
myq = query(valuation).filter(valuation.code=='000001.XSHE')
df = get_fundamentals(myq,'2018-4-12')
print("当日动态市盈率是：", df['pe_ratio'])
print('当日换手率是：', df['turnover_ratio'])
print('当日市销率是：', df['ps_ratio'])
---
当日动态市盈率是： 0    8.53
Name: pe_ratio, dtype: float64
当日换手率是： 0    0.6934
Name: turnover_ratio, dtype: float64
当日市销率是： 0    1.8698
Name: ps_ratio, dtype: float64
```
==示例：显示多只股票的财务数据==
```plain
import pandas as pd
# query表示查询
# filter表示条件筛选
# valuation是内置市值数据对象(  这里表示查询valuation下所有的属性)
# 最终返回一个Query对象
myq = query(valuation).filter(valuation.code.in_(['000001.XSHE', '600000.XSHG', '000009.XSHE']))
df = get_fundamentals(myq,'2018-4-12')
df
```
[![](https://pic3.zhimg.com/80/v2-46eab2dc91b085f9b4365d7f878b1086_720w.webp)](https://pic3.zhimg.com/80/v2-46eab2dc91b085f9b4365d7f878b1086_720w.webp)
==示例：更多不同的财务数据条件筛选==
```plain
import pandas as pd
df = get_fundamentals(query(
    valuation
    ).filter(
        valuation.market_cap > 1000,  # 筛选市值大于1000的
        valuation.pe_ratio < 10,  # 筛选市盈率小于10的
        ).order_by(
            valuation.market_cap.desc()  # 按照市值倒序排列
            ).limit(
                5  # 最多只显示5条数据
                ), date='2018-4-12')
df
```
[![](https://pic4.zhimg.com/80/v2-5215dd0a832583a9f604659df6576a6f_720w.webp)](https://pic4.zhimg.com/80/v2-5215dd0a832583a9f604659df6576a6f_720w.webp)
==示例：显示某股票2016年第四季度的季报，并放到列表中显示==
```plain
# query这里指定了要查询显示的字段
q = query(
    # income为利润数据对象
    income.statDate, # 统计日期
    income.code,  # 股票代码
    income.basic_eps,  # 基本每股收益
    # balance为负债数据对象
    balance.cash_equivalents,  # 货币资金
    # cash_flow为现金流数据对象
    cash_flow.goods_sale_and_service_render_cash  # 销售商品、提供劳务获得的现金
    ).filter(
        income.code == '000001.XSHE'
        )
rets = [get_fundamentals(q, statDate='2016q'+str(i)) for i in range(1, 5)]
rets
-----
[     statDate         code  basic_eps  cash_equivalents  \
 0  2016-03-31  000001.XSHE       0.43      2.961440e+11   
    goods_sale_and_service_render_cash  
 0                                 NaN  ,
      statDate         code  basic_eps  cash_equivalents  \
 0  2016-06-30  000001.XSHE       0.29      2.781780e+11   
    goods_sale_and_service_render_cash  
 0                                 NaN  ,
      statDate         code  basic_eps  cash_equivalents  \
 0  2016-09-30  000001.XSHE       0.37      3.039360e+11   
    goods_sale_and_service_render_cash  
 0                                 NaN  ,
      statDate         code  basic_eps  cash_equivalents  \
 0  2016-12-31  000001.XSHE       0.23      3.112580e+11   
    goods_sale_and_service_render_cash  
 0                                 NaN  ]
```
==各财务数据的意义：==
- ==statDate: 财报统计的季度的最后一天，例如 2016-03-31==
- ==code: 股票代码==
- ==basic_eps: 基本每股收益==
- ==cash_equivalents: 货币资金==
- ==good_sale_ane_service_render_cash: 销售商品、提供劳务收到的现金==
## ==get_fundamentals_continuously() 函数==
==`get_fundamentals()`====函数只能查询某一交易日的股票财务数信息，如果要查询多个交易日的股票财务数据信息，就要使用本函数；其语法如下：==
```plain
get_fundamentls_continuously(query_object, end_date=None, count=None)
```
==参数意义：==
- ==query_object : 这是一个 sqlalchemy.orm.query.Query 对象，可以通过全局的 query 函数获取 Query 对象==
- ==end_date: 查询日期==
- ==count: 获取end_date之前 count 个日期的数据==
==本函数返回值是一个====**pandas.Panel**==
==_出于性能考虑，返回总条数不超过10000条的限制_==
==示例：查询平安银行和浦发银行的财务信息==
```plain
import pandas as pd
q = query(
    valuation
).filter(
    valuation.code.in_(
        ['000001.XSHE', '600000.XSHG']))
panel = get_fundamentals_continuously(q, end_date='2018-01-01', count=5)
panel.minor_xs('600000.XSHG')
```
[![](https://pic3.zhimg.com/80/v2-5105e2261478f4b6d90a8ce0ca9aadde_720w.webp)](https://pic3.zhimg.com/80/v2-5105e2261478f4b6d90a8ce0ca9aadde_720w.webp)
## ==get_index_stocks() 函数==
==该函数可以获取一个指数给定日期的平台可交易的成分股列表；其语法如下：==
```plain
get_index_stocks(index_symbol, date=None)
```
==参数解析：==
- ==date: 是一个时间字符串或者一个时间对象。None会采用默认值。如果是默认值，在回测模块中，会动态采用回测日期，相当于context.current_dt；如果是在研究模块中，默认时间会采用当前时间==
- ==index_symbol: 是指指数代码；代码可以在看盘软件上查询或者在相关行情网站查询，例如：====**000001.XSHG 上证指数、 000002.XSHG A股指数、 000003.XSHG B股指数**==
==代码实例：获得沪深300指数的所有股票代码==
```plain
stocks = get_index_stocks('000300.XSHG')
stocks
---
['000001.XSHE',
 '000002.XSHE',
 '000060.XSHE',
 '000063.XSHE',
 ......,
 '603833.XSHG',
 '603858.XSHG',
 '603993.XSHG']
```
## ==get_industry_stocks()函数==
==该函数可以获取在给定日期一个行业的所有股票代码；其语法如下：==
```plain
get_industry_stocks(industry_code, date=None)
```
==参数解析：==
- ==date: 同上==
- ==industry_code: 行业代码；例如：====**A01 农业、A02 林业、 C27 医药制造业**==
==代码示例：获得汽车制造业指数的所有股票代码==
```plain
stocks = get_industry_stocks('C36')
stocks
---
['000030.XSHE',
 '000338.XSHE',
 '000549.XSHE',
 '000550.XSHE',
......,
'603809.XSHG',
 '603922.XSHG',
 '603926.XSHG',
 '603997.XSHG']
```
## ==get_concept_stocks() 函数==
==该函数可以获取在给定日期一个概念板块的所有股票；其语法如下：==
```plain
get_concept_stocks(conpect_code, date=None)
```
==参数解析：==
- ==date: 同上==
- ==conpect_code: 概念板块代码；例如：====**GN028 智能电网、 GN030 物联网**==
==代码示例：获得一带一中概念板块的所有股票代码：==
```plain
stocks = get_concept_stocks('GN181')
stocks
---
['000018.XSHE',
 '000022.XSHE',
 '000063.XSHE',
 '000065.XSHE',
 ......,
  '601857.XSHG',
 '601872.XSHG',
 '601880.XSHG',
 '601919.XSHG',
 '601989.XSHG',
 '603169.XSHG']
```
## ==get_all_secruities()函数==
==本函数可以获取平台支持的所有股票、基金、指数、期货信息；其语法如下：==
```plain
get_all_securities(types=[], date=None)
```
==参数解析：==
- ==types: 表示列表类型，用来过滤 securities 的类型，列表元素及意义如下：==
- ==stock:　表示股票类型，即显示所有股票信息==
- ==fund: 表示基金类型，即显示所有基金信息==
- ==index: 表示指数类型，即显示所有指数信息==
- ==futures: 表示期货类型，即显示所有期货合约信息==
- ==eft: 表示 ETF基金，即显示所有 ETF 基金信息==
- ==lof: 表示 lof 基金，即显示所有 lof 基金信息==
- ==fja: 表示分级A，即显示所有分级基金A的信息==
- ==fjb: 表示分级B，即显示所有分级基金B的信息==
- ==open_fund: 表示开放式基金，即显示所有开放式基金的信息==
- ==bond_fund: 表示股票型基金，即显示所有股票型基金的信息==
- ==QDII_fund: 表示 QDII 基金，即显示所有 QDII 基金的信息==
- ==money_market_fund: 表示货币基金，即显示所有货币基金的信息==
- ==mixture_fund: 表示混合型基金，即显示所有混合型基金的信息==
==_需要注意的是，types为空时返回所有股票信息，不包括基金、指数和期货信息_==
- ==date:是一个时间字符串或一个时间对象，用于获取某日期还在上市的股票信息，默认值是None，表示获取所有日期的股票信息==
==**本函数返回的类型是 pandas.DataFrame 类型**==
==代码实例：获取所有的股票信息==
```plain
# 下面两行代码显示的结果是一样的
df = get_all_securities(['stock'])
stocks = get_all_securities()
stocks
```
[![](https://pic3.zhimg.com/80/v2-27510263e993d3d6e7f38455d7bcc146_720w.webp)](https://pic3.zhimg.com/80/v2-27510263e993d3d6e7f38455d7bcc146_720w.webp)
==3551 rows × 5 columns==
==列名字段解析：==
- ==display_name : 上市公司的股票的名称==
- ==name: 上市公司股票名称的缩写简称==
- ==start_date: 上市公司的上市日期==
- ==end_date: 上市公司的退市日期==
- ==type: 类型==
==代码示例：显示所有分级A与分级B的信息==
```plain
df = get_all_securities(['stock'])
stocks = get_all_securities()
stocks
```
[![](https://pic3.zhimg.com/80/v2-bf6fbc05f986afbe5342b6229e83f06a_720w.webp)](https://pic3.zhimg.com/80/v2-bf6fbc05f986afbe5342b6229e83f06a_720w.webp)
==254 rows × 5 columns==
==示例代码：显示2017-10-10日还在上市的eft和lof基金信息==
```plain
df = get_all_securities(['etf', 'lof'], '2017-10-10')
df
```
[![](https://pic1.zhimg.com/80/v2-d2e791acc958374842bd50620032dc2c_720w.webp)](https://pic1.zhimg.com/80/v2-d2e791acc958374842bd50620032dc2c_720w.webp)
==396 rows × 5 columns==
## ==get_security_info()函数==
==本函数可以获取一只股票（基金或指数）的信息，其语法如下：==
```plain
get_security_info(code)
```
==参数解析：==
- ==code: 是指证券代码。返回值是 pandas.DataFrame 类型，返回值的属性与== ==`get_all_securities()`== ==函数基本上是一样的；但返回值中多一个parent属性，是指分级基金的线基金的代码==
==代码示例：==
```plain
print('代码502050的证券名：', get_security_info('502050.XSHG').display_name)
print('代码502050的证券缩写简称：', get_security_info('502050.XSHG').name)
print('代码502050的证券上市日期：', get_security_info('502050.XSHG').start_date)
print('代码502050的证券退市日期：', get_security_info('502050.XSHG').end_date)
print('代码502050的证券类型：', get_security_info('502050.XSHG').type)
print('代码502050的证券分级基金的母基金：', get_security_info('502050.XSHG').parent)
---
代码502050的证券名： 上证50B
代码502050的证券缩写简称： SZ50B
代码502050的证券上市日期： 2015-04-27
代码502050的证券退市日期： 2200-01-01
代码502050的证券类型： fjb
代码502050的证券分级基金的母基金： 502048.XSHG
```
## ==get_billboard_list()函数==
==本函数可以获取指定日期区间内的龙虎榜数据，语法如下：==
```plain
get_billboard_list(stock_list, start_date, end_date, count)
```
==参数解析：==
- ==stock_list: 一个股票代码的list，当值为None时，返回指定日期的所有股票==
- ==start_date: 开始日期==
- ==end_date: 结束日期==
- ==count: 交易日数量，可以与end_date同时使用，表示获取 end_date 前 count 个交易日的数据==
==返回类型： pandas.DataFrame==
==代码示例：==
```plain
df = get_billboard_list(stock_list=None, end_date='2018-04-09', count=1)
df
```
[![](https://pic1.zhimg.com/80/v2-ce92b722a3ea81d55b1a708b054720b4_720w.webp)](https://pic1.zhimg.com/80/v2-ce92b722a3ea81d55b1a708b054720b4_720w.webp)
==各字段含义如下：==
- ==code: 股票代码==
- ==day: 日期==
- ==direction: All 表示 ”汇总“，Sell表示”卖“，Buy表示”买“==
- ==abnormal_code: 异常波动类型==
- ==abnormal_name: 异常波动名称==
- ==sales_depart_name: 营业部名称==
- ==rank: 0表示汇总， 1\~5表示买一到买五，6\~10表示卖一到卖五==
- ==buy_value: 买入金额==
- ==buy_rate: 买入金额占比（买入金额/市场总成交额度）==
- ==sell_value: 卖出金额==
- ==sell_rate: 卖出金额占比（卖出金额/市场总成交额）==
- ==net_value: 净额（买入金额-卖出金额）==
- ==amount: 市场总成交额==
## ==get_locked_shares()函数==
==本函数可以获取指定日期区间内的限售解禁数据，其语法如下：==
```plain
get_locked_shares(stock_list, start_date, end_date, forward_count)
```
==参数解析：各项参数与====`get_billbord_list`====差不多==
==这里只说一下返回信息中字段的意义==
- ==day: 解禁日期==
- ==code: 股票代码==
- ==num: 解禁股数==
- ==rate1: 解禁股数/总股本==
- ==rate2: 解禁股数/总流通股本==
==代码示例：==
```plain
df = get_locked_shares(stock_list=['000001.XSHE','000002.XSHG','000009.XSHE'], start_date='2016-4-16',forward_count=1200)
df
```
[![](https://pic3.zhimg.com/80/v2-40064d42a5bbfac12d73f242a4cb2de2_720w.webp)](https://pic3.zhimg.com/80/v2-40064d42a5bbfac12d73f242a4cb2de2_720w.webp)
==_注：本文章为个人学习笔记，参考了一些书籍与官方教程，不作任何商业用途！_==