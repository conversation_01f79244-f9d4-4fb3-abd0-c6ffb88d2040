---
Updated: 2023-11-24T17:38
tags:
  - AI->-Programming
  - AI->-Theory
Created: 2023-11-24T17:38
---
==泻药。普通的==[==attention map==](https://www.zhihu.com/search?q=attention%20map&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==可视化很简单，但是大家平时说的“注意力的流动”，其实并不是attention map，而是在下面这篇文章提出来的。**将注意力建模成流，等价于将self-attention层的神经元建模成节点，将有attention连接的神经元建模成边，然后注意力权重就能成为边的flow capacity。**用==[==最大流算法==](https://www.zhihu.com/search?q=%E6%9C%80%E5%A4%A7%E6%B5%81%E7%AE%97%E6%B3%95&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==，就能求出两个上下游节点间的最大流，把这个流值作为新的注意力值就能获得更好的效果。今天我就来present一篇令人感到“读起来非常流畅，就是我想做的东西”的文章。即使效果和==[==gradient-based==](https://www.zhihu.com/search?q=gradient-based&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==与occlusion等方法还有一些差距，但是比传统的注意力图要可靠很多。==
==更technical的总结：这篇文章提出了==[==attention rollout==](https://www.zhihu.com/search?q=attention%20rollout&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==和attention flow，分别用两种不同的图（权图和流图）建立基于attention map的token-level layer-wise importance，并且说明了方法的优越性。从结果来看，attention flow比attention rollout更鲁棒，而两种方法都大大优于==[==raw attention map==](https://www.zhihu.com/search?q=raw%20attention%20map&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==。但是因为整个方法依然是基于attention map的，所以普遍对于模型不鲁棒，且相距==[==gradient-based attribution==](https://www.zhihu.com/search?q=gradient-based%20attribution&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==等方法仍有性能差距。==
==论文原文：==[==https://arxiv.org/pdf/2005.00928.pdf==](https://link.zhihu.com/?target=https%3A//arxiv.org/pdf/2005.00928.pdf)==（400+ citation）==
==这篇文章提出了两个量化注意力流的指标：attention rollout（注意力展开）和attention flow（注意力流）。这两个方法基于使用 DAG（==[==有向无环图==](https://www.zhihu.com/search?q=%E6%9C%89%E5%90%91%E6%97%A0%E7%8E%AF%E5%9B%BE&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==）对网络中的信息流进行建模。====**图的节点是输入token和hidden embedding（注意不是word embedding，word embedding是第一层**==[==**embedding**==](https://www.zhihu.com/search?q=embedding&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==**，hidden embedding是第二层到最后一层embedding），边是每层**==[==**节点**==](https://www.zhihu.com/search?q=%E8%8A%82%E7%82%B9&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==**对前一层节点的attention，并且边的权重是注意力权重。**==
==**attention rollout**== ==假设输入token的identity根据注意力权重通过各层==[==线性组合==](https://www.zhihu.com/search?q=%E7%BA%BF%E6%80%A7%E7%BB%84%E5%90%88&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==。为了调整注意力权重，它展开权重以捕获从输入token到中间隐藏embedding的信息传播。====  
  
====**attention flow**== ==将注意力图视为一个流网络。使用最大流算法，它计算从隐藏embedding（源节点）到输入token（汇节点）的最大流值。====  
  
====在这两种方法中，我们都考虑了网络中的残差连接，以更好地对输入标记和隐藏嵌入之间的连接进行建模。**我们表明，与原始注意力相比，来自注意力展开和注意力流的令牌注意力与从==[==input graient==](https://www.zhihu.com/search?q=input%20graient&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==以及blank out（一种基于输入消融的归因方法）获得的重要性分数具有更高的相关性。**此外，我们将==[==token注意力==](https://www.zhihu.com/search?q=token%E6%B3%A8%E6%84%8F%E5%8A%9B&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==权重可视化，并证明与原始注意力相比，它们更好地近似输入==[==令牌==](https://www.zhihu.com/search?q=%E4%BB%A4%E7%89%8C&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==如何对预测输出做出贡献。==
==在我们的分析中，我们重点关注====**动词是否为复数的预测任务**====，即当输入是直到动词位置的句子时，预测句子中动词的单数或复数。我们使用**==[==主谓一致数据集==](https://www.zhihu.com/search?q=%E4%B8%BB%E8%B0%93%E4%B8%80%E8%87%B4%E6%95%B0%E6%8D%AE%E9%9B%86&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==**（Linzen 等人，2016）。此任务和数据集是方便的选择，因为**====它们提供了关于输入的哪一部分对于获得正确的解决方案至关重要的明确假设**。例如，假设“柜子的钥匙”作为输入，我们知道关注“钥匙”有助于模型预测单数作为输出，而关注“柜子”（====**一致**==[==**吸引子**==](https://www.zhihu.com/search?q=%E5%90%B8%E5%BC%95%E5%AD%90&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==，具有相反的数量）则无济于事。==
==做attribution的组一般上来就是主谓一致数据集，这是基本操作。==
==我们使用 GPT2 Transformer 块训练 Transformer== [==编码器==](https://www.zhihu.com/search?q=%E7%BC%96%E7%A0%81%E5%99%A8&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==，如（Radford 等人，2019；Wolf 等人，2019）中所述（====**无掩码**====）。该模型有 6 层和 8 个头，==[==隐藏/嵌入==](https://www.zhihu.com/search?q=%E9%9A%90%E8%97%8F%2F%E5%B5%8C%E5%85%A5&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==大小为 128。与 BERT（Devlin 等人，2019）类似，我们添加一个 CLS 令牌，====**并使用其在最后一层的embedding作为**==[==**分类器**==](https://www.zhihu.com/search?q=%E5%88%86%E7%B1%BB%E5%99%A8&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==**的输入**====。该模型在主谓一致任务上的准确度为 0.96。为了便于复制我们的实验，我们将在== [==https://github.com/samiraabnar/attention_flow==](https://link.zhihu.com/?target=https%3A//github.com/samiraabnar/attention_flow) ==上公开实现我们使用的模型和我们引入的算法。==
==GPT2+不mask前文，其实可以简单地理解成是BERT，就是个encoder而已。==
==我们首先在图 1a 中可视化原始注意力（如 Vig 2019）。这里给出的例子被正确分类了。至关重要的是，====**仅在前几层中，不同位置的注意力模式存在一些区别，而在较高层中，注意力权重相当均匀**====。图 2（左）给出了不同层（y 轴）的输入标记（x 轴）上的 CLS 标记的原始注意力分数，同样缺乏可解释的模式。这些观察反映了这样一个事实：====**随着我们深入模型，embedding更加上下文化**====，并且可能都携带相似的信息。这强调了需要一路追踪注意力权重直至输入层，并且与 Serrano 和 Smith (2019) 的发现一致，他们表明====**注意力权重不一定与输入token的相对重要性相对应**====。==
[![](https://picx.zhimg.com/50/v2-4eddb95987df69501c1b149af0d7902b_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-4eddb95987df69501c1b149af0d7902b_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-bfae5f6a56b25cf0ee9a86d50442b12b_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-bfae5f6a56b25cf0ee9a86d50442b12b_720w.jpg?source=1def8aca)
==为了量化原始注意力权重的有用性，以及我们在下一节中考虑的两种替代方案，除了==[==input gradients==](https://www.zhihu.com/search?q=input%20gradients&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==之外，我们还采用输入消融方法（blank-out）来估计每个输入标记的重要性得分。 **Blank-out 将输入中的每个标记一一替换为 UNK，并测量它对正确类别的预测概率的影响程度。**我们计算最后一层中 CLS 嵌入的注意力权重与blank out的重要性得分之间的 Spearman== [==等级相关系数==](https://www.zhihu.com/search?q=%E7%AD%89%E7%BA%A7%E7%9B%B8%E5%85%B3%E7%B3%BB%E6%95%B0&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==。如表 1 第一行所示，除了第一层之外，CLS 令牌的原始注意力权重与blank out分数之间的相关性相当低。正如我们在表 2 中看到的，当我们计算与==[==integrated gradient==](https://www.zhihu.com/search?q=integrated%20gradient&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==的相关性时也是这种情况。==
[![](https://picx.zhimg.com/50/v2-20c48f3471913fcd9f908479ed5e7464_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-20c48f3471913fcd9f908479ed5e7464_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-34bff81feccb527736b0d9b0154813e1_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-34bff81feccb527736b0d9b0154813e1_720w.jpg?source=1def8aca)
==相对地，====**注意力推出和注意力流以嵌入注意力作为输入，**==[==**递归**==](https://www.zhihu.com/search?q=%E9%80%92%E5%BD%92&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==**地计算给定模型每一层中的标记注意力**====。他们对较低层的注意力权重如何影响较高层的信息流以及是否计算相对于彼此或独立的token注意力的假设有所不同。==
==为了计算信息如何====**从输入层传播到更高层的嵌入**====，至关重要的是====**考虑模型中的**==[==**残差连接**==](https://www.zhihu.com/search?q=%E6%AE%8B%E5%B7%AE%E8%BF%9E%E6%8E%A5&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==**以及注意力权重**====。在 Transformer 块中，自注意力网络和==[==前馈网络==](https://www.zhihu.com/search?q=%E5%89%8D%E9%A6%88%E7%BD%91%E7%BB%9C&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==都由残差连接包裹，即这些模块的输入被添加到它们的输出中。**当我们只使用注意力权重来近似 Transformer 中的信息流时，我们忽略了残差连接。**但这些连接在连接不同层中的相应位置方面发挥着重要作用。因此，====**为了计算注意力展开和注意力流，我们用额外的权重来augment注意力图，从而表示残差连接**====。给定具有残差连接的注意力模块，我们将 l+1l+1 层中的值计算为 Vl+1=Vl+WattVlV_{l+1} = V_l+W_{att}V_l ，其中 WattW_{att} 是==[==注意力矩阵==](https://www.zhihu.com/search?q=%E6%B3%A8%E6%84%8F%E5%8A%9B%E7%9F%A9%E9%98%B5&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==。因此，我们有 Vl+1=(Watt+I)VlV_{l+1} = (W_{att} + I)V_l 。因此，为了考虑残差连接，我们将==[==单位矩阵==](https://www.zhihu.com/search?q=%E5%8D%95%E4%BD%8D%E7%9F%A9%E9%98%B5&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==添加到注意矩阵中并重新归一化权重。这导致 A=0.5Watt+0.5IA = 0.5W_{att} + 0.5I ，其中 A 是由残差连接更新的原始注意力。==
==这一步很好理解，本质就是在这一层的注意力流里面保留了一半上一层的注意力流的性质，来模拟残差连接。By the way Transformer中的残差连接在这里：==
[![](https://picx.zhimg.com/50/v2-b2c65abb0e3f4115ceb4018faaac613c_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-b2c65abb0e3f4115ceb4018faaac613c_720w.jpg?source=1def8aca)
==此外，分析各个头需要通过 Transformer 块中的==[==position-wise==](https://www.zhihu.com/search?q=position-wise&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D) ==FFN来考虑头之间的信息混合。**使用注意力推出和注意力流，还可以单独分析每个头。**我们在附录 A.1 中进行了更详细的解释。然而，在本文的分析中，为了简单起见，我们对所有头的每一层的注意力进行平均。==
==**注意力推出**== ==注意力推出是====**跟踪从输入层传播到更高层嵌入的信息**====的直观方法。给定一个具有 LL 层的 Transformer，我们想要计算从第 lil_i 层中的所有位置到第 ljl_j 层中的所有位置的注意力，其中 j<ij < i 。在注意力图中，从 lil_i 中位置 kk 处的节点 vv 到 ljl_j 中位置 mm 处的节点 uu 的路径是连接这两个节点的一系列边。**如果我们将每条边的权重视为两个节点之间传输的信息的比例，我们可以通过乘以该路径中所有边的权重来计算 vv 处的信息有多少通过特定路径传播到 uu 。**由于注意力图中两个节点之间可能存在不止一条路径，为了计算从 vv 到 uu 传播的信息总量，我们对这两个节点之间的所有可能路径进行求和。在实现层面，为了计算从 lil_i 到 ljl_j 的注意力，我们递归地乘以下面所有层中的注意力权重矩阵。==
[![](https://picx.zhimg.com/50/v2-3a856e52062b6a5d610e423fd45c30e7_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-3a856e52062b6a5d610e423fd45c30e7_720w.jpg?source=1def8aca)
==在这个等式中， A~\tilde A 是注意力展开， AA 是原始注意力，乘法运算是==[==矩阵乘法==](https://www.zhihu.com/search?q=%E7%9F%A9%E9%98%B5%E4%B9%98%E6%B3%95&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==。通过这个公式，为了计算输入注意力，我们设置 j=0j = 0 。==
==**注意力流**== ==在图论中，流网络是一个==[==有向图==](https://www.zhihu.com/search?q=%E6%9C%89%E5%90%91%E5%9B%BE&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==，每个边都有一个“====**容量**====”。形式上，给定 G=(V,E)G = (V, E) 是一个图，其中 VV 是节点(Vertex)的集合， EE 是 GG 中边(Edge)的集合； C={cuv∈R|∀u,v where eu,v∈E∧u≠v}C = \{c_{uv} ∈ R | ∀u, v\ \text{where}\ e_{u,v} ∈ E ∧u \ne v\} 表示边的容量， s,t∈Vs, t ∈ V 分别是源节点和目标（汇）节点；====**流**====是边到实数的==[==映射==](https://www.zhihu.com/search?q=%E6%98%A0%E5%B0%84&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==， f:E→Rf : E → R ，满足两个条件： (a) 容量约束：对于每条边，flow 值不应超过其容量， |fuv≤cuv||f_{uv} ≤ c_{uv}| ； (b) 流量守恒：对于除 ss 和 tt 之外的所有节点，输入流量应等于输出流量——传出边的流量之和应等于传入边的流量之和。====**给定一个流网络，最大流算法会找到一个在 ss 和 tt 之间具有最大可能值的流（Cormen 等人，2009）。**==
==我们====**假设**====注意力图是一个流网络，其中边的====**容量**====是注意力权重。使用任何最大流算法，我们可以计算从任何层中的任何节点到任何输入节点的最大注意力流。我们可以使用这个最大流值作为输入节点注意力的近似值。**在注意力流中，单个路径的权重是路径中边的权重的最小值，而不是权重的乘积。**此外，我们不能通过将这两个节点之间的所有路径的权重相加来计算节点 ss 对节点 tt 的注意力，因为路径之间可能存在重叠，这可能会导致重叠边的溢出。==
==**值得注意的是，所提出的两种方法都可以在**==[==**多项式时间**==](https://www.zhihu.com/search?q=%E5%A4%9A%E9%A1%B9%E5%BC%8F%E6%97%B6%E9%97%B4&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==**内计算。**== ==O(d∗n2)O(d*n^2 ) 是注意力展开的复杂度， O(d2∗n4)O(d^2 * n^4 ) 是注意力流的复杂度。其中 dd 是模型的深度， nn 是token的数量。==
==现在，我们仔细看看这三种注意力观点。图 1 描述了跨不同层的正确分类示例的原始注意力、注意力展开和注意力流。值得注意的是，====**第一层注意力展开和注意力流是相同的，它们与原始注意力的唯一区别是添加了残差连接**====。当我们移动到更高层时，我们看到残余连接逐渐消失。此外，====**与原始注意力相比，注意力展开和注意力流的模式在较高层中变得更加独特**====。==
[![](https://pic1.zhimg.com/50/v2-3f9a1067e7705cbcdc633f517e2d0c96_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-3f9a1067e7705cbcdc633f517e2d0c96_720w.jpg?source=1def8aca)
==图 2 和图 3 显示了三个示例中所有 6 层（y 轴）中的input token（x 轴）上的 CLS 嵌入的原始注意力、注意力展开和注意力流的权重。====  
  
====第一个例子与图1相同。====  
  
====第二个例子是“关于NNP大型系统的article====”，这是一个错误分类的例子，再次将“NNS”（==[==复数名词==](https://www.zhihu.com/search?q=%E5%A4%8D%E6%95%B0%E5%90%8D%E8%AF%8D&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==）更改为“NNP”（单数专有名词）会====**翻转模型的决策**====。==
==第三种情况不应该翻转，因为这个动词的主语是female而不是NNS。如果反转，说明这个模型注意到了错误的token（NNS）。看图3的attention rollout的第三列，NNS的权重大而female的权重小，说明rollout具有很好的可解释性。==
[![](https://pic1.zhimg.com/50/v2-8b8a68bfd4e4344d46c0908aed770e12_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-8b8a68bfd4e4344d46c0908aed770e12_720w.jpg?source=1def8aca)
[![](https://picx.zhimg.com/50/v2-381f160a434a11ab9fa3f3f795f5efea_720w.jpg?source=1def8aca)](https://picx.zhimg.com/50/v2-381f160a434a11ab9fa3f3f795f5efea_720w.jpg?source=1def8aca)
==对于所有情况，====**原始注意力权重在第三层之上几乎是均匀的**====（之前讨论过）。在正确分类的示例中，我们观察到注意力推出和注意力流都为动词的主语“文章”和attributor“系统”分配了相对较高的权重。对于错误分类的示例，注意力推出和注意力流都为“NNS”标记分配了相对较高的分数，该标记不是动词的主语。这可以解释模型的错误预测。==
==注意力展开和注意力流之间的主要区别在于，====**注意力流权重按照预期在最常参与的token集中进行摊销**====。注意力流可以指示一组对最终决策很重要的input token。因此它们之间没有明显的区别。另一方面，====**与注意力流权重相比，注意力展开权重更加集中**====，这对于第三个示例是合理的，但对于第二个示例则不那么重要。==
==此外，如表 1 和表 2 所示，与原始注意力相比，注意力展开和注意力流与空白分数和输入梯度的相关性更好，但====**注意力流权重比注意力推出更可靠**====。这两种方法之间的差异根源在于它们对注意力权重的不同看法。====**注意力流将它们视为容量，并且在算法的每一步中，它都会使用尽可能多的容量。因此，注意力流计算token indentity传播到更高层的最大可能性。尽管注意力展开将它们视为比例因子，并且在每一步中，它都允许token identity完全基于该比例因子传播到更高层。**====**这使得注意力展开比注意力流更严格，因此我们看到注意力展开为我们提供了更集中的注意力模式。然而，由于我们做了许多简化的假设，注意力推出的严格性并不会带来更准确的结果，而**====**注意力流的松弛似乎是一个有用的属性**====。==
==其实仔细看看，这个attention flow注意力图是不是更“流”畅了？flow的初衷也是这样，让attention流起来。==
==最后，为了说明注意力流和注意力展开在不同任务和不同模型上的应用，我们在两个预训练的 BERT 模型上进行了检查。我们使用== [==https://github.com/huggingface/transformers==](https://link.zhihu.com/?target=https%3A//github.com/huggingface/transformers) ==上提供的模型。==
==表 3 显示了从 DistillBERT（Sanh 等人，2019）模型的原始注意力、注意力推出和注意力流中获得的重要性得分的相关性，====**该模型经过微调以解决“SST-2”**====（Socher 等人，2013 年），====**来自glue benchmark的情绪分析任务**====（Wang et al., 2018）。尽管对于该模型，所有三种方法与输入梯度的相关性都非常低，但我们仍然可以看到注意力推出和注意力流略好于原始注意力。==
[![](https://pic1.zhimg.com/50/v2-3b7ef4c06614245e58777aabe04ba3e8_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-3b7ef4c06614245e58777aabe04ba3e8_720w.jpg?source=1def8aca)
==这里暴露了这个方法的一个致命问题：当baseline（raw注意力）很差的时候，rollout和flow也好不到哪里去。基于注意力==[==图的方法==](https://www.zhihu.com/search?q=%E5%9B%BE%E7%9A%84%E6%96%B9%E6%B3%95&search_source=Entity&hybrid_search_source=Entity&hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3209394886%7D)==往往很依赖模型本身，说到底性能还是个黑箱。==
==此外，在图 4 中，我们展示了将这些方法应用于预训练的 Bert 的示例，以====**了解它如何解析句子中的代词**====。我们在这里所做的就是====**向模型输入一个句子，掩盖一个代词**====。接下来，我们查看模型对屏蔽词的预测，并比较分配给“她”和“他”的概率。然后我们看看所有层的mask代词嵌入的原始注意力、注意力推出和注意力流权重。在第一个示例中，如图 4a 所示，注意力推出和注意力流彼此一致，并且与模型的预测一致。然而，原始注意力的最后一层似乎与模型的预测不一致，并且在不同层之间变化很大。在第二个例子中，在图4b中，只有注意力流权重与模型的预测一致。==
[![](https://pic1.zhimg.com/50/v2-ba2d927cf7a0a1ec6e3d3c090adf1e29_720w.jpg?source=1def8aca)](https://pic1.zhimg.com/50/v2-ba2d927cf7a0a1ec6e3d3c090adf1e29_720w.jpg?source=1def8aca)
==对于a，预测mask是his，那么显然应该注意到author。因此raw的表现是inconsistent且wrong，看rollout和flow就是consistent且correct。====  
  
====对于b，预测mask是her，那么显然应该注意到Mary。因此raw是inconsistent且wrong，rollout是consistent且wrong，而flow是consistent且correct。==
==总结一下，这篇文章提出了attention rollout和attention flow，用两种不同的图（权图和流图）建立基于attention map的token-level layer-wise importance，并且说明了方法的优越性。从结果来看，attention flow比attention rollout更鲁棒。但是因为整个方法依然是基于attention map的，所以普遍对于模型不鲁棒，且相距gradient-based attribution等方法仍有性能差距。==