---
Updated: 2024-04-03T09:23
tags:
  - AI->-Theory
URL: https://zhuanlan.zhihu.com/p/689067888?utm_id=0
Created: 2024-04-03T08:50
---
[![](https://picx.zhimg.com/v2-d64fc88430bb8a1c79c7ebf3d6bcb827_1440w.jpg?source=172ae18b)](https://picx.zhimg.com/v2-d64fc88430bb8a1c79c7ebf3d6bcb827_1440w.jpg?source=172ae18b)
随着Gemini 1M context length和Sora出世，如何训练超长上下文的大模型引起了大家广泛关注。
文本对比两种目前炙手可热长文本训练方法[DeepSpeed Ulysess](https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2309.14509)和[Ring-Attention](https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2310.01889)。23年末，二者几乎同时出现，但是设计方法大相径庭，可谓一时瑜亮。
DeepSpeed Ulysess：切分Q、K、V序列维度，核心卖点保持通信复杂度低，和GPU数无关，和序列长度呈线性关系。
Ring-Attention：切分Q、K、V序列维度，核心卖点是通信和计算重叠。
下面，我用FlashAttention Style的示意图来对比二者区别。图中_N_表示序列长度，_d_表示hidden_size=(hc * hs)，hc = head_cnt，hs=head_size，_P_表示GPU数目（图中_P_=4）。红色虚线表示通信，黑色虚线表示计算。
## DS-Ulysses
DS-Ulysses对Q、K、V沿着_N_维度切分成_P_份，三个分布式矩阵通过All2All变成沿_d_维度切分了。参见[我之前的文章](https://zhuanlan.zhihu.com/p/653968730)，All2All等价于一个分布式转置操作。之后就是正常的softmax(QK^T)V计算，可以用FlashAttention加速，得到结果再通过All2All转置回来。
因为All2All最有通信量是O(n)，n是message size，所以DS-Ulysses通信量位O(Nxd)，和P没关系。所以可以扩展到很多GPU上。Ulysses可以和ZeRO正交使用，ZeRO可以进一步切分Q、K、V，减少显存消耗。
Ulysses也有明显缺点，就是转置后切分维度d/P，我们希望d/P=hc/P * head_size，即对head_cnt所在维度切分，这样Attention的计算都在一张卡上完成，从而可以使用FlashAttention等单卡优化。但是如果遇到GQA或者MQA情况，K、V的head_cnt很小，导致GPU数目_P_也不能变得很大。
[![](https://pic4.zhimg.com/80/v2-b0c3d1d6b65ab9a9b4e59879b1f6d05b_720w.jpg)](https://pic4.zhimg.com/80/v2-b0c3d1d6b65ab9a9b4e59879b1f6d05b_720w.jpg)
## Ring-Attention
Ring-Attention就是FlashAttention（FA）的分布式版本，利用了online softmax这个大杀器。FlashAttention知乎上文章一搜一大把，[我也解读过](https://zhuanlan.zhihu.com/p/664061672)。这里推荐朱小霖的Ring-Attention文章，里面有一个非常好开源实现，在原始Ring基础上做了很多改进。
Ring-Attention采用FA的双循环计算模式，外层循环循环遍历Q，内层循环遍历K、V，使用online softmax增量更新最终结果，这和FA一模一样。当K、V计算穿越下图虚线部分时候，需要P2P通信，向相邻的GPU卡通信。通信和计算可以重叠起来。下图只画了一个head的Attention计算，可以并行做head_cnt个这样的计算。
Ring-Attention的分块大小（下图中的参数_c_）是可调节的。红色箭头表示的fp16格式KVCache的P2P通信量是 4\times c\times hs bytes。QKV分块计算量是 4\times hs\times c^2 FLOPS。所以只要满足计算量大于通信量，计算通信可以重叠起来，从而让通信开销消失。另外K、V的计算结果intermediate tensor只需要c x c大小部分，内存消耗很少。
Ring-Attention也有很多缺陷。比如Self-Atention计算有效部分一般是一个下三角，所以均匀切分Q的话，负载是不均衡的。这个问题 [@朱小霖](https://www.zhihu.com/people/5aca37fd8e5afa7c0338b7f0bd6211d3) 的实现做了优化。另外处理变长序列也不容易，这在SFT任务中比较常见。
## 二者比较
**通信量：**Ulysses完胜。
- DS-Ulysses三次All2All通信量3x**O(**_**N**_**x**_**d**_**)**。
- Ring-Attention ：N/P/c x (P-1)/PxO(Nxd)=**O(N^2xd/(Pxc))**，外层循环每个GPU需要N/P/c次迭代，内层循环每个GPU收发(P-1)/P x O(Nxd)数据。通信会随着序列长度增长而平方增长。所以非常依赖和计算重叠。
**通信方式：**Ring-Attention更鲁棒。
- DS-Ulysses需要All2All通信，对底层XCCL实现和网络硬件拓扑要求比较。一般All2All跨机器涉及拥塞控制，带宽比较差。
- Ring-Attention需要P2P通信，对网络需求很低。
**内存使用：**二者近似
二者Q、K、V显存消耗一致，对于QK计算结果intermediate tensor也都可以和FlashAttention等memory efficient attention方法兼容。二者也都可以和ZeRO、TP等其他并行方式兼容，所以我认为二者内存消耗类似。
**网络硬件泛化型：**Ring-Attention更好
- Ulysses没有重叠All2All和计算，不过这个并不是不可以做。
- Ring-Attention重叠P2P和计算，对分块大小_c_需要调节。
**模型结构泛化性：**Ring-Attention更好
- Ulysses会受到head数目限制，导致无法完成切分。尤其是和TP结合，有序列并行degree * 张量并行degree <= head_cnt的限制。
- Ring-Attention对网络结构参数不敏感。
**输入长度泛化性：**Ulysses更好。
- Ring-Attention处理变长输入很难处理，Ulysses无所谓。
总体来说，Ring-Attention侵入式修改Attention计算流程，实现复杂，同时对变长输入、并行分块大小选择、三角矩阵计算负载均衡等问题处理起来很麻烦。而Ulysses对Attention计算保持未动，实现简单，但是缺陷就是对num head参数敏感。
用奥卡姆剃刀原理，我觉得Ulysses后面也许会是主流方案。
## 混合并行（2024.4.1 update）
Ulysses和Ring可以组成一个混合序列并行方案。同时克服并行度<=num_head的限制，和避免P2P低效带宽利用。比如，在下面8xA100 NVLink, num_head=8上可以相比ring-flash-attn有18%和31%的训练和推理性能提升。
在下面8xA100 NVLink, num_head=2上，训练有54%训练性能提升，得益于利用All2All高带宽利用率的特性。
实现参见本人repo。