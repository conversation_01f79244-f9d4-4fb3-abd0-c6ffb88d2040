---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-18T10:35
tags:
  - AI->-Theory
  - AI->-ToDO
  - AI>-RL
Created: 2023-12-31T11:56
---
Reference
霍夫丁不等式（Hoeffding's Inequality）
马尔可夫决策过程
[详解大模型RLHF过程（配代码解读） - 知乎 (zhihu.com)](https://zhuanlan.zhihu.com/p/624589622?utm_medium=social&utm_oi=1239682420452487168&utm_psn=1635641325230948352&utm_source=wechat_session)
强化学习班
  
# Reference
```LaTeX
\#Easy RL 《蘑菇书》适合适合细嚼慢咽的读者
https://github.com/datawhalechina/easy-rl?tab=readme-ov-file
https://datawhalechina.github.io/easy-rl/#/
\#Easy NLP
https://github.com/alibaba/EasyNLP
https://www.yuque.com/easyx/easynlp/rxne07
\#JoyRL适合具有一定编程基础且希望快速进入实践应用的读者
https://github.com/datawhalechina/joyrl-book.git
\#AI Learning
https://github.com/tangyudi/Ai-learn
```
  
# 霍夫丁不等式（Hoeffding's Inequality）
$\begin{align}$
```LaTeX
假设我们有一个硬币，我们想要评估这个硬币是不是公平的。我们抛这个硬币 $n=$ 100 次，头像朝上 (我们称之为“成功") 的次数会有一个分布。这里每次抛硬币可以视为一个随机变量 $X_i$ ，它的值为 1 (如果硬币头像朝上) 或者 0 (如果硬币字面朝上) 。因此，每个 $X_i$ 的 $a_i=0$ 和 $b_i=1$ 。
如果硬市是公平的，那么 $\mu=0.5$ 。现在我们想知道，实际观测到的头像朝上的比率偏离 0.5 至少 0.1 (即 $t=0.1$ ) 的概率是多少。
根据霍夫丁不等式:
$$
\begin{aligned}
& P(|\bar{X}-0.5| \geq 0.1) \leq 2 \exp \left(-\frac{2 \cdot 100^2 \cdot 0.1^2}{\sum_{i=1}^{100}(1-0)^2}\right) \\
& P(|\bar{X}-0.5| \geq 0.1) \leq 2 \exp \left(-\frac{2 \cdot 100 \cdot 0.01}{100}\right) \\
& P(|\bar{X}-0.5| \geq 0.1) \leq 2 \exp (-2)
\end{aligned}
$$
计算这个表达式，我们可以得到头像朝上的比率偏离 0.5 至少 0.1 的概率上界。这个结果可以告诉我们，如果观测到的偏离很大，那么可能硬市并不公平。
```
$\begin{aligned}$
```LaTeX
在多臂老虎机 (Multi-Armed Bandit, MAB) 问题中，增量式蒙特卡洛 (Incremental Monte Carlo) 方法用于估计每个动作的值 (即拉动每个老虎机臂的长期回报) 。在传统的方法中，我们需要存储所有的回报，然后计算它们的平均值来估计动作的值。如果我们拉动老虎机臂 $N$ 次，那么计算平均值的时间复杂度为 $O(N)$ ，因为我们需要对所有 $N$ 次回报进行求和。
增量式蒙特卡洛方法采用另一种方式来更新估计的平均奖励，这样就不需要存储所有历史奖励值。它通过以下更新规则实现：
设 $Q_n$ 表示第 $n$ 次尝试后动作的估计值， $R_n$ 是第 $n$ 次尝试获得的回报。那么，当我们进行第 $n+1$ 次尝试并获得回报 $R_{n+1}$ 时，我们可以用下面的增量更新公式来更新动作的值:
$$
Q_{n+1}=Q_n+\frac{1}{n+1}\left(R_{n+1}-Q_n\right)
$$
这个更新规则的好处是，它不需要记住之前所有的回报值，只需要知道当前的估计值 $Q_n$ 和当前尝试的次数 $n$ 。每次更新的时间复杂度是常数 $O(1)$ ，因为它仅包含了几次基本的算术运算。
所以，使用增量式蒙特卡洛方法，我们可以在每次接收到新的奖励时快速更新动作的值，这极大地减少了算法的时间复杂度，使其从 $O(N)$ 优化至 $O(1)$ 。这在处理大量数据或需要实时更新的应用中特别有用。
```
$$
  
# 马尔可夫决策过程
  
$\begin{equation}V^\pi(\boldsymbol{s})=\sum_{\boldsymbol{a} \in \mathcal{A}} \pi(\boldsymbol{a} \mid \boldsymbol{s}) Q^\pi(\boldsymbol{s}, \boldsymbol{a})\end{equation}$
Bellman 公式
  
$\begin{equation}Q^\pi(\boldsymbol{s}, \boldsymbol{a})=r(\boldsymbol{s}, \boldsymbol{a})+\gamma \sum_{\boldsymbol{s}^{\prime} \in \mathcal{S}} P\left(\boldsymbol{s}^{\prime} \mid \boldsymbol{s}, \boldsymbol{a}\right) V^\pi\left(\boldsymbol{s}^{\prime}\right)\end{equation}$
![[Notion/AI/🦜🦜🦜Reinforcement Learning Memo List/attachments/Untitled.png|Untitled.png]]
$\pi(s) = \arg\max_a \left( Q(s, a) \right) = \arg\max_a \left( r(s, a) + \gamma \sum_{s'} P(s' | s, a) V(s') \right)$
$V^{k+1}(s) = \max_a \left( r(s, a) + \gamma \sum_{s' \in S} P(s' | s, a) V^k (s') \right)$
$V^{k+1}(s) = \sum_{a \in A} \pi(a | s) \left( r(s, a) + \gamma \sum_{s' \in S} P(s' | s, a) V^k (s') \right)$
  
  
# [详解大模型RLHF过程（配代码解读） - 知乎 (zhihu.com)](https://zhuanlan.zhihu.com/p/624589622?utm_medium=social&utm_oi=1239682420452487168&utm_psn=1635641325230948352&utm_source=wechat_session)
```LaTeX
https://zhuanlan.zhihu.com/p/624589622?utm_medium=social&utm_oi=1239682420452487168&utm_psn=1635641325230948352&utm_source=wechat_session
```
[AI 发现新抗生素，OpenAI 改进安全性，研究人员定义 AGI 等等 --- AI Discovers New Antibiotics, OpenAI Revamps Safety, Researchers Define AGI, and more (deeplearning.ai)](https://www.deeplearning.ai/the-batch/issue-231/)
[[2305.18290] Direct Preference Optimization: Your Language Model is Secretly a Reward Model (arxiv.org)](https://arxiv.org/abs/2305.18290?utm_campaign=The%20Batch&utm_source=hs_email&utm_medium=email&_hsenc=p2ANqtz--NdvYr0Fu7Gh2F34MUf_eZj8T0X0RgaluAJRvSnkTttkzl0Fk8qT4WTi4QTPFX0QSA1Ow2)
  
![[Notion/AI/🦜🦜🦜Reinforcement Learning Memo List/attachments/Untitled 1.png|Untitled 1.png]]
  
# 强化学习班
[https://xmind.ai/share/ZdNVhYk9?xid=OseI1TpO](https://xmind.ai/share/ZdNVhYk9?xid=OseI1TpO)
```LaTeX
\#source code
https://github.com/datawhalechina/joyrl
\#manual
https://datawhalechina.github.io/joyrl-book/#/joyrl_docs/main
【JoyRL开发周报，便于让大家关注我们的动态！】 https://datawhale.feishu.cn/docx/OM8fdsNl0o5omoxB5nXcyzsInGe?from=from_copylink
https://github.com/datawhalechina/joyrl-book
\#book(上面的manual也可以是书)
https://datawhalechina.github.io/easy-rl/
\#environment setup
conda create -n joyrl python=3.8.17
conda activate joyrl
pip install torch==1.10.0+cu113 torchvision==0.11.0+cu113 torchaudio==0.10.0 --extra-index-url https://download.pytorch.org/whl/cu113
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
pip install jupiter
pip install -U joyrl
sudo apt-get update
sudo apt-get install swig
pip install gymnasium[box2d]
\#under almalinux
sudo dnf install mesa-libGL
import joyrl
if __name__ == "__main__":
    print(joyrl.__version__)
    yaml_path = "./presets/ClassControl/CartPole-v1/CartPole-v1_DQN.yaml"
    joyrl.run(yaml_path = yaml_path)
\#monitoring
cd /opt/workspace/researcher/joyrl
tensorboard --logdir=tasks --bind_all

\#gym code change
https://github.com/openai/gym/blob/master/gym/core.py
```
![[Notion/AI/🦜🦜🦜Reinforcement Learning Memo List/attachments/Untitled 2.png|Untitled 2.png]]
  
  
  
  
快速扫描蘑菇书 -》 看JoyRL如何实现 -》看录像同时细读蘑菇书 （同时可以看迪哥录像)  
  
西瓜速读 -》看录像  
  
  
  
```LaTeX
今天正式开始《深度强化学习基础与实践(二)》航海之旅咯～
Task01：马尔可夫过程、DQN算法回顾（3天）
教程：第二章、第七章、第八章，打卡截止时间：1月18日03:00
任务：
1.学习第二章马尔可夫过程、第七、八章DQN算法，回顾课程知识；
2.整理学习笔记，输出链接【CSDN、github、简书、Datawhale论坛等】在小程序中打卡。
============
课程内容参见下方《课程链接》
============
所有小伙伴的每个学习任务打卡需要有至少50字的学习总结。此外，有精力的小伙伴可以去平台写总结，获得链接，填了可以得到作业反馈。
【课程链接】
https://github.com/datawhalechina/joyrl-book
【学习者手册】
https://mp.weixin.qq.com/s/pwWg0w1DL2C1i_Hs3SZedg
【小程序使用手册】
https://mp.weixin.qq.com/s/iPmzb72Yk0mhIA2NYezXDg

实际问题中，有很多例子其实是不符合马尔可夫性质的，比如我们所熟知的棋类游戏，因为在我们决策的过程中不仅需要考虑到当前棋子的位置和对手的情况，还需要考虑历史走子的位置例如吃子等。
如果状态数是无限的，通常会使用另一种方式来对问题建模，称为泊松（ 
 ）过程。这个过程又被称为连续时间马尔可夫过程，它允许发生无限次事件，每个事件发生的机会相对较小，但当时间趋近于无穷大时，这些事件以极快的速度发生。尽管泊松过程在某些应用领域中非常有用，
但是对于大多数强化学习场景，还是用的有限状态马尔可夫决策过程。
非常重要的点就是，状态转移矩阵是环境的一部分，跟智能体是没什么关系的，而智能体会根据状态转移矩阵来做出决策
马尔可夫链（马尔可夫过程）的基础上增加奖励元素就会形成马尔可夫奖励过程（Markov reward process, MRP），在马尔可夫奖励过程基础上增加动作的元素就会形成马尔可夫决策过程，也就是强化学习的基本问题模型之一
Markov Chain -> MRP ->MDP
马尔可夫决策过程描述成一个今天常用的写法，即用一个五元组 S A R P gama 来表示
动态规划问题有三个性质，最优化原理、无后效性和有重叠子问题。其中有重叠子问题不是动态规划问题的必要条件
```
  
```LaTeX
./joyrl/presets/Box2D/LunarLander-v2/LunarLander-v2_PPO_Train.yaml
device: cuda
env_name: gym
max_buffer_size: 1000000
------------------------------------------------
./joyrl/joyrl/algos/PPO/policy.py
<<def learn()>>
# 检查 actions 是否已经是二维的
if actions.dim() == 1:
   actions = actions.unsqueeze(-1)  # 或 actions.view(-1, 1)        
torch_dataset = Data.TensorDataset(states, actions, old_log_probs, returns)
...
for _ in range(self.k_epochs):
            for batch_states, batch_actions, batch_old_log_probs, batch_returns in train_loader:
                # 使用 batch_states, batch_actions, batch_old_log_probs, batch_returns
                values = self.critic(batch_states)  # 直接使用 batch_states
                advantages = batch_returns - values.detach()  # 计算 advantages
                # 更新动作和日志概率的形状以匹配模型的期望输入
                _ = self.actor(batch_states) # list
                batch_actions = batch_actions.squeeze(dim=1)
                new_log_probs = self.actor.action_layers.get_log_probs_action(batch_actions).unsqueeze(dim=1)
                entropy_mean = self.actor.action_layers.get_mean_entropy()
                # 继续您的损失计算和优化逻辑
                ratio = torch.exp(new_log_probs - batch_old_log_probs)  # 计算比率
                surr1 = ratio * advantages
                surr2 = torch.clamp(ratio, 1 - self.eps_clip, 1 + self.eps_clip) * advantages
                self.actor_loss = - (torch.mean(torch.min(surr1, surr2)) + self.entropy_coef * entropy_mean)
                self.critic_loss = self.cfg.critic_loss_coef * nn.MSELoss()(batch_returns, values)
                # 优化器更新逻辑
                if self.share_optimizer:
                    self.optimizer.zero_grad()
                    self.tot_loss = self.actor_loss + self.critic_loss
                    self.tot_loss.backward()
                else:
                    self.actor_optimizer.zero_grad()
                    self.actor_loss.backward()
                    self.actor_optimizer.step()
                    self.critic_optimizer.zero_grad()
                    self.critic_loss.backward()
                    self.critic_optimizer.step()
            self.update_summary()
--------------------------------------------------
/databank/app/raylab/joyrl/joyrl/framework/tester.py
def _eval_policy(self):
for _ in range(self.cfg.online_eval_episode):
                    ep_reward, ep_step = 0, 0
                    while True:
                        action = self.policy.get_action(self.curr_obs, mode = 'predict')
                        action = action[0] if isinstance(action, list) else action
                        
                        obs, reward, terminated, truncated, info = self.env.step(action)
--------------------------------------------------
<<Main>>
import joyrl
if __name__ == "__main__":
    print(joyrl.__version__) # print version
    yaml_path = "./raylab/joyrl/presets/Box2D/LunarLander-v2/LunarLander-v2_PPO_Train.yaml"
    joyrl.run(yaml_path = yaml_path)
```
  
  
  
```LaTeX
登月计划
/opt/workspace/researcher/joyrl/PPO-PyTorch/PPO.py
```