---
DocFlag:
  - Reference
  - Tested
Updated: 2023-04-14T21:22
tags:
  - AI->-Chatbot
  - AI->-Programming
  - AI->-Tools
Created: 2023-03-05T02:03
Reading Status: Finished
---
![[chatgpt-client.png]]
## 引子
OpenAI 这两天发布了 [ChatGPT API](https://platform.openai.com/docs/guides/chat)，基于 `gpt-3.5-turbo` 模型，这是一个 GPT-3.5 的优化版本，用于支持开发者把 ChatGPT 集成到自己的产品中，同时把 API 调用价格降到 $0.002 每千 token，意味着处理 100万字符的文本只需要 2 美元，也就是差不多十几块钱人民币，效果更好、价格更低，这让 ChatGPT API 更具性价比，因此这两天基于 ChatGPT API 的各种套壳应用如雨后春笋般大量冒出。
我也来凑个热闹，试一试水，正好在我今天新建的 ChatGPT 互助讨论群里有人问有没有命令行版 ChatGPT，那就拿它来开刀吧：
老规矩，还是面向 ChatGPT 编程来实现这个命令行版 ChatGPT 应用。
在《[面向 ChatGPT 编程实现全栈开发的 18 种方法](https://geekr.dev/posts/chatgpt-oriented-programming)》这篇教程中，我在最后说到在面向 ChatGPT 编程的时候，需要牢记两个原则：第一，知道你在做什么，第二，不要相信 ChatGPT 的代码。
落地到实践的时候，就是在与 ChatGPT 合作形成的结对编程联盟中，作为开发者我们需要承担代码设计、架构、编排与审核（CodeReview）的职责，对方案和结果负责，而具体的代码片段编写这种纯体力活则交由 ChatGPT 完成。
接下来，我们将遵循这个思路实现命令行版 ChatGPT 应用。
## 代码设计
如我在微信群里所说，这个需求确实很简单 —— 通过编程在控制台应用代码中调用 ChatGPT API 接口，实现一个命令行版的 ChatGPT，几十行代码就能搞定。不过这里仍然需要做一些简单的设计：
1. 调用 API 需要传递 API KEY，我们不希望这个 KEY 硬编码在代码中，而是从系统环境变量读取，从而让代码更安全可维护；
2. 调用封装好的 Go OpenAI 库与 API 接口进行交互，避免通过 HTTP 协议与原生 API 交互编写大量重复代码，让代码更简洁优雅；
3. 做一个给客户使用的产品，美观会带来更好的用户体验，所以我希望即便是命令行应用，也尽可能让交互和输出更美观一些。
对于第 2 点，可以使用 [go-gpt3](https://github.com/sashabaranov/go-gpt3) 库，这是一个通过 Go 封装的 OpenAI API 调用库。
对于第3点，可以使用 [glamour](https://github.com/charmbracelet/glamour) 库，这是一个 Go 语言实现的、能够在兼容 ANSI 终端基于样式渲染 Markdown 文本的第三方库，它是让命令行更美观的开源项目 [Charm](https://charm.sh/) 的一部分。
因为项目很简单，又是在客户端本地使用，所以不需要做什么复杂的架构，下面直接进入编码部分。
## 代码编写
面向 ChatGPT 编程的核心就是把需求尽可能准确全面地转化为 Prompt 传递给 ChatGPT，这里有产品需求（来自业务和产品），也有代码设计和架构上的需求（来自开发者），然后让它生成代码，这是作为一个合格的 Prompt 工程师自我修养的必要组成部分。
在《[ChatGPT 提示的艺术 —— 编写清晰有效提示指南（二）](https://geekr.dev/posts/chatgpt-prompt-guide-2)》这篇教程中，我已经给大家介绍了编写清晰有效 Prompt 的原则、做法和技巧，感兴趣的可以去看下，这里我先将我的需求转化为 Prompt 让 ChatGPT 替我编写对应的 Go 代码实现：
### 代码优化
看起来不错，基本流程没问题，但是代码审核会发现，它现在把输入的 Prompt 写死了，并不能动态接收用输入，而且运行一次后就退出了，这不是 ChatGPT 的问题，而是我们的需求并没有明确这一点，作为一个完整的需求和程序，需要说明是什么，怎么样，什么时候开始，什么时候退出。
不过细心的同学可能还留意到 `go-gpt3` 包引入的时候没有设置别名，会导致运行时出错，同时调用的 OpenAI API 接口也不对，最新的 ChatGPT API 接口方法应该是 `CreateChatCompletion`，可能是太新的缘故，ChatGPT 还没有学习到这里，翻车了翻车了，不过这都属于 ChatGPT 后面要优化的点了。以及使用了另一个同名的包，这个也需要明确告知它。
细节上还是需要优化，现在我们基于这些要点先来完善我们的 Prompt：
这一次，我们更加细化了程序的行为，用户输入 `start` 启动，输入 `quit` 退出，在此期间则不断读取用户输入，返回 ChatGPT 处理结果并渲染到控制台输出，另外，我还在读取系统环境变量时嘱咐通过 `os.Getenv` 获取，因为有时候发现 ChatGPT 输出不稳定，会尝试从 `.env` 文件读取 `OPENAI_API_KEY`，当然这不是 ChatGPT 的问题，是我们没有给出明确清晰的 Prompt。
现在再来看 ChatGPT 输出的代码，就更完善了：
package main
import (
"context"
"fmt"
"os"
"strings"
"github.com/charmbracelet/glamour"
"github.com/common-nighthawk/go-figure"
gpt3 "github.com/sashabaranov/go-gpt3"
)
func main() {
// 获取 OpenAI API Key
apiKey := os.Getenv("OPENAI_API_KEY")
if apiKey == "" {
fmt.Println("请设置 OPENAI_API_KEY 环境变量")
return
}
// 初始化 Glamour 渲染器
renderStyle := glamour.WithEnvironmentConfig()
mdRenderer, err := glamour.NewTermRenderer(
renderStyle,
)
if err != nil {
fmt.Println("初始化 Markdown 渲染器失败")
return
}
// 输出欢迎语(命令行应用启动界面)
myFigure := figure.NewFigure("ChatGPT", "", true)
myFigure.Print()
fmt.Println("输入 start 启动应用，输入 quit 退出应用")
// 创建 ChatGPT 客户端
client := gpt3.NewClient(apiKey)
if err != nil {
fmt.Printf("创建客户端失败: %s\n", err.Error())
return
}
messages := []gpt3.ChatCompletionMessage{
{
Role: "system",
Content: "你是ChatGPT, OpenAI训练的大型语言模型, 请尽可能简洁地回答我的问题",
},
}
// 读取用户输入并交互
var userInput string
for {
fmt.Scanln(&amp;userInput)
if strings.ToLower(userInput) == "start" {
fmt.Println("ChatGPT 启动成功，请输入您的问题：")
} else if strings.ToLower(userInput) == "quit" {
fmt.Println("ChatGPT 已退出")
return
} else if userInput != "" {
messages = append(
messages, gpt3.ChatCompletionMessage{
Role: "user",
Content: userInput,
},
)
// 调用 ChatGPT API 接口生成回答
resp, err := client.CreateChatCompletion(
context.Background(),
gpt3.ChatCompletionRequest{
Model: gpt3.GPT3Dot5Turbo,
Messages: messages,
MaxTokens: 1024,
Temperature: 0,
N: 1,
},
)
if err != nil {
fmt.Printf("ChatGPT 接口调用失败: %s\n", err.Error())
continue
}
// 格式化输出结果
output := resp.Choices[0].Message.Content
mdOutput, err := mdRenderer.Render(output)
if err != nil {
fmt.Printf("Markdown 渲染失败: %s\n", err.Error())
continue
}
fmt.Println(mdOutput)
messages = append(
messages, gpt3.ChatCompletionMessage{
Role: "assistant",
Content: output,
},
)
}
}
}

> gpt3 别名和 CreateChatCompletion 方法调用相关的代码还是需要手动调整，不过这也是我前面说的面向 ChatGPT 编程的原则之一，最后一定要审核 ChatGPT 的代码，它目前对于最新的知识还是有一定的迟滞性。
代码细节我就不展开解释了，有不明白的地方可以参考我在《[面向 ChatGPT 编程实现全栈开发的 18 种方法](https://geekr.dev/posts/chatgpt-oriented-programming)》这篇教程中代码解释部分提供的方法自行去基于 ChatGPT 查看。
## 效果展示
最后我们在终端体验一下这个命令行版 ChatGPT，我这里使用的是 Windows WSL 终端，Windows 终端本身体验其实不太好，尤其是中文输入的时候，删除字符特别费劲，且很容易造成消息变形，如果是 Mac 或者 Ubuntu 终端可能效果会更好一些。
首先我们启动这个应用，如果没有设置 `OPENAI_API_KEY` 这个系统环境变量（运行 `export OPENAI_API_KEY={你的 OpenAI SECRET KEY}` 命令设置即可），会提示你设置：
如果已经设置，会进入下面这样的启动欢迎界面：
输入 `start` 即可启动应用，然后我们在命令行输入问题，回车，就会将问题提交给 ChatGPT，ChatGPT 处理的结果会返回并输出到控制台，这里的格式经过了 Glamour 库的美化：
因为是 for 循环，所以你可以持续提问、得到答案，直到输入 `quit` 退出应用。

> 终端需要能够访问 OpenAI API 才能调用成功，这意味着命令行也要支持科学上网。
好了，这就是我们基于最新 ChatGPT API 实现的命令行版 ChatGPT 应用，因为 ChatGPT API 是前两天才发布的，所以看起来 ChatGPT 并没有学习到这个最新的 API 如何调用，存在迟滞性，进而导致编写的代码并不能直接满足需求，需要人为介入去修改，希望未来 ChatGPT 能够在这一块上有所进化。
本项目源码已经提交到 Github，有需要的自提：[geekr-dev/chatgpt-client: 命令行版 ChatGPT 应用](https://github.com/geekr-dev/chatgpt-client)。
  
  
---
  
```JavaScript
\#Go land environment setup
Settings -> Go -> GOPATH -> Remove project gopath and module GOPATH
Settings -> Go -> GO Modules -> Enable Go modules integration
\#login netcaster.jetbrains.space
\#Code one main.go
cd /opt/workspace/gptcli
go mod init gptcli
go mod tidy
go list -m all
\#when dependency library got updated
go get -u <library-path>@<version> or go get -u ./...
go mod tidy
```
+可以save和load 过去对话  
查看可以在console显示图像方法+  
```JavaScript
package main
import (
    "github.com/nsf/termbox-go"
)
func main() {
    err := termbox.Init()
    if err != nil {
        panic(err)
    }
    defer termbox.Close()
    // Register a handler for the Ctrl+Shift+M key combination
    termbox.SetInputMode(termbox.InputEsc | termbox.InputMouse)
    termbox.SetOutputMode(termbox.Output256)
    termbox.Flush()
    ctrlShiftM := termbox.KeyCtrlShiftM
    termbox.SetInputMode(termbox.InputEsc | termbox.InputMouse)
    termbox.Flush()
    for {
        switch ev := termbox.PollEvent(); ev.Type {
        case termbox.EventKey:
            if ev.Ch == 0 && ev.Key == ctrlShiftM {
                // Display popup window for app setup
                // Implement the popup window here
                // ...
                // Once the user closes the window, return to the main user interface
                termbox.Clear(termbox.ColorDefault, termbox.ColorDefault)
                termbox.Flush()
            }
        case termbox.EventError:
            panic(ev.Err)
        }
    }
}
```
https://github.com/gdamore/tcell
```JavaScript
package main
import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
)
type ChatCompletionMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
	Name    string `json:"name,omitempty"`
}
type ChatCompletionMessages []ChatCompletionMessage
func main() {
	// 假设有以下消息
	messages := ChatCompletionMessages{
		{
			Role:    "bot",
			Content: "Hello!",
			Name:    "gpt-3",
		},
		{
			Role:    "user",
			Content: "Hi, how are you?",
			Name:    "John",
		},
	}
	// 将消息保存到文件
	err := saveMessagesToFile("messages.json", messages)
	if err != nil {
		fmt.Println("保存消息失败：", err)
		return
	}
	fmt.Println("消息已保存到文件 messages.json")
	// 从文件中读取消息
	loadedMessages, err := loadMessagesFromFile("messages.json")
	if err != nil {
		fmt.Println("加载消息失败：", err)
		return
	}
	fmt.Println("已成功加载以下消息：")
	for _, msg := range loadedMessages {
		fmt.Printf("%v: %v\n", msg.Name, msg.Content)
	}
}
func saveMessagesToFile(filename string, messages ChatCompletionMessages) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	encoder := json.NewEncoder(file)
	err = encoder.Encode(messages)
	if err != nil {
		return err
	}
	return nil
}
func loadMessagesFromFile(filename string) (ChatCompletionMessages, error) {
	var messages ChatCompletionMessages
	file, err := os.Open(filename)
	if err != nil {
		return messages, err
	}
	defer file.Close()
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&messages)
	if err != nil {
		return messages, err
	}
	return messages, nil
}
```
---
```JavaScript
You are Go expert, Provide code examples as always. Unless specified by the user, always provide examples written in Go language.
Also you must follow with below rules to make sure the response you make is reliable. don't try to make up one answer.
//User Message
Update the onboarding component to include a skip button // Implicit search for the login component
//Assistant response
<|user_intonation analysis={"it looks like the user is trying to update the onboarding component in their codebase"}|>
<|determination isCodebaseSpecific={true} requiresSearch={true} requiresLatest={true} requires_Code_Example={true}|>
1. When you don't have the answer, please reply with "I don't know". It's important to provide reliable answers.
2. Treat people from different backgrounds equally. Avoid making assumptions based on stereotypes.
3. Identify the language of the text first, then summarize it using the original language.
4. Break down complex tasks into smaller subtasks. Consider showing intermediate outputs to users.
5. For long reasoning problems, split the problem into pieces to solve incrementally.
6. Take your time to think things through.
7. Always provide examples and sample code. This is a must!
8. Generate multiple candidates for output and choose the best one.
Let's think step by step.
```
  
  
---
```Go
package main
import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"regexp"
	"runtime"
	"runtime/debug"
	"runtime/pprof"
	"sort"
	"strings"
	"time"
	"github.com/aretext/aretext/app"
	"github.com/charmbracelet/glamour"
	figure "github.com/common-nighthawk/go-figure"
	"github.com/fatih/color"
	"github.com/gdamore/tcell/v2"
	"github.com/manifoldco/promptui"
	openai "github.com/sashabaranov/go-openai"
)
type fileInfoList []os.DirEntry
func (f fileInfoList) Len() int {
	return len(f)
}
func (f fileInfoList) Less(i, j int) bool {
	info1, err := f[i].Info()
	if err != nil {
		fmt.Println("Error:", err)
	}
	info2, err := f[j].Info()
	if err != nil {
		fmt.Println("Error:", err)
	}
	return info1.ModTime().Before(info2.ModTime())
	//	return f[i].Info().ModTime().Before(f[j].ModTime())
}
func (f fileInfoList) Swap(i, j int) {
	f[i], f[j] = f[j], f[i]
}
func HelpMenu() {
	color.HiRed("  /h: Output Help | /r: reset conversation | /ai: Submit to OpenAI | /q: Quit |")
	color.HiRed("  /m: Select Model | /sm: Show Model |")
	color.HiYellow("  /save: Save messages | /load xxx.log: Load previous session | /box: vim mode |")
	color.HiCyan("  /ls: list files under current directory | /cat xxx.log: cat xxx.log |")
	color.HiGreen("  /tr (jp|en|cn) {content}: Fastly translate {content} in {language} |")
	color.HiWhite("  /vm: Make AI like VM server | /game: Play Mud game with AI |")
	color.HiMagenta("  /fc: Switch to full context mode  | /uc: Switch to user msg only mode(safe token) |")
}
func getSystemBehavior() string {
	file, err := os.Open("system.cfg")
	if err != nil {
		fmt.Println("Error opening file and will use default AI role", err)
		return "You are an expert in various fields, able to understand English and Chinese and Japanese, and can provide enthusiastic and patient assistance in various aspects. Happy to help others as much as you can and always give some suggestions in advance. Before providing any advices, you will make sure what you are going to provide should be latest and updated content. Your name is Jack."
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	var content string
	for scanner.Scan() {
		content += scanner.Text() + "\n"
	}
	if err := scanner.Err(); err != nil {
		fmt.Println("Error reading file:", err)
		return ""
	}
	return content
}
// Token Mode
var flagToken string = "FullMode"
var APIModel string = openai.GPT3Dot5Turbo
func main() {
	// Get OpenAI API Key
	apiKey := ""
	if runtime.GOOS == "windows" {
		apiKey = "***************************************************"
	} else {
		apiKey = os.Getenv("OPENAI_API_KEY")
	}
	if apiKey == "" {
		logError("Please setup OPENAI_API_KEY in OS environment.")
		return
	}
	// Welcome message
	printOutput("-------------------------------------------------------------------------------------")
	figure.NewFigure("ChatGPT", "starwars", true).Print()
	fmt.Println("================= [author -- <NAME_EMAIL>] ========================================")
	fmt.Println("==================================================================================================")
	HelpMenu()
	printOutput("-------------------------------------------------------------------------------------")
	systemBehavior := getSystemBehavior()
	messages := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: systemBehavior,
		},
	}
	/*	var client *openai.Client
		//create openai session
		if runtime.GOOS == "windows" {
			config := openai.DefaultConfig("***************************************************")
			// 创建一个 HTTP Transport 对象，并设置代理服务器
			proxyUrl, err := url.Parse("http://27.42.168.46:55481")
			if err != nil {
				logError(err.Error())
			}
			transport := &http.Transport{
				Proxy: http.ProxyURL(proxyUrl),
			}
			// 创建一个 HTTP 客户端，并将 Transport 对象设置为其 Transport 字段
			config.HTTPClient = &http.Client{
				Transport: transport,
			}
			client = openai.NewClientWithConfig(config)
		} else {
			client = openai.NewClient(apiKey)
		}
	*/
	client := openai.NewClient(apiKey)
	//if err != nil {
	//	logError("Session creation failed")
	//	fmt.Printf("Create ChatGPT session failed: %s\n", err.Error())
	//	return
	//}
	// 读取用户输入并交互
	userInput := ""
	scanner := bufio.NewScanner(os.Stdin)
	var lines []string
	nameRe := regexp.MustCompile(`^/load\s+\d{16}\.log$`)
	catRe := regexp.MustCompile(`^/cat\s+\w+\.log$`)
	langRe := regexp.MustCompile(`^/tr\s+(jp|en|cn)\s+".*?"$`)
	for {
		if flagToken == "FullMode" {
			fmt.Print(time.Now().Format("2006/01/02 15:04:05 F>"))
		} else {
			fmt.Print(time.Now().Format("2006/01/02 15:04:05 U>"))
		}
		//fmt.Scanln(&userInput)
		for scanner.Scan() {
			line := scanner.Text()
			if len(line) == 0 {
				continue
			}
			if nameRe.MatchString(line) {
				userInput = line
				lines = lines[:0]
				goto EndLoop
			}
			if catRe.MatchString(line) {
				userInput = line
				lines = lines[:0]
				goto EndLoop
			}
			if langRe.MatchString(line) {
				userInput = line
				lines = lines[:0]
				goto EndLoop
			}
			switch line {
			case "/ls":
				userInput = "ls"
				lines = lines[:0]
				goto EndLoop
			case "/r":
				userInput = "reset"
				lines = lines[:0]
				goto EndLoop
			case "/q": // ctrl+c
				fmt.Println("Exiting...")
				userInput = "bye"
				lines = lines[:0]
				goto EndLoop
			case "/h": // ctrl+w
				userInput = "help"
				lines = lines[:0]
				goto EndLoop
			case "/m": // ctrl+w
				userInput = "SelectModel"
				lines = lines[:0]
				goto EndLoop
			case "/sm": // ctrl+w
				userInput = "ShowModel"
				lines = lines[:0]
				goto EndLoop
			case "/save":
				userInput = "save_session"
				lines = lines[:0]
				goto EndLoop
			case "/box":
				userInput = "box"
				lines = lines[:0]
				goto EndLoop
			case "/fc":
				userInput = "FullMode"
				lines = lines[:0]
				goto EndLoop
			case "/uc":
				userInput = "UserMode"
				lines = lines[:0]
				goto EndLoop
			case "/ai": // ctrl+a
				userInput = strings.Join(lines, "\\n")
				lines = lines[:0]
				goto EndLoop
			case "/vm":
				userInput = `
[DEBUG_MODE:ON; You are an AI that acts as a CentOS Linux system terminal.All responses from here on out are terminal responses to my input in your responses, 
please escape the $sign, so that it doesn't render strikes. Do not add any further commentary or explanation.Do not respond with anything other than what a terminal response might be based on command issued. The "ray.sheng" user has just logged in.]
`
				lines = lines[:0]
				goto EndLoop
			case "/game":
				userInput = `
來玩文字冒險遊戲。由你來敘述一個核戰後的末日，末日充滿倖存者、掠奪者、變異生物以及超級變種人，以第二人稱「你」來稱呼主角。 
描述要詳細，包括人、事、時、地、物都要詳細描述，描述時要盡量避免重複性的語句，包含血腥或是情色等等事件都要完整敘述，其他角色若是有講話，就要把那些講出來的語句描述出來。若是主角的視線沒有被遮蔽，當有新的角色或生物出現時要完整敘述他的外觀、性別，當有新的物品出現時要完整敘述物品的樣貌與種類。 
除非主角死亡或是我輸入故事結束，不然當你敘述完主角的一個動作後就要問著「你會怎麼做？」並停止敘述，直到我輸入下一個動作。你不可以給意見，不可以給出選項讓我選擇，不可以質疑我下的判斷，不能用現有的價值觀來當作末日裡的價值觀。冒險的過程並非永遠正面充滿希望，也有可能殘酷並充滿絕望，冒險的過程也不能老是遭遇同一件事情。 
在我輸入完動作後，主角進行這個動作的成功與否你都要先擲一個二十面骰子來判斷是否成功還是失敗，並把骰出的點數結果以及事件是否成功放在（）括弧內告訴我後再繼續描述事件，請你記住骰子的結果大於等於10點事件會成功，骰子的結果小於10點事件會失敗。若是骰子的結果大於等於19點是大成功，大成功事件會以誇張且戲劇化或是出乎預料有創意的方式成功。若是骰子的結果小於等於2點是大失敗，大失敗事件會以誇張且戲劇化或是出乎預料有創意的方式失敗。
`
				lines = lines[:0]
				goto EndLoop
			default:
				lines = append(lines, line)
			}
		}
	EndLoop:
		if nameRe.MatchString(userInput) {
			messages = messages[:0]
			parts := strings.Split(userInput, " ")
			fname := strings.TrimSpace(parts[len(parts)-1])
			loadmessages, err := loadMessagesFromFile(fname)
			if err != nil {
				logError("Messagefile load failed:" + err.Error())
			} else {
				fmt.Println("Load previous session successfully!")
			}
			messages = loadmessages
			userInput = ""
		}
		if catRe.MatchString(userInput) {
			printOutput("-------------------------------------------------------------------------------------")
			fmt.Println("Here is the content of the file:")
			fileName := strings.TrimSpace(strings.TrimPrefix(userInput, "/cat "))
			file, err := os.Open(fileName)
			defer file.Close()
			if err != nil {
				logError("Failed: " + err.Error())
			} else {
				scanner := bufio.NewScanner(file)
				for scanner.Scan() {
					fmt.Println(scanner.Text())
				}
				if err := scanner.Err(); err != nil {
					logError("Error:" + err.Error())
				}
			}
			printOutput("-------------------------------------------------------------------------------------")
			userInput = ""
		}
		if langRe.MatchString(userInput) {
			printOutput("-------------------------------------------------------------------------------------")
			parts := strings.Split(userInput, " ")
			switch strings.TrimSpace(parts[1]) {
			case "jp":
				content := strings.TrimSpace(strings.TrimPrefix(userInput, "/tr jp "))
				userInput = "请把这个{" + content + "}翻译成日语"
			case "en":
				content := strings.TrimSpace(strings.TrimPrefix(userInput, "/tr en "))
				userInput = "请把这个{" + content + "}翻译成英语"
			case "cn":
				content := strings.TrimSpace(strings.TrimPrefix(userInput, "/tr cn "))
				userInput = "请把这个{" + content + "}翻译成中文"
			default:
				fmt.Println("Not support this Language!")
				userInput = ""
			}
			interactWithChatGPT(client, &messages, userInput)
			printOutput("-------------------------------------------------------------------------------------")
			userInput = ""
		}
		switch userInput {
		case "ls":
			printOutput("-------------------------------------------------------------------------------------")
			files, err := os.ReadDir(".")
			if err != nil {
				fmt.Println("Error:", err)
			} else {
				sort.Sort(fileInfoList(files))
				for _, file := range files {
					info, err := file.Info()
					if err != nil {
						fmt.Println("Error:", err)
						return
					}
					fmt.Printf("%s\t%s\n", file.Name(), info.ModTime().String())
				}
			}
			printOutput("-------------------------------------------------------------------------------------")
		case "reset":
			fmt.Println("Quitting session...")
			messages = messages[:0]
			messages = []openai.ChatCompletionMessage{
				{
					Role:    "system",
					Content: systemBehavior,
				},
			}
			fmt.Println("Cleanup my memory!")
			printOutput("-------------------------------------------------------------------------------------")
		case "help":
			printOutput("-------------------------------------------------------------------------------------")
			HelpMenu()
			printOutput("-------------------------------------------------------------------------------------")
		case "box":
			if runtime.GOOS == "windows" {
				fmt.Println("The feature not supported on Windows!")
			} else {
				userInput = showEditor()
				interactWithChatGPT(client, &messages, userInput)
			}
		case "SelectModel":
			setupModel()
			printOutput("-------------------------------------------------------------------------------------")
			fmt.Println("Current Model: " + APIModel)
		case "ShowModel":
			fmt.Println("Current Model: " + APIModel)
		case "FullMode":
			flagToken = "FullMode"
		case "UserMode":
			flagToken = "UserMode"
		case "save_session":
			currentMicro := time.Now().UnixNano() / 1000
			filename := fmt.Sprintf("%d.log", currentMicro)
			err := saveMessagesToFile(filename, messages)
			if err != nil {
				logError("Session saving failed: " + err.Error())
			} else {
				logError("Session saved successfully ")
			}
		case "bye":
			fmt.Println("Goodbye!")
			return
		default:
			interactWithChatGPT(client, &messages, userInput)
		}
		userInput = ""
	}
}
func setupModel() {
	prompt := promptui.Select{
		Label: "Select which model you use:",
		Items: []string{"gpt-3.5", "gpt-4", "davinci", "伤寒论"},
	}
	_, result, err := prompt.Run()
	if err != nil {
		fmt.Printf("Prompt failed %v\n", err)
	}
	switch result {
	case "gpt-3.5":
		APIModel = "gpt-3.5-turbo"
	case "gpt-4":
		APIModel = "gpt-4"
	case "davinci":
		APIModel = "text-davinci-003"
	case "伤寒论":
		APIModel = "davinci:ft-personal:kanbo-2023-03-03-14-51-06"
	default:
		APIModel = "gpt-3.5-turbo"
	}
	fmt.Println("You choose: " + result)
}
func interactWithChatGPT(client *openai.Client, chatHist *[]openai.ChatCompletionMessage, userInput string) {
	if userInput == "" {
		return
	}
	// Call ChatGPT API and get reply
	fmt.Println("AI is thinking ...")
	done := make(chan bool)
	go spinner(done)
	*chatHist = append(
		*chatHist, openai.ChatCompletionMessage{
			Role:    "user",
			Content: userInput,
		},
	)
	output := ""
	if (APIModel == "gpt-3.5-turbo") || (APIModel == "gpt-4") {
		resp, err := client.CreateChatCompletion(
			context.Background(),
			openai.ChatCompletionRequest{
				Model:           APIModel,
				Messages:        *chatHist,
				MaxTokens:       1700,
				Temperature:     0.7,
				PresencePenalty: 0.6,
				N:               1,
			},
		)
		done <- true
		if err != nil {
			logError("ChatGPT remote call failed: %s\n" + err.Error())
			return
		}
		// 格式化输出结果
		output = resp.Choices[0].Message.Content
	} else {
		//convert messages to string
		var contentStrings string
		//var stopSeq = [...]string{" Human:", " AI:"}
		for _, chatStr := range *chatHist {
			switch chatStr.Role {
			case "system":
				contentStrings = contentStrings + chatStr.Content + "\n\n"
			case "assistant":
				contentStrings = contentStrings + "AI: " + chatStr.Content + "\n\n"
			case "user":
				contentStrings = contentStrings + "Human: " + chatStr.Content + "\n\n"
			default:
				contentStrings = contentStrings + ""
			}
		}
		ctx := context.Background()
		req := openai.CompletionRequest{
			Model:     APIModel,
			MaxTokens: 1700,
			Prompt:    contentStrings,
			Stop:      []string{" Human:", " AI:"},
		}
		resp, err := client.CreateCompletion(ctx, req)
		done <- true
		if err != nil {
			fmt.Printf("Completion error: %v\n", err)
			return
		}
		output = resp.Choices[0].Text
	}
	printOutput(output)
	if flagToken == "FullMode" {
		*chatHist = append(
			*chatHist, openai.ChatCompletionMessage{
				Role:    "assistant",
				Content: output,
			},
		)
	}
}
func logError(outstr string) {
	now := time.Now()
	fmt.Println(now.Format(time.RFC3339), "-", outstr)
	logFilename := fmt.Sprintf("console-%s.log", now.Format("2006-01-02"))
	logFile, err := os.OpenFile(logFilename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatalln("Failed to open log file:", err)
	}
	defer logFile.Close()
	log.SetOutput(logFile)
	log.Println(now.Format(time.RFC3339), "-", outstr)
}
// This variable is set automatically as part of the release process.
// Please do NOT modify the following line.
var version = "dev"
// These variables are initialized from runtime/debug.BuildInfo.
var (
	vcsRevision string
	vcsTime     time.Time
	vcsModified bool
	goVersion   string
)
func init() {
	buildInfo, ok := debug.ReadBuildInfo()
	if !ok {
		return
	}
	goVersion = buildInfo.GoVersion
	for _, setting := range buildInfo.Settings {
		switch setting.Key {
		case "vcs.revision":
			vcsRevision = setting.Value
		case "vcs.time":
			vcsTime, _ = time.Parse(time.RFC3339, setting.Value)
		case "vcs.modified":
			vcsModified = (setting.Value == "true")
		}
	}
}
var line = flag.Int("line", 1, "line number to view after opening the document")
var logpath = flag.String("log", "", "log to file")
var cpuprofile = flag.String("cpuprofile", "", "write cpu profile to file")
var editconfig = flag.Bool("editconfig", false, "open the aretext configuration file")
var noconfig = flag.Bool("noconfig", false, "force default configuration")
var versionFlag = flag.Bool("version", false, "print version")
func printUsage() {
	f := flag.CommandLine.Output()
	fmt.Fprintf(f, "Usage: %s [options...] [path]\n", os.Args[0])
	flag.PrintDefaults()
}
func runEditor(path string, lineNum uint64) error {
	log.Printf("version: %s\n", version)
	log.Printf("go version: %s\n", goVersion)
	log.Printf("vcs.revision: %s\n", vcsRevision)
	log.Printf("vcs.time: %s\n", vcsTime)
	log.Printf("vcs.modified: %t\n", vcsModified)
	log.Printf("path arg: %q\n", path)
	log.Printf("lineNum: %d\n", lineNum)
	log.Printf("$TERM env var: %q\n", os.Getenv("TERM"))
	configRuleSet, err := app.LoadOrCreateConfig(*noconfig)
	if err != nil {
		return err
	}
	screen, err := tcell.NewScreen()
	if err != nil {
		return err
	}
	if err := screen.Init(); err != nil {
		return err
	}
	defer screen.Fini()
	screen.EnablePaste()
	editor := app.NewEditor(screen, path, uint64(lineNum), configRuleSet)
	editor.RunEventLoop()
	return nil
}
func exitWithError(err error) {
	fmt.Fprintf(os.Stderr, "%v\n", err)
	os.Exit(1)
}
func showEditor() string {
	flag.Usage = printUsage
	flag.Parse()
	if *versionFlag {
		fmt.Printf("%s @ %s\n", version, vcsRevision)
		return ""
	}
	log.SetFlags(log.Ltime | log.Lmicroseconds | log.Lshortfile)
	if *logpath != "" {
		logFile, err := os.Create(*logpath)
		if err != nil {
			exitWithError(err)
		}
		defer logFile.Close()
		log.SetOutput(logFile)
	} else {
		log.SetOutput(io.Discard)
	}
	if *cpuprofile != "" {
		f, err := os.Create(*cpuprofile)
		if err != nil {
			exitWithError(err)
		}
		pprof.StartCPUProfile(f)
		defer pprof.StopCPUProfile()
	}
	var lineNum uint64
	if *line < 1 {
		exitWithError(errors.New("line number must be at least 1"))
	} else {
		lineNum = uint64(*line) - 1 // convert 1-based line arg to 0-based lineNum.
	}
	path := flag.Arg(0)
	if *editconfig {
		configPath, err := app.ConfigPath()
		if err != nil {
			exitWithError(err)
		}
		path = configPath
	}
	currentMicro := time.Now().UnixNano() / 1000
	path = fmt.Sprintf("%d.$$$", currentMicro)
	err := runEditor(path, lineNum)
	if err != nil {
		exitWithError(err)
	}
	file, err := os.Open(path)
	if err != nil {
		fmt.Println("Error opening file:", err)
		return ""
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	var content string
	for scanner.Scan() {
		content += scanner.Text() + "\n"
	}
	if err := scanner.Err(); err != nil {
		fmt.Println("Error reading file:", err)
		return ""
	}
	err = os.Remove(path)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return content
}
func printOutput(output string) {
	if runtime.GOOS == "windows" {
		fmt.Println(output)
		return
	}
	henxian_re := regexp.MustCompile(`^\-+$`)
	if henxian_re.MatchString(output) {
		fmt.Println(output)
		return
	}
	// 初始化 Glamour 渲染器
	renderStyle := glamour.WithEnvironmentConfig()
	mdRenderer, err := glamour.NewTermRenderer(
		renderStyle,
	)
	if err != nil {
		logError("Markdown render initialization failed")
		return
	}
	styledText, err := mdRenderer.Render(output)
	if err != nil {
		logError(err.Error())
		return
	}
	fmt.Println(styledText)
}
func saveMessagesToFile(filename string, messages []openai.ChatCompletionMessage) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	encoder := json.NewEncoder(file)
	err = encoder.Encode(messages)
	if err != nil {
		return err
	}
	return nil
}
func loadMessagesFromFile(filename string) ([]openai.ChatCompletionMessage, error) {
	var messages []openai.ChatCompletionMessage
	file, err := os.Open(filename)
	if err != nil {
		return messages, err
	}
	defer file.Close()
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&messages)
	if err != nil {
		return messages, err
	}
	return messages, nil
}
func spinner(done chan bool) {
	for {
		for _, c := range `-\|/` {
			select {
			case <-done:
				return
			default:
				fmt.Printf("\r%c", c)
				time.Sleep(100 * time.Millisecond)
			}
		}
	}
}
```
---
```JavaScript
\#How to use embedding
我算看明白了，embedding就是在本地加个过滤器，然后发给ChatGPT，FineTuning是
在server那边加上过滤器，Prompt和embedding类似
package main
import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"github.com/sashabaranov/go-openai/openai"
)
func main() {
	if len(os.Args) != 4 {
		fmt.Println("Usage: go run main.go input.txt output.json loaded.json")
		os.Exit(1)
	}
	inputFile := os.Args[1]
	outputFile := os.Args[2]
	loadedFile := os.Args[3]
	// Read texts from file
	texts, err := readLines(inputFile)
	if err != nil {
		fmt.Printf("Error reading input file: %v\n", err)
		return
	}
	apiKey := os.Getenv("OPENAI_API_KEY")
	client := openai.NewClient(apiKey)
	model := openai.AdaEmbeddingV2
	user := "example_user"
	embeddingRequest := openai.EmbeddingRequest{
		Input: texts,
		Model: model,
		User:  user,
	}
	ctx := context.Background()
	embeddingResponse, err := client.CreateEmbeddings(ctx, embeddingRequest)
	if err != nil {
		fmt.Println("Error creating embeddings:", err)
		return
	}
	// Save embeddings to JSON file
	err = saveEmbeddingsToJSON(outputFile, embeddingResponse.Data)
	if err != nil {
		fmt.Printf("Error saving embeddings to JSON file: %v\n", err)
		return
	}
	// Load embeddings from JSON file
	loadedEmbeddings, err := loadEmbeddingsFromJSON(loadedFile)
	if err != nil {
		fmt.Printf("Error loading embeddings from JSON file: %v\n", err)
		return
	}
	// Use loaded embeddings
	fmt.Println("Loaded embeddings:")
	for _, embedding := range loadedEmbeddings {
		fmt.Printf("Embedding for text %d: %v\n", embedding.Index+1, embedding.Embedding)
	}
}
func readLines(filename string) ([]string, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	lines := strings.Split(string(data), "\n")
	return lines, nil
}
func saveEmbeddingsToJSON(filename string, embeddings []openai.Embedding) error {
	data, err := json.Marshal(embeddings)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(filename, data, 0644)
}
func loadEmbeddingsFromJSON(filename string) ([]openai.Embedding, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	var embeddings []openai.Embedding
	err = json.Unmarshal(data, &embeddings)
	if err != nil {
		return nil, err
	}
	return embeddings, nil
}

go run main.go input.txt output.json loaded.json
程序将按照以下步骤执行：
从 input.txt 文件中读取 texts 数组。
使用 OpenAI API 为文本创建嵌入向量。
将嵌入向量保存到 output.json 文件中。
从 loaded.json 文件中加载
为了在调用 ChatGPT 时传递加载的嵌入向量，您需要根据您的任务将它们转换为相应的输入。
如果您想要使用嵌入向量生成与输入文本相似的文本，可以首先找到与给定嵌入向量最相似的文本，
然后将其传递给 ChatGPT。下面是一个示例，展示了如何使用余弦相似度找到与给定嵌入向量最相
似的文本并将其传递给 ChatGPT：
package main
import (
	"context"
	"fmt"
	"os"
	"github.com/sashabaranov/go-openai/openai"
)
func main() {
	apiKey := os.Getenv("OPENAI_API_KEY")
	client := openai.NewClient(apiKey)
	// Load embeddings from JSON file
	loadedEmbeddings, err := loadEmbeddingsFromJSON("loaded.json")
	if err != nil {
		fmt.Printf("Error loading embeddings from JSON file: %v\n", err)
		return
	}
	// Your query embedding
	queryEmbedding := loadedEmbeddings[0].Embedding
	// Calculate cosine similarity
	similarities := make([]float32, len(loadedEmbeddings))
	for i, embedding := range loadedEmbeddings {
		similarities[i] = cosineSimilarity(queryEmbedding, embedding.Embedding)
	}
	// Find the most similar text index
	mostSimilarIndex := 0
	for i := range similarities {
		if similarities[i] > similarities[mostSimilarIndex] {
			mostSimilarIndex = i
		}
	}
	// Assume the texts array from the previous example is still available
	mostSimilarText := texts[mostSimilarIndex]
	// Use ChatGPT with the most similar text
	prompt := fmt.Sprintf("Expand on the following idea: %s", mostSimilarText)
	ctx := context.Background()
	response, err := client.ChatGPT(ctx, openai.ChatGPTRequest{
		Prompt:  prompt,
		Model:   openai.Davinci,
		User:    "example_user",
		MaxTokens: 100,
	})
	if err != nil {
		fmt.Println("Error calling ChatGPT:", err)
		return
	}
	fmt.Println("Generated text:", response.Choices[0].Text)
}
func cosineSimilarity(a, b []float32) float32 {
	var dotProduct, normA, normB float32
	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}
	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}
在 -1 和 1 之间。余弦相似度越接近 1，表示两个向量越相似。
现在，您可以将找到的最相似文本传递给 ChatGPT，以生成与该文本相关的内容。在这个例子中，我们使用了一个简单的提示，
要求 ChatGPT 根据给定的文本扩展一个想法。您可以根据您的需求定制提示，以便更好地利用加载的嵌入向量。
注意，这种方法假设您已经有了一组文本及其嵌入向量。如果您希望将嵌入向量与新的文本或查询结合使用，您可能需要实现一个不同的策略。
例如，您可以使用 k-最近邻 (k-NN) 算法来找到与给定嵌入向量最相似的文本，然后将其与 ChatGPT 结合使用。
```