---
DocFlag:
  - Tested
  - Testing
Updated: 2024-03-18T11:04
tags:
  - AI->-Fine-Tuning
  - AI->-Programming
Created: 2023-09-27T10:37
---
reference
/opt/workspace/app/Awesome-Backbones/smallsampleTrain.ipynb
  
### 数据
```JavaScript
使用机器学习，训练下面这个分类问题，因为样本不多，可以采用K fold方法
■□□■ → ▣
■□■■ → □ 
□■□■■ → □ 
■□■■□■ → ▣
■□■■■□■ → ▣ 
■■■□■■■ → ▣ 
■□□□■■■ → □ 
■□■□□■■■ → □ 
□■■■□■■■□ → ▣
■□■■□□■■□■ → ▣ 
训练结束，用它来回答这个问题
■□■■□□■■□■■ → ?
```
  
```JavaScript
1. 数据准备
1.1 我们可以将 "■" 和 "□" 转换为 1 和 0，然后将 "▣" 和 "□" 作为类标签，分别表示为 1 和 0。
2. 模型选择
2.1 由于这是一个二分类问题，并且样本量小，逻辑回归或者决策树等简单模型应该就足够了。
3. 交叉验证
3.1 采用 K 折交叉验证，比如 K=5，即将所有数据分为 5 份，轮流将其中 4 份作为训练数据，1 份作为测试数据。
```
  
```JavaScript
from sklearn.model_selection import KFold
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
import numpy as np
# 数据准备
X = np.array([
    [1, 0, 0, 1],
    [1, 0, 1, 1],
    [0, 1, 0, 1, 1],
    [1, 0, 1, 1, 0, 1],
    [1, 0, 1, 1, 1, 0, 1],
    [1, 1, 1, 0, 1, 1, 1],
    [1, 0, 0, 0, 1, 1, 1],
    [1, 0, 1, 0, 0, 1, 1, 1],
    [0, 1, 1, 1, 0, 1, 1, 1, 0],
    [1, 0, 1, 1, 0, 0, 1, 1, 0, 1]
])
y = np.array([1, 0, 0, 1, 1, 1, 0, 0, 1, 1])
# 模型选择
model = LogisticRegression()
# 数据准备：使用固定长度的特征表示
# 特征1：序列长度
# 特征2：“■”（表示为1）的数量
X_features = np.array([[len(seq), np.sum(seq)] for seq in X])
# 交叉验证和模型训练
kf = KFold(n_splits=5, shuffle=True, random_state=1)
accuracies = []
for train_index, test_index in kf.split(X_features):
    X_train, X_test = X_features[train_index], X_features[test_index]
    y_train, y_test = y[train_index], y[test_index]
    
    # 模型训练
    model.fit(X_train, y_train)
    
    # 模型评估
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    accuracies.append(accuracy)
# 平均准确性
mean_accuracy = np.mean(accuracies)
# 预测新数据
new_data_features = np.array([len([1, 0, 1, 1, 0, 0, 1, 1, 0, 1]), np.sum([1, 0, 1, 1, 0, 0, 1, 1, 0, 1])]).reshape(1, -1)
prediction = model.predict(new_data_features)
mean_accuracy, prediction
准确率 0.4
```
  
  
  
随机森林  
  
```JavaScript
import numpy as np
from sklearn.model_selection import KFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
# 准备数据
data = [
    [1, 0, 0, 1],
    [1, 0, 1, 1],
    [0, 1, 0, 1, 1],
    [1, 0, 1, 1, 0, 1],
    [1, 0, 1, 1, 1, 0, 1],
    [1, 1, 1, 0, 1, 1, 1],
    [1, 0, 0, 0, 1, 1, 1],
    [1, 0, 1, 0, 0, 1, 1, 1],
    [0, 1, 1, 1, 0, 1, 1, 1, 0],
    [1, 0, 1, 1, 0, 0, 1, 1, 0, 1]
]
labels = [1, 0, 0, 1, 1, 1, 0, 0, 1, 1]
# 初始化随机森林分类器
clf = RandomForestClassifier(random_state=42)
# 使用K折交叉验证进行模型训练
kf = KFold(n_splits=5, shuffle=True, random_state=42)
accuracies = []
for train_index, test_index in kf.split(data):
    X_train, X_test = [data[i] for i in train_index], [data[i] for i in test_index]
    y_train, y_test = [labels[i] for i in train_index], [labels[i] for i in test_index]
    # 由于数据长度不一致，我们使用零填充使其长度相同
    max_len = max(max(len(x) for x in X_train), max(len(x) for x in X_test))
    X_train = [x + [0] * (max_len - len(x)) for x in X_train]
    X_test = [x + [0] * (max_len - len(x)) for x in X_test]
    # 训练模型
    clf.fit(X_train, y_train)
    # 进行预测
    y_pred = clf.predict(X_test)
    # 计算准确度
    acc = accuracy_score(y_test, y_pred)
    accuracies.append(acc)
# 计算平均准确度
average_accuracy = np.mean(accuracies)
average_accuracy
准确率 0.1
#给于更多的特征提示
1和0出现的顺序以及个数是重要的特征。因此，我们可以尝试提取这些特征来改进模型。
1的个数
0的个数
1和0的交替次数
# 特征工程函数
def extract_features(sequence):
    num_ones = sequence.count(1)
    num_zeros = sequence.count(0)
    num_alternations = sum(sequence[i] != sequence[i+1] for i in range(len(sequence)-1))
    return [num_ones, num_zeros, num_alternations]
# 提取特征
data_features = [extract_features(seq) for seq in data]
new_data_features = extract_features(new_data)
data_features, new_data_features
([[2, 2, 2],
  [3, 1, 2],
  [3, 2, 3],
  [4, 2, 4],
  [5, 2, 4],
  [6, 1, 2],
  [4, 3, 2],
  [5, 3, 4],
  [6, 3, 4],
  [6, 4, 6]],
 [7, 4, 6])
# 初始化随机森林分类器
clf = RandomForestClassifier(random_state=42)
# 使用新的特征进行K折交叉验证和模型训练
kf = KFold(n_splits=5, shuffle=True, random_state=42)
accuracies = []
for train_index, test_index in kf.split(data_features):
    X_train, X_test = [data_features[i] for i in train_index], [data_features[i] for i in test_index]
    y_train, y_test = [labels[i] for i in train_index], [labels[i] for i in test_index]
    # 训练模型
    clf.fit(X_train, y_train)
    # 进行预测
    y_pred = clf.predict(X_test)
    # 计算准确度
    acc = accuracy_score(y_test, y_pred)
    accuracies.append(acc)
# 计算平均准确度
average_accuracy = np.mean(accuracies)
average_accuracy
准确率 0.3
```
  
使用LSTM来预测
  
```JavaScript
# 准备数据
data = [
    [1, 0, 0, 1],
    [1, 0, 1, 1],
    [0, 1, 0, 1, 1],
    [1, 0, 1, 1, 0, 1],
    [1, 0, 1, 1, 1, 0, 1],
    [1, 1, 1, 0, 1, 1, 1],
    [1, 0, 0, 0, 1, 1, 1],
    [1, 0, 1, 0, 0, 1, 1, 1],
    [0, 1, 1, 1, 0, 1, 1, 1, 0],
    [1, 0, 1, 1, 0, 0, 1, 1, 0, 1]
]
labels = [1, 0, 0, 1, 1, 1, 0, 0, 1, 1]
new_data = [1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1]
# 使用PyTorch进行序列填充
def pad_sequences_pytorch(sequences, maxlen, dtype=torch.float32, padding='post', value=0.0):
    num_samples = len(sequences)
    x = torch.full((num_samples, maxlen), value, dtype=dtype)
    for idx, s in enumerate(sequences):
        if padding == 'post':
            x[idx, :len(s)] = torch.tensor(s, dtype=dtype)
        elif padding == 'pre':
            x[idx, -len(s):] = torch.tensor(s, dtype=dtype)
    return x
# 使用PyTorch进行标签的one-hot编码
def to_categorical_pytorch(y, num_classes):
    return torch.eye(num_classes)[y]
# 应用函数
# 计算数据集中所有序列的最大长度
max_length_calculated = max(max(len(x) for x in data), len(new_data))
max_length_calculated
X_data_padded_pytorch = pad_sequences_pytorch(data, maxlen=max_length_calculated)
y_data_pytorch = to_categorical_pytorch(torch.tensor(labels), num_classes=2)
X_data_padded_pytorch, y_data_pytorch
# 使用调整后的输入数据重新训练PyTorch LSTM模型, 变为1维
X_data_padded_pytorch = X_data_padded_pytorch.view(X_data_padded_pytorch.shape[0], X_data_padded_pytorch.shape[1], 1)
X_data_padded_pytorch,y_data_pytorch
# 定义LSTM模型
class LSTMModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(LSTMModel, self).__init__()
        self.lstm = nn.LSTM(input_dim, hidden_dim, batch_first=True)
        self.linear = nn.Linear(hidden_dim, output_dim)
    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        linear_out = self.linear(lstm_out[:, -1, :])
        return linear_out
# 初始化模型、损失函数和优化器
model = LSTMModel(1, 50, 2)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)
# 使用调整后的输入数据重新训练PyTorch LSTM模型
for epoch in range(200):
    optimizer.zero_grad()
    outputs = model(X_data_padded_pytorch)
    loss = criterion(outputs, y_data_pytorch)
    loss.backward()
    optimizer.step()
    print(f'Epoch {epoch+1}, Loss: {loss.item()}')
# 使用训练好的PyTorch LSTM模型进行预测
new_data = [1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1]
# new_data = [1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1]
max_length_calculated = max(max(len(x) for x in data), len(new_data))
new_data_padded_pytorch = pad_sequences_pytorch([new_data], maxlen=max_length_calculated)
new_data_padded_pytorch = new_data_padded_pytorch.view(new_data_padded_pytorch.shape[0], new_data_padded_pytorch.shape[1], 1)
new_data_padded_pytorch
# 模型推理
with torch.no_grad():
    model.eval()
    outputs = model(new_data_padded_pytorch)
    # _, predicted_labels = torch.max(outputs, 1)
    predicted_labels = torch.argmax(outputs, dim=1).item()
outputs, predicted_labels
(tensor([[-3.9655,  3.5905]]), 1)
```