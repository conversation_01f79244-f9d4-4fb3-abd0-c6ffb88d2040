---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-18T11:09
tags:
  - AI-->-WebDev
  - AI->-Cha<PERSON><PERSON>
  - AI->-<PERSON><PERSON><PERSON><PERSON>
  - AI->-Programming
  - AI->-Prompt
Created: 2023-01-15T17:44
---
<<Reference>>
[https://medium.com/analytics-vidhya/running-go-code-from-python-a65b3ae34a2d](https://medium.com/analytics-vidhya/running-go-code-from-python-a65b3ae34a2d)
# OpenAI all you need to know GPT-3 InstructGPT ChatGPT Codex DALLE2
https://www.youtube.com/watch?v=B9q3-D9xZC0
https://beta.openai.com/playground/p/default-chat
Beginner of Streamlit
https://30days.streamlit.app/?challenge=Day+2
[https://www.youtube.com/watch?v=VqgUkExPvLY](https://www.youtube.com/watch?v=VqgUkExPvLY)
Useful Extenstion in chrome
- Merlin
- ChatGPT writer
- ChatGPT prompt Genius
\#SOmecomponent
“[https://www.youtube.com/watch?v=_Um12_OlGgw](https://www.youtube.com/watch?v=_Um12_OlGgw)”
---
\#install streamlist failed due to pip is too old
[https://www.modb.pro/db/58041](https://www.modb.pro/db/58041)
[[pipinst]]
Download
[https://pypi.org/project/pip/10.0.1/#files](https://pypi.org/project/pip/10.0.1/#files)
tar -xvf pip-10.0.1.tar.gz
cd pip-10.0.1
python [setup.py](http://setup.py/) build
python [setup.py](http://setup.py/) install
pip install --upgrade pip
jinja2 2.10 has requirement MarkupSafe>=0.23, but you'll have markupsafe 0.11 which is incompatible.
rtslib-fb 2.1.74 has requirement pyudev>=0.16.1, but you'll have pyudev 0.15 which is incompatible.
ipapython 4.6.8 has requirement dnspython>=1.15, but you'll have dnspython 1.12.0 which is incompatible.
ipapython 4.6.8 has requirement python-ldap>=3.0.0b1, but you'll have python-ldap 2.4.15 which is incompatible.
\#another issue
Attempting uninstall: pyparsing
Found existing installation: pyparsing 1.5.6
ERROR: Cannot uninstall 'pyparsing'. It is a distutils installed project and thus we cannot accurately determine which files belong to it which would lead to only a partial uninstall.
https://qiita.com/y-tsutsu/items/54c10e0b2c6b565c887a
pip install pipenv
pipenv install streamlit
pipenv run python [spam.py](http://spam.py/)
pipenv shell
pipenv --venv
pipenv --rm
\#ERROR: Cannot uninstall 'python-ldap'. It is a distutils installed project and thus we cannot accurately determine which files belong to it which would lead to only a partial uninstall.
[
pip install wheel
sudo pip install **python-ldap** --user -U --trusted-host [pypi.python.org](http://pypi.python.org/) --trusted-host [files.pythonhosted.org](http://files.pythonhosted.org/) --trusted-host [pypi.org](http://pypi.org/)
sudo pip install ipaclient --user -U --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --trusted-host pypi.org
sudo pip install ipalib --user -U --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --trusted-host pypi.org
sudo pip install ipapython -U --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --trusted-host pypi.org
sudo pip install rtslib-fb -U --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --trusted-host pypi.org
pip install streamlit --force
~~pip install --ignore-installed streamlit~~
---
Install python 3.8
https://computingforgeeks.com/how-to-install-python-3-on-centos/
wget [https://www.python.org/ftp/python/3.8.12/Python-3.8.12.tgz](https://www.python.org/ftp/python/3.8.12/Python-3.8.12.tgz)
tar xvf Python-3.8.12.tgz
cd Python-3.8*/
```Plain
\#when you run your chatbot, you get below issue
ModuleNotFoundError: No module named '_ctypes'
https://www.cnblogs.com/fanbi/p/12375023.html
ModuleNotFoundError: No module named '_bz2'
https://stackoverflow.com/questions/12806122/missing-python-bz2-module

yum install libffi-devel
yum install bzip2-devel
```
./configure --enable-optimizations
update-alternatives --list
sudo make altinstall
update-alternatives --install /usr/bin/python3 python3 /usr/local/bin/python3.8 2
---
Install pip3 streamlit
Requires: Python >=3.8
```Plain
pip3 install -U pip
pip3 install -U setuptools
pip3 install streamlit
pip3 install streamlit-chat
```
[https://pypi.org/project/streamlit-chat/](https://pypi.org/project/streamlit-chat/)
https://discuss.streamlit.io/t/new-component-streamlit-chat-a-new-way-to-create-chatbots/20412/11
\#when you run your chatbot, you get below issue
ModuleNotFoundError: No module named '_ctypes'
[https://www.cnblogs.com/fanbi/p/12375023.html](https://www.cnblogs.com/fanbi/p/12375023.html)
ModuleNotFoundError: No module named '_bz2'
[https://stackoverflow.com/questions/12806122/missing-python-bz2-module](https://stackoverflow.com/questions/12806122/missing-python-bz2-module)
Sample test
```Plain
import streamlit as st
from streamlit_chat import message

message("My message")
message("Hello bot!", is_user=True)  # align's the message to the right
```
---
[https://beta.openai.com/docs/api-reference/introduction](https://beta.openai.com/docs/api-reference/introduction)
\#OpenAI API Key
***************************************************
response = openai.Completion.create(
model="text-davinci-003",
prompt=generate_prompt(animal),
temperature=0.6
)
```Plain
import os
import openai
openai.api_key = os.getenv("OPENAI_API_KEY")
response = openai.Completion.create(
  model="text-davinci-003",
  prompt="The following is a conversation with an AI assistant. The assistant is helpful, creative, clever, and very friendly.\n\nHuman: Hello, who are you?\nAI: I am an AI created by OpenAI. How can I help you today?\nHuman: I'd like to cancel my subscription.\nAI:",
  temperature=0.9,
  max_tokens=150,
  top_p=1,
  frequency_penalty=0.0,
  presence_penalty=0.6,
  stop=[" Human:", " AI:"]
)
```
```Plain
import os
import openai
openai.api_key = os.getenv("OPENAI_API_KEY")
response = openai.Completion.create(
  model="code-davinci-002",
  prompt="### Postgres SQL tables, with their properties:\n#\n# Employee(id, name, department_id)\n# Department(id, name, address)\n# Salary_Payments(id, employee_id, amount, date)\n#\n### A query to list the names of the departments which employed more than 10 employees in the last 3 months\nSELECT",
  temperature=0,
  max_tokens=150,
  top_p=1.0,
  frequency_penalty=0.0,
  presence_penalty=0.0,
  stop=["#", ";"]
)
```
```Plain
import os
import openai
openai.api_key = os.getenv("OPENAI_API_KEY")
response = openai.Completion.create(
  model="text-davinci-003",
  prompt="Create a SQL request to find all users who live in California and have over 1000 credits:",
  temperature=0.3,
  max_tokens=60,
  top_p=1.0,
  frequency_penalty=0.0,
  presence_penalty=0.0
)
```
\#Gradio
[https://zhuanlan.zhihu.com/p/374238080](https://zhuanlan.zhihu.com/p/374238080)
```Plain
import os
import openai
import gradio as gr
openai.api_key = os.getenv("OPENAI_API_KEY")
start_sequence = "\nAI:"
restart_sequence = "\nHuman: "
prompt = "How are you?\nHuman:"
def openai_create(prompt):
    response = openai.Completion.create(
        model="text-davinci-003",
        prompt=prompt,
        temperature=0.9,
        max_tokens=150,
        top_p=1,
        frequency_penalty=0,
        presence_penalty=0.6,
        stop=[" Human:", " AI:"]
    )
    return response.choices[0].text
def chatgpt_clone(input, history):
    history= history or []
    s = list(sum(history, ()))
    s.append(input)
    inp = ' '.join(s)
    output = openai_create(inp)
    history.append((input,output))
    return history, history
block = gr.Blocks()
with block:
    gr.Markdown("""<h1><center>It is AI chat Demo</center></h1>""")
    chatbot= gr.Chatbot()
    message= gr.Textbox(placeholder=prompt)
    state = gr.State()
    submit = gr.Button("Send")
    submit.click(chatgpt_clone, inputs=[message,state], outputs=[chatbot, state])
block.launch(debug=True, server_name = "0.0.0.0")
```
[https://jman4190.medium.com/how-to-generate-sql-queries-from-text-with-gpt3-69ef7c44f47a](https://jman4190.medium.com/how-to-generate-sql-queries-from-text-with-gpt3-69ef7c44f47a)
[https://github.com/shreyashankar/gpt3-sandbox.git](https://github.com/shreyashankar/gpt3-sandbox.git)
[https://www.c-phrase.com/](https://www.c-phrase.com/)
---
Chat2SQL
[https://www.nlsql.com/profile](https://www.nlsql.com/profile)
[https://community.openai.com/t/text-to-sql-generation/21740](https://community.openai.com/t/text-to-sql-generation/21740)
[https://www.text2sql.ai/](https://www.text2sql.ai/)
[https://www.reddit.com/r/MediaSynthesis/comments/hwz6gd/tutorial_sentence_to_sql_converter_using_gpt3/](https://www.reddit.com/r/MediaSynthesis/comments/hwz6gd/tutorial_sentence_to_sql_converter_using_gpt3/)
![[Notion/AI/🦜🦜🦜Setup Streamlit and Python/attachments/image.png|image.png]]
---
Below working
\#put all 4 files under same folder
#[dashboard.py](http://dashboard.py/)
import streamlit as st
from home import app as home_app
from page1 import app as page1_app
from page2 import app as page2_app
def main():
# 设置默认页面
current_page = "dashboard"
# 创建侧边栏
st.sidebar.header("导航菜单")
menu = ["Dashboard", "画面1", "画面2"]
choice = st.sidebar.selectbox("选择页面", menu)
# 根据用户的选择更新当前页面
if choice == "Dashboard":
home_app()
elif choice == "画面1":
page1_app()
elif choice == "画面2":
page2_app()
if __name__ == '__main__':
main()
#[home.py](http://home.py/)
import streamlit as st
def app():
st.subheader("Home Page")
st.write("Welcome to my app!")
#[page1.py](http://page1.py/)
import streamlit as st
def app():
st.write("这是画面1")
if st.checkbox("显示详情"):
st.write("这是画面1的详细内容")
#[page2.py](http://page2.py/)
import streamlit as st
import pandas as pd
def app():
st.write("这是画面2")
df = pd.DataFrame({
"名字": ["张三", "李四", "王五"],
"年龄": [30, 25, 40],
"性别": ["男", "女", "男"]
})
st.write(df)
---
```JavaScript
Streamlit remote camera
https://zenn.dev/whitphx/articles/streamlit-realtime-cv-app
http://www.xnsms.com/
短信验证码
https://w3h5.com/post/619.html
https://www.zsrq.net/
IP Camera
1. detect motion
2. save to photo NFS 
Parrot -> IP Camera -> detect motion! -> capture photo and save to NFS share -> app1 detect new photo generated under NFS share
-> send photo to LLM -> LLM check and summary the photo -> If find parrot is doing bad thing -> Send email to Ray

capture = cv2.VideoCapture('rtsp://username:password@************/1')

import cv2
cap = cv2.VideoCapture('http://*************:8090/video')
while(True):
    ret, frame = cap.read()
    cv2.imshow('frame',frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        cv2.destroyAllWindows()
        break

cap = cv2.VideoCapture("ipcam_streaming_url")
cap = cv2.VideoCapture("http://*************:8090/test.mjpeg")
```