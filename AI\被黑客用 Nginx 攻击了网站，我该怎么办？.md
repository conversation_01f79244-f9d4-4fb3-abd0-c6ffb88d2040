---
DocFlag:
  - ToBeTested
Updated: 2023-03-24T09:54
tags:
  - AI->-ChatGPTPlugin
Created: 2023-03-24T09:53
---
[![](https://mmbiz.qpic.cn/mmbiz_jpg/8ZFzrRjqatpQoutD1IwvnAbicT4NibpjfKmJBr80SDiaborGSDIsa25Auww1BajdSD5NGF2TibnEFWOQumTPBBDzgw/0?wx_fmt=jpeg)](https://mmbiz.qpic.cn/mmbiz_jpg/8ZFzrRjqatpQoutD1IwvnAbicT4NibpjfKmJBr80SDiaborGSDIsa25Auww1BajdSD5NGF2TibnEFWOQumTPBBDzgw/0?wx_fmt=jpeg)
---
公众号关注 「奇妙的 Linux 世界」  
设为「星标」，每天带你玩转 Linux ！  
![[Notion/AI/被黑客用 Nginx 攻击了网站，我该怎么办？/attachments/640|640]]
​
最好的防御方式就是攻击 知己知彼，百战不殆。掌握攻击者的套路才好顶得住攻击。
可能我的读者多少了解过`Nginx`，我先给不了解的同学简单说一下原理。已经了解的跳到第二节。
## 3分钟了解`Nginx`
`Nginx`是一款高性能的`Web`服务器和反向代理服务器。
它可以用来搭建网站、做应用服务器，能够处理大量的并发连接和请求。
- 静态内容托管（主要）：可以用来做网页、图片、文件的 **“静态”内容托管。**
- 动态内容托管（主要）：将经常访问的动态内容缓存到内存中，提高访问速度和性能。
- 反向代理（主要）：将客户端的请求发送到后端真实服务器，并将后端服务器的响应返回给客户端。
类似于一个快递收发室，指挥快递（流量）应该投递到哪个买家。
它还能提供一些高级功能：
- **负载均衡**：将客户端的请求分发到多个后端服务器上，从而提高服务的可用性和性能。
- **SSL/TLS加密传输**：通过加密和认证保护数据传输安全。
- **HTTP/2支持**：通过多路复用技术提高并发连接处理能力和页面加载速度。
- **安全防护**：提供多种防护机制，如限制IP访问、请求频率限制、反爬虫等。
- **动态内容处理**：支持FastCGI、uWSGI等协议，与后端应用服务器进行动态内容交互。
- **日志记录**：记录访问日志和错误日志，方便监控和排查问题。
- **自定义模块开发**：支持自定义模块开发，可以根据需求进行二次开发和扩展。
读到这里，我知道很多人脑子都要爆了。现在让我们直入主题。结合以上功能的能做哪些攻击方式。
## 反向代理攻击
使用`Nginx`作为反向代理服务器，将攻击流量转发到目标服务器。这样就能隐藏攻击流量的真实地址。
```Plain
server {
    listen 80;
    server_name www.example.com;
    location / {
        proxy_pass http://backend_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```
- 所有访问`www.example.com:80`的流量全部都会转发到`http://backend_server`服务器上。
- `proxy_set_header X-Real-IP $remote_addr;` 设置请求头提供真实来源`ip`。
- `proxy_set_header Host $host;`设置访问的`Host`。
只要把`X-Real-IP`改成其他不存在的`IP`，就可以隐藏自己的真实IP地址，让攻击更难以被追踪和防御。当然相对于客户端来说，只能知道`nginx`的地址就不知道真实服务器的地址了。
## DDoS攻击
`DDoS`攻击就是借助某些工具瞬间发动大量的请求，让服务器资源耗尽，无法正常响应其他用户的请求，一般也常用于压力测试。介绍一些常用的工具：
- `ApacheBench` (ab)：常用的命令行工具，用于模拟多个并发请求。可以控制请求总数、并发数等参数。
- `Siege`：命令行工具，和上面一样，并且还支持 `HTTP` 和 `HTTPS` 协议。
- `JMeter`：一个功能强大的 `Java` 应用程序，可以用于模拟各种负载情况。`JMeter` 可以通过图形界面进行配置，支持更多协议和数据格式，包括 HTTP、HTTPS、SOAP、REST 等。
但事实往往比这个残酷，攻击者会做一些病毒，在网络上传播开来，病毒运行时可以直接疯狂访问服务器，或者利用`Nginx`提供的反向代理和其支持的比如`socket`、`SSL`，不断的建立握手请求。
## 限流、黑名单防御
小熊主要给大家介绍怎么防御。这种病毒感染方式就不说了，我害怕戴银手铐。
```Plain
http {
    limit_req_zone $binary_remote_addr zone=one:10m rate=5r/s;
    geo $block {
        default 0;
        include /path/to/block_ip.txt;
    }
    server {
        listen 80;
        location / {
            limit_req zone=one burst=10 nodelay;
            if ($block) {
                return 403;
            }
            proxy_pass http://backend;
        }
    }
}
```
- `limit_req_zone` 定义了一个名为“one”的限制请求速率的区域，该区域的大小为10MB，请求速率限制为每秒5个请求。
- `limit_req` 指定使用名为“one”的限制规则。
- `geo $block`是黑名单，这个文件可以写需要屏蔽的`ip`。
- `server`块中的`location`指令使用了`limit_req`和`if`表示黑名单的返回`403`状态码。
## 负载均衡防御
假设我有两个后端服务器。
```Plain
http {
  upstream backend {
    # 轮询方式的负载均衡
    server backend1.example.com;
    server backend2.example.com;
  }
...
  server{...}
}
```
有多种负载均衡方式。
```Plain
 server {
   ...
    location /api/ {
      # 轮训
      proxy_pass http://backend;
    }
    location /lb/ {
      # IP哈希方式的负载均衡
      ip_hash;
      proxy_pass http://backend;
    }
    location /upstream/ {
      # 根据服务器性能或响应时间进行加权轮询
      upstream backend {
        server backend1.example.com weight=2;
        server backend2.example.com;
      }
      # 对 backend 进行访问
      proxy_pass http://backend;
    }
    location /least_conn/ {
      # 最少连接数的负载均衡
      least_conn;
      proxy_pass http://backend;
    }
    location /random/ {
      # 随机方式的负载均衡
      random;
      proxy_pass http://backend;
    }
    location /sticky/ {
      # 基于客户端IP的哈希方式的负载均衡
      hash $remote_addr consistent;
      server backend1.example.com;
      server backend2.example.com;
    }
  }
```
很多人学`nginx`都会对`ip_hash`和基于客户端IP的哈希方式的负载均衡有疑惑。分不清，我一句话给大家讲清楚。
- `ip_hash`能保证相同来源一定能访问相同的服务器，适用于登录等有状态的场景。在请求量少的时候，容易出现很多`ip`落在同一服务器上，分布不均衡。
- 基于客户端ip的hash，是根据客户端 IP 地址计算哈希值，然后将哈希值与后端服务器数量取模。使请求平均分配到不同的服务器上，也能保证同一`ip`请求落到同一服务器上。但是可以保证各个服务器比较均衡。
我认为使用方式二更好，可能理解有限，欢迎各位读者分享自己的看法！
## 网络钓鱼攻击
黑客可以使用Nginx伪装成一个合法的网站，诱骗用户输入敏感信息。例如，他们可以使用Nginx构造一个伪造的登录页面，让用户输入用户名和密码，然后将这些信息发送给黑客服务器。
其实就是静态托管+反向代理功能的组合。
```Plain
server {
    listen       80;
    server_name  example.com;
    # 静态网站托管
    location / {
        root   /var/www/mywebsite/dist;
        index  index.html index.htm;
    }
    # API代理转发
    location /api {
        proxy_pass  http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```
- 访问根目录就访问到静态的网站资源。
- 访问`/api`路由转发到`api`服务上。
我的博客就用了这种方式，同样的我写了很多了不同的`server{}`块，来监听不同的域名，这样我可以把多个网站全部部署在同一台机器上，极限利用服务器资源。
## 最后
对于今天所述的文章，真正有不良居心的人，总是会有更`sao`的攻击方法。我也不敢说得太多，而且这些攻击的行为都是违法的，建议大家学会以后手下留情。
今天的目的实际上是教大家学会`nginx`的常用配置方法，用心良苦，莫辜负点个赞再走。搜转收藏

> 本文转载自：「机智的程序员小熊」，原文：https://tinyurl.com/353dd8h3，版权归原作者所有。欢迎投稿，投稿邮箱: <EMAIL>。
最近，我们建立了一个**技术交流微信群**。目前群里已加入了不少行业内的大神，有兴趣的同学可以加入和我们一起交流技术，在 **「奇妙的 Linux 世界」** 公众号直接回复 **「加群」** 邀请你入群。
**你可能还喜欢**
点击下方图片即可阅读
​
做技术真的有出路吗？
点击上方图片，『美团|饿了么』外卖红包天天免费领
更多有趣的互联网新鲜事，关注「奇妙的互联网」视频号全了解！