---
Updated: 2024-08-30T14:21
tags:
  - AI->-Obsidian
  - AI->-Tools
Created: 2024-08-30T14:21
---
==本文是基于==[==如何进行网页剪藏（MOC）==](https://publish.obsidian.md/chinesehelp/07+%E4%BF%A1%E6%81%AF%E6%BA%90%E4%B8%8E%E8%B4%A1%E7%8C%AE%E8%80%85/%E5%A6%82%E4%BD%95%E8%BF%9B%E8%A1%8C%E7%BD%91%E9%A1%B5%E5%89%AA%E8%97%8F%EF%BC%88MOC%EF%BC%89)==总结的阶段性文字，有一定时效性。本人能力有限，如有错误请在公众号留言。本文发布后会根据大家的反馈进行修正。中文教程中的UID号为20230426150136。==
# ==摘要==
==在2023年4月，我推荐使用简悦和五彩插件来作为obsidian剪藏软件。==
# ==引言==
==我对“剪藏”的理解，首先来自于“网博士”（一个目前已经无人知晓的软件），后期更为有名的应该是“印象笔记”（一个软件）。==
==根据以上软件的操作，“剪藏”本身是要把网页A上的内容（一般为文字和图片）保存到本地或者某处，并且在网页A在互联网上无法访问后（失效时），仍然可以查阅到网页A上原有的材料。==
==但关于剪藏有许多可以讨论的内容，例如：剪藏的内容能包括哪些，一般可能是文字、图片，很少包括视频；剪藏后的样式保留得怎样，是否去除了广告；是否能剪藏微信公众号文章，是否能识别网页正文；是否有其他衍生功能，例如稍后读，甚至现在最火的AI（人工智能）。==
==一般情况下，如果不涉及ob，我主要使用印象笔记来进行剪藏，我认为印象笔记的剪藏依然是它的强项，尽管其他功能有很多差评。==
==剪藏工作流有很多，实现的方法也很多，因此本章节主要介绍我个人的看法，并不全面。==
==最简单的剪藏可能是复制和粘贴，可以保存文字信息。如果要进一步增强，可以利用ob中自带的功能，在复制和粘贴过程中保留html格式，见== [==obsidian的复制增强功能与网页剪藏 by 软通达==](https://publish.obsidian.md/chinesehelp/07+%E4%BF%A1%E6%81%AF%E6%BA%90%E4%B8%8E%E8%B4%A1%E7%8C%AE%E8%80%85/obsidian%E7%9A%84%E5%A4%8D%E5%88%B6%E5%A2%9E%E5%BC%BA%E5%8A%9F%E8%83%BD%E4%B8%8E%E7%BD%91%E9%A1%B5%E5%89%AA%E8%97%8F+by+%E8%BD%AF%E9%80%9A%E8%BE%BE)==。==
==但上述流程太过于简单，缺少相关网页的信息，因此要体验更好的剪藏功能一般要借助剪藏软件。==
==首先针对全功能的网页剪藏，我推荐简悦，这软件我已经多次提及，官网也有相关的教程。优点：功能多；缺点：配置难。对此我有几点建议：==
- ==使用简悦为ob配置的开箱即用库，而非自己配置：== [==https://www.yuque.com/kenshin/simpread/psugef==](https://www.yuque.com/kenshin/simpread/psugef)
- ==有问题，请向简悦的人员或群寻求帮助。==
- ==最简单的剪藏其实可以不用开箱即用库，直接利用简悦（免费版就有）将网页复制成md的功能，再复制到ob中。==
==其次，最近有一个==[==五彩划线==](https://publish.obsidian.md/chinesehelp/%E4%BA%94%E5%BD%A9%E5%88%92%E7%BA%BF)==的软件（== [==https://marker.dotalk.cn==](https://marker.dotalk.cn/) ==），也支持了ob的剪藏，但它其实更偏向划线同步，但胜在简单。==
[![](https://gitee.com/cyddgi/picture-store/raw/master/img/202304261528313.png)](https://gitee.com/cyddgi/picture-store/raw/master/img/202304261528313.png)