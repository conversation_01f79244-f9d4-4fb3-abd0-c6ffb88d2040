{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [{"file_id": "1Mymlj-R-K_ky1vPMRtPb6FXQiDFiM1GO", "timestamp": 1738653973068}]}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mPr5mUI5Hsoz", "executionInfo": {"status": "ok", "timestamp": 1737964849272, "user_tz": -660, "elapsed": 3751, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "0be76458-65dd-4559-9e1f-e3c2074b6778"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting apify-client\n", "  Downloading apify_client-1.8.1-py3-none-any.whl.metadata (4.3 kB)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.11/dist-packages (2.10.5)\n", "Requirement already satisfied: openai in /usr/local/lib/python3.11/dist-packages (1.59.9)\n", "Collecting apify-shared>=1.1.2 (from apify-client)\n", "  Downloading apify_shared-1.2.1-py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: httpx>=0.25.0 in /usr/local/lib/python3.11/dist-packages (from apify-client) (0.28.1)\n", "Requirement already satisfied: more_itertools>=10.0.0 in /usr/local/lib/python3.11/dist-packages (from apify-client) (10.5.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic) (2.27.2)\n", "Requirement already satisfied: typing-extensions>=4.12.2 in /usr/local/lib/python3.11/dist-packages (from pydantic) (4.12.2)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from openai) (0.8.2)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.11/dist-packages (from openai) (4.67.1)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->openai) (3.10)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx>=0.25.0->apify-client) (2024.12.14)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.25.0->apify-client) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.25.0->apify-client) (0.14.0)\n", "Downloading apify_client-1.8.1-py3-none-any.whl (73 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m73.5/73.5 kB\u001b[0m \u001b[31m1.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading apify_shared-1.2.1-py3-none-any.whl (12 kB)\n", "Installing collected packages: apify-shared, apify-client\n", "Successfully installed apify-client-1.8.1 apify-shared-1.2.1\n"]}], "source": ["!pip install apify-client pydantic openai\n"]}, {"cell_type": "code", "source": [], "metadata": {"id": "ZhQM02IU2B0k"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from getpass import getpass\n", "from typing import List, Dict, Any, Optional, Type\n", "from pydantic import BaseModel, Field\n", "from openai import OpenAI\n", "from datetime import datetime, timedelta\n", "import json\n", "from apify_client import ApifyClient\n", "import requests"], "metadata": {"id": "aQf02tMrH92E"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["os.environ['OPENAI_API_KEY'] = getpass()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4f4m2ySLIbq4", "executionInfo": {"status": "ok", "timestamp": 1737964899722, "user_tz": -660, "elapsed": 44081, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "92139e1b-6453-4d1e-8c70-191e1aed168f"}, "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["··········\n"]}]}, {"cell_type": "code", "source": ["CHAT_MODEL_NAME = 'gpt-4o-mini'\n", "client = OpenAI()"], "metadata": {"id": "ONyxgyi4Igvy"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["os.environ['SPIDER_API_KEY'] = getpass()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "l3XFiJ_9LdGD", "executionInfo": {"status": "ok", "timestamp": 1737964929044, "user_tz": -660, "elapsed": 2296, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "e16782a0-75cd-4890-da29-14160cdf05aa"}, "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["··········\n"]}]}, {"cell_type": "code", "source": ["os.environ['APIFY_API_KEY'] = getpass()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HyRDdJsULtK1", "executionInfo": {"status": "ok", "timestamp": 1737964966392, "user_tz": -660, "elapsed": 1740, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "598565b3-de3b-4e63-e225-ad5514df75e5"}, "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["··········\n"]}]}, {"cell_type": "code", "source": ["os.environ['SERPER_API_KEY'] = getpass()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-tenWSEkMnv-", "executionInfo": {"status": "ok", "timestamp": 1737964998634, "user_tz": -660, "elapsed": 1158, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "6249694b-d981-4856-a0ed-d274cd553b4f"}, "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["··········\n"]}]}, {"cell_type": "code", "source": ["os.environ['RAPID_API_KEY'] = getpass()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S2SKU6PJP9yC", "executionInfo": {"status": "ok", "timestamp": 1737965025873, "user_tz": -660, "elapsed": 1613, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "d3aabff4-31cb-4a8c-b342-c32a709ef3e2"}, "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["··········\n"]}]}, {"cell_type": "markdown", "source": ["# LLM call"], "metadata": {"id": "aZ14k-edIndc"}}, {"cell_type": "markdown", "source": ["## Text completion"], "metadata": {"id": "EoAVubU6IpQb"}}, {"cell_type": "code", "source": ["response = client.completions.create(\n", "    model=\"gpt-3.5-turbo-instruct\",\n", "    prompt=\"Rose is red, and the sky is \"\n", ")\n", "\n", "print(response.choices[0].text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ekIIzgrKIq7n", "executionInfo": {"status": "ok", "timestamp": 1737937309053, "user_tz": -660, "elapsed": 1556, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "423c7496-f864-4df4-cb45-74a7836c2f80"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["blue,\n", "On a summer day, when the breeze is new,\n", "The birds are\n"]}]}, {"cell_type": "markdown", "source": ["## Chat completion"], "metadata": {"id": "5LffPAxWItnY"}}, {"cell_type": "code", "source": ["completion = client.chat.completions.create(\n", "    model=CHAT_MODEL_NAME,\n", "    messages=[\n", "        {\"role\": \"developer\", \"content\": \"You are a helpful assistant.\"},\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Rose is red, and the sky is \"\n", "        }\n", "    ]\n", ")\n", "\n", "print(completion.choices[0].message.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "L558bI7VIuzz", "executionInfo": {"status": "ok", "timestamp": 1737937322802, "user_tz": -660, "elapsed": 1161, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "68e98e95-7a94-4530-c228-0c3e6865953f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["blue. It suggests a classic poetic theme! Would you like help crafting a poem or exploring a specific topic?\n"]}]}, {"cell_type": "code", "source": ["completion = client.chat.completions.create(\n", "    model=CHAT_MODEL_NAME,\n", "    messages=[\n", "        {\"role\": \"developer\", \"content\": \"You are a helpful assistant.\"},\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Rose is red, and the sky is \"\n", "        },\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": \"Blue\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Thank you\"\n", "        }\n", "    ]\n", ")\n", "\n", "print(completion.choices[0].message.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KfeLzSG9IyQo", "executionInfo": {"status": "ok", "timestamp": 1737937333984, "user_tz": -660, "elapsed": 1031, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "03fc6f22-7de7-45c5-b366-988be58ce48c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["You're welcome! If you have any more questions or need assistance with something else, feel free to ask!\n"]}]}, {"cell_type": "code", "source": ["def generate_joke(topic):\n", "    prompt_template = f\"\"\"You are a comedian specialized in clean, family-friendly humor.\n", "    Generate a funny joke about {topic}.\n", "    The joke should be:\n", "    - Appropriate for all ages\n", "    - Related to the topic provided\n", "    - Include both setup and punchline\n", "    Please provide just the joke without any additional commentary.\"\"\"\n", "\n", "    completion = client.chat.completions.create(\n", "        model=CHAT_MODEL_NAME,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a professional comedian.\"},\n", "            {\"role\": \"user\", \"content\": prompt_template}\n", "        ]\n", "    )\n", "\n", "    return completion.choices[0].message.content\n", "\n", "# Example usage\n", "joke = generate_joke(\"programming\")\n", "print(\"Here's your joke:\\n\")\n", "print(joke)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YDEZ1PbTI3JV", "executionInfo": {"status": "ok", "timestamp": 1737937360249, "user_tz": -660, "elapsed": 1219, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "f7922f61-1a28-40ac-82b8-8c92c120ad97"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Here's your joke:\n", "\n", "Why do programmers prefer dark mode?  \n", "\n", "Because light attracts bugs!\n"]}]}, {"cell_type": "markdown", "source": ["## Structured output"], "metadata": {"id": "Ols4orTxI8_w"}}, {"cell_type": "markdown", "source": ["### Example slack message"], "metadata": {"id": "lf-I7dGqJA2J"}}, {"cell_type": "code", "source": ["slack_messages = [\n", "    {\n", "        \"subtype\": \"bot_add\",\n", "        \"text\": \"added an integration to this channel: <https://qualitativecloud.slack.com/services/B08A5H2L40N|Relevance AI>\",\n", "        \"user\": \"U05BHQTTB8B\",\n", "        \"bot_link\": \"<https://qualitativecloud.slack.com/services/B08A5H2L40N|Relevance AI>\",\n", "        \"bot_id\": \"B08A5H2L40N\",\n", "        \"type\": \"message\",\n", "        \"ts\": \"1737933230.942869\"\n", "    },\n", "    {\n", "        \"user\": \"U07R7LMMF8F\",\n", "        \"type\": \"message\",\n", "        \"ts\": \"1737693820.846149\",\n", "        \"client_msg_id\": \"0778b5a9-1749-43e8-9a54-e8bb14c2881b\",\n", "        \"text\": \"Product suggestion: ability to give feedback on tool templates. Maybe the :+1: :-1: +comment  that already exists in the agent task flow?\\nWould love a way to figure out what’s broken and fix it or improve edge cases etc\",\n", "        \"team\": \"T023VGL9QEP\",\n", "        \"blocks\": [\n", "            {\n", "                \"type\": \"rich_text\",\n", "                \"block_id\": \"ZHVPK\",\n", "                \"elements\": [\n", "                    {\n", "                        \"type\": \"rich_text_section\",\n", "                        \"elements\": [\n", "                            {\"type\": \"text\", \"text\": \"Product suggestion: ability to give feedback on tool templates. Maybe the \"},\n", "                            {\"type\": \"emoji\", \"name\": \"thumbsup\", \"unicode\": \"1f44d\"},\n", "                            {\"type\": \"text\", \"text\": \" \"},\n", "                            {\"type\": \"emoji\", \"name\": \"thumbsdown\", \"unicode\": \"1f44e\"},\n", "                            {\"type\": \"text\", \"text\": \" +comment  that already exists in the agent task flow?\\nWould love a way to figure out what’s broken and fix it or improve edge cases etc\"}\n", "                        ]\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    },\n", "    {\n", "        \"text\": \"Hi team – a common user behaviour I have noticed is they copy their Core Instructions into a doc/notes/chatgpt. They'll frequently use external platforms to edit and refine, then copy back into Relevance.\\n\\nWhen there are tools mapped in the core instructions, copying them anywhere else renders the tool \\\"Unknown reference\\\". This means every time they edit it, and copy it back, they not only have to _remap_ the tool, but also have to remember which tool it was because it loses its label.\\n\\nI've already spoken to a few users who weren't even aware that their agent had lost its tool mapping in core instructions, and were asking me why it wasn't working properly.\\n\\nPotential solution, if we could perhaps have the tool interpret the Tool reference as \\\"/[tool_name]\\\" – it could at least keep its name. An even better thing would be that if it were pasted back into core instructions, it could automatically (or ask for confirmation to) remap to a tool.\\n\\nAny thoughts? :slightly_smiling_face:\",\n", "        \"files\": [\n", "            {\n", "                \"id\": \"F089NKD82ET\",\n", "                \"created\": 1737692094,\n", "                \"timestamp\": 1737692094,\n", "                \"name\": \"Screenshot 2025-01-24 at 3.14.30 pm.png\",\n", "                \"title\": \"Screenshot 2025-01-24 at 3.14.30 pm.png\",\n", "                \"mimetype\": \"image/png\",\n", "                \"filetype\": \"png\",\n", "                \"pretty_type\": \"PNG\",\n", "                \"user\": \"U088ARWGEUT\",\n", "                \"user_team\": \"T023VGL9QEP\",\n", "                \"editable\": <PERSON><PERSON><PERSON>,\n", "                \"size\": 855270,\n", "                \"mode\": \"hosted\",\n", "                \"is_external\": <PERSON><PERSON><PERSON>,\n", "                \"external_type\": \"\",\n", "                \"is_public\": True,\n", "                \"public_url_shared\": <PERSON><PERSON><PERSON>,\n", "                \"display_as_bot\": <PERSON><PERSON><PERSON>,\n", "                \"username\": \"\",\n", "                \"url_private\": \"https://files.slack.com/files-pri/T023VGL9QEP-F089NKD82ET/screenshot_2025-01-24_at_3.14.30___pm.png\",\n", "                \"url_private_download\": \"https://files.slack.com/files-pri/T023VGL9QEP-F089NKD82ET/download/screenshot_2025-01-24_at_3.14.30___pm.png\",\n", "                \"permalink\": \"https://qualitativecloud.slack.com/files/U088ARWGEUT/F089NKD82ET/screenshot_2025-01-24_at_3.14.30___pm.png\",\n", "                \"permalink_public\": \"https://slack-files.com/T023VGL9QEP-F089NKD82ET-c40c4fd238\",\n", "                \"is_starred\": <PERSON><PERSON><PERSON>\n", "            }\n", "        ],\n", "        \"user\": \"U088ARWGEUT\",\n", "        \"display_as_bot\": False\n", "    },\n", "    {\n", "        \"user\": \"U07R7LMMF8F\",\n", "        \"type\": \"message\",\n", "        \"ts\": \"1737617931.621759\",\n", "        \"client_msg_id\": \"b9ec801d-b68d-4691-9e74-a9386a932b5b\",\n", "        \"text\": \"Handling errors and Fallback options clash (for python and JS steps): would be good to try and align them all if possible (although i know all steps have fallback which could make it hard).\\nAdditionally; adding or changing the current \\u201cerror\\u201d method to raise the final error string would help me explain to the agent why something errored without huge stack-traces being added to context. <https://www.loom.com/share/54b00976fbdf4046bff7b7531b17b848?sid=d2e4c9d1-dce1-4ffd-8dd6-a360ae6eba65>\",\n", "        \"team\": \"T023VGL9QEP\",\n", "        \"attachments\": [\n", "            {\n", "                \"id\": 1,\n", "                \"blocks\": [\n", "                    {\n", "                        \"type\": \"video\",\n", "                        \"block_id\": \"aqyUo\",\n", "                        \"video_url\": \"https://www.loom.com/embed/54b00976fbdf4046bff7b7531b17b848?sid=d2e4c9d1-dce1-4ffd-8dd6-a360ae6eba65&unfurl=blocks\",\n", "                        \"thumbnail_url\": \"https://cdn.loom.com/sessions/thumbnails/54b00976fbdf4046bff7b7531b17b848-3a6159341fbd566c-4x3.jpg\",\n", "                        \"alt_text\": \"tool title | Notebook - 23 January 2025\",\n", "                        \"title\": {\"type\": \"plain_text\", \"text\": \"tool title | Notebook - 23 January 2025\", \"emoji\": True},\n", "                        \"author_name\": \"<PERSON>\",\n", "                        \"provider_name\": \"Loom\"\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    },\n", "    {\n", "        \"user\": \"U0783M2F0TD\",\n", "        \"type\": \"message\",\n", "        \"ts\": \"**********.072939\",\n", "        \"client_msg_id\": \"6dfa9f4d-ed92-4cf9-9f97-2c944a111078\",\n", "        \"text\": \"Probably not the right use case, but I thought about using metadata as storage and we can use it to pass variables between tools. So essentially it becomes part of the agent metadata. This way we can save things that are used multiple times through different tools and ensure each time it refers to that piece of information it's the same each time\",\n", "        \"team\": \"T023VGL9QEP\"\n", "    }\n", "]\n"], "metadata": {"id": "y-aiwtESI-xr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### Example of extract slack insights"], "metadata": {"id": "ZrW8RPPhJGrj"}}, {"cell_type": "code", "source": ["response = client.chat.completions.create(\n", "    model=CHAT_MODEL_NAME,\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You analyze Slack conversations and extract structured insights and feedback.\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Recent slack messages: {slack_messages}\"\n", "        }\n", "    ],\n", "    response_format={\n", "        \"type\": \"json_schema\",\n", "        \"json_schema\": {\n", "            \"name\": \"slack_insights\",\n", "            \"description\": \"Insights and feedback extracted from Slack conversations\",\n", "            \"schema\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"summary\": {\n", "                        \"description\": \"Overall summary of the conversations and key insights\",\n", "                        \"type\": \"string\"\n", "                    },\n", "                    \"feedback_items\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"category\": {\n", "                                    \"type\": \"string\",\n", "                                    \"enum\": [\"feature request\", \"bug\", \"existing feature improvements\", \"other\"]\n", "                                },\n", "                                \"title\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Brief title of the feedback\"\n", "                                },\n", "                                \"description\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Detailed description of the feedback\"\n", "                                },\n", "                                \"priority\": {\n", "                                    \"type\": \"string\",\n", "                                    \"enum\": [\"high\", \"medium\", \"low\"]\n", "                                },\n", "                                \"reporters\": {\n", "                                    \"type\": \"array\",\n", "                                    \"items\": {\n", "                                        \"type\": \"string\"\n", "                                    },\n", "                                    \"description\": \"List of users who reported this feedback\"\n", "                                },\n", "                                \"date\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Date when the feedback was reported\"\n", "                                }\n", "                            },\n", "                            \"required\": [\"category\", \"title\", \"description\", \"priority\", \"reporters\", \"date\"]\n", "                        }\n", "                    }\n", "                },\n", "                \"required\": [\"summary\", \"feedback_items\"]\n", "            }\n", "        }\n", "    }\n", ")\n", "\n", "print(json.dumps(json.loads(response.choices[0].message.content), indent=2))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UURJEx6_JEMk", "executionInfo": {"status": "ok", "timestamp": 1737937441531, "user_tz": -660, "elapsed": 4828, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "e560ef35-8618-4c11-a060-0f64e5582920"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{\n", "  \"summary\": \"The recent Slack conversations highlighted several user experiences and suggestions for improving tool integrations and feedback mechanisms within the platform. Users expressed concerns over tool mapping issues when editing instructions and suggested enhancements for error handling and metadata usage.\",\n", "  \"feedback_items\": [\n", "    {\n", "      \"category\": \"feature request\",\n", "      \"title\": \"Feedback on tool templates\",\n", "      \"description\": \"Users suggested implementing a feature that allows feedback on tool templates using a thumbs-up/thumbs-down system along with comments, mirroring the agent task flow feedback options.\",\n", "      \"priority\": \"high\",\n", "      \"reporters\": [\n", "        \"U07R7LMMF8F\"\n", "      ],\n", "      \"date\": \"2025-01-24\"\n", "    },\n", "    {\n", "      \"category\": \"existing feature improvements\",\n", "      \"title\": \"Tool mapping preservation\",\n", "      \"description\": \"When users copy core instructions containing tool references to external platforms for editing, they encounter 'Unknown reference' issues upon pasting back. A suggestion was made to implement a feature that maintains tool names during this process and offers remapping options when pasting back.\",\n", "      \"priority\": \"high\",\n", "      \"reporters\": [\n", "        \"U088ARWGEUT\"\n", "      ],\n", "      \"date\": \"2025-01-24\"\n", "    },\n", "    {\n", "      \"category\": \"feature request\",\n", "      \"title\": \"Error handling improvements\",\n", "      \"description\": \"Users proposed aligning error handling and fallback options across Python and JavaScript steps. Additionally, altering the current error method to provide a clearer final error message instead of extensive stack traces was suggested.\",\n", "      \"priority\": \"medium\",\n", "      \"reporters\": [\n", "        \"U07R7LMMF8F\"\n", "      ],\n", "      \"date\": \"2025-01-24\"\n", "    },\n", "    {\n", "      \"category\": \"feature request\",\n", "      \"title\": \"Using metadata for variable storage\",\n", "      \"description\": \"A user proposed utilizing metadata as a storage solution to pass variables between tools, facilitating the retention of consistent information across different tools within an agent's workflow.\",\n", "      \"priority\": \"medium\",\n", "      \"reporters\": [\n", "        \"U0783M2F0TD\"\n", "      ],\n", "      \"date\": \"2025-01-24\"\n", "    }\n", "  ]\n", "}\n"]}]}, {"cell_type": "markdown", "source": ["### Example of extract image data"], "metadata": {"id": "-7w208iqJQmK"}}, {"cell_type": "code", "source": ["completion = client.chat.completions.create(\n", "    model=CHAT_MODEL_NAME,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\"type\": \"text\", \"text\": \"What's in this image?\"},\n", "                {\n", "                    \"type\": \"image_url\",\n", "                    \"image_url\": {\n", "                        \"url\": \"https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg\",\n", "                    }\n", "                },\n", "            ],\n", "        }\n", "    ],\n", "    response_format={\n", "        \"type\": \"json_schema\",\n", "        \"json_schema\": {\n", "            \"name\": \"image_analysis_schema\",\n", "            \"schema\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"image_description\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"Short description of the image content\"\n", "                    },\n", "                    \"tags\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\"type\": \"string\"},\n", "                        \"description\": \"Relevant tags describing the image elements\"\n", "                    },\n", "                    \"objects\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\"type\": \"string\"},\n", "                        \"description\": \"List of distinct objects identified in the image\"\n", "                    },\n", "                    \"type\": {\n", "                        \"type\": \"string\",\n", "                        \"enum\": [\"photo\", \"cartoon\", \"artwork\", \"illustration\", \"digital_art\"],\n", "                        \"description\": \"The type/medium of the image\"\n", "                    }\n", "                },\n", "                \"required\": [\"image_description\", \"tags\", \"objects\", \"type\"]\n", "            }\n", "        }\n", "    }\n", ")\n", "\n", "print(json.dumps(json.loads(completion.choices[0].message.content), indent=2))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rRScqH0LJOsB", "executionInfo": {"status": "ok", "timestamp": 1737937462787, "user_tz": -660, "elapsed": 11178, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "1ba6b04b-dea3-47cb-f65a-8339a74e95b4"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{\n", "  \"image_description\": \"A wooden pathway meandering through a lush green field under a blue sky with clouds.\",\n", "  \"tags\": [\n", "    \"nature\",\n", "    \"pathway\",\n", "    \"grass\",\n", "    \"sky\",\n", "    \"outdoors\"\n", "  ],\n", "  \"objects\": [\n", "    \"wooden pathway\",\n", "    \"tall grass\",\n", "    \"bushes\",\n", "    \"clouds\",\n", "    \"blue sky\"\n", "  ],\n", "  \"type\": \"photo\"\n", "}\n"]}]}, {"cell_type": "markdown", "source": ["# Tool call"], "metadata": {"id": "EupEeHFmJa3z"}}, {"cell_type": "markdown", "source": ["## 1. Call model with get_weather tool defined"], "metadata": {"id": "-YNyDTu1Jdi0"}}, {"cell_type": "code", "source": ["def get_weather(latitude, longitude):\n", "    response = requests.get(f\"https://api.open-meteo.com/v1/forecast?latitude={latitude}&longitude={longitude}&current=temperature_2m,wind_speed_10m&hourly=temperature_2m,relative_humidity_2m,wind_speed_10m\")\n", "    data = response.json()\n", "    return data['current']['temperature_2m']"], "metadata": {"id": "a9nQQ7gOJgwH"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["tools = [{\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"get_weather\",\n", "        \"description\": \"Get current temperature for provided coordinates in celsius.\",\n", "        \"parameters\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"latitude\": {\"type\": \"number\"},\n", "                \"longitude\": {\"type\": \"number\"}\n", "            },\n", "            \"required\": [\"latitude\", \"longitude\"],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        },\n", "        \"strict\": True\n", "    }\n", "}]\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"What's the weather like in Paris today?\"}]\n", "\n", "completion = client.chat.completions.create(\n", "    model=\"gpt-4o\",\n", "    messages=messages,\n", "    tools=tools,\n", ")\n", "\n", "print(completion.choices[0].message)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TdnoIsX2Jja8", "executionInfo": {"status": "ok", "timestamp": 1737937535938, "user_tz": -660, "elapsed": 1292, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "3614ffa4-7bb5-49cd-8ff5-47e1baf9ec6a"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_p59qK7O42WC9l5IkWXs3LYwx', function=Function(arguments='{\"latitude\":48.8566,\"longitude\":2.3522}', name='get_weather'), type='function')])\n"]}]}, {"cell_type": "code", "source": ["# completion.choices[0].message.tool_calls\n", "print(json.dumps({\n", "    \"role\": completion.choices[0].message.role,\n", "    \"content\": completion.choices[0].message.content,\n", "    \"tool_calls\": [{\n", "        \"id\": t.id,\n", "        \"type\": t.type,\n", "        \"function\": {\n", "            \"name\": t.function.name,\n", "            \"arguments\": json.loads(t.function.arguments)\n", "        }\n", "    } for t in completion.choices[0].message.tool_calls]\n", "}, indent=2))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4edE45rnJo7J", "executionInfo": {"status": "ok", "timestamp": 1737937557590, "user_tz": -660, "elapsed": 328, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "12049c50-c85f-44ea-a43b-6a05d4dd9c50"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{\n", "  \"role\": \"assistant\",\n", "  \"content\": null,\n", "  \"tool_calls\": [\n", "    {\n", "      \"id\": \"call_p59qK7O42WC9l5IkWXs3LYwx\",\n", "      \"type\": \"function\",\n", "      \"function\": {\n", "        \"name\": \"get_weather\",\n", "        \"arguments\": {\n", "          \"latitude\": 48.8566,\n", "          \"longitude\": 2.3522\n", "        }\n", "      }\n", "    }\n", "  ]\n", "}\n"]}]}, {"cell_type": "markdown", "source": ["## Execute function code"], "metadata": {"id": "W0yB98XkJq6_"}}, {"cell_type": "code", "source": ["tool_call = completion.choices[0].message.tool_calls[0]\n", "args = json.loads(tool_call.function.arguments)\n", "\n", "result = get_weather(args[\"latitude\"], args[\"longitude\"])\n", "\n", "result"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AzWkwpLzJsvl", "executionInfo": {"status": "ok", "timestamp": 1737937576799, "user_tz": -660, "elapsed": 906, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "c93f37ec-57df-4ac8-efeb-cbc14caa04a2"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["9.9"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "markdown", "source": ["### Supply LLM with results - so it can decide next steps"], "metadata": {"id": "xnXf9Rp8J89c"}}, {"cell_type": "code", "source": ["messages.append(completion.choices[0].message)  # append model's function call message\n", "messages.append({                               # append result message\n", "    \"role\": \"tool\",\n", "    \"tool_call_id\": tool_call.id,\n", "    \"content\": str(result)  # Convert numeric result to string\n", "})\n", "\n", "messages"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GvupUQEyKDYd", "executionInfo": {"status": "ok", "timestamp": 1737937669090, "user_tz": -660, "elapsed": 332, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "13f1e030-2365-40c7-b0a0-2f428dd1cc8f"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[{'role': 'user', 'content': \"What's the weather like in Paris today?\"},\n", " ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_p59qK7O42WC9l5IkWXs3LYwx', function=Function(arguments='{\"latitude\":48.8566,\"longitude\":2.3522}', name='get_weather'), type='function')]),\n", " {'role': 'tool',\n", "  'tool_call_id': 'call_p59qK7O42WC9l5IkWXs3LYwx',\n", "  'content': '9.9'}]"]}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["completion_2 = client.chat.completions.create(\n", "    model=\"gpt-4o\",\n", "    messages=messages,\n", "    tools=tools,\n", ")"], "metadata": {"id": "EE-B6rk8JwRP"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["completion_2.choices[0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Xk4Dm4J-Jx19", "executionInfo": {"status": "ok", "timestamp": 1737937685976, "user_tz": -660, "elapsed": 466, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "6161914e-0561-4651-b758-b8e01ba82333"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='The current temperature in Paris is 9.9°C.', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None))"]}, "metadata": {}, "execution_count": 22}]}, {"cell_type": "code", "source": ["completion_2.choices[0].message.content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "cFo-nQqFJzAJ", "executionInfo": {"status": "ok", "timestamp": 1737937687579, "user_tz": -660, "elapsed": 323, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "6555e46e-0f68-4b0a-f369-60ab4a83db78"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'The current temperature in Paris is 9.9°C.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 23}]}, {"cell_type": "markdown", "source": ["# Tool call agent"], "metadata": {"id": "MUj9XnU9KMaF"}}, {"cell_type": "code", "source": ["def run_conversation(user_input: str, tool_map):\n", "    messages = [{\"role\": \"user\", \"content\": user_input}]\n", "\n", "    while True:\n", "        response = client.chat.completions.create(\n", "            model=CHAT_MODEL_NAME,\n", "            messages=messages,\n", "            tools=tools,\n", "            tool_choice=\"auto\"\n", "        )\n", "\n", "        response_message = response.choices[0].message\n", "\n", "        messages.append(response_message)\n", "\n", "        if not response_message.tool_calls:\n", "            return response_message.content\n", "\n", "        for tool_call in response_message.tool_calls:\n", "            function_name = tool_call.function.name\n", "            function_args = json.loads(tool_call.function.arguments)\n", "            print(f'**[INFO] Calling tool: {function_name} with args {function_args}**')\n", "            function_call = tool_map[function_name]\n", "            function_response = function_call(**function_args)\n", "\n", "            print(f'**[FUNCTION CALL RESULT] {function_name}: {function_response}**')\n", "\n", "            messages.append({\n", "                \"tool_call_id\": tool_call.id,\n", "                \"role\": \"tool\",\n", "                \"name\": function_name,\n", "                \"content\": json.dumps(function_response)\n", "            })"], "metadata": {"id": "Ts_U7fhIKNnG"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### Example: Research agent"], "metadata": {"id": "za47T6bT0QWM"}}, {"cell_type": "code", "source": ["# Define functions:\n", "# - scrape website\n", "# - google search\n", "# - extract linkedin profile\n", "# - extract instagram\n", "\n", "\n", "def scrape_website(url: str) -> str:\n", "    headers = {\n", "        'Authorization': f'Bearer {os.getenv(\"SPIDER_API_KEY\")}',\n", "        'Content-Type': 'application/json',\n", "    }\n", "\n", "    payload = {\n", "        \"limit\": 1,\n", "        \"return_format\": \"markdown\",\n", "        \"url\": url\n", "    }\n", "\n", "    response = requests.post('https://api.spider.cloud/crawl', headers=headers, json=payload)\n", "    response.raise_for_status()\n", "    return response.json()\n", "\n", "def google_search(query: str) -> str:\n", "    headers = {\n", "        'X-API-KEY': os.getenv(\"SERPER_API_KEY\"),\n", "        'Content-Type': 'application/json',\n", "    }\n", "\n", "    payload = {\n", "        \"q\": query\n", "    }\n", "\n", "    response = requests.post('https://google.serper.dev/search', headers=headers, json=payload)\n", "    response.raise_for_status()\n", "    return response.text\n", "\n", "def extract_linkedin_profile(linkedin_url: str) -> str:\n", "    headers = {\n", "        \"x-rapidapi-key\": os.getenv(\"RAPID_API_KEY\"),\n", "        \"x-rapidapi-host\": \"fresh-linkedin-profile-data.p.rapidapi.com\",\n", "    }\n", "\n", "    params = {\n", "        \"linkedin_url\": linkedin_url,\n", "        \"include_skills\": \"false\",\n", "        \"include_certifications\": \"false\",\n", "        \"include_publications\": \"false\",\n", "        \"include_honors\": \"false\",\n", "        \"include_volunteers\": \"false\",\n", "        \"include_projects\": \"false\",\n", "        \"include_patents\": \"false\",\n", "        \"include_courses\": \"false\",\n", "        \"include_organizations\": \"true\",\n", "        \"include_profile_status\": \"false\",\n", "        \"include_company_public_url\": \"false\",\n", "    }\n", "\n", "    response = requests.get('https://fresh-linkedin-profile-data.p.rapidapi.com/get-linkedin-profile', headers=headers, params=params)\n", "    response.raise_for_status()\n", "    return response.json()\n", "\n", "def extract_instagram(instagram_url: str) -> str:\n", "    headers = {\n", "        'Authorization': f'Bearer {os.getenv(\"APIFY_API_KEY\")}',\n", "        'Content-Type': 'application/json',\n", "    }\n", "\n", "    payload = {\n", "        \"directUrls\": [instagram_url],\n", "        \"resultsType\": \"posts\",\n", "        \"resultsLimit\": 5,\n", "        \"searchType\": \"hashtag\",\n", "        \"searchLimit\": 1,\n", "        \"addParentData\": <PERSON><PERSON><PERSON>,\n", "    }\n", "\n", "    response = requests.post('https://api.apify.com/v2/actor-runs/shu8hvrXbJbY3Eb9W/run-sync-get-dataset-items', headers=headers, json=payload)\n", "    response.raise_for_status()\n", "    return response.json()\n"], "metadata": {"id": "czVqM025Kl07"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["tools = [\n", "  {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "      \"name\": \"scrape_website\",\n", "      \"description\": \"Scrapes a website and returns the content in markdown format\",\n", "      \"strict\": True,\n", "      \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"required\": [\n", "          \"url\"\n", "        ],\n", "        \"properties\": {\n", "          \"url\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The URL of the website to scrape\"\n", "          }\n", "        },\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "      }\n", "    }\n", "  },\n", "  {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"google_search\",\n", "        \"description\": \"Performs a Google search and returns the results as a string\",\n", "        \"strict\": True,\n", "        \"parameters\": {\n", "          \"type\": \"object\",\n", "          \"required\": [\n", "            \"query\"\n", "          ],\n", "          \"properties\": {\n", "            \"query\": {\n", "              \"type\": \"string\",\n", "              \"description\": \"The search query string\"\n", "            }\n", "          },\n", "          \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "  },\n", "  {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"extract_linkedin_profile\",\n", "        \"description\": \"Extracts LinkedIn profile information from a given LinkedIn URL.\",\n", "        \"strict\": True,\n", "        \"parameters\": {\n", "          \"type\": \"object\",\n", "          \"required\": [\n", "            \"linkedin_url\"\n", "          ],\n", "          \"properties\": {\n", "            \"linkedin_url\": {\n", "              \"type\": \"string\",\n", "              \"description\": \"The LinkedIn profile URL to extract information from, e.g. https://www.linkedin.com/in/cjfollini/.\"\n", "            }\n", "          },\n", "          \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "  },\n", "  {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"extract_instagram\",\n", "        \"description\": \"Extracts data from Instagram using a given URL\",\n", "        \"strict\": True,\n", "        \"parameters\": {\n", "          \"type\": \"object\",\n", "          \"required\": [\n", "            \"instagram_url\"\n", "          ],\n", "          \"properties\": {\n", "            \"instagram_url\": {\n", "              \"type\": \"string\",\n", "              \"description\": \"The URL of the Instagram page to extract data from, e.g. https://www.instagram.com/saunaamalfi/\"\n", "            }\n", "          },\n", "          \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "      }\n", "    }\n", "\n", "]"], "metadata": {"id": "yTzWOaIYRs4y"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["tool_map = {\n", "    'scrape_website': scrape_website,\n", "    'google_search': google_search,\n", "    'extract_linkedin_profile': extract_linkedin_profile,\n", "    'extract_instagram': extract_instagram\n", "}"], "metadata": {"id": "uo85J2oKSr-0"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["query = \"Research where did <PERSON> from Relevance AI work previously\"\n", "response = run_conversation(query, tool_map)\n", "print(response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rSjTdGn5T9id", "executionInfo": {"status": "ok", "timestamp": 1737967082114, "user_tz": -660, "elapsed": 13049, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "d8c740d6-4cbe-4a1c-92ed-21b8e8567982"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**[INFO] Calling tool: google_search with args {'query': '<PERSON> Relevance AI previous work experience'}**\n", "**[FUNCTION CALL RESULT] google_search: {\"searchParameters\":{\"q\":\"Jason Relevance AI previous work experience\",\"type\":\"search\",\"engine\":\"google\"},\"organic\":[{\"title\":\"<PERSON> - Relevance AI | LinkedIn\",\"link\":\"https://au.linkedin.com/in/jasonzhoudesign\",\"snippet\":\"Local director of Marketing and Communication AIESEC in Mainland of China. Sep 2012 - Dec 2013 1 year 4 months\",\"position\":1},{\"title\":\"Relevance AI tutorial - AI Jason\",\"link\":\"https://www.ai-jason.com/learning-ai/autogpt-tutorial-how-to-build-your-personal-assistant\",\"snippet\":\"In this tutorial, we will learn how to build such an AI assistant using Relevance AI, a no-code platform.\",\"attributes\":{\"Missing\":\"work | Show results with:work\"},\"position\":2},{\"title\":\"<PERSON> (@jasonzhou1993) / X\",\"link\":\"https://x.com/jasonzhou1993?lang=en\",\"snippet\":\"I build & teach AI stuff | Learn to build with AI at https://t.co/3PgPBGhGR0 | Product @RelevanceAI_.\",\"position\":3},{\"title\":\"Jason Wild - Arteria AI | LinkedIn\",\"link\":\"https://www.linkedin.com/in/jasonwild\",\"snippet\":\"I am a strategist and advisor. My career took off in Chicago and led me to big names like… · Experience: Arteria AI · Location: New York City Metropolitan ...\",\"position\":4},{\"title\":\"Is This the SMARTEST AI Model Ever? OpenAI o1 Break ... - YouTube\",\"link\":\"https://www.youtube.com/watch?v=QZ5ScM578OI\",\"snippet\":\"... experiences. Is This the SMARTEST AI Model Ever? OpenAI o1 Break Down ft. AI Jason. 2.2K views · 4 months ago ...more. Relevance AI. 5.72K.\",\"date\":\"Sep 13, 2024\",\"position\":5},{\"title\":\"Overcoming FOBO: Mercer's Jason Averbook on rethinking HR\",\"link\":\"https://eightfold.ai/blog/mercer-jason-averbook-hr-relevant-age-ai/\",\"snippet\":\"This fear of becoming obsolete is a growing trend among workers who worry that new technologies — especially AI — will take their jobs.\",\"date\":\"Apr 8, 2024\",\"position\":6},{\"title\":\"[PDF] Perspectives on Research in Artificial Intelligence and Artificial ...\",\"link\":\"https://irp.fas.org/agency/dod/jason/ai-dod.pdf\",\"snippet\":\"This JASON study was sponsored by DoD/OSD/ASD(R&E). AI technologies are of great importance to DoD missions. Defense systems and platforms with varying degrees ...\",\"position\":7},{\"title\":\"Best AI assistant - Jason AI - Reply.io\",\"link\":\"https://reply.io/jason-ai/\",\"snippet\":\"Jason is Reply.io's AI agent that books more meetings by automating sales outreach, from finding prospects to handling responses. Try AI SDR Book a demo.\",\"position\":8},{\"title\":\"Earley AI Podcast - Guest Jason Radisson - YouTube\",\"link\":\"https://www.youtube.com/watch?v=ygnSHLrmet0\",\"snippet\":\"... work backwards from customer-first use cases and ... AI startups and the importance of having a quality team with enterprise experience.\",\"date\":\"Aug 8, 2024\",\"position\":9},{\"title\":\"Jason Kim - OII\",\"link\":\"https://www.oii.ox.ac.uk/people/profiles/jason-kim/\",\"snippet\":\"Jason leads a global analytics team at Google focused on building trusted experiences for users and the responsible development of AI. Prior to his experience ...\",\"position\":10}],\"relatedSearches\":[{\"query\":\"Jason relevance ai previous work experience reddit\"},{\"query\":\"Jason relevance ai previous work experience zhou\"},{\"query\":\"Jason relevance ai previous work experience github\"},{\"query\":\"Jason AI\"},{\"query\":\"Relevance AI documentation\"},{\"query\":\"Relevance AI Careers\"},{\"query\":\"Relevance AI agents\"},{\"query\":\"Build an entire AI agent Workforce\"}],\"credits\":1}**\n", "**[INFO] Calling tool: extract_linkedin_profile with args {'linkedin_url': 'https://au.linkedin.com/in/jasonzhoudesign'}**\n", "**[FUNCTION CALL RESULT] extract_linkedin_profile: {'data': {'about': None, 'city': 'Sydney', 'company': 'Relevance AI', 'company_description': 'Relevance AI is the home of the AI workforce: where anyone can build and recruit teams of AI agents to complete tasks on autopilot. \\n\\nOur no-code platform is built for ops teams, no technical background required. Subject-matter experts can use Relevance to design powerful AI agents and AI teams without relying on developer resources. \\n\\nScale excellence across every area or team with your intelligent, purpose-built AI workforce.', 'company_domain': 'relevanceai.com', 'company_employee_range': '11-50', 'company_industry': 'Software Development', 'company_linkedin_url': 'https://www.linkedin.com/company/relevanceai', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D560BAQHYhwAvHL8aRg/company-logo_400_400/company-logo_400_400/0/1718350495354/relevanceai_logo?e=**********&v=beta&t=X9Z0guq_5CO2gwHIMLrEM__ZWZ3cYRrkgjitf4Qj_Tc', 'company_website': 'https://relevanceai.com', 'company_year_founded': 2020, 'connection_count': 1497, 'country': 'Australia', 'current_company_join_month': 8, 'current_company_join_year': 2023, 'current_job_duration': '1 yr 6 mos', 'educations': [{'activities': \"Activities and societies: Received the Dean's list award\", 'date_range': '2015 - 2016', 'degree': 'Master of Interaction Design and Electronic Art', 'end_month': '', 'end_year': 2016, 'field_of_study': 'Human Computer Interaction', 'school': 'University of Sydney', 'school_id': '166676', 'school_linkedin_url': 'https://www.linkedin.com/company/166676/', 'school_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQHbPUZaDAMT6g/company-logo_200_200/company-logo_200_200/0/1646696237577/university_of_sydney_logo?e=**********&v=beta&t=KPNQ3cXDyPlGS6rnTVOi80OyYI-Xf2V3xP9NLeZEdPY', 'start_month': '', 'start_year': 2015}, {'activities': '', 'date_range': '', 'degree': '', 'end_month': '', 'end_year': '', 'field_of_study': '', 'school': 'Chongqing University', 'school_id': '1607923', 'school_linkedin_url': 'https://www.linkedin.com/company/1607923/', 'school_logo_url': 'https://media.licdn.com/dms/image/v2/C510BAQE6SiaAN2RwDg/company-logo_200_200/company-logo_200_200/0/1631319247882?e=**********&v=beta&t=JZKioDlkJoBIubGlYXGiXWNsV5crmSNI8asdaPvsyyk', 'start_month': '', 'start_year': ''}], 'email': '', 'experiences': [{'company': 'Relevance AI', 'company_id': '36167121', 'company_linkedin_url': 'https://www.linkedin.com/company/36167121', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D560BAQHYhwAvHL8aRg/company-logo_200_200/company-logo_200_200/0/1718350495354/relevanceai_logo?e=**********&v=beta&t=IE_TAiiBDdX1qTzu1SSao1t99vh1JXatCFRWXDx7ZF4', 'date_range': 'Aug 2023 - Present', 'description': 'Enable business train & hire AI employees', 'duration': '1 yr 6 mos', 'end_month': '', 'end_year': '', 'is_current': True, 'job_type': 'Full-time', 'location': 'Sydney, New South Wales, Australia · Hybrid', 'skills': '', 'start_month': 8, 'start_year': 2023, 'title': 'Head of Product Design'}, {'company': 'Qwestive', 'company_id': '80869460', 'company_linkedin_url': 'https://www.linkedin.com/company/80869460', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFaXhFbSA5HOg/company-logo_200_200/company-logo_200_200/0/1651272844974?e=**********&v=beta&t=r74CC8XNHp1gU6hx-JW8kdSJJgKJ5H8EC3t4wfYd0Ls', 'date_range': 'May 2022 - Mar 2024', 'description': 'Backed by Binance lab, building the web3 growth platform to help projects reach audience at scale', 'duration': '1 yr 11 mos', 'end_month': 3, 'end_year': 2024, 'is_current': False, 'job_type': 'Full-time', 'location': '', 'skills': '', 'start_month': 5, 'start_year': 2022, 'title': 'Head of Product | Co-founder'}, {'company': 'Blackbird', 'company_id': '2932996', 'company_linkedin_url': 'https://www.linkedin.com/company/2932996', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C4E0BAQHWaiOceN6xkg/company-logo_200_200/company-logo_200_200/0/1658259492050/blackbirdvc_logo?e=**********&v=beta&t=OiGYbW6lkDe2BUcBq4buOtu9DH0RvA0ZE0uoRSQvohA', 'date_range': 'May 2021 - Mar 2024', 'description': \"Mentoring early-stage startup founders on MVP design in Blackbird's Giants program. https://blackbird.vc/giants\", 'duration': '2 yrs 11 mos', 'end_month': 3, 'end_year': 2024, 'is_current': False, 'job_type': '', 'location': 'Sydney, New South Wales, Australia', 'skills': '', 'start_month': 5, 'start_year': 2021, 'title': 'Mentor'}, {'company': 'SafetyCulture', 'company_id': '2467945', 'company_linkedin_url': 'https://www.linkedin.com/company/2467945', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D560BAQFMxR0B_JddtQ/company-logo_200_200/company-logo_200_200/0/1688336869599/safetyculturehq_logo?e=**********&v=beta&t=dvd3fUj0LoLCcdVeQPuZk6P4WJ7Do_Ph2zA7UN8WkOQ', 'date_range': '2022 - Jul 2022', 'description': \"Lead SafetyCulture's Emerging product design group, responsible for grow our new and adjacent products. I manage 4 product designers across our Actions, Assets, Heads Up, Issues and Sensors product teams. Group Product Design Manager\", 'duration': '7 mos', 'end_month': 7, 'end_year': 2022, 'is_current': False, 'job_type': '', 'location': 'Sydney, New South Wales, Australia', 'skills': '', 'start_month': '', 'start_year': 2022, 'title': 'Group Product Design Manager'}, {'company': 'SafetyCulture', 'company_id': '2467945', 'company_linkedin_url': 'https://www.linkedin.com/company/2467945', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D560BAQFMxR0B_JddtQ/company-logo_200_200/company-logo_200_200/0/1688336869599/safetyculturehq_logo?e=**********&v=beta&t=dvd3fUj0LoLCcdVeQPuZk6P4WJ7Do_Ph2zA7UN8WkOQ', 'date_range': 'Jan 2021 - Jan 2022', 'description': \"Leading product discovery for SafetyCulture's emerging product, Issues. (Allow frontline teams to report issues in real time), on the journey to reach product-market fit\\n\\nAlso Driving the feature adoption of 2nd biggest product area, Actions. (A product that allow users to turn their findings into actual resolutions from frontline) Principal Product Designer Jan 2021 - Jan 2022 · 1 yr 1 mo\", 'duration': '1 yr 1 mo', 'end_month': 1, 'end_year': 2022, 'is_current': False, 'job_type': '', 'location': 'Sydney, New South Wales, Australia', 'skills': '', 'start_month': 1, 'start_year': 2021, 'title': 'Principal Product Designer'}, {'company': 'SafetyCulture', 'company_id': '2467945', 'company_linkedin_url': 'https://www.linkedin.com/company/2467945', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D560BAQFMxR0B_JddtQ/company-logo_200_200/company-logo_200_200/0/1688336869599/safetyculturehq_logo?e=**********&v=beta&t=dvd3fUj0LoLCcdVeQPuZk6P4WJ7Do_Ph2zA7UN8WkOQ', 'date_range': 'Jan 2019 - Dec 2020', 'description': \"Led experience design for SafetyCulture's flagship product - Inspection. Led the redesign of the Inspection form builder and SmartScan service that allow users to upload a paper form and auto generate results to boost new user activation. Senior Product Designer Jan 2019 - Dec 2020 · 2 yrs\", 'duration': '2 yrs', 'end_month': 12, 'end_year': 2020, 'is_current': False, 'job_type': '', 'location': 'Sydney, Australia', 'skills': '', 'start_month': 1, 'start_year': 2019, 'title': 'Senior Product Designer'}, {'company': 'SafetyCulture', 'company_id': '2467945', 'company_linkedin_url': 'https://www.linkedin.com/company/2467945', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D560BAQFMxR0B_JddtQ/company-logo_200_200/company-logo_200_200/0/1688336869599/safetyculturehq_logo?e=**********&v=beta&t=dvd3fUj0LoLCcdVeQPuZk6P4WJ7Do_Ph2zA7UN8WkOQ', 'date_range': 'May 2016 - Dec 2018', 'description': 'Led the process of setting up a design system in SafetyCulture that was used by all product teams. Product Designer', 'duration': '2 yrs 8 mos', 'end_month': 12, 'end_year': 2018, 'is_current': False, 'job_type': '', 'location': 'Sydney, Australia', 'skills': '', 'start_month': 5, 'start_year': 2016, 'title': 'Product Designer'}, {'company': 'Think China | Digital Marketing Consulting', 'company_id': '3785577', 'company_linkedin_url': 'https://www.linkedin.com/company/3785577', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C510BAQH7ocegp3i0pg/company-logo_200_200/company-logo_200_200/0/1631426188108/think_china_digital_marketing_consulting_logo?e=**********&v=beta&t=qaMcEXM4u59k06dq2UgofBs9J6NO-HnM9u42oB4eqKc', 'date_range': 'Jul 2015 - Mar 2016', 'description': 'Lead digital transformation for a few key Australian customers, including Mr.Vitamins (Retail chain), Option group (Real estate).', 'duration': '9 mos', 'end_month': 3, 'end_year': 2016, 'is_current': False, 'job_type': '', 'location': 'Sydney, Australia', 'skills': '', 'start_month': 7, 'start_year': 2015, 'title': 'Senior Digital Designer'}, {'company': 'MacroSys, LLC', 'company_id': '9322150', 'company_linkedin_url': 'https://www.linkedin.com/company/9322150', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C4D0BAQFZwleMtF4yGg/company-logo_200_200/company-logo_200_200/0/1630417917943/macrosys_llc_logo?e=**********&v=beta&t=heHmFJeoJqiJLEFs6iruJALLYOqoIT-43nJVdOhxyx8', 'date_range': 'Aug 2014 - Jul 2015', 'description': 'Product design for some main clients including MyOwnMed on transform patient data collection digitization, and also internal startup project for street parking app.', 'duration': '1 yr', 'end_month': 7, 'end_year': 2015, 'is_current': False, 'job_type': '', 'location': 'Washington D.C. Metro Area', 'skills': '', 'start_month': 8, 'start_year': 2014, 'title': 'UX Designer'}], 'first_name': 'Jason', 'follower_count': 4064, 'full_name': 'Jason Zhou', 'headline': 'Product & Design @ Relevance AI', 'hq_city': 'Surry Hills', 'hq_country': 'AU', 'hq_region': 'New South Wales', 'job_title': 'Head of Product Design', 'languages': 'Chinese, English', 'last_name': 'Zhou', 'linkedin_url': 'https://www.linkedin.com/in/jasonzhoudesign/', 'location': 'Greater Sydney Area', 'organizations': [], 'phone': '', 'profile_id': '*********', 'profile_image_url': '', 'public_id': 'jasonzhoudesign', 'school': 'University of Sydney', 'state': 'New South Wales', 'urn': 'ACoAABCAIWcBvVuSVvRb_sUJ2damxTXTCkScMlg'}, 'message': 'ok'}**\n", "<PERSON>, the Head of Product Design at Relevance AI, has a diverse work history. Here are his previous positions:\n", "\n", "1. **Relevance AI** (Aug 2023 - Present)\n", "   - **Title:** Head of Product Design\n", "   - **Location:** Sydney, New South Wales, Australia\n", "   - **Description:** Enable businesses to train and hire AI employees.\n", "\n", "2. **Qwestive** (May 2022 - Mar 2024)\n", "   - **Title:** Head of Product | Co-founder\n", "   - **Description:** Building a web3 growth platform backed by Binance Lab to help projects reach audiences at scale.\n", "\n", "3. **Blackbird** (May 2021 - Mar 2024)\n", "   - **Title:** Mentor\n", "   - **Description:** Mentored early-stage startup founders on MVP design in Blackbird's Giants program.\n", "\n", "4. **SafetyCulture**:\n", "   - **Group Product Design Manager** (2022 - Jul 2022)\n", "     - Lead the emerging product design group across various product teams.\n", "   - **Principal Product Designer** (Jan 2021 - Jan 2022)\n", "     - Led the product discovery for an emerging service and managed user adoption for a major product area.\n", "   - **Senior Product Designer** (Jan 2019 - Dec 2020)\n", "     - Led experience design for SafetyCulture's flagship product.\n", "   - **Product Designer** (May 2016 - Dec 2018)\n", "     - Established a design system used by all product teams.\n", "\n", "5. **Think China | Digital Marketing Consulting** (Jul 2015 - Mar 2016)\n", "   - **Title:** Senior Digital Designer\n", "   - **Description:** Led digital transformation for major clients.\n", "\n", "6. **MacroSys, LLC** (Aug 2014 - Jul 2015)\n", "   - **Title:** UX Designer\n", "   - **Description:** Provided product design for various clients, focusing on digitization of patient data collection.\n", "\n", "<PERSON> has a strong background in product design, particularly in technology and startup environments, with extensive experience in AI and digital transformation.\n"]}]}, {"cell_type": "code", "source": ["query = \"What did <PERSON> posted recently on instagram, make sure you search for official instagram url\"\n", "response = run_conversation(query, tool_map)\n", "print(f\"--FINAL RESULT: {response}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1Ed-2j2m7JyU", "executionInfo": {"status": "ok", "timestamp": 1737967367838, "user_tz": -660, "elapsed": 47238, "user": {"displayName": "ai jason shared", "userId": "07676515028336894771"}}, "outputId": "379413a5-e2e7-4b74-b1a2-25b631f6bca5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**[INFO] Calling tool: google_search with args {'query': '<PERSON> official Instagram'}**\n", "**[FUNCTION CALL RESULT] google_search: {\"searchParameters\":{\"q\":\"<PERSON> official Instagram\",\"type\":\"search\",\"engine\":\"google\"},\"organic\":[{\"title\":\"President <PERSON> (@realdonaldtrump) - Instagram\",\"link\":\"https://www.instagram.com/realdonaldtrump/?hl=en\",\"snippet\":\"31M Followers, 45 Following, 7366 Posts - President <PERSON> (@realdonaldtrump) on Instagram: \\\"45th President of the United States\\\"\",\"sitelinks\":[{\"title\":\"Reels\",\"link\":\"https://www.instagram.com/realdonaldtrump/reels/\"},{\"title\":\"I am pleased to inform you that ...\",\"link\":\"https://www.instagram.com/realdonaldtrump/p/CrLwG77Mjrg/\"},{\"title\":\"Realdonaldtrump\",\"link\":\"https://www.instagram.com/realdonaldtrump/channel/\"},{\"title\":\"Instagram photos and videos\",\"link\":\"https://www.instagram.com/realdonaldtrump/%C2%A0/\"},{\"title\":\"Instagram video by President ...\",\"link\":\"https://www.instagram.com/realdonaldtrump/reel/DFQTIVkPuSc/\"}],\"position\":1},{\"title\":\"President Donald J. Trump (@potus) • Instagram photos and videos\",\"link\":\"https://www.instagram.com/potus/?hl=en\",\"snippet\":\"16M Followers, 3 Following, 14 Posts - President Donald J. Trump (@potus) on Instagram: \\\"45th & 47th President of the United States.\",\"sitelinks\":[{\"title\":\"Reels\",\"link\":\"https://www.instagram.com/potus/reels/\"},{\"title\":\"Not the top down.\",\"link\":\"https://www.instagram.com/potus/p/DBMn1cest4q/\"},{\"title\":\"Filipinos and Filipino...\",\"link\":\"https://www.instagram.com/potus/p/DBZGomDMJcc/\"},{\"title\":\"3 hours ago\",\"link\":\"https://www.instagram.com/potus/p/DAy5mq9Syc3/\"}],\"position\":2},{\"title\":\"Vice President JD Vance (@vp) • Instagram photos and videos\",\"link\":\"https://www.instagram.com/vp/?hl=en\",\"snippet\":\"15M Followers, 3 Following, 9 Posts - Vice President JD Vance (@vp) on Instagram: \\\"50th Vice President of the United States. Christian, husband, father.\",\"sitelinks\":[{\"title\":\"Reels\",\"link\":\"https://www.instagram.com/vp/reels/\"},{\"title\":\"Thank you to the small...\",\"link\":\"https://www.instagram.com/vp/p/C9nfXHRsj5F/\"},{\"title\":\"How it started → How it’s going\",\"link\":\"https://www.instagram.com/vp/p/CqdgUo8ODqv/\"},{\"title\":\"Vp\",\"link\":\"https://www.instagram.com/vp/feed/\"}],\"position\":3},{\"title\":\"Ivanka Trump (@ivankatrump) • Instagram photos and videos\",\"link\":\"https://www.instagram.com/ivankatrump/?hl=en\",\"snippet\":\"8M Followers, 2025 Following, 4071 Posts - Ivanka Trump (@ivankatrump) on Instagram: \\\"\\\"\",\"sitelinks\":[{\"title\":\"Fotos y videos de Instagram\",\"link\":\"https://www.instagram.com/ivankatrump/?locale=es&hl=pa\"},{\"title\":\"October 22\",\"link\":\"https://www.instagram.com/ivankatrump/reel/DBbyqcuuoqi/\"},{\"title\":\"November 4\",\"link\":\"https://www.instagram.com/ivankatrump/p/DB9t2NfBpxy/\"},{\"title\":\"October 19\",\"link\":\"https://www.instagram.com/ivankatrump/p/DBUyvh-OlpN/\"}],\"position\":4},{\"title\":\"Here's why you might be automatically following Trump ... - ABC News\",\"link\":\"https://abcnews.go.com/US/automatically-trump-instagram-facebook/story?id=117980032\",\"snippet\":\"In the days since Donald Trump assumed office, many people online have begun expressing alarm to find they were unwittingly following Trump ...\",\"date\":\"5 days ago\",\"position\":5},{\"title\":\"Here's why you're suddenly following President Trump on Instagram\",\"link\":\"https://www.cnbc.com/2025/01/22/heres-why-youre-suddenly-following-president-trump-on-instagram.html\",\"snippet\":\"Many Instagram users have complained that Meta has automatically enrolled them into following accounts for President Donald Trump without ...\",\"date\":\"5 days ago\",\"position\":6}],\"topStories\":[{\"title\":\"Here’s Why You’re Suddenly Following Donald Trump on Instagram, Even if You Didn’t Want To\",\"link\":\"https://www.vanityfair.com/style/story/donald-trump-potus-automatic-instagram-follow\",\"source\":\"Vanity Fair\",\"date\":\"2 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRd6SsIpST9jN3SM0B-zy6kCHTD7_dz2h9_tyO7sYrut4hqwx4mP1fR-CO85Q&s\"},{\"title\":\"Here's why you might already be following Donald Trump, JD Vance on Instagram, Facebook\",\"link\":\"https://www.yahoo.com/news/heres-why-might-already-following-*********.html\",\"source\":\"Yahoo\",\"date\":\"2 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTRR2GMF10LynlaVNPzyFCg9GV9knn9Ng4VDGHa66ZwwqqOwUGzSprLNB-YIQ&s\"},{\"title\":\"Kim Kardashian loses 150,000 Instagram followers after sharing photo of Melania Trump's inauguration outfit\",\"link\":\"https://www.dailymail.co.uk/tvshowbiz/article-14320601/Kim-Kardashian-loses-150k-Instagram-followers-Melania-Trump.html\",\"source\":\"Daily Mail\",\"date\":\"3 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRf4vPjCqUeMilPB3K329_UlR9Yf2heGyR5qBawigA8FzTsLJzR-qySWr8tHg&s\"},{\"title\":\"Why You Might Suddenly Be Following Trump on Instagram and Facebook\",\"link\":\"https://www.nytimes.com/2025/01/22/technology/personaltech/trump-instagram-facebook.html\",\"source\":\"The New York Times\",\"date\":\"4 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQmb-MIbn-8I3xtzex86TZZHgwV5RYBvOpWvQANoBpEJRUMvhBXbHhlBCKcaQ&s\"},{\"title\":\"Meta responds to claims that it's boosting Trump on Facebook, Instagram\",\"link\":\"https://www.nbcnews.com/tech/social-media/meta-responds-claims-boosting-trump-facebook-instagram-rcna188586\",\"source\":\"NBC News\",\"date\":\"4 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR4GGqSTDlxVdUJpc1NLrPRo3-T9x9vK8Z3y5ISKxQMmQ40hICcFnJtgHM0MA&s\"},{\"title\":\"Meta admits some people can’t unfollow Donald Trump on Instagram\",\"link\":\"https://www.the-independent.com/tech/trump-meta-following-potus-instagram-facebook-b2684756.html\",\"source\":\"The Independent\",\"date\":\"4 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQzPpwz_-MeD_w8RLkElZPfuZF-gZuEgib4o7xuVFqGf6sY2rwUmuS_1j-rjQ&s\"},{\"title\":\"Here's why you’re suddenly following President Trump on Instagram\",\"link\":\"https://www.cnbc.com/2025/01/22/heres-why-youre-suddenly-following-president-trump-on-instagram.html\",\"source\":\"CNBC\",\"date\":\"4 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRSpu1iaIHZc9jGl37MZZFyqxTSLN_ROiaVUR__oQU4g_Zfuz5IOqUwBntZNg&s\"},{\"title\":\"Meta accused of pro-Trump bias after #Democrat hashtag blocked on Instagram\",\"link\":\"https://www.cbc.ca/news/instagram-1.7437667\",\"source\":\"CBC\",\"date\":\"5 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRibnpClorf2TsrTyexnoFWUXc57ZllaWxiv_LYS_c3TULjec8FvSj8ZEotdQ&s\"},{\"title\":\"Is Meta boosting Trump and Vance on Facebook and Instagram?\",\"link\":\"https://www.cbc.ca/news/business/meta-boosting-trump-vance-facebook-instagram-1.7438276\",\"source\":\"CBC\",\"date\":\"4 days ago\",\"imageUrl\":\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSHFMDoqdaKSCzNlTn35CzP8cwSsupP_bn3URF0VU3KDgvBxhGcDO8bpxYumQ&s\"}],\"relatedSearches\":[{\"query\":\"Donald Trump Twitter\"},{\"query\":\"Donald Trump Truth Social\"},{\"query\":\"Donald Trump social media\"},{\"query\":\"Donald Trump net worth\"},{\"query\":\"Trump posts today\"},{\"query\":\"Trump tweets today\"},{\"query\":\"How to contact Donald Trump\"},{\"query\":\"Donald Trump X\"}],\"credits\":1}**\n", "**[INFO] Calling tool: extract_instagram with args {'instagram_url': 'https://www.instagram.com/realdonaldtrump/'}**\n", "**[FUNCTION CALL RESULT] extract_instagram: [{'inputUrl': 'https://www.instagram.com/realdonaldtrump/', 'id': '3552124823214131096', 'type': 'Video', 'shortCode': 'DFLrrTthoeY', 'caption': '', 'hashtags': [], 'mentions': [], 'url': 'https://www.instagram.com/p/DFLrrTthoeY/', 'commentsCount': 15274, 'firstComment': 'Sanction brokers that provide direct or indirect services to Iranians. Some of these brokers, such as Alpari, maintain significant fin×a×ncial connections with the leadership of <PERSON> and t×err×ori×st m×afi×a networks @realdonaldtrump @whitehouse @marcorubio @secrubio @sentedcruz @sentedbudd @treasurydept @senatorromney', 'latestComments': [{'id': '18065153206861803', 'text': 'Sanction brokers that provide direct or indirect services to Iranians. Some of these brokers, such as <PERSON><PERSON><PERSON>, maintain significant fin×a×ncial connections with the leadership of <PERSON> and t×err×ori×st m×afi×a networks @realdonaldtrump @whitehouse @marcorubio @secrubio @sentedcruz @sentedbudd @treasurydept @senatorromney', 'ownerUsername': 'dr.javid.lajevardi', 'ownerProfilePicUrl': 'https://instagram.fagc3-2.fna.fbcdn.net/v/t51.2885-19/247213929_472259527412412_6580234980702088937_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-2.fna.fbcdn.net&_nc_cat=100&_nc_ohc=34G0Dsx3XsMQ7kNvgHygp22&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCh7caCBPOFcSANI4-xKUFea3LFXGACOMvBkAzo6XS2xw&oe=679D10B6&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:27:44.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '50172135479', 'is_verified': False, 'profile_pic_url': 'https://instagram.fagc3-2.fna.fbcdn.net/v/t51.2885-19/247213929_472259527412412_6580234980702088937_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-2.fna.fbcdn.net&_nc_cat=100&_nc_ohc=34G0Dsx3XsMQ7kNvgHygp22&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCh7caCBPOFcSANI4-xKUFea3LFXGACOMvBkAzo6XS2xw&oe=679D10B6&_nc_sid=10d13b', 'username': 'dr.javid.lajevardi'}}, {'id': '18265802266264462', 'text': '#KingRezaPahlavi \\n#جاويدشاه_رمز_نجات_ایران', 'ownerUsername': 'parisairani99', 'ownerProfilePicUrl': 'https://instagram.fagc3-1.fna.fbcdn.net/v/t51.2885-19/14606973_342230049461565_2397924818822365184_a.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-1.fna.fbcdn.net&_nc_cat=109&_nc_ohc=NXAM2oWh3pMQ7kNvgFZ0EMa&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDbYxMhQlFtzlKFj2f_7vOKo20pOso44Bml1X7igu29zw&oe=679D1E0A&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:25:30.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '4005057406', 'is_verified': False, 'profile_pic_url': 'https://instagram.fagc3-1.fna.fbcdn.net/v/t51.2885-19/14606973_342230049461565_2397924818822365184_a.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-1.fna.fbcdn.net&_nc_cat=109&_nc_ohc=NXAM2oWh3pMQ7kNvgFZ0EMa&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDbYxMhQlFtzlKFj2f_7vOKo20pOso44Bml1X7igu29zw&oe=679D1E0A&_nc_sid=10d13b', 'username': 'parisairani99'}}, {'id': '18355867954122332', 'text': '#KingRezaPahlavi \\n#جاويدشاه_رمز_نجات_ایران', 'ownerUsername': 'parisairani99', 'ownerProfilePicUrl': 'https://instagram.fagc3-1.fna.fbcdn.net/v/t51.2885-19/14606973_342230049461565_2397924818822365184_a.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-1.fna.fbcdn.net&_nc_cat=109&_nc_ohc=NXAM2oWh3pMQ7kNvgFZ0EMa&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDbYxMhQlFtzlKFj2f_7vOKo20pOso44Bml1X7igu29zw&oe=679D1E0A&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:25:26.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '4005057406', 'is_verified': False, 'profile_pic_url': 'https://instagram.fagc3-1.fna.fbcdn.net/v/t51.2885-19/14606973_342230049461565_2397924818822365184_a.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-1.fna.fbcdn.net&_nc_cat=109&_nc_ohc=NXAM2oWh3pMQ7kNvgFZ0EMa&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDbYxMhQlFtzlKFj2f_7vOKo20pOso44Bml1X7igu29zw&oe=679D1E0A&_nc_sid=10d13b', 'username': 'parisairani99'}}, {'id': '17846628090402025', 'text': 'they shouldn’t have missed!!!', 'ownerUsername': 'number1onetrucker', 'ownerProfilePicUrl': 'https://instagram.fagc3-2.fna.fbcdn.net/v/t51.2885-19/475284116_3529343877371452_4730137655918923345_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-2.fna.fbcdn.net&_nc_cat=104&_nc_ohc=6VIcDLC2lkgQ7kNvgGwykjv&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYC-leg67iLAiKtL7hMEiQVK8-WnrkA2rAdb1zLheCy_AA&oe=679D2DDC&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:17:24.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '69501997910', 'is_verified': False, 'profile_pic_url': 'https://instagram.fagc3-2.fna.fbcdn.net/v/t51.2885-19/475284116_3529343877371452_4730137655918923345_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fagc3-2.fna.fbcdn.net&_nc_cat=104&_nc_ohc=6VIcDLC2lkgQ7kNvgGwykjv&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYC-leg67iLAiKtL7hMEiQVK8-WnrkA2rAdb1zLheCy_AA&oe=679D2DDC&_nc_sid=10d13b', 'username': 'number1onetrucker'}}], 'dimensionsHeight': 640, 'dimensionsWidth': 640, 'displayUrl': 'https://instagram.fagc3-1.fna.fbcdn.net/v/t51.2885-15/474366301_1192327748984216_403788917751217867_n.jpg?stp=dst-jpg_e15_tt6&_nc_ht=instagram.fagc3-1.fna.fbcdn.net&_nc_cat=1&_nc_ohc=18-qvkET84cQ7kNvgEFvXlT&_nc_gid=1d1074aec5cb4c9fb1f86493d6b787be&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBsu2BA0-8_pU_1hyvLbOTgl8aFPEcUV9b_CeVUDid2fw&oe=679D1078&_nc_sid=10d13b', 'images': [], 'videoUrl': 'https://instagram.fagc3-1.fna.fbcdn.net/o1/v/t16/f2/m86/AQPaSgIAtOwXhEf3m4iVhjl0Oa9rHQKl2iCuXFADpCAas7CFAENUA4bRbAxlWSUm0P2NDYnSQgGDRVhk3iCXesMyxz5X62Ph7V3VoIk.mp4?stp=dst-mp4&efg=eyJxZV9ncm91cHMiOiJbXCJpZ193ZWJfZGVsaXZlcnlfdnRzX290ZlwiXSIsInZlbmNvZGVfdGFnIjoidnRzX3ZvZF91cmxnZW4uY2xpcHMuYzIuNzIwLmJhc2VsaW5lIn0&_nc_cat=110&vs=609442228476959_3308106546&_nc_vs=HBksFQIYUmlnX3hwdl9yZWVsc19wZXJtYW5lbnRfc3JfcHJvZC82MTQzNDVFNThEQzI3MjUxNDg2QkUwMzc2NDQwQzE5NV92aWRlb19kYXNoaW5pdC5tcDQVAALIAQAVAhg6cGFzc3Rocm91Z2hfZXZlcnN0b3JlL0dETWtMUnktVnI1SU10RUVBUGxJQ3A1MURmWnpicV9FQUFBRhUCAsgBACgAGAAbABUAACaEwfeb99K7PxUCKAJDMywXQEPel41P3zsYEmRhc2hfYmFzZWxpbmVfMV92MREAdf4HAA%3D%3D&_nc_rid=1d107c70b3&ccb=9-4&oh=00_AYBEFGshxclH56OVdJBzl_QigNCMUsL8FAeEEI0J6tPgIg&oe=67993EBF&_nc_sid=10d13b', 'alt': None, 'likesCount': 577221, 'videoViewCount': 3515803, 'videoPlayCount': 10990835, 'timestamp': '2025-01-23T21:11:34.000Z', 'childPosts': [], 'ownerFullName': 'President Donald J. Trump', 'ownerUsername': 'realdonaldtrump', 'ownerId': '*********', 'productType': 'clips', 'videoDuration': 39.799, 'isSponsored': False, 'musicInfo': {'artist_name': 'donaldtrump.hq', 'song_name': 'Original audio', 'uses_original_audio': True, 'should_mute_audio': False, 'should_mute_audio_reason': '', 'audio_id': '1160077029048587'}}, {'inputUrl': 'https://www.instagram.com/realdonaldtrump/', 'id': '3553620073032463034', 'type': 'Image', 'shortCode': 'DFQ_qDnzDK6', 'caption': '', 'hashtags': [], 'mentions': [], 'url': 'https://www.instagram.com/p/DFQ_qDnzDK6/', 'commentsCount': 8109, 'firstComment': '🇺🇸🇺🇸🇺🇸', 'latestComments': [{'id': '18056655373956156', 'text': '🇺🇸🇺🇸🇺🇸', 'ownerUsername': 'olemusicinc', 'ownerProfilePicUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/443717443_826470349352039_4036963246949747291_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=111&_nc_ohc=qi8ehCjpr6IQ7kNvgF_0Fa3&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYATVpC6A9JefX74yA3LCy2sUp4VXB_PJGCy7HfXxEMHWg&oe=679D0EB3&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:41:02.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '7769300779', 'is_verified': False, 'profile_pic_url': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/443717443_826470349352039_4036963246949747291_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=111&_nc_ohc=qi8ehCjpr6IQ7kNvgF_0Fa3&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYATVpC6A9JefX74yA3LCy2sUp4VXB_PJGCy7HfXxEMHWg&oe=679D0EB3&_nc_sid=10d13b', 'username': 'olemusicinc'}}, {'id': '18071782996752729', 'text': 'MAGA', 'ownerUsername': 'olemusicinc', 'ownerProfilePicUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/443717443_826470349352039_4036963246949747291_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=111&_nc_ohc=qi8ehCjpr6IQ7kNvgF_0Fa3&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYATVpC6A9JefX74yA3LCy2sUp4VXB_PJGCy7HfXxEMHWg&oe=679D0EB3&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:40:56.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '7769300779', 'is_verified': False, 'profile_pic_url': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/443717443_826470349352039_4036963246949747291_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=111&_nc_ohc=qi8ehCjpr6IQ7kNvgF_0Fa3&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYATVpC6A9JefX74yA3LCy2sUp4VXB_PJGCy7HfXxEMHWg&oe=679D0EB3&_nc_sid=10d13b', 'username': 'olemusicinc'}}, {'id': '18075616669651833', 'text': '2 hours left until the event', 'ownerUsername': 'lifeisridingabike', 'ownerProfilePicUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/91338369_257089572122286_2593394441230221312_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=106&_nc_ohc=oTOmXJNYOYwQ7kNvgHAXe6D&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAdn0XZxfg2n4ZD4_NGhn8rZhUGVNj1Jcx2JXRgJThjgg&oe=679D0EAC&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:40:02.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '32475754218', 'is_verified': False, 'profile_pic_url': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/91338369_257089572122286_2593394441230221312_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=106&_nc_ohc=oTOmXJNYOYwQ7kNvgHAXe6D&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAdn0XZxfg2n4ZD4_NGhn8rZhUGVNj1Jcx2JXRgJThjgg&oe=679D0EAC&_nc_sid=10d13b', 'username': 'lifeisridingabike'}}, {'id': '18076349611724419', 'text': '🇺🇸', 'ownerUsername': 'juzzyjoke', 'ownerProfilePicUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/468034355_550971920903712_1560324574680449649_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=100&_nc_ohc=Ov-AH1EvA0oQ7kNvgEN2lnQ&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCKBe6XG8FRMuDMehJu5C4bsdW3vyacwSy11oxkLtqPyg&oe=679D1988&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:32:43.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '2955079261', 'is_verified': False, 'profile_pic_url': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/468034355_550971920903712_1560324574680449649_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=100&_nc_ohc=Ov-AH1EvA0oQ7kNvgEN2lnQ&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCKBe6XG8FRMuDMehJu5C4bsdW3vyacwSy11oxkLtqPyg&oe=679D1988&_nc_sid=10d13b', 'username': 'juzzyjoke'}}, {'id': '18019578083447492', 'text': '👏👏👏🇺🇸', 'ownerUsername': 'true_to_fashion', 'ownerProfilePicUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/329008501_1012249736408930_8436894906374615784_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=102&_nc_ohc=-x4IFx81ovEQ7kNvgHm8MEO&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBAhHPrv5PaLxon0Irb__BeY1d9NY-jEguQApYidfIEVQ&oe=679D326E&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:23:57.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '4475804487', 'is_verified': False, 'profile_pic_url': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/329008501_1012249736408930_8436894906374615784_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=102&_nc_ohc=-x4IFx81ovEQ7kNvgHm8MEO&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBAhHPrv5PaLxon0Irb__BeY1d9NY-jEguQApYidfIEVQ&oe=679D326E&_nc_sid=10d13b', 'username': 'true_to_fashion'}}, {'id': '18083330638605234', 'text': 'WWF made such comeback against WCW 27 years ago already, Eduardo.', 'ownerUsername': 'bsod404', 'ownerProfilePicUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/403942452_1194663101261584_1051841689406850500_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=104&_nc_ohc=4Op1JvwMr7kQ7kNvgHar3-D&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCgLnSAyjiP6zd9M8m3ewA-EeD6wlg0GSFgIBm8A5CBZQ&oe=679D1B3E&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:20:05.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '6808078621', 'is_verified': False, 'profile_pic_url': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/403942452_1194663101261584_1051841689406850500_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=104&_nc_ohc=4Op1JvwMr7kQ7kNvgHar3-D&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCgLnSAyjiP6zd9M8m3ewA-EeD6wlg0GSFgIBm8A5CBZQ&oe=679D1B3E&_nc_sid=10d13b', 'username': 'bsod404'}}, {'id': '18485129701007521', 'text': 'Nuh uh', 'ownerUsername': 'al3xj116', 'ownerProfilePicUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/417979320_781776580663208_8672448607487446401_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=108&_nc_ohc=QxApsjzhg0AQ7kNvgEdJUUH&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCcWTLPb4yUKdzJFTnQ6OH-bKKp9Xglu8EC53kiNXa77w&oe=679D14CA&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:18:10.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '57220778803', 'is_verified': False, 'profile_pic_url': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-19/417979320_781776580663208_8672448607487446401_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=108&_nc_ohc=QxApsjzhg0AQ7kNvgEdJUUH&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCcWTLPb4yUKdzJFTnQ6OH-bKKp9Xglu8EC53kiNXa77w&oe=679D14CA&_nc_sid=10d13b', 'username': 'al3xj116'}}], 'dimensionsHeight': 1080, 'dimensionsWidth': 1080, 'displayUrl': 'https://scontent-man2-1.cdninstagram.com/v/t51.2885-15/475090750_18520261585048669_675356557477604277_n.jpg?stp=dst-jpg_e15_fr_s1080x1080_tt6&_nc_ht=scontent-man2-1.cdninstagram.com&_nc_cat=1&_nc_ohc=OMgb3ivDME0Q7kNvgGKka3r&_nc_gid=f87890f7aba241f89eb4be6fb6cdc829&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYB-C_8OI5EIRjR44J3jA_1kOcxLdoC5pBT9v5VAYzfSng&oe=679D21C6&_nc_sid=10d13b', 'images': [], 'alt': 'Photo by President Donald J. Trump on January 25, 2025. May be an image of 1 person, standing and text.', 'likesCount': 471173, 'timestamp': '2025-01-25T22:35:45.000Z', 'childPosts': [], 'ownerFullName': 'President Donald J. Trump', 'ownerUsername': 'realdonaldtrump', 'ownerId': '*********', 'isSponsored': False}, {'inputUrl': 'https://www.instagram.com/realdonaldtrump/', 'id': '3553424241791526044', 'type': 'Video', 'shortCode': 'DFQTIVkPuSc', 'caption': '', 'hashtags': [], 'mentions': [], 'url': 'https://www.instagram.com/p/DFQTIVkPuSc/', 'commentsCount': 5657, 'firstComment': '😭', 'latestComments': [{'id': '18014602334673614', 'text': '😭', 'ownerUsername': 'annikabrobjer', 'ownerProfilePicUrl': 'https://scontent-nrt1-2.cdninstagram.com/v/t51.2885-19/19533874_401373160278937_5911176357206818816_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-2.cdninstagram.com&_nc_cat=101&_nc_ohc=3qX9f_IuSA4Q7kNvgHvL1FR&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBpJAqoPXHKvt2k_-jSWm2DjDVFqupycTfd_5KOcFuH7A&oe=679D1EC5&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:41:48.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '221298711', 'is_verified': False, 'profile_pic_url': 'https://scontent-nrt1-2.cdninstagram.com/v/t51.2885-19/19533874_401373160278937_5911176357206818816_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-2.cdninstagram.com&_nc_cat=101&_nc_ohc=3qX9f_IuSA4Q7kNvgHvL1FR&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBpJAqoPXHKvt2k_-jSWm2DjDVFqupycTfd_5KOcFuH7A&oe=679D1EC5&_nc_sid=10d13b', 'username': 'annikabrobjer'}}, {'id': '18029979131569396', 'text': 'Congestion president 👏👏👏👏', 'ownerUsername': 'king_gandav', 'ownerProfilePicUrl': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/475100982_962357405839823_3773719069781301268_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=111&_nc_ohc=EXll5_Zt0iIQ7kNvgGlzPx9&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBJ1vLioDvS6LUqA8RyYdHbJtEKOW5KM8-wdALSrc9jPw&oe=679D1987&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:40:15.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '2333208986', 'is_verified': False, 'profile_pic_url': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/475100982_962357405839823_3773719069781301268_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=111&_nc_ohc=EXll5_Zt0iIQ7kNvgGlzPx9&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBJ1vLioDvS6LUqA8RyYdHbJtEKOW5KM8-wdALSrc9jPw&oe=679D1987&_nc_sid=10d13b', 'username': 'king_gandav'}}, {'id': '18070279729695603', 'text': '❤️', 'ownerUsername': 'elin_sweett', 'ownerProfilePicUrl': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/451436406_1158885835381998_5847953030580529003_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=106&_nc_ohc=tZvTCfhwRCMQ7kNvgHmawt3&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBFdWBzn0ZSDRvNJ13Ot8mIiY503sNLA3JSlxnY-sgnWw&oe=679D07D3&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:39:03.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '67658337732', 'is_verified': False, 'profile_pic_url': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/451436406_1158885835381998_5847953030580529003_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=106&_nc_ohc=tZvTCfhwRCMQ7kNvgHmawt3&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBFdWBzn0ZSDRvNJ13Ot8mIiY503sNLA3JSlxnY-sgnWw&oe=679D07D3&_nc_sid=10d13b', 'username': 'elin_sweett'}}, {'id': '18341799307148133', 'text': 'THE CHOSEN ONE 🇺🇸', 'ownerUsername': 'thommysky01', 'ownerProfilePicUrl': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/336466436_2141295746261265_5623779767962778651_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=109&_nc_ohc=WNz--1B2XckQ7kNvgEbcSFe&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAWQBlDBR35fLgxUqX4wPajIDRiXlGC0x7E7CBQX-K8Aw&oe=679D0B34&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:21:49.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '58600079717', 'is_verified': False, 'profile_pic_url': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/336466436_2141295746261265_5623779767962778651_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=109&_nc_ohc=WNz--1B2XckQ7kNvgEbcSFe&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAWQBlDBR35fLgxUqX4wPajIDRiXlGC0x7E7CBQX-K8Aw&oe=679D0B34&_nc_sid=10d13b', 'username': 'thommysky01'}}, {'id': '18062477539905564', 'text': 'Best president ever 🇺🇸🇺🇸', 'ownerUsername': 'lizzyjoel03', 'ownerProfilePicUrl': 'https://scontent-nrt1-2.cdninstagram.com/v/t51.2885-19/474785691_1681045969153850_5647797358993450658_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-2.cdninstagram.com&_nc_cat=102&_nc_ohc=xW9TGeUX5ecQ7kNvgH5jPD3&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBoOhkNQAe1QN8F8tAbMVEolPbMZM1k3Xf1c3j-eCCqTQ&oe=679D1BCE&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:55:24.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '71678943508', 'is_verified': False, 'profile_pic_url': 'https://scontent-nrt1-2.cdninstagram.com/v/t51.2885-19/474785691_1681045969153850_5647797358993450658_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-2.cdninstagram.com&_nc_cat=102&_nc_ohc=xW9TGeUX5ecQ7kNvgH5jPD3&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBoOhkNQAe1QN8F8tAbMVEolPbMZM1k3Xf1c3j-eCCqTQ&oe=679D1BCE&_nc_sid=10d13b', 'username': 'lizzyjoel03'}}, {'id': '17977765649802508', 'text': 'لاوصيك اذبح الشواذ وعساك على القوة', 'ownerUsername': 'c95jz', 'ownerProfilePicUrl': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/461968387_1945646572527741_6277335423503965648_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=109&_nc_ohc=OvfPeUWueEQQ7kNvgHblKUb&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBNEwqo8FtRafFfk5rqEWDIfKnmtSlygWXX1W2gwOGlsQ&oe=679D0A4C&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:31:18.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '31633156077', 'is_verified': False, 'profile_pic_url': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-19/461968387_1945646572527741_6277335423503965648_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=109&_nc_ohc=OvfPeUWueEQQ7kNvgHblKUb&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYBNEwqo8FtRafFfk5rqEWDIfKnmtSlygWXX1W2gwOGlsQ&oe=679D0A4C&_nc_sid=10d13b', 'username': 'c95jz'}}, {'id': '17889036687186688', 'text': 'Please pray for Indian Christians, because there are Sikh community and also Hindus here in India Punjab who are harassing Christians and making fun of Jesus Christ and I want American people to stand up for Indian Christians.', 'ownerUsername': 'official_william5678', 'ownerProfilePicUrl': 'https://scontent-nrt1-2.cdninstagram.com/v/t51.2885-19/471938384_1592082568067104_4383125987017342514_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-2.cdninstagram.com&_nc_cat=104&_nc_ohc=ZQzaHkASxfsQ7kNvgHxrrZG&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDWOoTCAZdWhRSA0VhkEhH4ZD8f5K4IbijLzStRcjJ2rA&oe=679D2947&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:15:35.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '4346700066', 'is_verified': False, 'profile_pic_url': 'https://scontent-nrt1-2.cdninstagram.com/v/t51.2885-19/471938384_1592082568067104_4383125987017342514_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-nrt1-2.cdninstagram.com&_nc_cat=104&_nc_ohc=ZQzaHkASxfsQ7kNvgHxrrZG&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDWOoTCAZdWhRSA0VhkEhH4ZD8f5K4IbijLzStRcjJ2rA&oe=679D2947&_nc_sid=10d13b', 'username': 'official_william5678'}}], 'dimensionsHeight': 640, 'dimensionsWidth': 640, 'displayUrl': 'https://scontent-nrt1-1.cdninstagram.com/v/t51.2885-15/472529069_3511810665792192_8340058584958117766_n.jpg?stp=dst-jpg_e15_tt6&_nc_ht=scontent-nrt1-1.cdninstagram.com&_nc_cat=1&_nc_ohc=lmtFqcQ94dIQ7kNvgGUoM-e&_nc_gid=9d95e1c9ead14e67b125059d1f00861c&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYApW5pJQBieWYqWS7koWsyKSSL6EofW-O_3eiVHk_lbJw&oe=679D2B01&_nc_sid=10d13b', 'images': [], 'videoUrl': 'https://scontent-nrt1-2.cdninstagram.com/o1/v/t16/f2/m86/AQObptic9X1LcabXTmqP0O-uJoiVeOnNMTEpKVsZlB5wpIFzEomUMq3edbdTA6QjDDKeTUSU3U8hxIuZ2byns-F0e9zotNmPNd9enMM.mp4?stp=dst-mp4&efg=eyJxZV9ncm91cHMiOiJbXCJpZ193ZWJfZGVsaXZlcnlfdnRzX290ZlwiXSIsInZlbmNvZGVfdGFnIjoidnRzX3ZvZF91cmxnZW4uY2xpcHMuYzIuNzIwLmJhc2VsaW5lIn0&_nc_cat=100&vs=3929162890687527_489388998&_nc_vs=HBksFQIYUmlnX3hwdl9yZWVsc19wZXJtYW5lbnRfc3JfcHJvZC9FNzQwN0E2Qjg5MDAxODU2N0RGQjY4MTJENkE1MTBCQV92aWRlb19kYXNoaW5pdC5tcDQVAALIAQAVAhg6cGFzc3Rocm91Z2hfZXZlcnN0b3JlL0dKZGVUaHpDN1k4dENnc0NBQ0ZBZHMzY0FsUkdicV9FQUFBRhUCAsgBACgAGAAbABUAACb6rZ3bk4SHQBUCKAJDMywXQFFGdsi0OVgYEmRhc2hfYmFzZWxpbmVfMV92MREAdf4HAA%3D%3D&_nc_rid=9d95e1910d&ccb=9-4&oh=00_AYClDRBXtqSmhaI0nSSeDQljWP0ee1B8bwnaA3ZZF8YYjQ&oe=67991C0F&_nc_sid=10d13b', 'alt': None, 'likesCount': 178841, 'videoViewCount': 1167759, 'videoPlayCount': 4229925, 'timestamp': '2025-01-25T16:08:40.000Z', 'childPosts': [], 'ownerFullName': 'President Donald J. Trump', 'ownerUsername': 'realdonaldtrump', 'ownerId': '*********', 'productType': 'clips', 'videoDuration': 69.126, 'isSponsored': False, 'musicInfo': {'artist_name': 'realdonaldtrump', 'song_name': 'Original audio', 'uses_original_audio': True, 'should_mute_audio': False, 'should_mute_audio_reason': '', 'audio_id': '1434560227775519'}}, {'inputUrl': 'https://www.instagram.com/realdonaldtrump/', 'id': '3554262110178102633', 'type': 'Image', 'shortCode': 'DFTRo7Ty81p', 'caption': '', 'hashtags': [], 'mentions': [], 'url': 'https://www.instagram.com/p/DFTRo7Ty81p/', 'commentsCount': 16626, 'firstComment': 'COLD', 'latestComments': [{'id': '17945837528808925', 'text': 'COLD', 'ownerUsername': 'joe.eld', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/462739889_566591969046354_6734209402789133939_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=106&_nc_ohc=TmH16YRq5vAQ7kNvgGcJu-X&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDk1CsV83yQpL5hLJIfXMK6xS4l9dg4m8eQbtEvSubZYA&oe=679D125F&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:42:21.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '1419769195', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/462739889_566591969046354_6734209402789133939_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=106&_nc_ohc=TmH16YRq5vAQ7kNvgGcJu-X&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDk1CsV83yQpL5hLJIfXMK6xS4l9dg4m8eQbtEvSubZYA&oe=679D125F&_nc_sid=10d13b', 'username': 'joe.eld'}}, {'id': '18056671877004564', 'text': 'We will make this nation the one I always saw since I was very young! The great nation, the United States of America🇺🇸❤️', 'ownerUsername': 'mennillo_', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/474717163_2623651257820219_7617938144467382695_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=107&_nc_ohc=EMtEOejWUfwQ7kNvgFkjhKv&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCtGN3kQYgaj4c7heT6_yZmypkF1WWXdwrouNt5aWmpZw&oe=679D2EE6&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:42:04.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '1624115801', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/474717163_2623651257820219_7617938144467382695_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=107&_nc_ohc=EMtEOejWUfwQ7kNvgFkjhKv&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCtGN3kQYgaj4c7heT6_yZmypkF1WWXdwrouNt5aWmpZw&oe=679D2EE6&_nc_sid=10d13b', 'username': 'mennillo_'}}, {'id': '18002208311720897', 'text': 'BOSS!!! F%$k Around and Find Out', 'ownerUsername': 'durty_shaft', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/310192092_556084226425626_3775459991241663722_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=106&_nc_ohc=E9PuhFpQYugQ7kNvgEx4HZY&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYD_Bg7coiZrxgrUo8kmgJ_sszOIFQRFnPzhmlsa4Ygl-Q&oe=679D1882&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:42:02.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '2519040682', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/310192092_556084226425626_3775459991241663722_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=106&_nc_ohc=E9PuhFpQYugQ7kNvgEx4HZY&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYD_Bg7coiZrxgrUo8kmgJ_sszOIFQRFnPzhmlsa4Ygl-Q&oe=679D1882&_nc_sid=10d13b', 'username': 'durty_shaft'}}, {'id': '17982806321643898', 'text': 'Scammer', 'ownerUsername': 'bberkayyildirim', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/472956925_1616494585626517_1336819825436223816_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=101&_nc_ohc=yNGISJZxfaYQ7kNvgGQ5hV-&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAlZ60ZsTZvBbdF_BVYM4ZaiFkLp7bUIzYi-euxxMrBQQ&oe=679D0352&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:41:55.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '339171131', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/472956925_1616494585626517_1336819825436223816_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=101&_nc_ohc=yNGISJZxfaYQ7kNvgGQ5hV-&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAlZ60ZsTZvBbdF_BVYM4ZaiFkLp7bUIzYi-euxxMrBQQ&oe=679D0352&_nc_sid=10d13b', 'username': 'bberkayyildirim'}}, {'id': '18073117117670287', 'text': 'That’s my president ! Since 2015 ❤️👏🇺🇸😍', 'ownerUsername': 'taylorschon2', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/323839446_3452921684966426_1882881753735033138_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=102&_nc_ohc=nZ-4PFvvmvoQ7kNvgGqf0I6&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAM4yzFfQKzELdVIlBto4iN7VhjU_6_zJwPUvcsGyTz9g&oe=679D159F&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:41:17.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '57265262489', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/323839446_3452921684966426_1882881753735033138_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=102&_nc_ohc=nZ-4PFvvmvoQ7kNvgGqf0I6&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAM4yzFfQKzELdVIlBto4iN7VhjU_6_zJwPUvcsGyTz9g&oe=679D159F&_nc_sid=10d13b', 'username': 'taylorschon2'}}, {'id': '18035534744423146', 'text': '😂😂 🔥🔥🔥\\n\\nFAFO', 'ownerUsername': '_olufemi.fmd_', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/461273196_1243205833542463_750258986265923131_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=107&_nc_ohc=-Pcz2VfQqEoQ7kNvgECHWVS&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCRUKIo_AxxR2eP8_IXTshtQUDcx8pxTazEXO3ZBeMuxA&oe=679D2B5A&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:40:50.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '1633381605', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/461273196_1243205833542463_750258986265923131_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=107&_nc_ohc=-Pcz2VfQqEoQ7kNvgECHWVS&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCRUKIo_AxxR2eP8_IXTshtQUDcx8pxTazEXO3ZBeMuxA&oe=679D2B5A&_nc_sid=10d13b', 'username': '_olufemi.fmd_'}}, {'id': '17934170846986567', 'text': '😂😂😂😂', 'ownerUsername': 'k.a.m.o3476', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/338427877_184836494341498_3063382024979528296_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=110&_nc_ohc=Sb6Wv7RzwTsQ7kNvgHYElmw&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAmOH6zTrdVYdgvUXyUyiNNnxphLOqx_CgcFgpcAs-POQ&oe=679D358D&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:40:49.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '48792626728', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/338427877_184836494341498_3063382024979528296_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=110&_nc_ohc=Sb6Wv7RzwTsQ7kNvgHYElmw&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAmOH6zTrdVYdgvUXyUyiNNnxphLOqx_CgcFgpcAs-POQ&oe=679D358D&_nc_sid=10d13b', 'username': 'k.a.m.o3476'}}, {'id': '17938757774841356', 'text': 'Trump porque no le subes arancel a la cocaina? A eso si le tienes miedo 😂', 'ownerUsername': 'sharonmodels22', 'ownerProfilePicUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/14550092_575353836009017_1156647152512925696_a.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=105&_nc_ohc=vXkF0H0hSrUQ7kNvgGXVnyz&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCFH6DpBz5CHiQ87G3EUi3fuzQ8obJ65e6QDaYkGIKHFg&oe=679D2266&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:39:59.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '4000366212', 'is_verified': False, 'profile_pic_url': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-19/14550092_575353836009017_1156647152512925696_a.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=105&_nc_ohc=vXkF0H0hSrUQ7kNvgGXVnyz&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCFH6DpBz5CHiQ87G3EUi3fuzQ8obJ65e6QDaYkGIKHFg&oe=679D2266&_nc_sid=10d13b', 'username': 'sharonmodels22'}}], 'dimensionsHeight': 1080, 'dimensionsWidth': 1080, 'displayUrl': 'https://instagram.fnic4-1.fna.fbcdn.net/v/t51.2885-15/475130963_18520428076048669_6541680967500843245_n.jpg?stp=dst-jpg_e15_tt6&_nc_ht=instagram.fnic4-1.fna.fbcdn.net&_nc_cat=1&_nc_ohc=bWozEHxnsgEQ7kNvgEaOBRC&_nc_gid=253b3afb643e47b7950fae957702f826&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCLrA5C-j8Lk0935gQ93bWluGtTrjIqN01y34T-h4FAKQ&oe=679D05D3&_nc_sid=10d13b', 'images': [], 'alt': 'Photo by President Donald J. Trump on January 26, 2025. Μπορεί να είναι εικόνα 1 άτομο, καπνίζει, καπέλο, αφίσα, καπέλο μπόουλερ και κείμενο.', 'likesCount': 710056, 'timestamp': '2025-01-26T19:51:22.000Z', 'childPosts': [], 'ownerFullName': 'President Donald J. Trump', 'ownerUsername': 'realdonaldtrump', 'ownerId': '*********', 'isSponsored': False}, {'inputUrl': 'https://www.instagram.com/realdonaldtrump/', 'id': '3552879705139370723', 'type': 'Video', 'shortCode': 'DFOXUSShtrj', 'caption': 'THE MOST HISTORIC 100 HOURS IN AMERICAN HISTORY—THE GOLDEN AGE OF AMERICA BEGINS RIGHT NOW!', 'hashtags': [], 'mentions': [], 'url': 'https://www.instagram.com/p/DFOXUSShtrj/', 'commentsCount': 9519, 'firstComment': 'Love you Trump....what a man', 'latestComments': [{'id': '17957930453887569', 'text': 'Love you Trump....what a man', 'ownerUsername': 'kimefleurs_florist_sidcup', 'ownerProfilePicUrl': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/234333652_855653188425262_8137898776327747383_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=110&_nc_ohc=kkdCyBqu6ycQ7kNvgEOJX4e&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYB_DNdm3p4w7heDHArA1egRtyRzncOEWf_TZxPKm5DCLA&oe=679D076D&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:39:35.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '31568593140', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/234333652_855653188425262_8137898776327747383_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=110&_nc_ohc=kkdCyBqu6ycQ7kNvgEOJX4e&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYB_DNdm3p4w7heDHArA1egRtyRzncOEWf_TZxPKm5DCLA&oe=679D076D&_nc_sid=10d13b', 'username': 'kimefleurs_florist_sidcup'}}, {'id': '18030771287533393', 'text': 'Make Amerika Hetro again', 'ownerUsername': 'rtworul', 'ownerProfilePicUrl': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/433678256_1140847823738015_3253234145839213747_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=111&_nc_ohc=x5Oo7yH2fBEQ7kNvgHNtIHN&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYD1k1oFBr-uTHgk1EV6Vo-tq13qcLX-wRrEjvQMlRORag&oe=679D21B7&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:44:11.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '8458959085', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/433678256_1140847823738015_3253234145839213747_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=111&_nc_ohc=x5Oo7yH2fBEQ7kNvgHNtIHN&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYD1k1oFBr-uTHgk1EV6Vo-tq13qcLX-wRrEjvQMlRORag&oe=679D21B7&_nc_sid=10d13b', 'username': 'rtworul'}}, {'id': '18045975875241788', 'text': '❤️🇺🇸', 'ownerUsername': 'rxh.xyz', 'ownerProfilePicUrl': 'https://instagram.fpnq13-6.fna.fbcdn.net/v/t51.2885-19/464760996_1254146839119862_3605321457742435801_n.png?stp=dst-jpg_e0_s150x150_tt6&cb=8577c754-c2464923&_nc_ht=instagram.fpnq13-6.fna.fbcdn.net&_nc_cat=1&_nc_ohc=pTJXK6X9SScQ7kNvgGXnap8&_nc_gid=c565ac862e4f42828037f18a0f2b19a9&edm=AP7HCqgBAAAA&ccb=7-5&ig_cache_key=YW5vbnltb3VzX3Byb2ZpbGVfcGlj.3-ccb7-5-cb8577c754-c2464923&oh=00_AYAiH_8tpL5y7ytHFN2VHlnI3FFwvZlWfGHpeMftYEPT9Q&oe=679D1EE8&_nc_sid=b09b61', 'timestamp': '2025-01-27T07:39:38.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '4242756436', 'is_verified': False, 'profile_pic_url': 'https://instagram.fpnq13-6.fna.fbcdn.net/v/t51.2885-19/464760996_1254146839119862_3605321457742435801_n.png?stp=dst-jpg_e0_s150x150_tt6&cb=8577c754-c2464923&_nc_ht=instagram.fpnq13-6.fna.fbcdn.net&_nc_cat=1&_nc_ohc=pTJXK6X9SScQ7kNvgGXnap8&_nc_gid=c565ac862e4f42828037f18a0f2b19a9&edm=AP7HCqgBAAAA&ccb=7-5&ig_cache_key=YW5vbnltb3VzX3Byb2ZpbGVfcGlj.3-ccb7-5-cb8577c754-c2464923&oh=00_AYAiH_8tpL5y7ytHFN2VHlnI3FFwvZlWfGHpeMftYEPT9Q&oe=679D1EE8&_nc_sid=b09b61', 'username': 'rxh.xyz'}}, {'id': '18012435170500890', 'text': '👏👏👏 \\nSo Proud of you , we are so lucky to have you , in this world .', 'ownerUsername': 'la.aura5', 'ownerProfilePicUrl': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/344775626_2548650628619902_364376831137113915_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=110&_nc_ohc=tRmuiYnqbeYQ7kNvgFLqiTP&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAlCirE_uc4nxJXsk9lhV6Hxd3DDJGTT-9i1kWwH2w78A&oe=679D01BF&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:37:15.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '1483944688', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/344775626_2548650628619902_364376831137113915_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=110&_nc_ohc=tRmuiYnqbeYQ7kNvgFLqiTP&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYAlCirE_uc4nxJXsk9lhV6Hxd3DDJGTT-9i1kWwH2w78A&oe=679D01BF&_nc_sid=10d13b', 'username': 'la.aura5'}}, {'id': '18022724147374127', 'text': 'BAAAAAADASSSS! 🇺🇸🔥🇺🇸🔥💪💥💯💫', 'ownerUsername': 'amalkel', 'ownerProfilePicUrl': 'https://scontent-for2-2.cdninstagram.com/v/t51.2885-19/460947556_1043067617142795_8377682230058580270_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-2.cdninstagram.com&_nc_cat=103&_nc_ohc=E7TcdrPZtX0Q7kNvgFZpI0R&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDcqzLSIwUyYkt_QGelNBcrLjSnYvPxjWF9tBrdY1Hd1A&oe=679D1F56&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:32:14.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '7793099710', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-2.cdninstagram.com/v/t51.2885-19/460947556_1043067617142795_8377682230058580270_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-2.cdninstagram.com&_nc_cat=103&_nc_ohc=E7TcdrPZtX0Q7kNvgFZpI0R&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYDcqzLSIwUyYkt_QGelNBcrLjSnYvPxjWF9tBrdY1Hd1A&oe=679D1F56&_nc_sid=10d13b', 'username': 'amalkel'}}, {'id': '18050975813122659', 'text': 'The decline is here!', 'ownerUsername': 'lakeliferxoxo', 'ownerProfilePicUrl': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/298643118_142660505115265_1645191240331115611_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=101&_nc_ohc=3do89f4CVCAQ7kNvgErLwoT&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCGYW8ZlIJ4Fscn9zfCH6JvxBW5W6-vpzGgplSiBHZ9Sg&oe=679D2005&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:19:58.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '15002670', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/298643118_142660505115265_1645191240331115611_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=101&_nc_ohc=3do89f4CVCAQ7kNvgErLwoT&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCGYW8ZlIJ4Fscn9zfCH6JvxBW5W6-vpzGgplSiBHZ9Sg&oe=679D2005&_nc_sid=10d13b', 'username': 'lakeliferxoxo'}}, {'id': '18480090754018870', 'text': 'Thank you for putting America first 🙌🙌🙌', 'ownerUsername': 'monitea1', 'ownerProfilePicUrl': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/40993359_687613831601286_1070949107415121920_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=106&_nc_ohc=ChacPuZFuN8Q7kNvgHxZhKC&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCOjJbG8Zf8-SecMGwmW4mT2cCGx4w-1SdUx1t7uja48Q&oe=679D017D&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:16:07.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '3657143290', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/40993359_687613831601286_1070949107415121920_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=106&_nc_ohc=ChacPuZFuN8Q7kNvgHxZhKC&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCOjJbG8Zf8-SecMGwmW4mT2cCGx4w-1SdUx1t7uja48Q&oe=679D017D&_nc_sid=10d13b', 'username': 'monitea1'}}, {'id': '17882179764212512', 'text': 'Make America Great Again 🔥🔥🔥🙏🇺🇸❤️', 'ownerUsername': 'monitea1', 'ownerProfilePicUrl': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/40993359_687613831601286_1070949107415121920_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=106&_nc_ohc=ChacPuZFuN8Q7kNvgHxZhKC&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCOjJbG8Zf8-SecMGwmW4mT2cCGx4w-1SdUx1t7uja48Q&oe=679D017D&_nc_sid=10d13b', 'timestamp': '2025-01-27T07:13:52.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '3657143290', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-1.cdninstagram.com/v/t51.2885-19/40993359_687613831601286_1070949107415121920_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-1.cdninstagram.com&_nc_cat=106&_nc_ohc=ChacPuZFuN8Q7kNvgHxZhKC&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCOjJbG8Zf8-SecMGwmW4mT2cCGx4w-1SdUx1t7uja48Q&oe=679D017D&_nc_sid=10d13b', 'username': 'monitea1'}}, {'id': '18043869596365836', 'text': '@velociraptorhandler NYC is all you need to know from that guy😂💀', 'ownerUsername': 'masonlyon8', 'ownerProfilePicUrl': 'https://scontent-for2-2.cdninstagram.com/v/t51.2885-19/463845595_511966058501013_5715198641335214363_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-2.cdninstagram.com&_nc_cat=109&_nc_ohc=kibTlWZ7nvkQ7kNvgE1i1vT&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCoy5lpaKvH9VGYOwm7R9P3s_3_MNTqXRYx2_RMzdI0Ww&oe=679D2902&_nc_sid=10d13b', 'timestamp': '2025-01-27T08:19:58.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'id': '61850307394', 'is_verified': False, 'profile_pic_url': 'https://scontent-for2-2.cdninstagram.com/v/t51.2885-19/463845595_511966058501013_5715198641335214363_n.jpg?stp=dst-jpg_s150x150_tt6&_nc_ht=scontent-for2-2.cdninstagram.com&_nc_cat=109&_nc_ohc=kibTlWZ7nvkQ7kNvgE1i1vT&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYCoy5lpaKvH9VGYOwm7R9P3s_3_MNTqXRYx2_RMzdI0Ww&oe=679D2902&_nc_sid=10d13b', 'username': 'masonlyon8'}}], 'dimensionsHeight': 360, 'dimensionsWidth': 640, 'displayUrl': 'https://scontent-for2-2.cdninstagram.com/v/t51.2885-15/475122262_1135386997985340_888212184715545057_n.jpg?stp=dst-jpg_e15_tt6&_nc_ht=scontent-for2-2.cdninstagram.com&_nc_cat=1&_nc_ohc=ksY1R2m5b-YQ7kNvgHrIsoi&_nc_gid=6a80777fefb944a4a3f254084bbaab00&edm=APs17CUBAAAA&ccb=7-5&oh=00_AYC9dfa2IeEwMGqJTQX--Lq2a51aOpRW--XZ42YwYA5X5A&oe=679D2559&_nc_sid=10d13b', 'images': [], 'videoUrl': 'https://scontent-for2-2.cdninstagram.com/o1/v/t16/f2/m86/AQPWxqQmpRLjbxWuJ_fj8T_ODaIIiYgxAYBjWpsf-bkEWjp0mnYndjydTm3VZ-UJ6sUi9LxFC_xMkMD5TTLoRt7fzMxouOvQEjJgL8c.mp4?stp=dst-mp4&efg=eyJxZV9ncm91cHMiOiJbXCJpZ193ZWJfZGVsaXZlcnlfdnRzX290ZlwiXSIsInZlbmNvZGVfdGFnIjoidnRzX3ZvZF91cmxnZW4uY2xpcHMuYzIuMTI3OC5iYXNlbGluZSJ9&_nc_cat=107&vs=469207599386465_2700603221&_nc_vs=HBksFQIYUmlnX3hwdl9yZWVsc19wZXJtYW5lbnRfc3JfcHJvZC9GQTQxNTI4NkRGMTREODI4QzJCMjBFNURFNkE0NjNCRl92aWRlb19kYXNoaW5pdC5tcDQVAALIAQAVAhg6cGFzc3Rocm91Z2hfZXZlcnN0b3JlL0dCSFdUaHhmM084Q2lrQUVBSDJ1YUVZTmFtTkFicV9FQUFBRhUCAsgBACgAGAAbABUAACa2s%2Bnrk4%2BSQBUCKAJDMywXQFuHCj1wo9cYEmRhc2hfYmFzZWxpbmVfMV92MREAdf4HAA%3D%3D&_nc_rid=6a807fb8e1&ccb=9-4&oh=00_AYDVqtBfBG3zBbOqsZSmRppivcg_d-iZtRI7_xYL12Esvw&oe=67992CAF&_nc_sid=10d13b', 'alt': None, 'likesCount': 470979, 'videoViewCount': 1730196, 'videoPlayCount': 6621708, 'timestamp': '2025-01-24T22:08:19.000Z', 'childPosts': [], 'ownerFullName': 'President Donald J. Trump', 'ownerUsername': 'realdonaldtrump', 'ownerId': '*********', 'productType': 'clips', 'videoDuration': 110.11, 'isSponsored': False, 'musicInfo': {'artist_name': 'realdonaldtrump', 'song_name': 'Original audio', 'uses_original_audio': True, 'should_mute_audio': False, 'should_mute_audio_reason': '', 'audio_id': '***************'}}]**\n", "--FINAL RESULT: Here are some of <PERSON>'s recent posts on his official Instagram account (@realdonaldtrump):\n", "\n", "1. **Post Date:** January 26, 2025\n", "   - **Type:** Image\n", "   - **Likes:** 710,056\n", "   - **Caption:** *(No caption provided)*\n", "   - ![Image](https://scontent.fnic4-1.fna.fbcdn.net/v/t51.2885-15/475130963_18520428076048669_6541680967500843245_n.jpg)\n", "   - [View Post](https://www.instagram.com/p/DFTRo7Ty81p/)\n", "\n", "2. **Post Date:** January 25, 2025\n", "   - **Type:** Video\n", "   - **Likes:** 470,979\n", "   - **Caption:** *(No caption provided)*\n", "   - **Video Duration:** 110s\n", "   - [Watch Video](https://www.instagram.com/p/DFOXUSShtrj/)\n", "\n", "3. **Post Date:** January 24, 2025\n", "   - **Type:** Video\n", "   - **Likes:** 178,841\n", "   - **Caption:** \"THE MOST HISTORIC 100 HOURS IN AMERICAN HISTORY—THE GOLDEN AGE OF AMERICA BEGINS RIGHT NOW!\"\n", "   - **Video Duration:** 110.11s\n", "   - [Watch Video](https://www.instagram.com/p/DFQTIVkPuSc/)\n", "\n", "4. **Post Date:** January 23, 2025\n", "   - **Type:** Video\n", "   - **Likes:** 577,221\n", "   - [Watch Video](https://www.instagram.com/p/DFLrrTthoeY/)\n", "  \n", "These posts reflect his ongoing engagement with followers and themes of leadership and patriotism. If you'd like to see more specific posts or details about any of these, let me know!\n"]}]}]}