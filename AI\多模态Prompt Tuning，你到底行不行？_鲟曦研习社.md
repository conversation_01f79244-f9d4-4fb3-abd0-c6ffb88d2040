---
Updated: 2023-03-02T22:23
tags:
  - AI->-Theory
Created: 2023-03-01T21:01
---
最近两个月，[@避暑山庄梁朝伟](https://www.zhihu.com/people/6af28fdeed05552be99e0c57245085e8) 带着我和若干同学做的一次关于prompt tuning的尝试，将prompt tuning应用到生成式多模态预训练模型当中，具体就是实现在此前我们提出的OFA上。昨天刚正式公开了paper和github代码，欢迎查看了解更多细节代码这版本还比较糙，就先维护在prompt tuning的独立分支上。
[[2208.02532] Prompt Tuning for Generative Multimodal Pretrained Models (arxiv.org)](https://arxiv.org/abs/2208.02532)
[Prompt Tuning for Generative Multimodal Pretrained Models](https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2208.02532)
[[2208.02532] Prompt Tuning for Generative Multimodal Pretrained Models (arxiv.org)](https://arxiv.org/abs/2208.02532)
[GitHub - OFA-Sys/OFA at feature/prompt_tuning](https://github.com/OFA-Sys/OFA/tree/feature/prompt_tuning)
如果你只想了解结果，那我一句话总结：“和NLP类似，多模态prompt tuning是可以做到和finetuning相提并论的效果”。想了解更多细节或者我的言外之意，不妨下拉继续看看。
## 为什么做多模态Prompt Tuning
我们做了OFA之后，最初是想着OFA已经开始做了一些manual prompt的工作，接下来能不能围绕prompt这条路线继续做些研究。但是在这个高大上的问题上其实没讨论明白，我们决定先从为OFA接入prompt tuning的能力做起，于是有了今天的文章和repo更新。
这里谈的prompt tuning，主要关注的是soft prompt tuning，类似Adapter[1]的轻量化调优方法，并不涉及manual prompt和in-context learning的讨论。之前的prompt tuning绝大多数都是在NLP预训练模型上开展的，如大家耳熟能详的清华的P-Tuning[2]及P-Tuning v2[3]，斯坦福的Prefix Tuning[4]，谷歌的Prompt tuning[5]，同样来自清华的PPT[6]，复旦的Black-box Tuning及v2[7, 8]，等等。CV上其实也已经有了VPT[9]。多模态领域其实也都开始做prompt tuning了，可以看梁朝伟之前写的文章。
[NLP领域最近比较火的prompt，能否借鉴到多模态领域？一文跟进最新进展 - 知乎 (zhihu.com)](https://zhuanlan.zhihu.com/p/477078586)
不难发现，当前多模态prompt tuning主要集中在对比学习类的预训练模型上[10, 11, 12]，生成式模型的我们之前没有看到。这里对比学习式其实就是指CLIP[13]，生成式指的就是之前BERT预训练类型的UNITER[14]、VilBERT[15]系列，以及近期比较火热的Encoder-decoder系列，如SimVLM[16]，BLIP[17]，OFA[18]等。我们率先吃个螃蟹，以OFA为例在生成式多模态预训练模型上实现Prompt Tuning。
## 生成式多模态模型的Prompt Tuning
具体的实现方式非常简单，模型图其实就已经有比较清晰的呈现了。在这里，我们选择的方法是Prefix Tuning，也是P Tuning v2。我们之前就对比过谷歌的Prompt Tuning和P Tuning v2，稳定性和效果都存在非常大的差异，这个事情很多文章也都提过。并且，因为我们没有用大模型，P Tuning v2会是一个更加合理的选项。相比之下，不同之处在于我们不存在分类头的参数调优，因为OFA本质上是一个把多类任务统一到生成模式的模型，所以我们需要调优的参数就只有加在每个Transformer层的Prompt embedding。
实现prompt embedding需要考虑的细节问题有很多，我们抓了其中几个做了些简单的分析。一是prompt embedding的生成方式，最简单的方式是建立一个sparse embedding matrix从中按position id取值，但还可以利用MLP layer来生成。二是prompt的长度，这算是比较经典的prompt做分析的问题了。三是prompt的深度，其实我们就主要想看看是不是每层都要加prompt。
实验的结果说实话，属于完全符合预期也没有超出预期。我们主要用了base和large两个size的模型，大概参数量是180M和470M。实验主要做在之前我们常做的多模态理解和多模态生成任务，包括RefCOCO系列的物体定位任务，MSCOCO Image Captioning，还有经典VQA。这一批实验做下来，大致结论就是base模型上，很明显prompt tuning相比finetuning差距还是真的不小的，一般下来都有4-5个点，但是large模型的话，情况就不太一样，除了VQA基本都能做到和finetuning大差不差。除此之外，还做了和bitfit和adapter的对比。这俩我们是尽可能调优了，但相比prompt tuning还是稍微差点。
那除了效果其实还想看看有没啥有意思的点可以再挖挖，就做了下对抗攻击的实验，从实验结果看这方面prompt tuning确实是有优势的。这个比较符合我的直觉，毕竟大规模预训练得到的参数都被freeze了，相对来说应该更robust一点。其它的讨论就比较empirical了，跟NLP的有点不同。总体趋势呢还是prompt的长度越长越好，但我们做这几个任务其实发现太长了其实非但不涨了可能还往下掉点，也没啥很solid的讨论，只能盲目经验式推荐64了，虽然其它文章的讨论也都有些类似问题。至于prompt放在哪最好，放多少层，我们做了个简化版本分析，结果就是“全放 > 放encoder > 放decoder”，从放prompt的角度来说，放底层还是优先级更高一些。
但是问题来了，按照正常的逻辑，如果不太掉点，那干脆把finetuning全面替换成prompt tuning不就好了，这些年也挺多人在鼓吹这事。但事实上，NLP领域也不见得大家都去做这个替换。我们额外讨论了一个其实不太有利的点，就是训练效率的问题。通常情况下，我们会去比较单步的性能，算算tokens/s之类的指标，那会发现prompt tuning其实还真是比finetuning快的。但问题是它的walltime是怎么样的，至少我们之前没怎么看过别人谈这事。这事呢，看任务，因为我们模型本身做了统一，其实这些下游任务形式很多都见过，比如Captions和VQA，预训练本身就有比较多这类型数据，这时候prompt tuning就存在一些性能优势，但如果说见得相对少的或者压根没见过的，那prompt tuning其实是要比finetuning慢不少的。另一个问题是训练稳定性的问题，这事我们之前做NLP实验很常见，超参数恨不得上grid search，多模态相对好点，至少这几个实验看用一个稳定且值比较大的学习率能取得不错的效果。有兴趣的可以看我们的附录。
## 写在最后
其实想说，这玩意儿看起来其实还是有前途的，我甚至觉得我们这么做在多模态上比NLP那些玩法更有前途。但问题是，性价比这个根本问题还是得解决，如果不能两全其美，那就要么明显快不太掉点要么明显更好。另外一个就是在此基础上发挥它省memory的优势实现落地应用。一方面它在训练阶段确实省memory，毕竟需要optimize的部分参数确实少，另一方面是它的存储也变得很便捷。假设一个端云协同的场景，用户侧存储的只有各自的prompt，而云上存储共享的大模型，听起来就很美好。但具体怎么实现，作用在什么场景，还会衍生哪些问题，都得通过踩坑才能想明白。
## 相关文献
1. Houlsby, N., Giurgiu, A., Jastrzebski, S., Morrone, B., De Laroussilhe, Q., Gesmundo, A., ... & Gelly, S. (2019, May). Parameter-efficient transfer learning for NLP. InInternational Conference on Machine Learning(pp. 2790-2799). PMLR.
2. Liu, X., Zheng, Y., Du, Z., Ding, M., Qian, Y., Yang, Z., & Tang, J. (2021). GPT understands, too.arXiv preprint arXiv:2103.10385.
3. Liu, X., Ji, K., Fu, Y., Du, Z., Yang, Z., & Tang, J. (2021). P-tuning v2: Prompt tuning can be comparable to fine-tuning universally across scales and tasks.arXiv preprint arXiv:2110.07602.
4. Li, X. L., & Liang, P. (2021, August). Prefix-Tuning: Optimizing Continuous Prompts for Generation. InProceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing (Volume 1: Long Papers)(pp. 4582-4597).
5. Lester, B., Al-Rfou, R., & Constant, N. (2021, November). The Power of Scale for Parameter-Efficient Prompt Tuning. InProceedings of the 2021 Conference on Empirical Methods in Natural Language Processing(pp. 3045-3059).
6. Gu, Y., Han, X., Liu, Z., & Huang, M. (2022, May). PPT: Pre-trained Prompt Tuning for Few-shot Learning. InProceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)(pp. 8410-8423).
7. Sun, T., Shao, Y., Qian, H., Huang, X., & Qiu, X. (2022). Black-box tuning for language-model-as-a-service.arXiv preprint arXiv:2201.03514.
8. Sun, T., He, Z., Qian, H., Huang, X., & Qiu, X. (2022). BBTv2: Pure Black-Box Optimization Can Be Comparable to Gradient Descent for Few-Shot Learning.arXiv preprint arXiv:2205.11200.
9. Jia, M., Tang, L., Chen, B. C., Cardie, C., Belongie, S., Hariharan, B., & Lim, S. N. (2022). Visual prompt tuning.arXiv preprint arXiv:2203.12119.
10. Zhou, K., Yang, J., Loy, C. C., & Liu, Z. (2022). Learning to prompt for vision-language models.International Journal of Computer Vision, 1-12.
11. Zhou, K., Yang, J., Loy, C. C., & Liu, Z. (2022). Conditional prompt learning for vision-language models. InProceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition(pp. 16816-16825).
12. Rao, Y., Zhao, W., Chen, G., Tang, Y., Zhu, Z., Huang, G., ... & Lu, J. (2022). Denseclip: Language-guided dense prediction with context-aware prompting. InProceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition(pp. 18082-18091).
13. Radford, A., Kim, J. W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., ... & Sutskever, I. (2021, July). Learning transferable visual models from natural language supervision. InInternational Conference on Machine Learning(pp. 8748-8763). PMLR.
14. Chen, Y. C., Li, L., Yu, L., El Kholy, A., Ahmed, F., Gan, Z., ... & Liu, J. (2020, August). Uniter: Universal image-text representation learning. In European conference on computer vision (pp. 104-120). Springer, Cham.
15. Lu, J., Batra, D., Parikh, D., & Lee, S. (2019). Vilbert: Pretraining task-agnostic visiolinguistic representations for vision-and-language tasks. Advances in neural information processing systems, 32.
16. Wang, Z., Yu, J., Yu, A. W., Dai, Z., Tsvetkov, Y., & Cao, Y. (2021, September). SimVLM: Simple Visual Language Model Pretraining with Weak Supervision. InInternational Conference on Learning Representations.
17. Li, J., Li, D., Xiong, C., & Hoi, S. (2022). Blip: Bootstrapping language-image pre-training for unified vision-language understanding and generation.arXiv preprint arXiv:2201.12086.
18. Wang, P., Yang, A., Men, R., Lin, J., Bai, S., Li, Z., ... & Yang, H. (2022). Unifying architectures, tasks, and modalities through a simple sequence-to-sequence learning framework.arXiv preprint arXiv:2202.03052.
免责声明：作者保留权利，不代表本站立场。如想了解更多和作者有关的信息可以查看页面右侧作者信息卡片。