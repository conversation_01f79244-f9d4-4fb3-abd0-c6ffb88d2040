---
DocFlag:
  - Reference
  - Tested
Updated: 2023-04-14T21:21
tags:
  - AI->-Chatbot
  - AI->-Fine-Tuning
  - AI->-Model
  - AI->-Programming
Created: 2023-03-14T13:10
---
一个名为 EleutherAI 的开源 AI 研究团队也一直在贡献开源大模型成果。自 2020 年 7 月成立以来，该团队先后开源了基于 GPT-3 的、包含 60 亿参数的 NLP 模型 GPT-J，类 GPT 的 [27 亿参数模型 GPT-Neo](http://mp.weixin.qq.com/s?__biz=MzA3MzI4MjgzMw==&mid=2650811446&idx=3&sn=8fd83872891490aa4509f43807963fe7&chksm=84e5e848b392615ead5f87678a8214e883884a8b2f0310eafb40548535b760d96420871c243d&scene=21#wechat_redirect)，以及 200 亿参数的 [GPT-NeoX-20B](http://mp.weixin.qq.com/s?__biz=MzA3MzI4MjgzMw==&mid=2650837507&idx=2&sn=aa408f4bb5013bb5f54004b61c4ceb13&chksm=84e5567db392df6b50c7faad1150968214fc73e7595e9c6ecf33f9676b1c144e5e00b9a5384c&scene=21#wechat_redirect)。据悉，他们的最终目标是将开源模型的参数规模扩展到 1700 亿左右，就像 GPT-3 一样。
在 ChatGPT 火遍全球之际，EleutherAI 的这些开源成果派上了用场。一家名为 Together 的组织表示，他们基于 EleutherAI 开源的 GPT-Neo 和 GPT-J 构建了一个新的类 ChatGPT 开源项目 ——OpenChatKit。
OpenChatKit 是一个类 ChatGPT 开源工具包，内含一个 20B 参数量的大模型，而且该模型在 4300 万条指令上进行了微调。
![[176fb3f2274af44d8f6b2c827c0b3dce.png]]
项目链接：https://github.com/togethercomputer/OpenChatKit
这个项目提供了一个强大的、开源的基础，可以为各种应用创建专门和通用的聊天机器人。具体来说，它包含：
- 一个参数量达 20B 的开源模型，该模型基于 EleutherAI 的 GPT-NeoX-20B，在 4300 万条指令上进行了微调；
    
- 一个参数量达 60 亿的审核模型（moderation model），可以帮模型过滤不当内容；
    
- 一个可扩展的检索系统，可以帮模型检索维基百科等资源库，从而给出最新信息。
    
根据 Together 提供的链接，我们可以粗略看到 OpenChatKit 在摘要提取、问答、写作等方面的表现。以下是 Together 提供的一些 demo：
![[6e762f6eaa9215fc43d5226f30d084b1.png]]
![[9b55a379a585f055620bcceeb92644c1.png]]
想要深入了解 OpenChatKit 的读者也可以亲自测试。
测试链接：[https://huggingface.co/spaces/togethercomputer/OpenChatKit](https://huggingface.co/spaces/togethercomputer/OpenChatKit%C2%A0)
从目前的表现来看，OpenChatKit 可能还不太令人满意。但在此开源项目的基础上，社区有望构建出更加强大的聊天机器人应用。
**参数量 20B 的指令调优大模型**
GPT-NeoXT-Chat-Base-20B 是构成 OpenChatKit 基础的大型语言模型。它基于 EleutherAI 的 GPT-NeoX 模型，并通过专注于对话互动的数据进行了微调。Together 在 Huggingface 上发布了这个模型的预训练权重：[https://huggingface.co/togethercomputer/GPT-NeoXT-Chat-Base-20B](https://huggingface.co/togethercomputer/GPT-NeoXT-Chat-Base-20B%C2%A0)
Together 的开发者将模型调整的重点放在几个任务上，如多轮对话、问答、分类、提取和总结。他们用 4300 万条高质量指令对模型进行了微调，并与 LAION 和 Ontocord 合作，创建了该模型所基于的 OIG-43M 数据集。数据集信息参见：[https://laion.ai/blog/oig-dataset/](https://laion.ai/blog/oig-dataset/)
![[b458f99ae8d0dda2bba642b856e365be.png]]
_OIG-43M 数据集示例。_
开箱即用的 GPT-NeoXT-Chat-Base-20B 为一系列广泛的自然语言任务提供了一个强大的基础。从质量上看，它在 HELM 基准上的得分比其基础模型 GPT-NeoX 高，特别是在涉及问答、提取和分类的任务上。
![[4ba126de4ee93a57da650350a7e84d6f.png]]
在 HELM 上评估 GPT-NeoXT-Chat-Base-20B 并与 GPT-NeoX 进行比较。†表示测试包含微调语料库中的数据。
**模型长处**
OpenChatKit 有几个任务是开箱即用的，包括：
1、将一份长的文件总结成一句话，并回答与该文件相关的问题，问答可进行多轮；
2、从非结构化文件中提取结构化信息，如下图所示；
3、将一个句子或段落分为不同的类别（比如情绪是积极还是消极）。
_利用 OpenChatKit 把一段长文字内容转化成图表。_
![[fd6f8da35b867c81d4dcc7df7ecc279c.png]]
**模型短板**
OpenChatKit 目前的短板包括：
- 基于知识的封闭式问答。该聊天机器人可能会给出不正确的结果，需要用户提供反馈；
    
- 代码相关任务。由于训练数据中没有足够多的代码，因此该聊天机器人在代码方面表现欠佳；
    
- 重复性。该聊天机器人有时会重复自己的回答，用户可以点击刷新，开始新的对话；
    
- 上下文切换。该聊天机器人不太擅长转换话题。
    
- 创意写作和较长的答案。该聊天机器人不会生成长的、有创意的文本，如论文或故事。
    
**针对特定任务定制聊天机器人**
在一般问答任务中，大型语言模型已经显示出令人印象深刻的能力。当为特定的应用进行微调时，它们往往能达到更高的准确率。例如，谷歌的 PaLM 在医学回答上达到了大约 50% 的准确率，但是通过添加指令支持和对医学特定信息的微调，谷歌创造了 Med-PaLM，其准确率达到了 92.6%。同样的方法也可以用于其他任务。
OpenChatKit 提供了一些工具来为专门的应用微调聊天机器人。其开发团队正在与研究小组和公司合作，帮助他们为各种任务创建自定义模型。这些任务包括：
- 教育助手：在开放的教科书数据集上进行微调，创建一个聊天机器人，通过自然对话帮助各年龄段的学生了解各种主题；
    
- 金融问答：微调并利用美国证券交易委员会文件等金融数据的检索，实现金融领域的问答；
    
- 客户支持代理：利用知识库数据进行微调，创建聊天机器人，帮助终端用户分辨问题并快速找到答案。
    
**如何进行微调**
微调需要的操作包括
- 准备好你的数据集，使用指定格式的交互示例；
    
- 将你的数据集保存为 jsonl 文件，并按照 OpenChatKit 的 GitHub 文档对聊天模型进行微调；
    
- 不要忘记审核模型！在开始使用你的微调模型之前，请注意审核模型可能需要过滤的域外问题。如果有必要，准备一些调节数据并微调审核模型。
    
这个过程的文档和源代码可以在 OpenChatKit 的 GitHub 链接中找到。由于 OpenChatKit 在 Apache-2.0 许可下完全开源，你可以为自己的应用或研究深入调整、修改或检查权重。
**用于实时更新答案的可扩展检索系统**
OpenChatKit 还包括一个可扩展的检索系统。有了这个检索系统，聊天机器人能够将定期更新的内容或自定义的内容，如来自维基百科的知识、新闻提要或体育比赛成绩纳入回答中。
![[b67d7b33950b9b1fddbe3205b958928c.png]]
检索增强系统的工作流程示例。
**审核模型在必要时进行干预**
OpenChatKit 的最后一个组件是一个由 GPT-JT 微调的 60 亿个参数的审核模型。在聊天应用中，审核模型与主聊天模型同步运行，检查用户话语中是否有任何不适当的内容。基于审核模型的评估，聊天机器人可以将输入的内容限制在经过审核的主题上。当然，这个审核模型只是一个基线，用户可以根据不同的需求进行调整和定制。
在推理过程中，开发者进行了 few-shot 分类，将用户问题分为五类。聊天机器人只在问题落入允许的分类中时才会做出回应。
![[843d02f51929d874911657dc860b03ac.png]]
---
---
  
Setup
[https://blog.csdn.net/eloudy/article/details/129518728](https://blog.csdn.net/eloudy/article/details/129518728)
https://github.com/togethercomputer/OpenChatKit
[https://github.dev/togethercomputer/OpenChatKit](https://github.dev/togethercomputer/OpenChatKit)
  
[https://huggingface.co/togethercomputer/GPT-NeoXT-Chat-Base-20B](https://huggingface.co/togethercomputer/GPT-NeoXT-Chat-Base-20B)
\#How to handle big model
[https://huggingface.co/docs/accelerate/v0.11.0/en/big_modeling](https://huggingface.co/docs/accelerate/v0.11.0/en/big_modeling)
https://github.com/huggingface/transformers/issues/20361
[https://github.com/oobabooga/text-generation-webui/issues/147#issuecomment-1456040134](https://github.com/oobabooga/text-generation-webui/issues/147#issuecomment-1456040134)
conda usage
[https://blog.mistgpu.com/2021/02/03/Conda虚拟环境/](https://blog.mistgpu.com/2021/02/03/Conda%E8%99%9A%E6%8B%9F%E7%8E%AF%E5%A2%83/)
```Go
\#Environment Setup
download https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
and run it
If you'd prefer that conda's base environment not be activated on startup,
   set the auto_activate_base parameter to false:
conda config --set auto_activate_base false
conda update conda
\#to fix solve environment freeze issue. 使用新的solver而不是它自带的
Spin-off on https://github.com/conda/conda/issues/11919
Instead of waiting (maybe hours) to resolve SAT (A well-known NP-Complete problem) environment, it would be helpful for you to install the faster Conda resolver (https://www.anaconda.com/blog/a-faster-conda-for-a-growing-community). Just so you know, the resolver is not installed by default with Anaconda, so you need to install it manually.
conda update -n base conda
conda install -n base conda-libmamba-solver
conda config --set solver libmamba
conda install pytorch torchvision torchaudio pytorch-cuda=11.7 -c pytorch -c nvidia
\#conda config --set channel_priority strict
conda config --set channel_priority flexible  <= default one
conda env create -f environment.yml
#
# To activate this environment, use
#
#     $ conda activate OpenChatKit
#
# To deactivate an active environment, use
#
#     $ conda deactivate

\#install git lfs
https://qiita.com/cokemaniaIIDX/items/5a9643b39ddd7a906875
curl -s https://packagecloud.io/install/repositories/github/git-lfs/script.rpm.sh | sudo bash
dnf install git-lfs
git lfs --version
git lfs install
\#to confirm GPU cuda library works
(ChatGLM) [raysheng@MONSTER:~]$ cat test.py
import torch
print(torch.cuda.is_available())
(ChatGLM) [raysheng@MONSTER:~]$ python test.py
True
\#if false, then install below again
pip install -U torch torchvision
#下载PreTraining的数据
Pretrained weight
https://huggingface.co/togethercomputer/GPT-NeoXT-Chat-Base-20B
vi test.py
from transformers import pipeline
pipe = pipeline(model='togethercomputer/GPT-NeoXT-Chat-Base-20B')
pipe('''<human>: Hello!\n<bot>:''')
python test.py 
will automaticall download pretrained data and test 

Or run
from transformers import AutoTokenizer, AutoModelForCausalLM
tokenizer = AutoTokenizer.from_pretrained("togethercomputer/GPT-NeoXT-Chat-Base-20B")
model = AutoModelForCausalLM.from_pretrained("togethercomputer/GPT-NeoXT-Chat-Base-20B")
python pretrained/GPT-NeoX-20B/prepare.py
\#above memory is not enought, then directly download from hf
cd /opt/workspace/openchat/OpenChatKit
git clone https://huggingface.co/togethercomputer/GPT-NeoXT-Chat-Base-20B
(OpenChatKit) [raysheng@MONSTER:/opt/workspace/openchat/OpenChatKit]$ mkdir huggingface_models && python tools/convert_to_hf_gptneox.py \
     --ckpt-path model_ckpts/GPT-Neo-XT-Chat-Base-20B/checkpoint_5 \
     --save-path /huggingface_models/GPT-NeoXT-Chat-Base-20B \
     --n-stages 8 \
     --n-layer-per-stage 6
cd /opt/workspace/openchat/OpenChatKit/pretrained/GPT-NeoX-20B/EleutherAI_gpt-neox-20b
mkdir old
mv *.* old
mv ../../../GPT-NeoXT-Chat-Base-20B/* .
\#Before you can use this model to perform inference, it must be converted to the Hugginface format.
mkdir huggingface_models && python tools/convert_to_hf_gptneox.py \
     --ckpt-path model_ckpts/GPT-Neo-XT-Chat-Base-20B/checkpoint_5 \
     --save-path huggingface_models/GPT-NeoXT-Chat-Base-20B \
     --n-stages 8 \
     --n-layer-per-stage 6
\#inference
\#to use ImportError: Using `low_cpu_mem_usage=True` or a `device_map` requires Accelerate: `pip install accelerate
pip install accelerate
\#modify program key part
from transformers import AutoTokenizer, AutoModelForCausalLM
class ChatModel:
    human_id = "<human>"
    bot_id = "<bot>"
def __init__(self, model_name, gpu_id):
        device = torch.device('cuda', gpu_id)
        \#self.config = AutoConfig.from_pretrained(model_name)
        \#with init_empty_weights():
        #    self.model = AutoModelForCausalLM.from_config(AutoConfig.from_pretrained(model_name))
        \#self._model = AutoModelForCausalLM.from_pretrained(model_name, use_auth_token=True, low_cpu_mem_usage=True, device_map="auto", offload_folder="./offload").half().quantize(4)
        self._model = AutoModelForCausalLM.from_pretrained(model_name, use_auth_token=True, low_cpu_mem_usage=True, device_map="auto", offload_folder="./offload", torch_dtype=torch.float16).half()
        \#self._model = AutoModelForCausalLM.from_pretrained(model_name, use_auth_token=True, torch_dtype=torch.float16, load_in_8bit=True,  device_map="auto").half()
        \#self._model = self._model.eval()
        \#self._model.to(device)
        self._tokenizer = AutoTokenizer.from_pretrained(model_name,use_auth_token=True, use_fast=True)
        num_layers_to_keep = 24
def do_inference(self, prompt, max_new_tokens, do_sample, temperature, top_k):
        inputs = (
            self._tokenizer(prompt, padding=False, return_tensors='pt')
            .to(self._model.device)
        )
        with torch.no_grad():
            outputs = self._model.generate(
                  **inputs,
                  max_new_tokens=max_new_tokens,
                  do_sample=do_sample,
                  temperature=temperature,
                  top_k=top_k,
                  pad_token_id=self._tokenizer.eos_token_id
           )
        output = self._tokenizer.batch_decode(outputs)[0]
        # remove the context from the output
        output = output[len(prompt):]
        return output
--------------------
使用AutoModelForCausalLM.from_pretrained()方法加载预训练的因果语言模型。
这里使用了model_name参数来指定模型名称，同时设置了use_auth_token=True（表示需要验证访问的权限），
device_map="auto"（表示自动将模型分布到可用的设备上），
offload_folder="./offload"（表示将模型权重存储在名为offload的文件夹中）。
使用.half()和.quantize(4)方法对模型进行优化，
分别表示将模型权重转换为半精度浮点数（float16）和量化（将权重表示为4位整数），以减小模型大小和提高运行速度。
使用eval()方法将模型设置为评估模式，这意味着模型在推理过程中不会更新权重。
----------------------
#通过调参，减少内存使用
1.decreae max-tokens 
'--max-tokens',
        default=100,
2. increase temperature. able to decrease finding best result
'--temperature',
        default=1,
3. Maybe haven't tried. decrease top-k
'--top-k',
        default=40,
python inference/bot.py --model ./huggingface_models/GPT-NeoXT-Chat-Base-20B


DataSet 
cd /opt/workspace/openchat/OpenChatKit
python data/OIG/prepare.py
python pretrained/GPT-NeoX-20B/prepare.py
```
  
  
  
```Go
当您使用 transformers 库运行程序时，它会自动下载 GPT-NeoXT-Chat-20B 模型（如果您的系统中尚未存在）。模型和相关文件通常存储在您的系统的缓存目录中。对于大多数系统，该缓存目录的位置如下：
Linux: ~/.cache/huggingface/transformers/
macOS: ~/Library/Caches/huggingface/transformers/
Windows: %userprofile%\.cache\huggingface\transformers\
在这个目录下，您会找到一个包含模型文件、配置文件和词汇表文件等的文件夹。文件夹名称通常是模型名称（如 togethercomputer/GPT-NeoXT-Chat-Base-20B）的哈希值。如果您想要更改缓存目录的位置，可以设置环境变量 HF_HOME。
例如，在 Linux 或 macOS 上执行以下命令可以更改缓存目录的位置：
bash
Copy code
export HF_HOME=/path/to/your/desired/directory
在 Windows 上，您可以使用以下命令：
bash
Copy code
set HF_HOME=C:\path\to\your\desired\directory
将上述命令中的 /path/to/your/desired/directory 或 C:\path\to\your\desired\directory 替换为您希望使用的目录。之后，transformers 库会将缓存文件存储在您指定的目录中。
```
  
```Go
\#fix OOM-kill when download files.
(base) [root@MONSTER:/swap]# sudo vi /etc/sysctl.conf
(base) [root@MONSTER:/swap]# conda activate OpenChatKi^C
(base) [root@MONSTER:/swap]# sysctl -p
vm.overcommit_memory = 2
vm.swappiness = 20
\#create one 32G swapfile 
swapon /swap/swapfile
```
```Go
Tried to run on winodws , Not Working
# when install environment.yml with conda
got error nccl not able to runing in windows
```
```Go
return t.to(device, dtype if t.is_floating_point() or t.is_complex() else None, non_blocking)
NotImplementedError: Cannot copy out of meta tensor; no data!
\#guess . because i select devive_map=auto. again put to to(device) will have problem. so comment out them.
self._model = AutoModelForCausalLM.from_pretrained(model_name, use_auth_token=True, low_cpu_mem_usage=True, device_map="auto", offload_folder="./offload", torch_dtype=torch.float16).half()
        \#self._model = self._model.eval()
        \#self._model.to(device)
mapy be can try
1. load_in_8bit=True
https://github.com/oobabooga/text-generation-webui/issues/147\#issuecomment-1456040134
\#ImportError: Using `load_in_8bit=True` requires Accelerate: `pip install accelerate` and the latest version of bitsandbytes `pip install -i https://test.pypi.org/simple/ bitsandbytes` or pip install bitsandbytes`
# compile manually and build
git clone https://github.com/TimDettmers/bitsandbytes.git
export USE_CUDA=1
conda list | grep cudatoolkit
nvcc --version
nvidia-sm
cd /usr/sbin
sudo ln -s /home/<USER>/miniconda3/envs/OpenChatKit/bin/nvcc
cd /bin
sudo ln -s /home/<USER>/miniconda3/envs/OpenChatKit/bin/nvcc
cd /opt/workspace/bitsandbytes
make CUDA_VERSION=DETECTED_CUDA_VERSION
export CUDA_VERSION=116 CUDA_HOME=~/miniconda3/pkgs/cuda-nvcc-11.6.124-hbba6d2d_0 LD_LIBRARY_PATH=~/miniconda3/pkgs/cuda-nvcc-11.6.124-hbba6d2d_0/lib
CUDA_VERSION=116 make cuda11x_nomatmul
python setup.py install
# install egg file
cd /opt/workspace/bitsandbytes
pip install .
cd ~/miniconda3/envs/OpenChatKit/lib/python3.10/site-packages/bitsandbytes-0.37.1-py3.10.egg/bitsandbytes/cuda_setup
(OpenChatKit) [raysheng@MONSTER:~/miniconda3/envs/OpenChatKit/lib/python3.10/site-packages/bitsandbytes-0.37.1-py3.10.egg/bitsandbytes/cuda_setup]$ git diff main.py.orig main.py
diff --git a/main.py.orig b/main.py
index ffa80ba..295b57c 100644
--- a/main.py.orig
+++ b/main.py
@@ -119,10 +119,10 @@ class CUDASetup:
                     self.generate_instructions()
                     self.print_log_stack()
                     raise Exception('CUDA SETUP: Setup Failed!')
-                self.lib = ct.cdll.LoadLibrary(binary_path)
+                self.lib = ct.cdll.LoadLibrary(str(binary_path))
             else:
                 self.add_log_entry(f"CUDA SETUP: Loading binary {binary_path}...")
-                self.lib = ct.cdll.LoadLibrary(binary_path)
+                self.lib = ct.cdll.LoadLibrary(str(binary_path))
         except Exception as ex:
             self.add_log_entry(str(ex))
             self.print_log_stack()
@@ -365,7 +365,7 @@ def evaluate_cuda_setup():
         print('='*35 + 'BUG REPORT' + '='*35)
         print('Welcome to bitsandbytes. For bug reports, please submit your error trace to: https://github.com/TimDettmers/bitsandbytes/issues')
         print('='*80)
-    if not torch.cuda.is_available(): return 'libsbitsandbytes_cpu.so', None, None, None, None
+    if torch.cuda.is_available(): return 'libbitsandbytes_cuda116.so', None, None, None, None
     cuda_setup = CUDASetup.get_instance()
     cudart_path = determine_cuda_runtime_lib_path()
\#get below alerts
ValueError:
                        Some modules are dispatched on the CPU or the disk. Make sure you have enough GPU RAM to fit
                        the quantized model. If you want to dispatch the model on the CPU or the disk while keeping
                        these modules in 32-bit, you need to set `load_in_8bit_fp32_cpu_offload=True` and pass a custom
                        `device_map` to `from_pretrained`. Check
                        https://huggingface.co/docs/transformers/main/en/main_classes/quantization\#offload-between-cpu-and-gpu
                        for more details.
Looks I have to dispath it like https://github.com/huggingface/transformers/issues/20361
the reason is even i use INT8 but some of module in CPU and some of in GPU. bitsandbytes not working!
from transformers import AutoModel
custom_device_map = {
    "layer.0": "cpu",
    "layer.1": "cpu",
    # Add more layers to offload to CPU as needed
}
model = AutoModel.from_pretrained(
    "path/to/your/model",
    load_in_8bit_fp32_cpu_offload=True,
    device_map=custom_device_map,
)

2. device map dispatch 
https://huggingface.co/docs/accelerate/v0.11.0/en/big_modeling
https://github.com/huggingface/transformers/issues/20361
3. decrease layer
from transformers import GPT2LMHeadModel
# 加载预训练模型
model = GPT2LMHeadModel.from_pretrained("gpt2")
# 设定保留的层数
num_layers_to_keep = 6
# 获取模型的配置
config = model.config
# 修改配置中的层数
config.n_layer = num_layers_to_keep
# 保留指定数量的Transformer层
model.transformer.h = model.transformer.h[:num_layers_to_keep]
# 使用修改后的配置更新模型的结构
model.config = config
```
  
Completed this test
  
![[Notion/AI/🦜🦜🦜ChatGPT开源平替OpenChatKit：参数量200亿，在4300万条指令上微调而成 - 智源社区/attachments/Untitled.png|Untitled.png]]
![[Notion/AI/🦜🦜🦜ChatGPT开源平替OpenChatKit：参数量200亿，在4300万条指令上微调而成 - 智源社区/attachments/Untitled 1.png|Untitled 1.png]]
![[Notion/AI/🦜🦜🦜ChatGPT开源平替OpenChatKit：参数量200亿，在4300万条指令上微调而成 - 智源社区/attachments/Untitled 2.png|Untitled 2.png]]
```Go
Offload folder 中我们可以看到
除了总的
gpt_neox.final_layer_norm.weight.dat
gpt_neox.final_layer_norm.bias.dat
embed_out.weight.dat
还有
input_layernorm.weight.dat
input_layernorm.bias.dat
post_attention_layernorm.weight.dat
post_attention_layernorm.bias.dat
attention.bias.dat
attention.masked_bias.dat
attention.rotary_emb.inv_freq.dat
attention.query_key_value.weight.dat
attention.query_key_value.bias.dat
attention.dense.weight.dat
attention.dense.bias.dat
mlp.dense_h_to_4h.weight.dat
mlp.dense_h_to_4h.bias.dat
mlp.dense_4h_to_h.weight.dat
mlp.dense_4h_to_h.bias.dat
```
---
```JavaScript
<< ISSUE >>>
ImportError: /lib64/libstdc++.so.6: version `GLIBCXX_3.4.30' not found (required by /home/<USER>/miniconda3/envs/OpenChatKit/lib/python3.10/site-packages/faiss/../../../libfaiss.so)
1. find , if not find. sudo yum install libstdc++-devel
locate libstdc++.so.6
2. strings /path/to/libstdc++.so.6 | grep GLIBCXX
3. sudo ln -s /path/to/libstdc++.so.6 /usr/lib64/libstdc++.so.6
之前已经有了，我保存了一下
lrwxrwxrwx 1 root root         30 Apr  3 23:10  libstdc++.so.6.orig -> /usr/lib64/libstdc++.so.6.0.29
(base) [root@MONSTER:/opt/workspace/ai/GLMtuning/best_ckpt]# rm -f libstdc++.so.6.orig
(base) [root@MONSTER:/opt/workspace/ai/GLMtuning/best_ckpt]# ln -s /usr/lib64/libstdc++.so.6.0.29 /usr/lib64/libstdc++.so.6.orig
(base) [root@MONSTER:/opt/workspace/ai/GLMtuning/best_ckpt]# ls -lrt /usr/lib64/libstdc*
-rwxr-xr-x 1 <USER> <GROUP> 2306864 Oct 18 01:15 /usr/lib64/libstdc++.so.6.0.29
lrwxrwxrwx 1 root root      61 Apr  3 23:08 /usr/lib64/libstdc++.so.6 -> /home/<USER>/miniconda3/envs/OpenChatKit/lib/libstdc++.so.6
lrwxrwxrwx 1 root root      30 Apr  3 23:10 /usr/lib64/libstdc++.so.6.orig -> /usr/lib64/libstdc++.so.6.0.29
```