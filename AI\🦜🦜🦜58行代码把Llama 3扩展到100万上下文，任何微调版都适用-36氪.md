---
DocFlag:
  - Reference
  - Tested
Updated: 2024-05-07T23:01
tags:
  - AI->-Fine-Tuning
  - AI->-Programming
  - AI->-Theory
  - AI->-llama
Created: 2024-05-07T22:39
---
  
# Merge 代码
[[merge_adapters.py]]
  
# 权重下载地方
[crusoeai/Llama-3-70B-Instruct-Gradient-1048k-GGUF · Hugging Face](https://huggingface.co/crusoeai/Llama-3-70B-Instruct-Gradient-1048k-GGUF)
  
# Lora Adapter
[cognitivecomputations/Llama-3-70B-Gradient-1048k-adapter · Hugging Face](https://huggingface.co/cognitivecomputations/Llama-3-70B-Gradient-1048k-adapter)
This adapter can be run with (or merged with) any Llama3-70b based model, to give it a 1048k context.
  
# 对于不同量化权重文件的解释
[[🦜🦜🦜Difference in different quantization methods · ggerganov-llama.cpp · Discussion 2094]]
```Plain
Allowed quantization types:
   2  or  Q4_0   :  3.50G, +0.2499 ppl @ 7B - small, very high quality loss - legacy, prefer using Q3_K_M
   3  or  Q4_1   :  3.90G, +0.1846 ppl @ 7B - small, substantial quality loss - legacy, prefer using Q3_K_L
   8  or  Q5_0   :  4.30G, +0.0796 ppl @ 7B - medium, balanced quality - legacy, prefer using Q4_K_M
   9  or  Q5_1   :  4.70G, +0.0415 ppl @ 7B - medium, low quality loss - legacy, prefer using Q5_K_M
  10  or  Q2_K   :  2.67G, +0.8698 ppl @ 7B - smallest, extreme quality loss - not recommended
  12  or  Q3_K   : alias for Q3_K_M
  11  or  Q3_K_S :  2.75G, +0.5505 ppl @ 7B - very small, very high quality loss
  12  or  Q3_K_M :  3.06G, +0.2437 ppl @ 7B - very small, very high quality loss
  13  or  Q3_K_L :  3.35G, +0.1803 ppl @ 7B - small, substantial quality loss
  15  or  Q4_K   : alias for Q4_K_M
  14  or  Q4_K_S :  3.56G, +0.1149 ppl @ 7B - small, significant quality loss
  15  or  Q4_K_M :  3.80G, +0.0535 ppl @ 7B - medium, balanced quality - *recommended*
  17  or  Q5_K   : alias for Q5_K_M
  16  or  Q5_K_S :  4.33G, +0.0353 ppl @ 7B - large, low quality loss - *recommended*
  17  or  Q5_K_M :  4.45G, +0.0142 ppl @ 7B - large, very low quality loss - *recommended*
  18  or  Q6_K   :  5.15G, +0.0044 ppl @ 7B - very large, extremely low quality loss
   7  or  Q8_0   :  6.70G, +0.0004 ppl @ 7B - very large, extremely low quality loss - not recommended
   1  or  F16    : 13.00G              @ 7B - extremely large, virtually no quality loss - not recommended
   0  or  F32    : 26.00G              @ 7B - absolutely huge, lossless - not recommended
```
  
  
==堂堂开源之王Llama 3，====**原版上下文窗口**====居然只有……====**8k**====，让到嘴边的一句“真香”又咽回去了。====**‍**==
==在32k起步，100k寻常的今天，这是故意要给开源社区留做贡献的空间吗？==
[![](https://img.36krcdn.com/hsossms/20240506/v2_4c149c47186b44f28151b3add56b53cd@000000_oswg11490oswg289oswg174_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_4c149c47186b44f28151b3add56b53cd@000000_oswg11490oswg289oswg174_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==开源社区当然不会放过这个机会：==
==**现在只需58行代码，任何Llama 3 70b的微调版本都能自动扩展到1048k**====（一百万）====**上下文。**==
[![](https://img.36krcdn.com/hsossms/20240506/v2_68254dc3fae94a458ff1c6faf8bcab2a@000000_oswg131081oswg1080oswg512_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_68254dc3fae94a458ff1c6faf8bcab2a@000000_oswg131081oswg1080oswg512_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==背后是一个LoRA，从扩展好上下文的Llama 3 70B Instruct微调版本中提取出来，====**文件只有800mb**====。==
==接下来使用Mergekit，就可以与其他同架构模型一起运行或直接合并到模型中。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_d0a5ea9e83cb4640af706d21f1d87fab@000000_oswg67716oswg914oswg412_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_d0a5ea9e83cb4640af706d21f1d87fab@000000_oswg67716oswg914oswg412_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==所使用的1048k上下文微调版本，刚刚在流行的大海捞针测试中达到全绿（100%准确率）的成绩。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_adb9c7a7f79a4180999324ffe3e94378@000000_oswg345045oswg1080oswg1156_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_adb9c7a7f79a4180999324ffe3e94378@000000_oswg345045oswg1080oswg1156_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==不得不说，开源的进步速度是指数级的。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_fd636e008959473fb6f314a7122e411b@000000_oswg51337oswg834oswg184_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_fd636e008959473fb6f314a7122e411b@000000_oswg51337oswg834oswg184_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
## ==**1048k上下文LoRA怎么炼成的**==
==首先1048k上下文版Llama 3微调模型来自====**Gradient AI**====，一个企业AI解决方案初创公司。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_635788844ac5458a8dfc29fbc9ddc552@000000_oswg172361oswg1080oswg605_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_635788844ac5458a8dfc29fbc9ddc552@000000_oswg172361oswg1080oswg605_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==而对应的LoRA来自开发者====**Eric Hartford**====，通过比较微调模型与原版的差异，提取出参数的变化。==
==他先制作了524k上下文版，随后又更新了1048k版本。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_2fd8138995664cbbb33d6005087019ff@000000_oswg204705oswg1080oswg404_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_2fd8138995664cbbb33d6005087019ff@000000_oswg204705oswg1080oswg404_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==首先，Gradient团队先在原版Llama 3 70B Instruct的基础上继续训练，得到Llama-3-70B-Instruct-Gradient-1048k。==
==具体方法如下：==
==**调整位置编码：**用NTK-aware插值初始化RoPE theta的最佳调度，进行优化，防止扩展长度后丢失高频信息==
==**渐进式训练：**使用UC伯克利Pieter Abbeel团队提出的Blockwise RingAttention方法扩展模型的上下文长度==
==值得注意的是，团队通过自定义网络拓扑在Ring Attention之上分层并行化，更好地利用大型GPU集群来应对设备之间传递许多KV blocks带来的网络瓶颈。==
==最终使模型的训练速度提高了33倍。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_67140eaa3be447169fdc7792be2d9a38@000000_oswg137070oswg994oswg1460_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_67140eaa3be447169fdc7792be2d9a38@000000_oswg137070oswg994oswg1460_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==长文本检索性能评估中，只在最难的版本中，当“针”藏在文本中间部分时容易出错。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_4d95dda07e2b44adb516e433df1c60fa@000000_oswg163085oswg1080oswg390_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_4d95dda07e2b44adb516e433df1c60fa@000000_oswg163085oswg1080oswg390_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
[![](https://img.36krcdn.com/hsossms/20240506/v2_08e03c854dcd4a7eb2f63d2124bb447a@000000_oswg74636oswg1080oswg238_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_08e03c854dcd4a7eb2f63d2124bb447a@000000_oswg74636oswg1080oswg238_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==有了扩展好上下文的微调模型之后，使用开源工具Mergekit比较微调模型和基础模型，提取参数的差异成为LoRA。==
==同样使用Mergekit，就可以把提取好的LoRA合并到其他同架构模型中了。==
==合并代码也由Eric Hartford开源在GitHub上，只有58行。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_983be3be4b7f41ea83dabc87dc89f241@000000_oswg136357oswg1080oswg772_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_983be3be4b7f41ea83dabc87dc89f241@000000_oswg136357oswg1080oswg772_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==目前尚不清楚这种LoRA合并是否适用于在中文上微调的Llama 3。==
==不过可以看到，中文开发者社区已经关注到了这一进展。==
[![](https://img.36krcdn.com/hsossms/20240506/v2_cbc0d5477b2246a99a3bdb3277a4568e@000000_oswg123215oswg1080oswg237_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)](https://img.36krcdn.com/hsossms/20240506/v2_cbc0d5477b2246a99a3bdb3277a4568e@000000_oswg123215oswg1080oswg237_img_000?x-oss-process=image/format,jpg/interlace,1/format,jpg/interlace,1/format,jpg/interlace,1)
==524k版本LoRA：https://huggingface.co/cognitivecomputations/Llama-3-70B-Gradient-524k-adapter==
==1048k版本LoRA：https://huggingface.co/cognitivecomputations/Llama-3-70B-Gradient-1048k-adapter==
==合并代码：https://gist.github.com/ehartford/731e3f7079db234fa1b79a01e09859ac==
==参考链接：[1]https://twitter.com/erhartford/status/1786887884211138784==
==本文来自微信公众号==[==“量子位”（ID：QbitAI）==](http://mp.weixin.qq.com/s?__biz=MzIzNjc1NzUzMw==&mid=2247727774&idx=4&sn=02bfe9f19fbbadec47e4d8c934430ee7&chksm=e9a23edb064571f3568244227be948afb58569eede7d67344690b1b66b952ba64c7f54f09bb8&scene=0&xtrack=1#rd)==，作者：梦晨，36氪经授权发布。==
==该文观点仅代表作者本人，36氪平台仅提供信息存储空间服务。==