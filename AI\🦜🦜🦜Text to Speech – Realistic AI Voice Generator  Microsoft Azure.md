---
DocFlag:
  - Reference
  - Tested
Updated: 2024-04-06T17:40
tags:
  - AI->-Azure
  - AI->-Model
  - AI->-Voice
  - AI->-数字人
URL: https://azure.microsoft.com/en-us/products/ai-services/text-to-speech
Created: 2024-04-05T23:18
---
Bring your apps to life with natural-sounding voices
Lifelike synthesized speech
Customizable text-talker voices
Fine-grained text-to-talk audio controls
Flexible deployment
Tailor your speech output
Deploy Text to Speech anywhere, from the cloud to the edge
Build a custom voice for your brand
Fuel App Innovation with Cloud AI Services
Comprehensive privacy and security
Documentation
Training
Comprehensive security and compliance, built in
Get started with an Azure free account
Guidelines for building responsible synthetic voices
Test in WSL
  
  
  
An AI Speech feature that converts text to lifelike speech.
[Try Text to speech free](https://azure.microsoft.com/en-us/free/cognitive-services/) [Create a pay-as-you-go account](https://azure.microsoft.com/en-us/pricing/purchase-options/pay-as-you-go)
- [Overview](https://azure.microsoft.com/en-us/products/ai-services/text-to-speech#overview)
- [Features](https://azure.microsoft.com/en-us/products/ai-services/text-to-speech#features)
- [Security](https://azure.microsoft.com/en-us/products/ai-services/text-to-speech#security)
- [Pricing](https://azure.microsoft.com/en-us/products/ai-services/text-to-speech#pricing)
- [Get started](https://azure.microsoft.com/en-us/products/ai-services/text-to-speech#get-started)
- [Resources](https://azure.microsoft.com/en-us/products/ai-services/text-to-speech#resources)
## Bring your apps to life with natural-sounding voices
Build apps and services that speak naturally. Differentiate your brand with a customized, realistic voice generator, and access voices with different speaking styles and emotional tones to fit your use case—from text readers and talkers to customer support chatbots.
[Start with $200 Azure credit](https://azure.microsoft.com/en-us/free/)
## Lifelike synthesized speech
Enable fluid, natural-sounding text to speech that matches the intonation and emotion of human voices.
## Customizable text-talker voices
Create a unique AI voice generator that reflects your brand's identity.
## Fine-grained text-to-talk audio controls
Tune voice output for your scenarios by easily adjusting rate, pitch, pronunciation, pauses, and more.
## Flexible deployment
Run Text to Speech anywhere—in the cloud, on-premises, or at the edge in containers.
## Tailor your speech output
Fine-tune synthesized speech audio to fit your scenario. [Define lexicons](https://go.microsoft.com/fwlink/?linkid=2221050) and control speech parameters such as pronunciation, pitch, rate, pauses, and intonation with [Speech Synthesis Markup Language](https://go.microsoft.com/fwlink/?linkid=2221050) (SSML) or with the [audio content creation tool](https://go.microsoft.com/fwlink/?linkid=2220876).
## Deploy Text to Speech anywhere, from the cloud to the edge
Run Text to Speech wherever your data resides. Build lifelike speech synthesis into applications optimized for both robust cloud capabilities and edge locality using [containers](https://go.microsoft.com/fwlink/?linkid=2220961).
## Build a custom voice for your brand
Differentiate your brand with a unique [custom voice](https://go.microsoft.com/fwlink/?linkid=2220374). Develop a highly realistic voice for more natural conversational interfaces using the Custom Neural Voice capability, starting with 30 minutes of audio.
## Fuel App Innovation with Cloud AI Services
Learn five key ways your organization can get started with AI to realize value quickly.
[Read the report](https://azure.microsoft.com/en-us/resources/forrester-study-fuel-application-innovation-with-specialized-cloud-ai-services/?culture=en-us&country=us)
## Comprehensive privacy and security
### Documentation
AI Speech, part of Azure AI Services, is [certified](https://go.microsoft.com/fwlink/?linkid=2220556) by SOC, FedRAMP, PCI DSS, HIPAA, HITECH, and ISO.
View and delete your custom voice data and synthesized speech models at any time. Your data is encrypted while it’s in storage.
### Training
Your data remains yours. Your text data isn't stored during data processing or audio voice generation.
Backed by Azure infrastructure, AI Speech offers enterprise-grade security, availability, compliance, and manageability.
## Comprehensive security and compliance, built in
- Microsoft invests more than **$1 billion annually** on cybersecurity research and development.
    
[![](https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/security_portal-azure-security-center-1?resMode=sharp2&op_usm=1.5,0.65,15,0&wid=1034&qlt=100&fmt=png-alpha&fit=constrain)](https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/security_portal-azure-security-center-1?resMode=sharp2&op_usm=1.5,0.65,15,0&wid=1034&qlt=100&fmt=png-alpha&fit=constrain)
A security center overview in Azure showing policy and compliance data and resource security hygiene
- We employ more than **3,500 security experts** who are dedicated to data security and privacy.
    
[![](https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/security_portal-azure-security-center-2?resMode=sharp2&op_usm=1.5,0.65,15,0&wid=1028&qlt=100&fmt=png-alpha&fit=constrain)](https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/security_portal-azure-security-center-2?resMode=sharp2&op_usm=1.5,0.65,15,0&wid=1028&qlt=100&fmt=png-alpha&fit=constrain)
The security center compute and apps tab in Azure showing a list of recommendations
[Learn more about security on Azure](https://azure.microsoft.com/en-us/explore/security)
- [See pricing](https://azure.microsoft.com/en-us/pricing/details/cognitive-services/speech-services/)
    
    ## Flexible pricing gives you the power and control you need
    
    Pay only for what you use, with no upfront costs. With Text to Speech, you pay as you go based on the number of characters you convert to audio.
    
## Get started with an Azure free account
1
[**Start free**](https://azure.microsoft.com/en-us/free)**.** Get $200 credit to use within 30 days. While you have your credit, get free amounts of many of our most popular services, plus free amounts of 55+ other services that are always free.
2
After your credit, move to **pay as you go** to keep building with the same free services. Pay only if you use more than your free monthly amounts.
3
**After 12 months**, you'll keep getting 55+ always-free services—and still pay only for what you use beyond your free monthly amounts.
## Guidelines for building responsible synthetic voices
Transparency is foundational to responsible use of computer voice generators and synthetic voices. Help ensure that users understand when they’re hearing a synthetic voice and that voice talent is aware of how their voice will be used. Learn more with our disclosure design guidelines.
  
# Test in WSL
```Python
https://github.com/Azure-Samples/cognitive-services-speech-sdk/blob/master/quickstart/python/from-microphone/quickstart.ipynb
https://learn.microsoft.com/en-us/azure/ai-services/speech-service/how-to-recognize-speech?pivots=programming-language-python

Issue => Have to wait for new SDK version or run in docker/downgrade openssl 1.1.1
https://stackoverflow.com/questions/72751026/how-to-fix-microsoft-cognitive-speech-error-failed-to-initialize-platform-azur
https://github.com/Azure-Samples/cognitive-services-speech-sdk/issues?q=azure-c-shared
https://github.com/Azure-Samples/cognitive-services-speech-sdk/issues/2204
\#playground 
https://speech.azure.cn/portal/speechtotexttool
/opt/workspace/researcher/azureTTS.py

'''
  For more samples please visit https://github.com/Azure-Samples/cognitive-services-speech-sdk 
'''
import azure.cognitiveservices.speech as speechsdk
# Creates an instance of a speech config with specified subscription key and service region.
speech_key = "72ea520f35b847d7aefaba59fd46d68a"
service_region = "eastasia"
speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=service_region)
# Note: the voice setting will not overwrite the voice element in input SSML.
speech_config.speech_synthesis_voice_name = "ja-JP-KeitaNeural"
text = "Hi, this is 圭太"
# use the default speaker as audio output.
speech_synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config)
result = speech_synthesizer.speak_text_async(text).get()
# Check result
if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
    print("Speech synthesized for text [{}]".format(text))
elif result.reason == speechsdk.ResultReason.Canceled:
    cancellation_details = result.cancellation_details
    print("Speech synthesis canceled: {}".format(cancellation_details.reason))
    if cancellation_details.reason == speechsdk.CancellationReason.Error:
        print("Error details: {}".format(cancellation_details.error_details))
===============================================================================
<<Temp solution>>
\#1 create dockerfile and build via vcs
FROM mcr.microsoft.com/dotnet/aspnet:8.0
# 安装 Python 和 pip
RUN apt-get update && \
    apt-get install -y python3 python3-pip python3-venv 
# Install prerequisites for Azure Speech Services
# See https://docs.microsoft.com/en-us/azure/cognitive-services/speech-service/quickstarts/setup-platform
RUN apt-get -y install build-essential libssl-dev libasound2 wget
# Next four lines are needed in order to be able to run on .NET 8.0
# until this issue is fixed by Microsoft: https://github.com/Azure-Samples/cognitive-services-speech-sdk/issues/2204
# These lines will install OpenSSL 1.1.1 which is needed by the Speech SDK.
RUN wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
RUN dpkg -i libssl1.1_1.1.1f-1ubuntu2_amd64.deb
RUN wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl-dev_1.1.1f-1ubuntu2_amd64.deb
RUN dpkg -i libssl-dev_1.1.1f-1ubuntu2_amd64.deb
RUN python3 -m venv /appenv
RUN /appenv/bin/pip3 install --upgrade pip
RUN /appenv/bin/pip3 install azure-cognitiveservices-speech


\#2 Run my script
docker run -it -v /opt/workspace/researcher:/app speech-sdk-image
/appenv/bin/python azureTTS.py
But above dokcer instance will exist, us below way to keep it running
docker run -d --name speech-sdk-container -v /opt/workspace/researcher:/app speech-sdk-image
-》上面会退出，必须要在容器里有东西运行
docker run -d --name speech-sdk-container -v /opt/workspace/researcher:/app speech-sdk-image tail -f /dev/null
docker exec speech-sdk-container /appenv/bin/python /app/azureTTS.py
docker start speech-sdk-container
docker stop speech-sdk-container
-- delete this dokcer instance
docker rm speech-sdk-container
```
  
  
```Python
/opt/workspace/researcher/azureTTS.py
import argparse
import azure.cognitiveservices.speech as speechsdk
# 创建解析器并添加参数
parser = argparse.ArgumentParser(description='Azure Text-to-Speech')
parser.add_argument('text', type=str, help='Text to synthesize')
'''
  For more samples please visit https://github.com/Azure-Samples/cognitive-services-speech-sdk 
'''
# 解析参数
args = parser.parse_args()
text = args.text
# Creates an instance of a speech config with specified subscription key and service region.
speech_key = "72ea520f35b847d7aefaba59fd46d68a"
service_region = "eastasia"
speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=service_region)
# Note: the voice setting will not overwrite the voice element in input SSML.
# speech_config.speech_synthesis_voice_name = "ja-JP-KeitaNeural"
speech_config.speech_synthesis_voice_name = "zh-CN-XiaoxiaoNeural"
# Set the output audio format and filename
Filename = "/app/output.wav"
audio_config = speechsdk.audio.AudioOutputConfig(filename=Filename)
# Create a speech synthesizer using the speech config and audio output config
speech_synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config, audio_config=audio_config)
result = speech_synthesizer.speak_text_async(text).get()
# Check result
if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
    print("Speech synthesized for text [{}] and saved to [{}]".format(text, Filename))
elif result.reason == speechsdk.ResultReason.Canceled:
    cancellation_details = result.cancellation_details
    print("Speech synthesis canceled: {}".format(cancellation_details.reason))
    if cancellation_details.reason == speechsdk.CancellationReason.Error:
        print("Error details: {}".format(cancellation_details.error_details))
=======================================================
Speech to Text
https://docs.azure.cn/zh-cn/ai-services/speech-service/how-to-recognize-speech?pivots=programming-language-python
import os
import azure.cognitiveservices.speech as speechsdk
def recognize_from_microphone():
    # This example requires environment variables named "SPEECH_KEY" and "SPEECH_REGION"
    speech_config = speechsdk.SpeechConfig(subscription=os.environ.get('SPEECH_KEY'), region=os.environ.get('SPEECH_REGION'))
    speech_config.speech_recognition_language="en-US"
    audio_config = speechsdk.audio.AudioConfig(use_default_microphone=True)
    speech_recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config, audio_config=audio_config)
    print("Speak into your microphone.")
    speech_recognition_result = speech_recognizer.recognize_once_async().get()
    if speech_recognition_result.reason == speechsdk.ResultReason.RecognizedSpeech:
        print("Recognized: {}".format(speech_recognition_result.text))
    elif speech_recognition_result.reason == speechsdk.ResultReason.NoMatch:
        print("No speech could be recognized: {}".format(speech_recognition_result.no_match_details))
    elif speech_recognition_result.reason == speechsdk.ResultReason.Canceled:
        cancellation_details = speech_recognition_result.cancellation_details
        print("Speech Recognition canceled: {}".format(cancellation_details.reason))
        if cancellation_details.reason == speechsdk.CancellationReason.Error:
            print("Error details: {}".format(cancellation_details.error_details))
            print("Did you set the speech resource key and region values?")
recognize_from_microphone()

import azure.cognitiveservices.speech as speechsdk
def from_mic():
    speech_config = speechsdk.SpeechConfig(subscription="YourSpeechKey", region="YourSpeechRegion")
    speech_recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config)
    print("Speak into your microphone.")
    result = speech_recognizer.recognize_once_async().get()
    print(result.text)
from_mic()

import azure.cognitiveservices.speech as speechsdk
def from_file():
    speech_config = speechsdk.SpeechConfig(subscription="YourSpeechKey", region="YourSpeechRegion")
    audio_config = speechsdk.AudioConfig(filename="your_file_name.wav")
    speech_recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config, audio_config=audio_config)
    result = speech_recognizer.recognize_once_async().get()
    print(result.text)
from_file()
```
main function to call docker
```Python
https://www.linuxuprising.com/2021/03/how-to-get-sound-pulseaudio-to-work-on.html
https://blog.csdn.net/Parabolaa/article/details/126611447
# 要按照pulseserver比较麻烦，我直接在windows上运行成功了
G:\ResearchDirection\AI\azure\TestTTS.ipynb
import subprocess
def synthesize_text(text):
    # 构建 docker exec 命令
    command = f'docker exec speech-sdk-container /appenv/bin/python /app/azureTTS.py "{text}"'
    # 执行命令
    subprocess.run(command, shell=True)
def play_sound_with_ffplay(filename):
    subprocess.run(['ffplay', '-nodisp', '-autoexit', filename])
    
text_to_synthesize = "你好啊，我是白白。"
synthesize_text(text_to_synthesize)
play_sound_with_ffplay("\\\\wsl.localhost\AlmaLinux9\opt\workspace\\researcher\output.wav")
==============================================
import simpleaudio as sa
def play_sound(filename):
    wave_obj = sa.WaveObject.from_wave_file(filename)
    play_obj = wave_obj.play()
    play_obj.wait_done()  # 等待播放完成

play_sound("\\\\wsl.localhost\AlmaLinux9\opt\workspace\\researcher\output.wav")
```