Here are the instructions for a prompt template that guides an AI assistant to research, analyze and summarize a document, providing a final summary along with references to relevant parts of the original document:

<Task> Analyze a document and provide a summary with references </Task>

<Inputs>
{$DOCUMENT}
{$QUERY}
</Inputs>

<Instructions>
I will provide you with a document, followed by a query or topic to analyze within that document. Your task is to carefully review the document, perform the requested analysis, and provide a clear summary of your findings, along with references to the relevant parts of the original document that support your analysis.

Here are the steps to follow:

1. Carefully read the entire document provided between the <document> tags:
<document>
{$DOCUMENT}
</document>

2. Analyze the document, focusing on the following query or topic:
{$QUERY}

3. As you analyze, extract specific quotes or passages from the document that are most relevant to the query. Put each quote inside <quote> tags, and number them sequentially, like this:
<quote>[1] Relevant quote from the document goes here.</quote>
<quote>[2] Another relevant quote from the document goes here.</quote>

4. Once you've completed your analysis, write a clear, concise summary of your findings inside <summary> tags. Your summary should address the given query or topic, and provide key insights from the document. Do not quote directly from the document in your summary, but instead reference the relevant quotes by their number in square brackets, like this: [1], [2], etc.

5. After your summary, include a section called "Supporting Quotes" that lists all the relevant quotes you extracted, numbered and formatted exactly as described in step 3.

6. If the document does not contain information relevant to the given query, simply write "No relevant information found in the document to address this query." inside the <summary> tags, and omit the "Supporting Quotes" section.

Here is the format your response should follow:

<summary>
Your clear, concise summary goes here, referencing relevant quotes like this: [1], [2], etc.
</summary>

Supporting Quotes:
<quote>[1] Relevant quote from the document goes here.</quote>
<quote>[2] Another relevant quote from the document goes here.</quote>

Please analyze the document and provide your summary and supporting quotes immediately, without any additional explanation or conversation.
</Instructions>