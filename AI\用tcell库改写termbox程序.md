---
Updated: 2023-03-07T13:16
tags:
  - AI->-Programming
Created: 2023-03-06T20:52
---
```JavaScript
package main
import (
	"bufio"
	"context"
	"fmt"
	"log"
	"os"
	"runtime"
	"strings"
	"time"
	"github.com/charmbracelet/glamour"
	figure "github.com/common-nighthawk/go-figure"
	openai "github.com/sashabaranov/go-openai"
)
func main() {
	// Get OpenAI API Key
	apiKey := ""
	apiKey = os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		logError("Please setup OPENAI_API_KEY in OS environment.")
		return
	}
	// Welcome message
	figure.NewFigure("ChatGPT", "starwars", true).Print()
	fmt.Println("================= author -- <NAME_EMAIL> ====================")
	fmt.Println("  Type 'help' for a list of available commands")
	messages := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: "You are an expert in various fields, able to understand both English and Chinese, and can provide enthusiastic and patient assistance in various aspects",
		},
	}
	client := openai.NewClient(apiKey)
	// 读取用户输入并交互
	userInput := ""
	scanner := bufio.NewScanner(os.Stdin)
	var lines []string
	for {
		fmt.Print("> ")
		//fmt.Scanln(&userInput)
		for scanner.Scan() {
			line := scanner.Text()
			if line == "" {
				break
			}
			lines = append(lines, line)
		}
		userInput = strings.Join(lines, "\\n")
		lines = lines[:0]
		switch userInput {
		case "reset":
			fmt.Println("Quitting session...")
			messages = messages[:0]
			messages = []openai.ChatCompletionMessage{
				{
					Role:    "system",
					Content: "You are an expert in various fields, able to understand both English and Chinese, and can provide enthusiastic and patient assistance in various aspects",
				},
			}
			fmt.Println("Cleanup my memory!")
		case "help":
			fmt.Println("help: print this one; reset: reset session; bye: quit this app")
		case "bye":
			fmt.Println("Goodbye!")
			return
		default:
			interactWithChatGPT(client, &messages, userInput)
		}
		userInput = ""
	}
}
func interactWithChatGPT(client *openai.Client, chatHist *[]openai.ChatCompletionMessage, userInput string) {
	if userInput == "" {
		return
	}
	*chatHist = append(
		*chatHist, openai.ChatCompletionMessage{
			Role:    "user",
			Content: userInput,
		},
	)
	// Call ChatGPT API and get reply
	resp, err := client.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model:           openai.GPT3Dot5Turbo,
			Messages:        *chatHist,
			MaxTokens:       1750,
			Temperature:     0.7,
			PresencePenalty: 0.6,
			N:               1,
		},
	)
	if err != nil {
		logError("ChatGPT remote call failed: %s\n" + err.Error())
		return
	}
	// 格式化输出结果
	output := resp.Choices[0].Message.Content
	printOutput(output)
	*chatHist = append(
		*chatHist, openai.ChatCompletionMessage{
			Role:    "assistant",
			Content: output,
		},
	)
}
func logError(outstr string) {
	now := time.Now()
	fmt.Println(now.Format(time.RFC3339), "-", outstr)
	logFilename := fmt.Sprintf("console-%s.log", now.Format("2006-01-02"))
	logFile, err := os.OpenFile(logFilename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatalln("Failed to open log file:", err)
	}
	defer logFile.Close()
	log.SetOutput(logFile)
	log.Println(now.Format(time.RFC3339), "-", outstr)
}
func printOutput(output string) {
	if runtime.GOOS == "windows" {
		fmt.Println(output)
		return
	}
	// 初始化 Glamour 渲染器
	renderStyle := glamour.WithEnvironmentConfig()
	mdRenderer, err := glamour.NewTermRenderer(
		renderStyle,
	)
	if err != nil {
		logError("Markdown render initialization failed")
		return
	}
	styledText, err := mdRenderer.Render(output)
	if err != nil {
		logError(err.Error())
		return
	}
	fmt.Println(styledText)
}
```
To add the requested functionality using [github.com/gdamore/tcell](http://github.com/gdamore/tcell), you would need to modify the code to listen for keyboard events and display a popup window when ctrl+shift+m is pressed. The window should have input fields for "Save" and "Load", as well as an "OK" button. If the user enters a file name in the "Save" input field and presses "OK", the messages slice should be saved to a file with the specified name. If the user enters a file name in the "Load" input field and presses "OK", the file should be read and its contents should be loaded into the messages slice. If the file does not exist or if the save/load operation fails, an error message should be displayed.
  
  
下面是使用tcell库添加所需功能的代码。你需要将代码放在主程序中的main()函数里，并且确保已经安装了tcell库。
```Plain
package main
import (
    "fmt"
    "os"
    "strings"
    "github.com/gdamore/tcell"
    "github.com/gdamore/tcell/views"
)
func main() {
    // Get OpenAI API Key
    apiKey := ""
    apiKey = os.Getenv("OPENAI_API_KEY")
    if apiKey == "" {
        logError("Please setup OPENAI_API_KEY in OS environment.")
        return
    }
    // Welcome message
    // 这里省略
    // 初始化tcell应用程序
    app := tcell.NewApplication()
    // 初始化popup窗口
    popup := views.NewModalView()
    popup.SetTitle("Add A and B")
    popup.SetButtons(views.NewModalButton("OK", func() {
        a := popup.GetFormItemValue("A")
        b := popup.GetFormItemValue("B")
        fmt.Println(strings.TrimSpace(a) + strings.TrimSpace(b))
        app.SetFocus(chatView)
    }), views.NewModalButton("Quit", func() {
        app.SetFocus(chatView)
    }))
    // 添加inputField
    popup.AddFormItem("A", "A:", "")
    popup.AddFormItem("B", "B:", "")
    // 初始化聊天视图
    chatView := views.NewTextView()
    chatView.SetDynamicColors(true)
    chatView.SetWrap(true)
    chatView.SetBorder(true)
    chatView.SetTitle("Chat")
    // 设置焦点
    chatView.SetDoneFunc(func(key tcell.Key) {
        if key == tcell.KeyCtrlShiftM {
            popup.SetFocus("A")
            app.SetFocus(popup)
        }
    })
    // 展示视图
    root := views.NewGridLayout()
    root.SetRows(0, 1)
    root.SetColumns(0)
    root.AddItem(chatView, 0, 0, 1, 1, 0, 0, true)
    root.AddItem(popup, 1, 0, 1, 1, 0, 0, true)
    if err := app.SetRoot(root, true).Run(); err != nil {
        fmt.Fprintf(os.Stderr, "%v\\n", err)
        os.Exit(1)
    }
}
```
这段代码将添加一个名为“Add A and B”的popup窗口，其中包含两个inputField“ A”和“ B”，以及一个“ OK”按钮。当用户在“ A”和“ B”中输入值并按下“ OK”时，程序将返回主视图并输出A + B的值。如果用户按下“ Quit”按钮，则弹出窗口将关闭并返回到主视图。
在主视图中，用户可以按下Ctrl + Shift + m来打开popup窗口。当焦点在popup窗口中时，用户可以使用tab键来切换inputField之间的焦点。