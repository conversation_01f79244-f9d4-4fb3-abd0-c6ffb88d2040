{"openapi": "3.1.0", "info": {"title": "Browse AI API Documentation", "description": "If you are still using the deprecated API v1 version, you can see its documentation [here](https://www.browse.ai/docs/api/v1).", "version": "v2"}, "servers": [{"url": "https://api.browse.ai/v2"}], "tags": [{"name": "internal", "x-displayName": "Internal", "description": "There are some endpoints that are only used by our integrations, so we don't add this tag to our core resources\n"}, {"name": "system", "x-displayName": "System", "description": "This tag is used for endpoints that are used to check the status of Browse AI infrastructure\n"}, {"name": "robots", "x-displayName": "Robots", "description": "A robot can be trained do almost anything you do manually on the web. For example:\n\n  - Open a webpage,\n  - Log in,\n  - Click on buttons,\n  - Fill out forms,\n  - Extract structured data from a webpage into a spreadsheet,\n  - Take screenshots,\n  - Monitor specific parts of webpages for visual or content changes.\n\nRobots are created either by using Prebuilt Robots or using Browse AI Recorder and its click-and-extract interface.  Every robot has a few input parameters (like the webpage address) that you can adjust every time you run it.\n"}, {"name": "tasks", "x-displayName": "Tasks", "description": "Each robot is trained to perform a certain task. Every time you run that robot, it will perform that task and the  details, including the extracted data, will be stored under that task.\n\nIf you set up a monitoring robot to monitor a webpage for changes daily,  it will have to run a task every day or about 30 tasks per month for you.\n"}, {"name": "monitors", "x-displayName": "Monitors", "description": "Each robot on Browse AI can optionally have one or more monitors. Monitoring robots come with one monitor by default.\n\nFor example, if you set up a monitoring robot to monitor a category page on an e-commerce site for changes daily,  you can set up additional monitors to monitor other category pages on the same site using the same robot.\n\nEach monitor can have different input parameters and schedule.\n"}, {"name": "webhooks", "x-displayName": "Webhooks", "description": "After a robot finishes a task, its webhooks will be called.\n"}, {"name": "bulk runs", "x-displayName": "Bulk Runs", "description": "You can run up to 50,000 tasks at once using a robot with different input parameters for each task.\n"}], "x-tagGroups": [{"name": "Core Resources", "tags": ["system", "robots", "tasks", "monitors", "webhooks", "bulk runs"]}], "paths": {"/status": {"get": {"tags": ["system"], "summary": "Endpoint for checking the status of Browse AI infrastructure", "operationId": "getSystemStatus", "description": "This endpoint provides you with real-time information regarding the operational status of the Browse AI infrastructure. It gives insights into the condition of the tasks queue, thus allowing you to understand if the services are running smoothly or are under maintenance.\n", "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/status\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/status\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/status\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/status\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/status',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/status\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/status\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/status\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/status\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/status\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/status \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/status\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/status\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON containing Browse AI infrastructure status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getSystemStatus-200"}}}}}, "security": [{"bearerAuth": []}]}}, "/teams": {"get": {"tags": ["internal"], "summary": "Retrieve list of teams under user account", "operationId": "getUserTeams", "description": "this endpoint be used on Browse AI integrations to fetch all of the teams by auth0 access token\n", "parameters": [], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/teams\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/teams\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/teams\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/teams\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/teams',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/teams\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/teams\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/teams\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/teams\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/teams\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/teams \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/teams\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/teams\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON containing the total number of the user.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getUserTeams-200"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots": {"get": {"tags": ["robots"], "summary": "Retrieve list of robots under your account", "operationId": "getRobots", "description": "If you have already created a few robots on [your dashboard](https://dashboard.browse.ai), you can use this endpoint to retrieve a list of them.\n  \nYou can then use other endpoints to retrieve more information about your robots or run robots.\n", "parameters": [], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/robots \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON containing the total number of robots and an array of robots under this team.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getRobots-200"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}": {"get": {"tags": ["robots"], "summary": "Retrieve single robot by ID", "operationId": "getRobot", "description": "You can use this endpoint to retrieve a single robot by ID.\n", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID You can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/robots/{robotId} \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON containing the total number of robots and an array of robots under this team.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getRobot-200"}}}}, "400": {"description": "A JSON containing an error code and message.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getRobot-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/cookies": {"patch": {"tags": ["robots"], "summary": "Update a robot's cookies", "operationId": "upsertRobotCookies", "description": "Update a robot's cookies", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RobotCookie"}}}}}, "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\");\nvar request = new RestRequest(Method.PATCH);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nrequest.AddParameter(\"undefined\", \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\", ParameterType.RequestBody);\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\n\tpayload := strings.NewReader(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\")\n\n\treq, _ := http.NewRequest(\"PATCH\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.patch(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .body(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"PATCH\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/monitors/{monitorId}\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.write(JSON.stringify({\n  name: 'Monitor products',\n  inputParameters: {\n    originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n  },\n  schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n  notifyOnCapturedScreenshotChange: false,\n  notifyOnCapturedTextChange: true,\n  capturedScreenshotNotificationThreshold: 16\n}));\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'PATCH',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'},\n  body: {\n    name: 'Monitor products',\n    inputParameters: {\n      originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n    },\n    schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n    notifyOnCapturedScreenshotChange: false,\n    notifyOnCapturedTextChange: true,\n    capturedScreenshotNotificationThreshold: 16\n  },\n  json: true\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\nNSDictionary *parameters = @{ @\"name\": @\"Monitor products\",\n                              @\"inputParameters\": @{ @\"originUrl\": @\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\" },\n                              @\"schedule\": @\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n                              @\"notifyOnCapturedScreenshotChange\": @NO,\n                              @\"notifyOnCapturedTextChange\": @YES,\n                              @\"capturedScreenshotNotificationThreshold\": @16 };\n\nNSData *postData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"PATCH\"];\n[request setAllHTTPHeaderFields:headers];\n[request setHTTPBody:postData];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"PATCH\",\n  CURLOPT_POSTFIELDS => \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\npayload = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\"\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"PATCH\", \"/v2/robots/{robotId}/monitors/{monitorId}\", payload, headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\npayload = {\n    \"name\": \"Monitor products\",\n    \"inputParameters\": {\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\n    \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n    \"notifyOnCapturedScreenshotChange\": False,\n    \"notifyOnCapturedTextChange\": True,\n    \"capturedScreenshotNotificationThreshold\": 16\n}\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"PATCH\", url, json=payload, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Patch.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\nrequest.body = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\"\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request PATCH \\\n  --url https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId} \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":false,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":16}'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method PATCH \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --body-data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":false,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":16}' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\nlet parameters = [\n  \"name\": \"Monitor products\",\n  \"inputParameters\": [\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"],\n  \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n  \"notifyOnCapturedScreenshotChange\": false,\n  \"notifyOnCapturedTextChange\": true,\n  \"capturedScreenshotNotificationThreshold\": 16\n] as [String : Any]\n\nlet postData = JSONSerialization.data(withJSONObject: parameters, options: [])\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"PATCH\"\nrequest.allHTTPHeaderFields = headers\nrequest.httpBody = postData as Data\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the updated monitor.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/upsertRobotCookies-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/upsertRobotCookies-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/tasks": {"get": {"tags": ["tasks"], "summary": "Get all tasks by a robot", "operationId": "getRobotTasks", "description": "Get all of a robot's tasks\n", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "example": 1}, "required": false, "description": "Page number"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["failed", "successful", "in-progress"], "example": "successful"}, "required": false, "description": "Task status"}, {"in": "query", "name": "robotBulkRunId", "schema": {"type": "string", "example": "f6fb62b6-f06a-4bf7-a623-c6a35c2e70b0"}, "required": false, "description": "filter the result based on robot bulk run ID"}, {"in": "query", "name": "sort", "schema": {"type": "string", "example": "-createdAt,finishedAt"}, "required": false, "description": "A comma separated list of fields to sort by. Default sorting is ascending and prefixing field names with a hyphen '-' yields a descending order."}, {"in": "query", "name": "includeRetried", "schema": {"type": "boolean", "example": false}, "required": false, "description": "by passing false you can exclude the retried tasks"}, {"in": "query", "name": "fromDate", "schema": {"type": "integer", "example": *************}, "required": false, "description": "From task creation date and time in the form of a Unix timestamp"}, {"in": "query", "name": "toDate", "schema": {"type": "integer", "example": *************}, "required": false, "description": "To task creation date and time in the form of a Unix timestamp"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/tasks?page=1\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/tasks?page=1\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}/tasks?page=1\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/tasks?page=1\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/tasks',\n  qs: {page: '1'},\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/tasks?page=1\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/tasks?page=1\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}/tasks?page=1\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/tasks\"\n\nquerystring = {\"page\":\"1\"}\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers, params=querystring)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/tasks?page=1\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url 'https://api.browse.ai/v2/robots/{robotId}/tasks?page=1' \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - 'https://api.browse.ai/v2/robots/{robotId}/tasks?page=1'\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/tasks?page=1\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON including robots list.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getRobotTasks-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getRobotTasks-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["tasks"], "summary": "Run a robot", "operationId": "newRobotTask", "description": "Run a robot on-demand with custom input parameters.\n\nWhen you need to run a robot and get its captured data, you can use this endpoint to run  the task, and then use webhooks to receive the captured data as soon as the task is finished.  Alternatively, you can poll the GET endpoint to retrieve a ...", "requestBody": {"required": false, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewRobotTaskBodyParams"}}}}, "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/tasks\");\nvar request = new RestRequest(Method.POST);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nrequest.AddParameter(\"undefined\", \"{\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"}}\", ParameterType.RequestBody);\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/tasks\"\n\n\tpayload := strings.NewReader(\"{\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"}}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.browse.ai/v2/robots/{robotId}/tasks\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .body(\"{\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"}}\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"POST\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/tasks\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.write(JSON.stringify({\n  inputParameters: {\n    originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n  }\n}));\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'POST',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/tasks',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'},\n  body: {\n    inputParameters: {\n      originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n    }\n  },\n  json: true\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\nNSDictionary *parameters = @{ @\"inputParameters\": @{ @\"originUrl\": @\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\" } };\n\nNSData *postData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/tasks\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"POST\"];\n[request setAllHTTPHeaderFields:headers];\n[request setHTTPBody:postData];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/tasks\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"}}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\npayload = \"{\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"}}\"\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"POST\", \"/v2/robots/{robotId}/tasks\", payload, headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/tasks\"\n\npayload = {\"inputParameters\": {\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"}}\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/tasks\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Post.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\nrequest.body = \"{\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"}}\"\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request POST \\\n  --url https://api.browse.ai/v2/robots/{robotId}/tasks \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --data '{\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"}}'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method POST \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --body-data '{\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"}}' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/tasks\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\nlet parameters = [\"inputParameters\": [\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"]] as [String : Any]\n\nlet postData = JSONSerialization.data(withJSONObject: parameters, options: [])\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/tasks\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"POST\"\nrequest.allHTTPHeaderFields = headers\nrequest.httpBody = postData as Data\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON including robots list.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/newRobotTask-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/newRobotTask-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "403": {"description": "The request can not be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreditsLimitReachedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}, "503": {"description": "A JSON containing error attributes.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RobotUnderMaintenanceResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/tasks/{taskId}": {"get": {"tags": ["tasks"], "summary": "Retrieve a task", "operationId": "getRobotTask", "description": "Retrieve a task's details and captured data.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "path", "name": "taskId", "schema": {"type": "string"}, "required": true, "example": "f3672790-4561-424b-8a7b-7b7df182b236", "description": "Unique task ID"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/tasks/{taskId}\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}/tasks/{taskId}\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId} \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/tasks/{taskId}\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON including robots list.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getRobotTask-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getRobotTask-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/monitors": {"get": {"tags": ["monitors"], "summary": "Retrieve a robot's monitors", "operationId": "getMonitors", "description": "Retrieve a robot's monitors list.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/monitors\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/monitors\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}/monitors\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/monitors\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/monitors',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/monitors\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/monitors\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}/monitors\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/monitors\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/monitors\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/robots/{robotId}/monitors \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/monitors\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/monitors\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON including monitors list.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getMonitors-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getMonitors-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["monitors"], "summary": "Create a new monitor on a robot", "operationId": "createNewMonitor", "description": "Create a new monitor on a robot.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNewMonitorRequestBody"}}}}, "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/monitors\");\nvar request = new RestRequest(Method.POST);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nrequest.AddParameter(\"undefined\", \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\", ParameterType.RequestBody);\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/monitors\"\n\n\tpayload := strings.NewReader(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.browse.ai/v2/robots/{robotId}/monitors\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .body(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"POST\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/monitors\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.write(JSON.stringify({\n  name: 'Monitor products',\n  inputParameters: {\n    originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n  },\n  schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n  notifyOnCapturedScreenshotChange: true,\n  notifyOnCapturedTextChange: true,\n  capturedScreenshotNotificationThreshold: 15\n}));\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'POST',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/monitors',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'},\n  body: {\n    name: 'Monitor products',\n    inputParameters: {\n      originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n    },\n    schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n    notifyOnCapturedScreenshotChange: true,\n    notifyOnCapturedTextChange: true,\n    capturedScreenshotNotificationThreshold: 15\n  },\n  json: true\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\nNSDictionary *parameters = @{ @\"name\": @\"Monitor products\",\n                              @\"inputParameters\": @{ @\"originUrl\": @\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\" },\n                              @\"schedule\": @\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n                              @\"notifyOnCapturedScreenshotChange\": @YES,\n                              @\"notifyOnCapturedTextChange\": @YES,\n                              @\"capturedScreenshotNotificationThreshold\": @15 };\n\nNSData *postData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/monitors\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"POST\"];\n[request setAllHTTPHeaderFields:headers];\n[request setHTTPBody:postData];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/monitors\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\npayload = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\"\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"POST\", \"/v2/robots/{robotId}/monitors\", payload, headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/monitors\"\n\npayload = {\n    \"name\": \"Monitor products\",\n    \"inputParameters\": {\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\n    \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n    \"notifyOnCapturedScreenshotChange\": True,\n    \"notifyOnCapturedTextChange\": True,\n    \"capturedScreenshotNotificationThreshold\": 15\n}\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/monitors\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Post.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\nrequest.body = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\"\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request POST \\\n  --url https://api.browse.ai/v2/robots/{robotId}/monitors \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":true,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":15}'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method POST \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --body-data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":true,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":15}' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/monitors\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\nlet parameters = [\n  \"name\": \"Monitor products\",\n  \"inputParameters\": [\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"],\n  \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n  \"notifyOnCapturedScreenshotChange\": true,\n  \"notifyOnCapturedTextChange\": true,\n  \"capturedScreenshotNotificationThreshold\": 15\n] as [String : Any]\n\nlet postData = JSONSerialization.data(withJSONObject: parameters, options: [])\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/monitors\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"POST\"\nrequest.allHTTPHeaderFields = headers\nrequest.httpBody = postData as Data\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the newly created monitor.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/createNewMonitor-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateMonitorBadRequestResponse"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "403": {"description": "A JSON containing error attributes.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateMonitorForbiddenResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/monitors/{monitorId}": {"get": {"tags": ["monitors"], "summary": "Retrieve a robot's monitor", "operationId": "getMonitor", "description": "Retrieve a robot's monitor.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "path", "name": "monitorId", "schema": {"type": "string"}, "required": true, "example": "e524ab69-4269-4d9d-b3d8-678112a10d29", "description": "Unique monitor ID\n\nYou can find a monitor's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/monitors/{monitorId}\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}/monitors/{monitorId}\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId} \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the monitor.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getMonitor-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getMonitor-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["monitors"], "summary": "Update a robot's monitor", "operationId": "updateMonitor", "description": "Update a robot's monitor", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "path", "name": "monitorId", "schema": {"type": "string"}, "required": true, "example": "e524ab69-4269-4d9d-b3d8-678112a10d29", "description": "Unique monitor ID\n\nYou can find a monitor's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "inputParameters": {"type": "object", "properties": {"originUrl": {"type": "string"}}, "required": ["originUrl"]}, "schedule": {"type": "string"}, "notifyOnCapturedScreenshotChange": {"type": "boolean"}, "notifyOnCapturedTextChange": {"type": "boolean"}, "capturedScreenshotNotificationThreshold": {"type": "integer"}}, "required": ["name", "inputParameters", "schedule"]}}}}, "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\");\nvar request = new RestRequest(Method.PATCH);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nrequest.AddParameter(\"undefined\", \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\", ParameterType.RequestBody);\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\n\tpayload := strings.NewReader(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\")\n\n\treq, _ := http.NewRequest(\"PATCH\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.patch(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .body(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"PATCH\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/monitors/{monitorId}\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.write(JSON.stringify({\n  name: 'Monitor products',\n  inputParameters: {\n    originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n  },\n  schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n  notifyOnCapturedScreenshotChange: false,\n  notifyOnCapturedTextChange: true,\n  capturedScreenshotNotificationThreshold: 16\n}));\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'PATCH',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'},\n  body: {\n    name: 'Monitor products',\n    inputParameters: {\n      originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n    },\n    schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n    notifyOnCapturedScreenshotChange: false,\n    notifyOnCapturedTextChange: true,\n    capturedScreenshotNotificationThreshold: 16\n  },\n  json: true\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\nNSDictionary *parameters = @{ @\"name\": @\"Monitor products\",\n                              @\"inputParameters\": @{ @\"originUrl\": @\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\" },\n                              @\"schedule\": @\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n                              @\"notifyOnCapturedScreenshotChange\": @NO,\n                              @\"notifyOnCapturedTextChange\": @YES,\n                              @\"capturedScreenshotNotificationThreshold\": @16 };\n\nNSData *postData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"PATCH\"];\n[request setAllHTTPHeaderFields:headers];\n[request setHTTPBody:postData];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"PATCH\",\n  CURLOPT_POSTFIELDS => \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\npayload = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\"\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"PATCH\", \"/v2/robots/{robotId}/monitors/{monitorId}\", payload, headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\npayload = {\n    \"name\": \"Monitor products\",\n    \"inputParameters\": {\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\n    \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n    \"notifyOnCapturedScreenshotChange\": False,\n    \"notifyOnCapturedTextChange\": True,\n    \"capturedScreenshotNotificationThreshold\": 16\n}\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"PATCH\", url, json=payload, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Patch.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\nrequest.body = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":false,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":16}\"\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request PATCH \\\n  --url https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId} \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":false,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":16}'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method PATCH \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --body-data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":false,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":16}' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\nlet parameters = [\n  \"name\": \"Monitor products\",\n  \"inputParameters\": [\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"],\n  \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n  \"notifyOnCapturedScreenshotChange\": false,\n  \"notifyOnCapturedTextChange\": true,\n  \"capturedScreenshotNotificationThreshold\": 16\n] as [String : Any]\n\nlet postData = JSONSerialization.data(withJSONObject: parameters, options: [])\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"PATCH\"\nrequest.allHTTPHeaderFields = headers\nrequest.httpBody = postData as Data\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the updated monitor.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/updateMonitor-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateMonitorBadRequestResponse"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "403": {"description": "A JSON containing error attributes.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateMonitorForbiddenResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["monitors"], "summary": "Delete a robot's monitor", "operationId": "deleteMonitor", "description": "Delete a robot's monitor.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "path", "name": "monitorId", "schema": {"type": "string"}, "required": true, "example": "e524ab69-4269-4d9d-b3d8-678112a10d29", "description": "Unique monitor ID\n\nYou can find a monitor's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\");\nvar request = new RestRequest(Method.DELETE);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\n\treq, _ := http.NewRequest(\"DELETE\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"DELETE\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/monitors/{monitorId}\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'DELETE',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"DELETE\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"DELETE\", \"/v2/robots/{robotId}/monitors/{monitorId}\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"DELETE\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Delete.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request DELETE \\\n  --url https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId} \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method DELETE \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/monitors/{monitorId}\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"DELETE\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the deleted monitor.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/deleteMonitor-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/deleteMonitor-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/bulk-runs": {"post": {"tags": ["bulk runs"], "summary": "Bulk run tasks", "operationId": "newBulkRun", "description": "Bulk run up to 1,000 tasks at a time using a robot.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkRunBodyParams"}}}}, "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\");\nvar request = new RestRequest(Method.POST);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nrequest.AddParameter(\"undefined\", \"{\\\"title\\\":\\\"Bulk Run Title\\\",\\\"inputParameters\\\":[{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\\\"},{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\\\"}]}\", ParameterType.RequestBody);\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\"\n\n\tpayload := strings.NewReader(\"{\\\"title\\\":\\\"Bulk Run Title\\\",\\\"inputParameters\\\":[{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\\\"},{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\\\"}]}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .body(\"{\\\"title\\\":\\\"Bulk Run Title\\\",\\\"inputParameters\\\":[{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\\\"},{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\\\"}]}\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"POST\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/bulk-runs\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.write(JSON.stringify({\n  title: 'Bulk Run Title',\n  inputParameters: [\n    {\n      originUrl: 'https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories'\n    },\n    {\n      originUrl: 'https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers'\n    }\n  ]\n}));\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'POST',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/bulk-runs',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'},\n  body: {\n    title: 'Bulk Run Title',\n    inputParameters: [\n      {\n        originUrl: 'https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories'\n      },\n      {\n        originUrl: 'https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers'\n      }\n    ]\n  },\n  json: true\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\nNSDictionary *parameters = @{ @\"title\": @\"Bulk Run Title\",\n                              @\"inputParameters\": @[ @{ @\"originUrl\": @\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\" }, @{ @\"originUrl\": @\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\" } ] };\n\nNSData *postData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"POST\"];\n[request setAllHTTPHeaderFields:headers];\n[request setHTTPBody:postData];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\\\"title\\\":\\\"Bulk Run Title\\\",\\\"inputParameters\\\":[{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\\\"},{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\\\"}]}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\npayload = \"{\\\"title\\\":\\\"Bulk Run Title\\\",\\\"inputParameters\\\":[{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\\\"},{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\\\"}]}\"\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"POST\", \"/v2/robots/{robotId}/bulk-runs\", payload, headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\"\n\npayload = {\n    \"title\": \"Bulk Run Title\",\n    \"inputParameters\": [{\"originUrl\": \"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\"}, {\"originUrl\": \"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\"}]\n}\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Post.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\nrequest.body = \"{\\\"title\\\":\\\"Bulk Run Title\\\",\\\"inputParameters\\\":[{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\\\"},{\\\"originUrl\\\":\\\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\\\"}]}\"\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request POST \\\n  --url https://api.browse.ai/v2/robots/{robotId}/bulk-runs \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --data '{\"title\":\"Bulk Run Title\",\"inputParameters\":[{\"originUrl\":\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\"},{\"originUrl\":\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\"}]}'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method POST \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --body-data '{\"title\":\"Bulk Run Title\",\"inputParameters\":[{\"originUrl\":\"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\"},{\"originUrl\":\"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\"}]}' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/bulk-runs\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\nlet parameters = [\n  \"title\": \"Bulk Run Title\",\n  \"inputParameters\": [[\"originUrl\": \"https://www.seattlecoffeegear.com/shop-gear/accessories/espresso-accessories\"], [\"originUrl\": \"https://www.seattlecoffeegear.com/shop-gear/coffee-makers/drip-brewers\"]]\n] as [String : Any]\n\nlet postData = JSONSerialization.data(withJSONObject: parameters, options: [])\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"POST\"\nrequest.allHTTPHeaderFields = headers\nrequest.httpBody = postData as Data\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the newly created Bulk run.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/newBulkRun-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/newBulkRun-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "403": {"description": "A JSON containing error attributes.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/newBulkRun-403"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}, "503": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RobotUnderMaintenanceResponse"}}}}}, "security": [{"bearerAuth": []}]}, "get": {"tags": ["bulk runs"], "summary": "Retrieve a robot's bulk runs list", "operationId": "getBulkRuns", "description": "Retrieve a robot's bulk runs list.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "example": 1}, "required": false, "description": "Page number"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/bulk-runs?page=1\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/bulk-runs',\n  qs: {page: '1'},\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}/bulk-runs?page=1\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs\"\n\nquerystring = {\"page\":\"1\"}\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers, params=querystring)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url 'https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1' \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - 'https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1'\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs?page=1\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the bulk runs list.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getBulkRuns-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getBulkRuns-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/bulk-runs/{bulkRunId}": {"get": {"tags": ["bulk runs"], "summary": "Retrieve a robot's bulk run", "operationId": "getBulkRun", "description": "Retrieve a robot's bulk run along with a list of tasks run within the bulk run.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "path", "name": "bulkRunId", "schema": {"type": "string"}, "required": true, "example": "5aa4df52-25bb-48da-bf38-ce4f2bd98dd5", "description": "Unique bulk run ID\n"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "example": 1}, "required": false, "description": "Page number"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}',\n  qs: {page: '1'},\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}\"\n\nquerystring = {\"page\":\"1\"}\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers, params=querystring)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url 'https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1' \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - 'https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1'\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/bulk-runs/{bulkRunId}?page=1\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the bulk run information along with a paginated list of all its tasks.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getBulkRun-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getBulkRun-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/webhooks": {"get": {"tags": ["webhooks"], "summary": "Retrieve a robot's webhooks", "operationId": "getWebhooks", "description": "Retrieve a robot's webhook list.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/webhooks\");\nvar request = new RestRequest(Method.GET);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/webhooks\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.browse.ai/v2/robots/{robotId}/webhooks\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"GET\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/webhooks\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'GET',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/webhooks',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/webhooks\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"GET\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/webhooks\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"GET\", \"/v2/robots/{robotId}/webhooks\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/webhooks\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/webhooks\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request GET \\\n  --url https://api.browse.ai/v2/robots/{robotId}/webhooks \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method GET \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/webhooks\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/webhooks\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"GET\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON including monitors list.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getWebhooks-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getWebhooks-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["webhooks"], "summary": "Create a new webhook on a robot", "operationId": "createNewWebhook", "description": "Create a new webhook on a robot", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNewWebhookBodyParams"}}}}, "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/monitors\");\nvar request = new RestRequest(Method.POST);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nrequest.AddParameter(\"undefined\", \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\", ParameterType.RequestBody);\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/monitors\"\n\n\tpayload := strings.NewReader(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.browse.ai/v2/robots/{robotId}/monitors\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .body(\"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"POST\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/monitors\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.write(JSON.stringify({\n  name: 'Monitor products',\n  inputParameters: {\n    originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n  },\n  schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n  notifyOnCapturedScreenshotChange: true,\n  notifyOnCapturedTextChange: true,\n  capturedScreenshotNotificationThreshold: 15\n}));\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'POST',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/monitors',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'},\n  body: {\n    name: 'Monitor products',\n    inputParameters: {\n      originUrl: 'https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines'\n    },\n    schedule: 'FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR',\n    notifyOnCapturedScreenshotChange: true,\n    notifyOnCapturedTextChange: true,\n    capturedScreenshotNotificationThreshold: 15\n  },\n  json: true\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\nNSDictionary *parameters = @{ @\"name\": @\"Monitor products\",\n                              @\"inputParameters\": @{ @\"originUrl\": @\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\" },\n                              @\"schedule\": @\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n                              @\"notifyOnCapturedScreenshotChange\": @YES,\n                              @\"notifyOnCapturedTextChange\": @YES,\n                              @\"capturedScreenshotNotificationThreshold\": @15 };\n\nNSData *postData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/monitors\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"POST\"];\n[request setAllHTTPHeaderFields:headers];\n[request setHTTPBody:postData];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/monitors\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\npayload = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\"\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"POST\", \"/v2/robots/{robotId}/monitors\", payload, headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/monitors\"\n\npayload = {\n    \"name\": \"Monitor products\",\n    \"inputParameters\": {\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\n    \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n    \"notifyOnCapturedScreenshotChange\": True,\n    \"notifyOnCapturedTextChange\": True,\n    \"capturedScreenshotNotificationThreshold\": 15\n}\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/monitors\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Post.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\nrequest.body = \"{\\\"name\\\":\\\"Monitor products\\\",\\\"inputParameters\\\":{\\\"originUrl\\\":\\\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\\\"},\\\"schedule\\\":\\\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\\\",\\\"notifyOnCapturedScreenshotChange\\\":true,\\\"notifyOnCapturedTextChange\\\":true,\\\"capturedScreenshotNotificationThreshold\\\":15}\"\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request POST \\\n  --url https://api.browse.ai/v2/robots/{robotId}/monitors \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":true,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":15}'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method POST \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --body-data '{\"name\":\"Monitor products\",\"inputParameters\":{\"originUrl\":\"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"},\"schedule\":\"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\"notifyOnCapturedScreenshotChange\":true,\"notifyOnCapturedTextChange\":true,\"capturedScreenshotNotificationThreshold\":15}' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/monitors\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\nlet parameters = [\n  \"name\": \"Monitor products\",\n  \"inputParameters\": [\"originUrl\": \"https://www.espressozone.com/espresso-machines/semi-automatic-espresso-machines\"],\n  \"schedule\": \"FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR\",\n  \"notifyOnCapturedScreenshotChange\": true,\n  \"notifyOnCapturedTextChange\": true,\n  \"capturedScreenshotNotificationThreshold\": 15\n] as [String : Any]\n\nlet postData = JSONSerialization.data(withJSONObject: parameters, options: [])\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/monitors\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"POST\"\nrequest.allHTTPHeaderFields = headers\nrequest.httpBody = postData as Data\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the newly created monitor.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/createNewWebhook-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/createNewWebhook-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/robots/{robotId}/webhooks/{webhookId}": {"delete": {"tags": ["webhooks"], "summary": "Delete a robot's webhook", "operationId": "deleteWebhook", "description": "Delete a robot's webhook.", "parameters": [{"in": "path", "name": "robotId", "schema": {"type": "string"}, "required": true, "example": "c3689adb-50aa-44af-b265-a7e0d4e5846e", "description": "Unique robot ID\n\nYou can find a robot's ID by opening it on the dashboard and copying its ID in the browser address bar.\n"}, {"in": "path", "name": "webhookId", "schema": {"type": "string"}, "required": true, "example": "6d7f1218-43fb-4735-ac71-21e81b1ab23e", "description": "Unique webhookId ID\n"}], "x-codeSamples": [{"lang": "C#", "label": "C#", "source": "var client = new RestClient(\"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\");\nvar request = new RestRequest(Method.DELETE);\nrequest.AddHeader(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\");\nIRestResponse response = client.Execute(request);\n"}, {"lang": "Go", "label": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\"\n\n\treq, _ := http.NewRequest(\"DELETE\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}\n"}, {"lang": "Java", "label": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\")\n  .header(\"Authorization\", \"Bearer YOUR_SECRET_API_KEY\")\n  .asString();\n"}, {"lang": "Node", "label": "<PERSON><PERSON> (Native)", "source": "const http = require(\"https\");\n\nconst options = {\n  \"method\": \"DELETE\",\n  \"hostname\": \"api.browse.ai\",\n  \"port\": null,\n  \"path\": \"/v2/robots/{robotId}/webhooks/{webhookId}\",\n  \"headers\": {\n    \"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"\n  }\n};\n\nconst req = http.request(options, function (res) {\n  const chunks = [];\n\n  res.on(\"data\", function (chunk) {\n    chunks.push(chunk);\n  });\n\n  res.on(\"end\", function () {\n    const body = Buffer.concat(chunks);\n    console.log(body.toString());\n  });\n});\n\nreq.end();\n"}, {"lang": "Node", "label": "Node (request)", "source": "const request = require('request');\n\nconst options = {\n  method: 'DELETE',\n  url: 'https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}',\n  headers: {Authorization: 'Bearer YOUR_SECRET_API_KEY'}\n};\n\nrequest(options, function (error, response, body) {\n  if (error) throw new Error(error);\n\n  console.log(body);\n});\n"}, {"lang": "Objective-C", "label": "Objective-C", "source": "#import <Foundation/Foundation.h>\n\nNSDictionary *headers = @{ @\"Authorization\": @\"Bearer YOUR_SECRET_API_KEY\" };\n\nNSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@\"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\"]\n                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy\n                                                   timeoutInterval:10.0];\n[request setHTTPMethod:@\"DELETE\"];\n[request setAllHTTPHeaderFields:headers];\n\nNSURLSession *session = [NSURLSession sharedSession];\nNSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request\n                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {\n                                                if (error) {\n                                                    NSLog(@\"%@\", error);\n                                                } else {\n                                                    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;\n                                                    NSLog(@\"%@\", httpResponse);\n                                                }\n                                            }];\n[dataTask resume];\n"}, {"lang": "PHP", "label": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Bearer YOUR_SECRET_API_KEY\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}\n"}, {"lang": "Python", "label": "Python (python3)", "source": "import http.client\n\nconn = http.client.HTTPSConnection(\"api.browse.ai\")\n\nheaders = { 'Authorization': \"Bearer YOUR_SECRET_API_KEY\" }\n\nconn.request(\"DELETE\", \"/v2/robots/{robotId}/webhooks/{webhookId}\", headers=headers)\n\nres = conn.getresponse()\ndata = res.read()\n\nprint(data.decode(\"utf-8\"))\n"}, {"lang": "Python", "label": "Python (requests)", "source": "import requests\n\nurl = \"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\"\n\nheaders = {\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"}\n\nresponse = requests.request(\"DELETE\", url, headers=headers)\n\nprint(response.text)\n"}, {"lang": "<PERSON>", "label": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'openssl'\n\nurl = URI(\"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\nhttp.verify_mode = OpenSSL::SSL::VERIFY_NONE\n\nrequest = Net::HTTP::Delete.new(url)\nrequest[\"Authorization\"] = 'Bearer YOUR_SECRET_API_KEY'\n\nresponse = http.request(request)\nputs response.read_body\n"}, {"lang": "Shell", "label": "Shell (cURL)", "source": "curl --request DELETE \\\n  --url https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId} \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY'\n"}, {"lang": "Shell", "label": "Shell (wget)", "source": "wget --quiet \\\n  --method DELETE \\\n  --header 'Authorization: Bearer YOUR_SECRET_API_KEY' \\\n  --output-document \\\n  - https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\n"}, {"lang": "Swift", "label": "Swift", "source": "import Foundation\n\nlet headers = [\"Authorization\": \"Bearer YOUR_SECRET_API_KEY\"]\n\nlet request = NSMutableURLRequest(url: NSURL(string: \"https://api.browse.ai/v2/robots/{robotId}/webhooks/{webhookId}\")! as URL,\n                                        cachePolicy: .useProtocolCachePolicy,\n                                    timeoutInterval: 10.0)\nrequest.httpMethod = \"DELETE\"\nrequest.allHTTPHeaderFields = headers\n\nlet session = URLSession.shared\nlet dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in\n  if (error != nil) {\n    print(error)\n  } else {\n    let httpResponse = response as? HTTPURLResponse\n    print(httpResponse)\n  }\n})\n\ndataTask.resume()\n"}], "responses": {"200": {"description": "A JSON object containing the deleted monitor.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/deleteWebhook-200"}}}}, "400": {"description": "A JSON containing error attributes. This will happen if any of the parameters are not valid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/deleteWebhook-400"}}}}, "401": {"description": "The request is not authorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "The resource is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundResponse"}}}}, "500": {"description": "There was an error on the server", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerResponse"}}}}}, "security": [{"bearerAuth": []}]}}}, "webhooks": {"taskWebhook": {"post": {"summary": "Task Finished", "tags": ["webhooks"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["event", "task"], "properties": {"event": {"type": "string", "enum": ["task.finishedSuccessfully", "task.finishedWithError", "task.captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], "example": "task.finishedSuccessfully", "description": "The event type that triggered the webhook"}, "task": {"$ref": "#/components/schemas/RobotTaskWebhook"}}}}}}, "responses": {"200": {"description": "Your webhook URL is called only once – regardless of the response status. This behavior may change in the future and we may introduce automatic retries in cases where the response status is not 200 and your software is having a downtime. At this time, however, you need to make sure that the webhook URL is always available and set the response status code to 200.", "content": {"application/json": {"example": {"status": "success"}}}}}}}}, "components": {"schemas": {"getSystemStatus-200": {"type": "object", "required": ["tasksQueueStatus"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "tasksQueueStatus": {"type": "string", "enum": ["OK", "UNDER_MAINTENANCE"], "example": "OK"}}}, "Team": {"type": "object", "required": ["id", "api", "createdAt"], "properties": {"id": {"type": "string", "example": "b04eaafa-00c2-41a2-9c6a-7f7d32805a91", "description": "Unique team ID"}, "name": {"type": "string", "nullable": true, "example": "Browse AI team", "description": "Team name"}, "api": {"type": "boolean", "example": true, "description": "API accessibility"}, "createdAt": {"type": "integer", "example": *************, "description": "Team creation date and time in the form of a Unix timestamp"}}}, "getUserTeams-200": {"type": "object", "required": ["statusCode", "messageCode", "teams"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "teams": {"type": "object", "required": ["totalCount", "items"], "properties": {"totalCount": {"type": "integer", "example": 20, "description": "Total number of Team this user has."}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Team"}}}}}}, "UnauthorizedResponse": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [401]}, "messageCode": {"type": "string", "enum": ["unauthorized", "no_api_access"], "example": "unauthorized"}}}, "CommonParameterPart": {"type": "object", "required": ["name", "label", "encrypted"], "properties": {"type": {"type": "string", "description": "Parameter type"}, "name": {"type": "string", "example": "originUrl", "description": "Parameter name"}, "label": {"type": "string", "example": "Origin URL", "description": "Parameter title"}, "encrypted": {"type": "boolean", "example": false, "description": "Whether parameter value and defaultValue are encrypted"}}}, "TextParameter": {"title": "Text Parameter"}, "NumberParameter": {"title": "Numeric Parameter"}, "URLParameter": {"title": "URL Parameter"}, "RobotInputParameters": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/TextParameter"}, {"$ref": "#/components/schemas/NumberParameter"}, {"$ref": "#/components/schemas/URLParameter"}]}}, "Robot": {"type": "object", "required": ["id", "createdAt"], "properties": {"id": {"type": "string", "example": "4f5cd7ff-6c98-4cac-8cf0-d7d0cb050b06", "description": "Unique robot ID"}, "name": {"type": "string", "example": "Extract data from Realtor.com", "description": "Robot name"}, "createdAt": {"type": "integer", "example": *************, "description": "Robot creation date and time in the form of a Unix timestamp"}, "inputParameters": {"$ref": "#/components/schemas/RobotInputParameters"}}}, "getRobots-200": {"type": "object", "required": ["statusCode", "messageCode", "robots"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "robots": {"type": "object", "required": ["totalCount", "items"], "properties": {"totalCount": {"type": "integer", "example": 20, "description": "Total number of robots this team has."}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Robot"}}}}}}, "getRobot-200": {"type": "object", "required": ["statusCode", "messageCode", "robot"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "robot": {"$ref": "#/components/schemas/Robot"}}}, "getRobot-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["bad_request", "invalid_robot_id"], "example": "bad_request"}}}, "NotFoundResponse": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [404]}, "messageCode": {"type": "string", "enum": ["not_found"]}}}, "InternalServerResponse": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [500]}, "messageCode": {"type": "string", "enum": ["internal_server_error"]}}}, "RobotCookie": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the cookie.", "example": "ACCOUNT_CHOOSER"}, "value": {"type": "string", "description": "The value of the cookie.", "example": ********}, "domain": {"type": "string", "description": "The domain associated with the cookie. Specifies the domains to which the cookie should be sent.", "example": ".example.com"}, "expirationDate": {"type": "number", "format": "int64", "description": "The expiration date of the cookie in seconds since the UNIX epoch (e.g., POSIX time). If not provided, the cookie will be treated as a session cookie.", "example": **********}, "path": {"type": "string", "description": "The URL path to which the cookie should be sent. If not provided, it defaults to the current path of the document location.", "example": "/products/"}, "secure": {"type": "boolean", "description": "Indicates whether the cookie should only be sent over secure (HTTPS) connections. If true, the cookie will not be sent over unencrypted HTTP connections.", "example": true}, "httpOnly": {"type": "boolean", "description": "If true, the cookie is accessible only through the HTTP(S) protocol and cannot be accessed through JavaScript or other client-side scripts.", "example": true}, "hostOnly": {"type": "boolean", "description": "If true, the cookie is only sent to the exact domain specified in the \"domain\" property. If false, the cookie is sent to subdomains as well, provided that the \"domain\" property allows it.", "example": true}}, "required": ["name", "value"]}, "upsertRobotCookies-200": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "cookies": {"type": "array", "items": {"$ref": "#/components/schemas/RobotCookie"}}}}, "CookieError": {"type": "object", "required": ["summary", "fields"], "properties": {"name": {"type": "string", "description": "Name of the cookie", "example": "ACCOUNT_CHOOSER"}, "summary": {"type": "string", "description": "Summary of the error", "example": "Errors found in existing cookie fields"}, "fields": {"type": "array", "items": {"type": "object", "required": ["field", "message"], "properties": {"field": {"type": "string", "description": "Field name with the error", "example": "value"}, "message": {"type": "string", "description": "Error message for the field", "example": "Required"}}}}}}, "upsertRobotCookies-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "bad_request", "body_parse_error"], "example": "bad_request"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/Cookie<PERSON>rror"}}}}, "InputParameters": {"type": "object", "description": "An object of input parameters to override default input parameters.", "example": {"originUrl": "https://www.ycombinator.com/companies/airbnb", "companies_skip": 0, "companies_limit": 10}, "additionalProperties": {"oneOf": [{"type": "string"}, {"type": "number"}]}, "properties": {}}, "CapturedTexts": {"type": "object", "description": "Captured texts", "example": {"Product Name": "<PERSON>", "Width": "15", "Pattern Repeat": "PATTERN REPEAT", "Construction": "Hand woven", "Fiber": "100% Wool", "Color": null, "Main Image": "https://isteam.wsimg.com/ip/e31f7bba-252b-4669-9209-639d1c00765d/ols/258_original"}, "additionalProperties": {"type": "string", "nullable": true}, "properties": {}}, "CapturedScreenshots": {"type": "object", "description": "All screenshots captured in this task.", "example": {"top-ads": {"id": "b4d132f3-12d9-4770-ac7d-88e481fc5b47", "name": "Top ads", "src": "https://prod-browseai-captured-data.s3.amazonaws.com/1fae674a-2788-46a8-83c8-95c4664c6d25/6326b3c1-7b16-4256-a323-7d8d8954bd4e/1061671f-7f71-42ac-bb9a-207d126d1f3a/00001-user-1620230947-6f113cf2-90ef-4c66-a448-9d5c6bd64873.png", "width": 600, "height": 120, "x": 201, "y": 142, "deviceScaleFactor": 1.2, "full": "page", "comparedToScreenshotId": "29d742c2-6f45-4f29-9d48-ba6fe66e6e3d", "diffImageSrc": "https://prod-browseai-captured-data.s3.amazonaws.com/1fae674a-2788-46a8-83c8-95c4664c6d25/6326b3c1-7b16-4256-a323-7d8d8954bd4e/1061671f-7f71-42ac-bb9a-207d126d1f3a/00001-user-1620230947-6f113cf2-90ef-4c66-a448-9d5c6bd64873.png", "changePercentage": 20, "diffThreshold": 5, "fileRemovedAt": *************}}, "additionalProperties": {"type": "object", "required": ["id", "src", "width", "height", "x", "y", "deviceScaleFactor", "changePercentage", "diffT<PERSON><PERSON><PERSON>"], "properties": {"id": {"type": "string", "example": "b4d132f3-12d9-4770-ac7d-88e481fc5b47", "description": "Unique screenshot ID"}, "name": {"type": "string", "nullable": true, "example": "Top ads", "description": "Screenshot name"}, "src": {"type": "string", "example": "https://prod-browseai-captured-data.s3.amazonaws.com/1fae674a-2788-46a8-83c8-95c4664c6d25/6326b3c1-7b16-4256-a323-7d8d8954bd4e/1061671f-7f71-42ac-bb9a-207d126d1f3a/00001-user-1620230947-6f113cf2-90ef-4c66-a448-9d5c6bd64873.png", "description": "Screenshot image link"}, "width": {"type": "number", "example": 600, "minimum": 0, "description": "Screenshot rectangle width in pixels"}, "height": {"type": "number", "example": 120, "minimum": 0, "description": "Screenshot rectangle height in pixels"}, "x": {"type": "number", "example": 201, "description": "Screenshot rectangle distance from the left edge of the page in pixels"}, "y": {"type": "number", "example": 142, "description": "Screenshot rectangle distance from the top edge of the page in pixels"}, "deviceScaleFactor": {"type": "number", "example": 1.2, "description": "Device scale factor when screenshot was taken"}, "full": {"type": "string", "nullable": true, "example": "page", "description": "Whether this is a full *page* screenshot, a *viewport* screenshot, or a different type of screenshot.\n\n`null` means it is either an HTML element screenshot or a particular rectangle (x, y, w, h) screenshot.\n"}, "comparedToScreenshotId": {"type": "string", "nullable": true, "example": "29d742c2-6f45-4f29-9d48-ba6fe66e6e3d", "description": "If screenshot was taken in a monitoring check, this is the ID of the screenshot it was compared to."}, "diffImageSrc": {"type": "string", "nullable": true, "example": "https://prod-browseai-captured-data.s3.amazonaws.com/1fae674a-2788-46a8-83c8-95c4664c6d25/6326b3c1-7b16-4256-a323-7d8d8954bd4e/1061671f-7f71-42ac-bb9a-207d126d1f3a/00001-user-1620230947-6f113cf2-90ef-4c66-a448-9d5c6bd64873.png", "description": "A transparent PNG highlighting changes compared to previous screenshot in red"}, "changePercentage": {"type": "number", "example": 20, "minimum": 0, "description": "How much screenshot changed compared to previous screenshot"}, "diffThreshold": {"type": "integer", "example": 5, "minimum": 0, "description": "The change percentage threshold above which the user would be notified of the change, based on monitor settings."}, "fileRemovedAt": {"type": "integer", "nullable": true, "example": *************, "description": "After your account's data retention period, screenshots get removed and  this field will be screenshot removal date and time in the form of a Unix timestamp.\n"}}}, "properties": {}}, "CapturedLists": {"type": "object", "description": "All lists captured in this task.", "example": {"companies": [{"Position": "1", "name": "Airbnb", "location": "San Francisco, CA, USA", "description": "Book accommodations around the world."}, {"Position": "2", "name": "Coin base", "location": "San Francisco, CA, USA", "description": "Buy, sell, and manage crypto currencies."}, {"Position": "3", "name": "DoorDash", "location": "San Francisco, CA, USA", "description": "Restaurant delivery."}]}, "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/CapturedTexts"}}, "properties": {}}, "RobotTask": {"type": "object", "required": ["id", "inputParameters", "robotId", "createdAt", "runByAPI", "triedRecordingVideo", "capturedTexts", "capturedScreenshots", "capturedLists"], "properties": {"id": {"type": "string", "example": "f6fb62b6-f06a-4bf7-a623-c6a35c2e70b0", "description": "Unique task ID"}, "inputParameters": {"$ref": "#/components/schemas/InputParameters"}, "robotId": {"type": "string", "example": "4f5cd7ff-6c98-4cac-8cf0-d7d0cb050b06", "description": "Unique robot ID"}, "status": {"type": "string", "enum": ["failed", "successful", "in-progress"], "example": "successful", "description": "task status"}, "runByUserId": {"type": "string", "nullable": true, "example": null, "description": "User ID who ran the robot on the dashboard"}, "robotBulkRunId": {"type": "string", "nullable": true, "example": null, "description": "Robot bulk run ID associated with this task"}, "runByTaskMonitorId": {"type": "string", "nullable": true, "example": null, "description": "Monitor ID that ran this check"}, "runByAPI": {"type": "boolean", "example": true, "description": "Whether the robot was ran through the API"}, "createdAt": {"type": "integer", "example": *************, "description": "Task creation date and time in the form of a Unix timestamp"}, "startedAt": {"type": "integer", "nullable": true, "example": *************, "description": "Task start date and time in the form of a Unix timestamp"}, "finishedAt": {"type": "integer", "nullable": true, "example": *************, "description": "Task finish date and time in the form of a Unix timestamp.\n\nIf `null`, it means robot is still running and capturing data.\n\nTasks time out with an error if they are not finished within 15 minutes (or the maximum duration allowed on your plan).\n"}, "userFriendlyError": {"type": "string", "nullable": true, "example": null, "description": "If task fails, a user-friendly error will be provided here."}, "triedRecordingVideo": {"type": "boolean", "example": true, "description": "Whether the robot tried to record a video while performing this task.\n\nYou can change a robot's video recording setting on its Settings page. \n\nRobots try to record a video when a task is failed and auto-retried as well.\n"}, "videoUrl": {"type": "string", "nullable": true, "example": "https://prod-browseai-captured-data.s3.amazonaws.com/1fae674a-2788-46a8-83c8-95c4664c6d25/6326b3c1-7b16-4256-a323-7d8d8954bd4e/1061671f-7f71-42ac-bb9a-207d126d1f3a/system-**********-b1a9688b-05d3-4682-beeb-9ce035e482b1.mp4", "description": "If a video was recorded for this task, this is the link to the video."}, "videoRemovedAt": {"type": "integer", "nullable": true, "example": *************, "description": "After your account's data retention period, task videos get removed and  this field will be video removal date and time in the form of a Unix timestamp.\n"}, "retriedOriginalTaskId": {"type": "string", "nullable": true, "example": "673da019-bf0c-476e-9c4f-d35252a151dc", "description": "The ID of the original failed task this task was retrying. For example, if task A failed and was retried by task B, and task B was retried by task C, `retriedOriginalTaskId` will point to task A in both task B and C."}, "retriedByTaskId": {"type": "string", "nullable": true, "example": null, "description": "The ID of the task that retried this task, if this task failed with an error and was retried.\n\nFailed tasks get retried if \"Double Check\" option is enabled on robot Settings page. \"Double Check\" is enabled by default.\n"}, "capturedDataTemporaryUrl": {"type": "string", "nullable": true, "example": "https://prod-browseai-captured-data.s3.amazonaws.com/1fae674a-2788-46a8-83c8-95c4664c6d25/6326b3c1-7b16-4256-a323-7d8d8954bd4e/1061671f-7f71-42ac-bb9a-207d126d1f3a/system-**********-b1a9688b-05d3-4682-beeb-9ce035e482b1.json?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAQVG3TPBVXHSCAX63%2F20221031%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20221031T185642Z&X-Amz-Expires=1800&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEJP%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJIMEYCIQDfX8VNAl5kBgttrCU85U5wc1ZtSOmshO6%2FPilXOv8nvgIhAIveFfsk%2B2CnEkrMZWriodEPsj0osO5a5zV6eVu%2FXfuZKp8DCHwQAhoMMDQ1NTU3NzA4OTA3IgyrbhVK0MP1WMFBXh0q%2FAJulP5qfaV5mn3NRbINqZN4hy4Dg3IujNrZjw8ef32sWE1Gj2D%2Fc0YTJUzvx%2Fnm7LxyNO6AR35mrVy%2FBm9Q80UIspkcLMl45EK%2FoUDO0fAvoUF8g6iZ905qS3MvnOTxXkObhM1PVmpFeJFMw3jksnOPfKE4X7Ut%2FJXNwD%2F5QzdkQCXkGem%2BlrYSSSf8jB8lihTAjT%2FNXmOKMv3jktmZ13T8J1R8F8zeuLPMQf7QphUzlKn5joPb28cConluQC97y%2BjwxqIYjvIFKXY9cZEoaHGh4c6FbXsia714zG3CQp8NSGLbqCCu93oJI1Z61E%2BZ6PhB3vZGdBvXi61AlJcxZ7sti6i0h4VAbWspiJIgWwoZzrsTtneBNNpUW9tvtacGgEZIwAKV%2F3AhVEZu3WC1eQ9HtfjT9%2FjW99SEB8VVGXwkM%2FA9mtT%2FuiL0cAfQZRMhtbQJXXDRdkYEw%2FWuhjJ3zxEtEB2m3uH%2B%2BUEzOzGTd5Knm%2Bero%2BhMfN8X%2Botm3DDbtICbBjqcAf5Riii0XE1w2TZvpm%2FPNHTchCu7FnNz5hfvflv8scpgO5M4bGpy%2FadI4%2F7AUQqCQXFw4scF0FCCdb8AKJZsFGG18W1jjDHyR0YuxZFQ%2FJQRt0JP3yr%2BkVxjAH7qTtc0AzF%2FnGTgy3MOF%2Bm6Y7EkyCWyV2r6o1JTBQMftlf7MI8Uvw4cSZE6JoZviaFtmKVLGGgR4F3cDiyU56augA%3D%3D&X-Amz-Signature=a7bb4d7597ad37cdf1f260890c3c474f7f49334db58c9650d75302a34126f7bc&X-Amz-SignedHeaders=host", "description": "If your task's captured data exceeds 100KB, the data will be only accessible through this link. There's a 24 hours expiration time for this link (you need to call this API again to get a new link if it expires)."}, "capturedTexts": {"$ref": "#/components/schemas/CapturedTexts"}, "capturedScreenshots": {"$ref": "#/components/schemas/CapturedScreenshots"}, "capturedLists": {"$ref": "#/components/schemas/CapturedLists"}}}, "getRobotTasks-200": {"type": "object", "required": ["statusCode", "messageCode", "result"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "result": {"type": "object", "required": ["robotTasks"], "properties": {"robotTasks": {"type": "object", "required": ["totalCount", "pageNumber", "hasMore", "items"], "properties": {"totalCount": {"type": "integer", "example": 20, "description": "Total number of tasks this robot has."}, "pageNumber": {"type": "integer", "example": 1, "description": "Current page number."}, "hasMore": {"type": "boolean", "example": true, "description": "Whether there are more tasks on the next page."}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RobotTask"}}}}}}}}, "getRobotTasks-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "bad_request", "invalid_from_date", "invalid_to_date"], "example": "invalid_robot_id"}}}, "NewRobotTaskBodyParams": {"type": "object", "properties": {"recordVideo": {"type": "boolean", "example": false, "description": "Try to record a video while running the task. This is not guaranteed to work as the robot might skip video recording if the site is too heavy.", "minLength": 1, "maxLength": 200}, "inputParameters": {"$ref": "#/components/schemas/InputParameters"}}}, "newRobotTask-200": {"type": "object", "required": ["statusCode", "messageCode", "result"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "result": {"$ref": "#/components/schemas/RobotTask"}}}, "newRobotTask-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["bad_request", "body_parse_error", "invalid_robot_id", "invalid_input_parameters"], "example": "bad_request"}}}, "CreditsLimitReachedResponse": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [403]}, "messageCode": {"type": "string", "enum": ["credits_limit_reached"]}}}, "RobotUnderMaintenanceResponse": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [503]}, "messageCode": {"type": "string", "enum": ["robot_under_maintenance"]}}}, "getRobotTask-200": {"type": "object", "required": ["statusCode", "messageCode", "result"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "result": {"$ref": "#/components/schemas/RobotTask"}}}, "getRobotTask-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["bad_request", "invalid_robot_id", "invalid_task_id"], "example": "bad_request"}}}, "Schedules": {"type": "array", "description": "Array of schedules.\n", "deprecated": true, "items": {"type": "object", "title": "Fixed interval schedule", "required": ["type", "everyMinutes"], "properties": {"type": {"type": "string", "enum": ["FIXED_INTERVAL"], "example": "FIXED_INTERVAL", "description": "Schedule type"}, "everyMinutes": {"type": "number", "example": 60, "description": "Schedule interval in minutes"}}}, "example": [{"type": "FIXED_INTERVAL", "everyMinutes": 60}]}, "Schedule": {"type": "string", "description": "recurring schedule.\n", "example": "FREQ=HOURLY;INTERVAL=1;BYWEEKDAY=MO,TU,WE,TH,FR"}, "Monitor": {"type": "object", "required": ["id", "name", "status", "pausedReason", "inputParameters", "notifyOnCapturedScreenshotChange", "notifyOnCapturedTextChange", "capturedScreenshotNotificationThreshold", "createdAt"], "properties": {"id": {"type": "string", "example": "f6fb62b6-f06a-4bf7-a623-c6a35c2e70b0", "description": "Unique robot monitor ID"}, "name": {"type": "string", "example": "Monitor Products", "description": "Monitor name"}, "status": {"type": "string", "enum": ["active", "paused"], "example": "active", "description": "Represents the current state of the monitor. 'active' indicates that the monitor is currently operational and performing its intended functions, while 'paused' signifies that the monitor's activities are temporarily suspended. The 'paused' state may be due to reasons specified in the 'pausedReason' attribute."}, "pausedReason": {"type": "string", "nullable": true, "enum": ["lowCredits", "tooManyFailures", "userRequested", "userInactivity"], "example": null, "description": "Specifies the reason why the monitor is in a paused state."}, "inputParameters": {"$ref": "#/components/schemas/InputParameters"}, "schedules": {"$ref": "#/components/schemas/Schedules"}, "schedule": {"$ref": "#/components/schemas/Schedule"}, "notifyOnCapturedScreenshotChange": {"type": "boolean", "example": true, "description": "If set to `true`, an email notification will be sent to you when a change is detected in captured screenshots."}, "notifyOnCapturedTextChange": {"type": "boolean", "example": true, "description": "If set to `true`, an email notification will be sent to you when a change is detected in captured texts."}, "capturedScreenshotNotificationThreshold": {"type": "number", "example": 15, "description": "The \"screenshot changed\" email notification will be sent to you if the change is greater than this threshold (in percent)."}, "createdAt": {"type": "integer", "example": *************, "description": "Monitor creation date and time in the form of a Unix timestamp."}, "pausedAt": {"type": "integer", "nullable": true, "example": *************, "description": "Monitor pause date and time in the form of a Unix timestamp."}, "updatedAt": {"type": "integer", "nullable": true, "example": *************, "description": "Monitor last update date and time in the form of a Unix timestamp."}}}, "getMonitors-200": {"type": "object", "required": ["statusCode", "messageCode", "monitors"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "monitors": {"type": "object", "required": ["totalCount", "items"], "properties": {"totalCount": {"type": "number", "example": 10, "description": "Total number of monitors this robot has"}, "items": {"type": "array", "description": "Array of all monitors", "items": {"$ref": "#/components/schemas/Monitor"}}}}}}, "getMonitors-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "bad_request"], "example": "bad_request"}}}, "CreateNewMonitorRequestBody": {"type": "object", "required": ["name", "inputParameters", "notifyOnCapturedScreenshotChange", "notifyOnCapturedTextChange", "capturedScreenshotNotificationThreshold"], "properties": {"name": {"type": "string", "example": "Monitor Products", "description": "Monitor name", "minLength": 1, "maxLength": 200}, "inputParameters": {"$ref": "#/components/schemas/InputParameters", "description": "An object of input parameters to override default input parameters."}, "schedules": {"$ref": "#/components/schemas/Schedules"}, "schedule": {"$ref": "#/components/schemas/Schedule"}, "notifyOnCapturedScreenshotChange": {"type": "boolean", "example": true, "description": "If set to `true`, an email notification will be sent to you when a change is detected in captured screenshots."}, "notifyOnCapturedTextChange": {"type": "boolean", "example": true, "description": "If set to `true`, an email notification will be sent to you when a change is detected in captured texts."}, "capturedScreenshotNotificationThreshold": {"type": "number", "example": 15, "description": "The \"screenshot changed\" email notification will be sent to you if the change is greater than this threshold (in percent)."}}}, "createNewMonitor-200": {"type": "object", "required": ["statusCode", "messageCode", "monitor"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "monitor": {"$ref": "#/components/schemas/Monitor"}}}, "CreateOrUpdateMonitorBadRequestResponse": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "bad_request", "body_parse_error", "invalid_name", "invalid_status", "invalid_input_parameters", "invalid_notifyOnCapturedScreenshotChange", "invalid_notifyOnCapturedTextChange", "invalid_capturedScreenshotNotificationThreshold", "invalid_schedules", "invalid_schedule", "invalid_monitor_id"], "example": "bad_request"}}}, "CreateOrUpdateMonitorForbiddenResponse": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [403]}, "messageCode": {"type": "string", "enum": ["schedule_interval_below_minimum"], "example": "schedule_interval_below_minimum"}}}, "getMonitor-200": {"type": "object", "required": ["statusCode", "messageCode", "monitor"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "monitor": {"$ref": "#/components/schemas/Monitor"}}}, "getMonitor-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "bad_request", "invalid_monitor_id"], "example": "bad_request"}}}, "deleteMonitor-200": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}}}, "deleteMonitor-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "invalid_monitor_id"], "example": "invalid_robot_id"}}}, "MonitorUpdateBodyParams": {"type": "object", "properties": {"name": {"type": "string", "example": "Monitor Products", "description": "Monitor name", "nullable": true}, "status": {"type": "string", "enum": ["active", "paused"], "example": "active", "description": "If set to `paused`, the monitor will stop working until an `active` status is sent.", "nullable": true}, "inputParameters": {"$ref": "#/components/schemas/InputParameters", "nullable": true}, "schedules": {"$ref": "#/components/schemas/Schedules"}, "schedule": {"$ref": "#/components/schemas/Schedule"}, "notifyOnCapturedScreenshotChange": {"type": "boolean", "example": true, "description": "If set to `true`, an email notification will be sent to you when a change is detected in captured screenshots.", "nullable": true}, "notifyOnCapturedTextChange": {"type": "boolean", "example": true, "description": "If set to `true`, an email notification will be sent to you when a change is detected in captured texts.", "nullable": true}, "capturedScreenshotNotificationThreshold": {"type": "number", "example": 15, "description": "The \"screenshot changed\" email notification will be sent to you if the change is greater than this threshold (in percent).", "nullable": true}}}, "updateMonitor-200": {"type": "object", "required": ["statusCode", "messageCode", "monitor"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "monitor": {"$ref": "#/components/schemas/Monitor"}}}, "BulkRun": {"type": "object", "required": ["id", "tasksCount", "robotId", "createdAt"], "properties": {"id": {"type": "string", "example": "f6fb62b6-f06a-4bf7-a623-c6a35c2e70b0", "description": "Unique bulk run ID"}, "title": {"type": "string", "nullable": true, "example": "Bulk Run Title", "description": "An optional string that describes the bulk run.", "minLength": 1, "maxLength": 200}, "tasksCount": {"type": "integer", "example": 10, "description": "Number of tasks under this bulk run."}, "robotId": {"type": "string", "example": "4f5cd7ff-6c98-4cac-8cf0-d7d0cb050b06", "description": "Unique robot ID"}, "createdAt": {"type": "integer", "example": *************, "description": "Bulk run creation date and time in the form of a Unix timestamp."}}}, "BulkRuns": {"type": "object", "required": ["totalCount", "pageNumber", "hasMore", "items"], "properties": {"totalCount": {"type": "integer", "example": 20, "description": "Total number of bulk runs a robot has had."}, "pageNumber": {"type": "integer", "example": 1, "description": "Current page number."}, "hasMore": {"type": "boolean", "example": true, "description": "Whether there are more bulk runs on the next page."}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/BulkRun"}}}}, "getBulkRuns-200": {"type": "object", "required": ["statusCode", "messageCode", "result"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "result": {"$ref": "#/components/schemas/BulkRuns"}}}, "getBulkRuns-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["bad_request", "invalid_robot_id"], "example": "bad_request"}}}, "ArrayOfUserInputParameters": {"type": "array", "description": "An array of input parameters to override the task's default input parameters.", "example": [{"originUrl": "https://www.ycombinator.com/companies/airbnb", "companies_skip": 0, "companies_limit": 10}, {"originUrl": "https://www.ycombinator.com/companies/coinbase", "companies_skip": 0, "companies_limit": 20}], "items": {"$ref": "#/components/schemas/InputParameters"}}, "BulkRunBodyParams": {"type": "object", "required": ["inputParameters"], "properties": {"title": {"type": "string", "example": "Bulk Run Title", "description": "A string that describes the bulk run.", "minLength": 1, "maxLength": 200}, "inputParameters": {"$ref": "#/components/schemas/ArrayOfUserInputParameters"}}}, "newBulkRun-200": {"type": "object", "required": ["statusCode", "messageCode", "result"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "result": {"type": "object", "properties": {"bulkRun": {"$ref": "#/components/schemas/BulkRun"}}}}}, "newBulkRun-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["bad_request", "body_parse_error", "invalid_robot_id", "invalid_input_parameters", "zero_length_parameters"], "example": "bad_request"}}}, "newBulkRun-403": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [403]}, "messageCode": {"type": "string", "enum": ["credits_limit_reached", "exceeded_bulk_run_threshold"], "example": "exceeded_bulk_run_threshold"}}}, "RobotTasks": {"type": "object", "description": "A paginated list of tasks.", "required": ["totalCount", "pageNumber", "hasMore", "items"], "properties": {"totalCount": {"type": "integer", "example": 20, "description": "Total number of tasks."}, "pageNumber": {"type": "integer", "example": 1, "description": "Current page number."}, "hasMore": {"type": "boolean", "example": true, "description": "Whether there are more tasks on the next page."}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RobotTask"}}}}, "getBulkRun-200": {"type": "object", "required": ["statusCode", "messageCode", "result"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "result": {"type": "object", "required": ["bulkRun", "robotTasks"], "properties": {"bulkRun": {"$ref": "#/components/schemas/BulkRun"}, "robotTasks": {"$ref": "#/components/schemas/RobotTasks"}}}}}, "getBulkRun-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["bad_request", "invalid_robot_id"], "example": "bad_request"}}}, "Webhook": {"type": "object", "required": ["id", "url", "webhookEvent", "createdAt"], "properties": {"id": {"type": "string", "example": "6d7f1218-43fb-4735-ac71-21e81b1ab23e", "description": "Unique webhook ID"}, "url": {"type": "string", "example": "https://example.com/v2/webhooks/callback/events", "description": "Webhook URL"}, "webhookEvent": {"type": "string", "enum": ["taskCapturedDataChanged", "taskFinished", "taskFinishedSuccessfully", "taskFinishedWithError"], "example": "taskFinished"}, "createdAt": {"type": "integer", "example": *************, "description": "Monitor creation date and time in the form of a Unix timestamp."}}}, "getWebhooks-200": {"type": "object", "required": ["statusCode", "messageCode", "webhooks"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "webhooks": {"type": "object", "required": ["totalCount", "items"], "properties": {"totalCount": {"type": "number", "example": 10, "description": "Total number of webhooks this robot has"}, "items": {"type": "array", "description": "Array of all webhooks", "items": {"$ref": "#/components/schemas/Webhook"}}}}}}, "getWebhooks-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "bad_request"], "example": "bad_request"}}}, "CreateNewWebhookBodyParams": {"type": "object", "required": ["hookUrl", "eventType"], "properties": {"hookUrl": {"type": "string", "example": "https://example.com/v2/webhooks/callback/events", "description": "Webhook URL"}, "eventType": {"type": "string", "enum": ["taskCapturedDataChanged", "taskFinished", "taskFinishedSuccessfully", "taskFinishedWithError"], "example": "taskFinished"}}}, "createNewWebhook-200": {"type": "object", "required": ["statusCode", "messageCode", "webhook"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}, "webhook": {"$ref": "#/components/schemas/Webhook"}}}, "createNewWebhook-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "bad_request", "body_parse_error", "invalid_hookUrl", "invalid_eventType"], "example": "bad_request"}}}, "deleteWebhook-200": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [200]}, "messageCode": {"type": "string", "enum": ["success"]}}}, "deleteWebhook-400": {"type": "object", "required": ["statusCode", "messageCode"], "properties": {"statusCode": {"type": "number", "enum": [400]}, "messageCode": {"type": "string", "enum": ["invalid_robot_id", "invalid_webhook_id", "bad_request"], "example": "invalid_robot_id"}}}, "RobotTaskWebhook": {"type": "object", "required": ["id", "inputParameters", "robotId", "createdAt", "runByAPI", "triedRecordingVideo", "capturedTexts", "capturedScreenshots", "capturedLists"], "properties": {"id": {"type": "string", "example": "f6fb62b6-f06a-4bf7-a623-c6a35c2e70b0", "description": "Unique task ID"}, "inputParameters": {"$ref": "#/components/schemas/InputParameters"}, "robotId": {"type": "string", "example": "4f5cd7ff-6c98-4cac-8cf0-d7d0cb050b06", "description": "Unique robot ID"}, "status": {"type": "string", "enum": ["failed", "successful", "in-progress"], "example": "successful", "description": "task status"}, "runByUserId": {"type": "string", "nullable": true, "example": null, "description": "User ID who ran the robot on the dashboard"}, "robotBulkRunId": {"type": "string", "nullable": true, "example": null, "description": "Robot bulk run ID associated with this task"}, "runByTaskMonitorId": {"type": "string", "nullable": true, "example": null, "description": "Monitor ID that ran this check"}, "runByAPI": {"type": "boolean", "example": true, "description": "Whether the robot was ran through the API"}, "createdAt": {"type": "integer", "example": *************, "description": "Task creation date and time in the form of a Unix timestamp"}, "startedAt": {"type": "integer", "nullable": true, "example": *************, "description": "Task start date and time in the form of a Unix timestamp"}, "finishedAt": {"type": "integer", "nullable": true, "example": *************, "description": "Task finish date and time in the form of a Unix timestamp.\n\nIf `null`, it means robot is still running and capturing data.\n\nTasks time out with an error if they are not finished within 15 minutes (or the maximum duration allowed on your plan).\n"}, "userFriendlyError": {"type": "string", "nullable": true, "example": null, "description": "If task fails, a user-friendly error will be provided here."}, "triedRecordingVideo": {"type": "boolean", "example": true, "description": "Whether the robot tried to record a video while performing this task.\n\nYou can change a robot's video recording setting on its Settings page. \n\nRobots try to record a video when a task is failed and auto-retried as well.\n"}, "videoUrl": {"type": "string", "nullable": true, "example": "https://prod-browseai-captured-data.s3.amazonaws.com/1fae674a-2788-46a8-83c8-95c4664c6d25/6326b3c1-7b16-4256-a323-7d8d8954bd4e/1061671f-7f71-42ac-bb9a-207d126d1f3a/system-**********-b1a9688b-05d3-4682-beeb-9ce035e482b1.mp4", "description": "If a video was recorded for this task, this is the link to the video."}, "videoRemovedAt": {"type": "integer", "nullable": true, "example": *************, "description": "After your account's data retention period, task videos get removed and  this field will be video removal date and time in the form of a Unix timestamp.\n"}, "retriedOriginalTaskId": {"type": "string", "nullable": true, "example": "673da019-bf0c-476e-9c4f-d35252a151dc", "description": "The ID of the original failed task this task was retrying. For example, if task A failed and was retried by task B, and task B was retried by task C, `retriedOriginalTaskId` will point to task A in both task B and C."}, "retriedByTaskId": {"type": "string", "nullable": true, "example": null, "description": "The ID of the task that retried this task, if this task failed with an error and was retried.\n\nFailed tasks get retried if \"Double Check\" option is enabled on robot Settings page. \"Double Check\" is enabled by default.\n"}, "capturedTexts": {"$ref": "#/components/schemas/CapturedTexts"}, "capturedScreenshots": {"$ref": "#/components/schemas/CapturedScreenshots"}, "capturedLists": {"$ref": "#/components/schemas/CapturedLists"}}}}, "parameters": {"authorization": {"in": "header", "name": "authorization", "required": true, "schema": {"type": "string"}, "example": "Bearer YOUR_SECRET_API_KEY", "description": "You can generate a new API key on [your dashboard](https://dashboard.browse.ai/api) if you do not have one.\n"}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}