---
tags:
  - AI/Theory
  - AI/Programming
Created: 2024-08-29T17:52
---
Loss Function
BCE loss and Focal Loss
FuneTuning

# Loss Function

## BCE loss and Focal Loss

```JavaScript
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
import torch
import torch.nn as nn
import torch.optim as optim
# 设置随机种子以确保结果可重现
np.random.seed(42)
torch.manual_seed(42)
# 生成不平衡数据集
X, y = make_classification(n_samples=10000, n_features=2, n_informative=2,
                           n_redundant=0, n_repeated=0, n_classes=2,
                           n_clusters_per_class=1, weights=[0.9, 0.1],
                           class_sep=0.8, random_state=42)
# 将数据集分为训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# 转换为PyTorch张量
X_train = torch.FloatTensor(X_train)
y_train = torch.FloatTensor(y_train)
X_test = torch.FloatTensor(X_test)
y_test = torch.FloatTensor(y_test)
# 定义简单的神经网络模型
class SimpleNet(nn.Module):
    def __init__(self):
        super(SimpleNet, self).__init__()
        self.fc = nn.Sequential(
            nn.Linear(2, 10),
            nn.ReLU(),
            nn.Linear(10, 1)
        )
  
    def forward(self, x):
        return torch.sigmoid(self.fc(x))
# 实现Focal Loss
class FocalLoss(nn.Module):
    def __init__(self, alpha=0.25, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
  
    def forward(self, inputs, targets):
        BCE_loss = nn.BCELoss(reduction='none')(inputs, targets)
        pt = torch.exp(-BCE_loss)
        F_loss = self.alpha * (1-pt)**self.gamma * BCE_loss
        return torch.mean(F_loss)
# 训练函数
def train_model(model, criterion, optimizer, num_epochs=100):
    losses = []
    for epoch in range(num_epochs):
        model.train()
        optimizer.zero_grad()
        outputs = model(X_train).squeeze()
        loss = criterion(outputs, y_train)
        loss.backward()
        optimizer.step()
        losses.append(loss.item())
    return losses
# 评估函数
def evaluate_model(model):
    model.eval()
    with torch.no_grad():
        outputs = model(X_test).squeeze()
        predictions = (outputs > 0.5).float()
        accuracy = (predictions == y_test).float().mean()
    return accuracy.item()
# 训练和评估使用BCE损失的模型
model_bce = SimpleNet()
criterion_bce = nn.BCELoss()
optimizer_bce = optim.Adam(model_bce.parameters(), lr=0.01)
losses_bce = train_model(model_bce, criterion_bce, optimizer_bce)
accuracy_bce = evaluate_model(model_bce)
# 训练和评估使用Focal损失的模型
model_focal = SimpleNet()
criterion_focal = FocalLoss()
optimizer_focal = optim.Adam(model_focal.parameters(), lr=0.01)
losses_focal = train_model(model_focal, criterion_focal, optimizer_focal)
accuracy_focal = evaluate_model(model_focal)
# 绘制损失曲线
plt.figure(figsize=(10, 5))
plt.plot(losses_bce, label='BCE Loss')
plt.plot(losses_focal, label='Focal Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.title('Training Loss Comparison')
plt.legend()
plt.show()
print(f"BCE Loss Accuracy: {accuracy_bce:.4f}")
print(f"Focal Loss Accuracy: {accuracy_focal:.4f}")
```

# FuneTuning

## Reference
[[大模型面经——LoRA最全总结]]
[“Llama-3.2 1B+3B Conversational + 2x faster finetuning.ipynb]
(https://colab.research.google.com/drive/1TS8ezuMBJADN6CWXZedsJ5dGrdtyM8TM)

## Kiln AI Tool
[[🦜🦜🦜Kiln-AI Kiln The easiest tool for fine-tuning LLM models, synthetic data generation, and collaborating on datasets]]
## How to check if we can improve model by adding more data
[[Should you gather more data]]

## Lora and PEFT 
[[Notion/AI_V2/Implementing LoRA From Scratch for Fine-tuning LLMs|Implementing LoRA From Scratch for Fine-tuning LLMs]]
```
 LoRA 插入位置的优化策略
可以根据经验或实验选择关键层：

输出层附近：靠近任务输出的层通常与任务相关性更强。
高参数量层：优先选择参数量大的层（如 Transformer 的多头注意力层）。
关键的中间层：例如 BERT 的中间层往往捕获了丰富的语义特征。

逐步测试：
先在最后几层尝试添加 LoRA，如果效果不好，再逐步扩展到其他层。
只加部分层：
针对关键层（如多头注意力层或特定的全连接层）添加 LoRA。
全量加权实验：
在所有层添加 LoRA，然后分析不同层对性能的贡献，去掉对任务影响较小的层。

```
## Training Reasoning with unsloth
[[🦜🦜🦜Train your own R1 reasoning model locally]]

# Performance Tricks

## cuDF for Pandas

cuDF is a Python GPU DataFrame library (built on the Apache Arrow columnar memory format) for loading, joining, aggregating, filtering, and otherwise manipulating tabular data using a DataFrame style API in the style of pandas.

cuDF now provides a pandas accelerator mode (cudf.pandas), allowing you to bring accelerated computing to your pandas workflows without requiring any code change.

This notebook is a short introduction to cudf.pandas.
https://colab.research.google.com/drive/1STF6fZr8JCRNg95y3BQEatjH1mHxRTBi


##  activation checkpointing and how it’s used to cope with limited GPU memory. 
https://youtu.be/9yhFzRepKsc

# 图像处理

## 随机生成图像数据

```python
import numpy as np
from PIL import Image

class CustomDataset(Dataset):
    def __init__(self, transform=None):
        self.transform = transform
      
        self.data = [Image.fromarray(np.uint8(np.random.rand(256, 256, 3) * 255)) \
                     for _ in range(10)]
  
    def __len__(self):
        return len(self.data)
  
    def __getitem__(self, idx):
        image = self.data[idx]
      
        if self.transform:
            image = self.transform(image)
      
        return image
```

[[Can I pre-transform images when training a deep learning model]]

## Image embedding

```python
import requests

# You can use your own uploaded images and captions. 
# You will be responsible for the legal use of images that 
#  you are going to use.

url1='http://farm3.staticflickr.com/2519/4126738647_cc436c111b_z.jpg'
cap1='A motorcycle sits parked across from a herd of livestock'

url2='http://farm3.staticflickr.com/2046/2003879022_1b4b466d1d_z.jpg'
cap2='Motorcycle on platform to be worked on in garage'

url3='http://farm1.staticflickr.com/133/356148800_9bf03b6116_z.jpg'
cap3='a cat laying down stretched out near a laptop'

img1 = {
  'flickr_url': url1,
  'caption': cap1,
  'image_path' : './shared_data/motorcycle_1.jpg'
}

img2 = {
    'flickr_url': url2,
    'caption': cap2,
    'image_path' : './shared_data/motorcycle_2.jpg'
}

img3 = {
    'flickr_url' : url3,
    'caption': cap3,
    'image_path' : './shared_data/cat_1.jpg'
}

# download images
imgs = [img1, img2, img3]
for img in imgs:
    data = requests.get(img['flickr_url']).content
    with open(img['image_path'], 'wb') as f:
        f.write(data)
  
import json
import os
import numpy as np
from numpy.linalg import norm
import cv2
from umap import UMAP
from sklearn.preprocessing import MinMaxScaler
import pandas as pd
from tqdm import tqdm
from utils import encode_image
from utils import bt_embedding_from_prediction_guard as bt_embeddings

embeddings = []
for img in [img1, img2, img3]:
    img_path = img['image_path']
    caption = img['caption']
    base64_img = encode_image(img_path)
    embedding = bt_embeddings(caption, base64_img)
    embeddings.append(embedding)

```

[[Useful Python toolkit for image embedding and youtube video download]]

![[Pasted image 20240913230129.png]]

## 使用多模态模型的例子
/opt/workspace/researcher2/echohive/dall-e-3-experiments
/opt/workspace/researcher2/echohive/gpt_plays_tetris
[[🦜🦜🦜PaliGemma    Google for Developers]]
[[openai-CLIP- CLIP (Contrastive Language-Image Pretraining),  Predict the most relevant text snippet given an image]]
[[GitHub - OpenGVLab-InternVL- CVPR 2024 Oral InternVL Family- A Pioneering Open-Source Alternative to GPT-4V. 接近GPT-4V表现的可商用开源多模态对话模型]]



# 性能优化
[[15 Ways to Optimize Neural Network Training]]
[[15 Ways to Optimize Neural Network Training (With Implementation)]]

![[9e7c9435-9466-4216-b41e-82da94afedc5_952x1074.gif]]

## Numba JIT 
```python
def func_without_numba():
    result = []
    for a in range(10000):
        for b in range(10000):
            if (a+b) % 11 == 0:
                result.append((a,b))

func_without_numba()
# Run-time: 8.34 sec

from numba import njit

@njit
def func_with_numba():
    # same code

func_with_numba()
# Run-time: 0.25 sec

```

## Pytorch


/opt/workspace/app/cursor/RayLab/AllKindsOfTest.ipynb

```python
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.profiler import profile, record_function, ProfilerActivity
import matplotlib.pyplot as plt
import numpy as np

# Define a simple model and dataset (as before)
class SimpleModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.fc = nn.Linear(10, 5)

    def forward(self, x):
        return self.fc(x)

dataset = torch.randn(1000, 10)
dataloader = DataLoader(dataset, batch_size=32)

model = SimpleModel()
criterion = nn.MSELoss()
optimizer = torch.optim.SGD(model.parameters(), lr=0.01)

# Profiler with more detailed activities
activities = [
    ProfilerActivity.CPU,
    ProfilerActivity.CUDA,
]

def train_and_profile(num_steps):
    memcpy_time = []
    dataloader_time = []
    kernel_time = []
  
    with profile(activities=activities, record_shapes=True, profile_memory=True) as prof:
        for i, data in enumerate(dataloader):
            if i >= num_steps:
                break
          
            with record_function("memcpy"):
                # Simulate memcpy operation
                torch.cuda.synchronize()
          
            with record_function("dataloader"):
                # Dataloader operations are already timed
                pass
          
            with record_function("kernel"):
                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, torch.randn(32, 5))
                loss.backward()
                optimizer.step()
          
            prof.step()
  
    events = prof.key_averages()
    for event in events:
        if "memcpy" in event.key:
            memcpy_time.append(event.cpu_time)
        elif "dataloader" in event.key:
            dataloader_time.append(event.cpu_time)
        elif "kernel" in event.key:
            kernel_time.append(event.cpu_time)
  
    return memcpy_time, dataloader_time, kernel_time

# Run profiling
num_steps = 10
memcpy_time, dataloader_time, kernel_time = train_and_profile(num_steps)

# Create stacked area plot
fig, ax = plt.subplots(figsize=(10, 6))

x = range(num_steps)
y = np.row_stack((memcpy_time, dataloader_time, kernel_time))

ax.stackplot(x, y, labels=['Memcpy', 'enumerate(dataloader)', 'Kernel'],
             colors=['#8dd3c7', '#80b1d3', '#fb8072'])

ax.set_xlabel('Time/Steps')
ax.set_ylabel('Resource/Time')
ax.set_title('PyTorch Profiler Results')
ax.legend(loc='upper left')

# Remove top and right spines
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)

plt.tight_layout()
plt.show()
```

## Improve dataloader speed

```
To improve dataloader performance, which seems to be a bottleneck in your case, consider:

Increasing the number of worker processes in your DataLoader.
Using pin_memory=True if you're using GPU.
Optimizing your dataset's __getitem__ method.
Using prefetch_factor to load data in advance.

Would you like more details on any part of this process?
```

### suggestion given by Cursor

Based on the PyTorch Profiler results shown in the image, it's clear that a significant portion of the execution time is spent on memory copy operations (Memcpy). This is a common bottleneck in GPU-accelerated applications, including those using PyTorch. Here are some strategies to potentially improve performance:
Use Pinned Memory:
When transferring data between CPU and GPU, use pinned (page-locked) memory. This can significantly speed up memory transfers.
   tensor = torch.tensor([1, 2, 3]).pin_memory()
Prefetch Data:
Use asynchronous data loading and prefetching to overlap data transfer with computation.
   dataloader = torch.utils.data.DataLoader(..., pin_memory=True, num_workers=4)
Reduce CPU to GPU Transfers:
Keep data on the GPU as much as possible. Avoid unnecessary transfers between CPU and GPU.
Use Mixed Precision Training:
Utilize mixed precision training (e.g., float16) to reduce memory usage and potentially speed up operations.
   from torch.cuda.amp import autocast
   with autocast():
       # Your model forward pass
Optimize Batch Size:
Experiment with different batch sizes to find an optimal balance between memory usage and computational efficiency.
Use torch.nn.DataParallel or DistributedDataParallel:
If you have multiple GPUs, consider using data parallelism to distribute the workload.
Profile and Optimize Data Preprocessing:
Ensure that data preprocessing steps are efficient and consider moving some preprocessing to the GPU if possible.
Use torch.utils.data.DataLoader with num_workers:
Increase the number of worker processes for data loading to parallelize data preparation.
Consider Using NVIDIA DALI:
For image-based tasks, NVIDIA DALI can significantly speed up data loading and preprocessing.
Optimize Model Architecture:
If possible, review and optimize your model architecture to reduce unnecessary data movement.
To get more specific recommendations, it would be helpful to see the code for your data loading pipeline and model architecture. Remember to profile your code after making changes to ensure that the optimizations are effective for your specific use case

```python
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.profiler import profile, record_function, ProfilerActivity
import matplotlib.pyplot as plt
import numpy as np
from torch.cuda.amp import autocast, GradScaler


scaler = GradScaler()

# Define a simple model and dataset (as before)
class SimpleModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.fc = nn.Linear(10, 5)

    def forward(self, x):
        return self.fc(x)

dataset = torch.randn(1000, 10)
dataloader = DataLoader(dataset, batch_size=32, pin_memory=True, num_workers=4)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = SimpleModel().to(device)
criterion = nn.MSELoss()
optimizer = torch.optim.SGD(model.parameters(), lr=0.01)

# Profiler with more detailed activities
activities = [
    ProfilerActivity.CPU,
    ProfilerActivity.CUDA,
]

def train_and_profile(num_steps):
    memcpy_time = []
    dataloader_time = []
    kernel_time = []
    
    with profile(activities=activities, record_shapes=True, profile_memory=True) as prof:
        for i, data in enumerate(dataloader):
            if i >= num_steps:
                break
            
            data = data.to(device, non_blocking=True)
            
            with record_function("memcpy"):
                # Simulate memcpy operation
                torch.cuda.synchronize()
            
            with record_function("dataloader"):
                # Dataloader operations are already timed
                pass
            
            with record_function("kernel"):
                optimizer.zero_grad()
                with autocast(dtype=torch.float16):
                    output = model(data)
                    # Create target tensor on the same device as output
                    target = torch.randn(data.size(0), 5, device=device)
                    loss = criterion(output, target)
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
            
            prof.step()
    
    events = prof.key_averages()
    for event in events:
        if "memcpy" in event.key:
            memcpy_time.append(event.cpu_time)
        elif "dataloader" in event.key:
            dataloader_time.append(event.cpu_time)
        elif "kernel" in event.key:
            kernel_time.append(event.cpu_time)
    
    return memcpy_time, dataloader_time, kernel_time

# Run profiling
num_steps = 10
memcpy_time, dataloader_time, kernel_time = train_and_profile(num_steps)

# Create stacked area plot
fig, ax = plt.subplots(figsize=(10, 6))

x = range(num_steps)
y = np.row_stack((memcpy_time, dataloader_time, kernel_time))

ax.stackplot(x, y, labels=['Memcpy', 'enumerate(dataloader)', 'Kernel'],
             colors=['#8dd3c7', '#80b1d3', '#fb8072'])

ax.set_xlabel('Time/Steps')
ax.set_ylabel('Resource/Time')
ax.set_title('PyTorch Profiler Results')
ax.legend(loc='upper left')

# Remove top and right spines
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)

plt.tight_layout()
plt.show()

```

Above is code after tuning

## 🦋Use pytorch lightning
[[A Detailed and Beginner-Friendly Introduction to PyTorch Lightning The Supercharged PyTorch]]
```python
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.profiler import profile, record_function, ProfilerActivity
import matplotlib.pyplot as plt
import numpy as np
import pytorch_lightning as pl
from torch.utils.data import TensorDataset
import torchmetrics

pl.seed_everything(42)

# Define a simple model and dataset (as before)
class SimpleModel(pl.LightningModule):
    def __init__(self):
        super(SimpleModel, self).__init__()
        self.fc = nn.Linear(10, 5)
        self.criterion = nn.CrossEntropyLoss()
        # Initialize Accuracy and Precision metrics with required arguments
        self.accuracy = torchmetrics.Accuracy(task='multiclass', num_classes=5)
        self.precision = torchmetrics.Precision(task='multiclass', num_classes=5, average='macro')
        self.f1 = torchmetrics.F1Score(task='multiclass', num_classes=5, average='macro')
        self.save_hyperparameters()

        
    def forward(self, x):
        return self.fc(x)
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self.forward(x)
        loss = self.criterion(y_hat, y)
        # Convert model outputs and targets to class predictions
        predictions = torch.argmax(y_hat, dim=1)
        targets = torch.argmax(y, dim=1)
        
        # Compute metrics
        acc = self.accuracy(predictions, targets)
        prec = self.precision(predictions, targets)
        f1 = self.f1(predictions, targets)
        
        # Log metrics
        self.log("train_loss", loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log("train_accuracy", acc, on_step=True, on_epoch=True, prog_bar=True)
        self.log("train_precision", prec, on_step=True, on_epoch=True, prog_bar=True)
        self.log("train_f1", f1, on_step=True, on_epoch=True, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self.forward(x)
        loss = self.criterion(y_hat, y)
        # Convert model outputs and targets to class predictions
        predictions = torch.argmax(y_hat, dim=1)
        targets = torch.argmax(y, dim=1)
        
        # Compute metrics
        acc = self.accuracy(predictions, targets)
        prec = self.precision(predictions, targets)
        f1 = self.f1(predictions, targets)
        # Log metrics
        self.log("val_loss", loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log("val_accuracy", acc, on_step=False, on_epoch=True, prog_bar=True)
        self.log("val_precision", prec, on_step=False, on_epoch=True, prog_bar=True)
        self.log("val_f1", f1, on_step=False, on_epoch=True, prog_bar=True)
        return loss
    
    def configure_optimizers(self):
        return torch.optim.SGD(self.parameters(), lr=0.01)
    
    def predict_step(self, batch, batch_idx):
        x = batch
        y_hat = self.forward(x)
        predictions = torch.argmax(y_hat, dim=1)
        return predictions
    
    def train_dataloader(self):
        # Generate random data for inputs and targets
        x = torch.randn(1000, 10) * 0.1
        y = torch.randn(1000, 5) * 0.1
        dataset = TensorDataset(x, y)
        return DataLoader(dataset, batch_size=32, pin_memory=True, num_workers=4)
    
    def val_dataloader(self):
        x_val = torch.randn(200, 10) * 0.1
        y_val = torch.randn(200, 5) * 0.1
        val_dataset = TensorDataset(x_val, y_val)
        return DataLoader(val_dataset, batch_size=32, pin_memory=True, num_workers=4)        


device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = SimpleModel().to(device)

trainer = pl.Trainer(max_epochs=10, enable_checkpointing=True, precision=16, 
                     callbacks=[pl.callbacks.ModelCheckpoint(
                         dirpath="checkpoints/", 
                         monitor="val_loss",
                         mode="min", 
                         save_top_k=3, 
                         filename="best_model.pt")])
trainer.fit(model)

trainer.validate(model)



```
###  Distribute GPU training(DP)
limitation : One Server multiple GPU
```python
# Declare Trainer object for multi-GPU training
trainer = pl.Trainer(max_epochs=5,
                     accelerator="gpu",
                     devices=2,
                     strategy="dp")
```

### Distributed Data Parallel
```python
# Declare Trainer object for multi-GPU training
trainer = pl.Trainer(max_epochs=5,
                     accelerator="gpu",
                     devices=2,
                     strategy="ddp")

```
## Use accelerate package
[[A Practical Guide to Scaling ML Model Training]]

## Control GPU Fan

#### Speed fan to 199%

```python
import pynvml

pynvml.nvmlInit()

# force fan speed to 100%
for i in range(pynvml.nvmlDeviceGetCount()):
    handle = pynvml.nvmlDeviceGetHandleByIndex(i)
    fan_count = pynvml.nvmlDeviceGetNumFans(handle)
    for fan_index in range(fan_count):
        try:
            pynvml.nvmlDeviceSetFanSpeed_v2(handle, fan_index, speed=100)
        except pynvml.NVMLError as err:
            print(f"Failed to set fan speed of fan {fan_index} of GPU {i} to 100%")
```

set FAN to default

```python
import pynvml

pynvml.nvmlInit()

# force fan speed to default
for i in range(pynvml.nvmlDeviceGetCount()):
    handle = pynvml.nvmlDeviceGetHandleByIndex(i)
    fan_count = pynvml.nvmlDeviceGetNumFans(handle)
    for fan_index in range(fan_count):
        try:
            pynvml.nvmlDeviceSetDefaultFanSpeed_v2(handle, fan_index)
        except pynvml.NVMLError as err:
            print(f"Failed to set fan speed of fan {fan_index} of GPU {i} to default")
```

---

# Rag Technology

jetson : /opt/workspace/app/cursor/rag-tech
https://studio.albus.org/board/00e03954d0e87842c868c657b0a99d9c9bc45a5c

# 合成数据

## Klin Tool

[[🦜🦜🦜Kiln-AI Kiln The easiest tool for fine-tuning LLM models, synthetic data generation, and collaborating on datasets]]
## distilable

### youtube

https://www.youtube.com/watch?v=8E01Xvc2ybk

### dataset

https://huggingface.co/datasets?other=distilabel

### github

https://github.com/argilla-io/distilabel

```python
import pandas as pd
from distilabel.llms import OllamaLLM
from distilabel.pipeline import Pipeline
from distilabel.steps import LoadDataFromHub, CombineColumns, tasks

llama8B=OllamaLLM(model="llama5.1", host=host_llama_8b)
llama70B=OllamaLLM(model="llama3.1:70b-instruct-q2_k", host=host_llama_70b)

with Pipeline(name="preference-datagen-llama3.1") as pipeline:
    # Load dataset with prompts
    load_dataset = LoadDataFromHub(
        name="load_dataset",
        output_mappings={"prompt": "instruction"},
    )
    # generate two responses
    generate = {
        tasks.TextGeneration(name='text_generation_8B', llm=llama8B),
        tasks.TextGeneration(name='text_generation_70B', llm=llama70B)
    }
    # combine responses into one col
    combine = CombineColumns(
        columns=["generation", "model_name"],
        output_columns=["generations", "model_names"]
    )
    # rate responses with 495B LLM-as-a-judge
    evaluate = tasks.UltraFeedback(aspect="overall-rating", llm=llama70B)

    # define and run pipeline
    load_dataset >> generate >> combine >> evaluate
```

### push to huggingface

https://qiita.com/mitzukan/items/da90ff9905c2d5df92a2
https://distilabel.argilla.io/1.2.2/sections/how_to_guides/advanced/distiset/#save-and-load-from-disk

### example

https://distilabel.argilla.io/1.3.2/sections/pipeline_samples/examples/

#### using pipeline

```python
import pandas as pd
from distilabel.llms import OllamaLLM
from distilabel.llms import OpenAILLM
from distilabel.pipeline import Pipeline
from distilabel.steps import LoadDataFromHub, CombineColumns, tasks
from pydantic import BaseModel
from distilabel.llms import LlamaCppLLM
from distilabel.steps.tasks import SelfInstruct
from datasets import Dataset
from distilabel.steps import LoadDataFromDicts
from distilabel.steps.tasks import TextGeneration

with Pipeline(
    name="self-instruct-pipeline",
    description=(
    "An AI assistant adept at answering a wide array of math, logic, and reasoning puzzles, trivia, "
    "and general questions. Users of this assistant love to ask the assistant to think and outlines "
    "the solutions step by step. It expects complete questions from users providing all the details "
    "to solve the proposed problem or respond to general knowledge questions. It covers general "
    "knowledge about math, puzzles, reasoning exercises, and real-life scenarios where math and "
    "reasoning are important."      
    )
) as pipeline:
    math_topics = [
    "Addition",
    "Subtraction",
    "Multiplication",
    "Division",
    "Algebra",
    "Geometry",
    "Trigonometry",
    "Calculus",
    "Statistics",  
    ]
  
    load_dataset = LoadDataFromDicts(
        name="load_instructions",
        data=[{
            "system_prompt": "You are a helpful assistant that can generate questions about math.",
            "input": f"{math_topic}",
        } for math_topic in math_topics],
    )

    self_Instruct = SelfInstruct(
        name="self_instruct_math",
        llm = OpenAILLM(model="gpt-4o-mini"),
        #llm=OllamaLLM(model="llama3.1", host="http://************:8234/v1"),
        num_instructions=1,
    )

    load_dataset >> self_Instruct

distiset = pipeline.run(
    parameters={
        self_Instruct.name: {
            "llm": {"generation_kwargs": {"max_new_tokens": 2048}}
        }
    },
    use_cache=False,
)

import json
# Print out the 9 rows
for i, row in enumerate(distiset['default']['train']):
    print(f"Row {i + 1}:")
    print(json.dumps(row, indent=2))
    print("\n")

# Save to disk
output_file = 'distiset_data.json'
distiset['default']['train'].to_json(output_file)
print(f"Data saved to {output_file}")

# Save to disk using Distiset's method
output_path = './distiset_output'
distiset.save_to_disk(output_path)
print(f"Data saved to {output_path}")
```

#### Not using pipeline

```python
import pandas as pd
from distilabel.llms import OllamaLLM
from distilabel.llms import OpenAILLM
from distilabel.pipeline import Pipeline
from distilabel.steps import LoadDataFromHub, CombineColumns, tasks
from pydantic import BaseModel
from distilabel.llms import LlamaCppLLM
from distilabel.steps.tasks import SelfInstruct
from datasets import Dataset
from distilabel.steps import LoadDataFromDicts
from distilabel.steps.tasks import TextGeneration

math_topics = [
    "Addition",
    "Subtraction",
    "Multiplication",
    "Division",
    "Algebra",
    "Geometry",
    "Trigonometry",
    "Calculus",
    "Statistics",  
]
llama8B=OllamaLLM(model="llama3.1", host="http://************:8234/v1")
llama8B.load()

application_description = (
    "An AI assistant adept at answering a wide array of math, logic, and reasoning puzzles, trivia, "
    "and general questions. Users of this assistant love to ask the assistant to think and outlines "
    "the solutions step by step. It expects complete questions from users providing all the details "
    "to solve the proposed problem or respond to general knowledge questions. It covers general "
    "knowledge about math, puzzles, reasoning exercises, and real-life scenarios where math and "
    "reasoning are important."
)

# by default 'SelfInstructTask' will generate 5 instructions, but we can tweak
# this behaviour passing the 'num_instructions' argument.
instruction_task = SelfInstruct(
    llm=llama8B,
    application_description=application_description,
    num_instructions=5,
)

instruction_task.load()
result = next(instruction_task.process([{"input": math_topics}]))


```

# 数据集中所有特征的影响

## 连续值特征
![[Pasted image 20240926195443.png]]

```python
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.datasets import make_regression
from sklearn.ensemble import RandomForestRegressor

# Step 1: Create a dummy dataset and train the model
X, y = make_regression(n_samples=1000, n_features=5, noise=0.1)
model = RandomForestRegressor(n_estimators=100, random_state=42)
model.fit(X, y)

# Step 2: Loop through all features and compute ICE and PDP
n_features = X.shape[1]
for feature_index in range(n_features):
    # Define the range of values for the chosen feature
    feature_values = np.linspace(X[:, feature_index].min(), X[:, feature_index].max(), num=10)
    
    # Modify the dataset for each feature value
    X_new = np.tile(X, (len(feature_values), 1))  # Duplicate dataset
    X_new[:, feature_index] = np.repeat(feature_values, X.shape[0])  # Replace selected feature
    
    # Generate predictions for all instances
    predictions = model.predict(X_new)
    predictions = predictions.reshape(len(feature_values), X.shape[0])  # Reshape to (10, 1000)
    
    # Compute the PDP (average of the predictions)
    pdp = np.mean(predictions, axis=1)
    
    # Plot ICE lines for the feature
    plt.figure(figsize=(8, 6))
    for i in range(X.shape[0]):
        plt.plot(feature_values, predictions[:, i], color="lightgray", lw=0.5, alpha=0.6)
    
    # Overlay the PDP (average line)
    plt.plot(feature_values, pdp, color='blue', lw=3, label='PDP (Average)')
    
    # Add rug plot to show the distribution of the feature
    sns.rugplot(X[:, feature_index], height=0.05, color='black', ax=plt.gca())
    
    # Customize plot
    plt.title(f'PDP for Feature-{feature_index}', fontsize=14)
    plt.xlabel(f'Feature-{feature_index} Values', fontsize=12)
    plt.ylabel('Predicted Outcome', fontsize=12)
    plt.legend()
    plt.grid(True)
    plt.show()
```

## 分类值特征
```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.ensemble import RandomForestClassifier

# Step 1: Create a dummy dataset and train the model
X, y = make_classification(n_samples=1000, n_features=5, n_classes=2)
model = RandomForestClassifier(n_estimators=100)
model.fit(X, y)

# Step 2: Loop through all features and compute ICE and PDP
n_features = X.shape[1]

for feature_index in range(n_features):
    # Define the range of values for the chosen feature
    feature_values = np.linspace(X[:, feature_index].min(), X[:, feature_index].max(), num=10)
    
    # Modify the dataset for each feature value
    X_new = np.tile(X, (len(feature_values), 1))  # Duplicate dataset
    X_new[:, feature_index] = np.repeat(feature_values, X.shape[0])  # Replace selected feature

    # Generate predictions for all instances
    probabilities = model.predict_proba(X_new)[:,1]
    probabilities = probabilities.reshape(len(feature_values), X.shape[0])  # Reshape to (10, 1000)

    # Compute the PDP (average of the predictions)
    pdp = np.mean(probabilities, axis=1)

    # Plot ICE lines for the feature
    plt.figure(figsize=(8, 6))
    for i in range(X.shape[0]):
        plt.plot(feature_values, probabilities[:, i], color="lightgray", lw=0.5, alpha=0.6)

    # Overlay the PDP (average line)
    plt.plot(feature_values, pdp, color='blue', lw=3, label='PDP (Average)')

    # Customize plot
    plt.title(f'PDP for Feature-{feature_index}', fontsize=14)
    plt.xlabel(f'Feature-{feature_index} Values', fontsize=12)
    plt.ylabel('Predicted Outcome', fontsize=12)
    plt.legend()
    plt.grid(True)
    plt.show()

```

## python feature
```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.datasets import make_regression
from sklearn.inspection import PartialDependenceDisplay

>>> PartialDependenceDisplay.from_estimator(model,
                                            X,
                                            features=[0])
                                        
PartialDependenceDisplay.from_estimator(model, X,
                                        kind="individual",
                                        features = [1])
PartialDependenceDisplay.from_estimator(model, X,                               kind="both",                                                              features = [1])                                        
```

# 模型压缩和剪枝

## 稀疏化
```
def updateBN():
    for m in model.modules():
        if isinstance(m, nn.BatchNorm2d):
            m.weight.grad.data.add_(args.s*torch.sign(m.weight.data))  # L1 大于0为1 小于0为-1 0还是0
```
# 模型的可视化
[[Torchvista Interactive Pytorch forward pass visualization in notebooks]]
