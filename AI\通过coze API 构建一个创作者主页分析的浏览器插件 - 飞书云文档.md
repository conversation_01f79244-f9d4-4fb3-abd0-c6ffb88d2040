---
Updated: 2024-08-09T11:51
tags:
  - AI->-Tools
URL: https://hcn5fnwbs1kx.feishu.cn/wiki/EdmDw9ya4i9kMakZwLbc37NjnMb
Created: 2024-08-09T10:49
---
[![](https://lf-scm-cn.feishucdn.com/ccm/pc/web/resource/bear/feishu.ico)](https://lf-scm-cn.feishucdn.com/ccm/pc/web/resource/bear/feishu.ico)
==输入“/”快速插入==
==通过coze API 构建一个创作者主页分析的浏览器插件​==
==今天修改==
==借助 Claude 和 API 文档从0开始编写业务代码​==
==掌握浏览器插件的基本工作原理​==
==效果演示​==
==​==
==正在以画中画形式播放==
==重播==
==播放==
==00:00 / 00:28 直播==
==00:24==
==00:24==
==进入全屏==
==画中画==
==1080p==
- ==360p==
- ==1080p==
- ==原画==
==1x==
- ==2x==
- ==1.5x==
- ==1x==
- ==0.75x==
- ==0.5x==
==点击按住可拖动视频==
==liMC9r-k282sLNbE7TJ--6-cpNlk.mp4==
![[Notion/AI/通过coze API 构建一个创作者主页分析的浏览器插件 - 飞书云文档/attachments/Untitled.png|Untitled.png]]
[![](https://www.notion.so)](https://www.notion.so)
==liMC9r-k282sLNbE7TJ--6-cpNlk==
==00:28==
==​==
==项目简介​==
==该项目是一款通过浏览器插件实现的轻量级应用，能够对创作者主页进行 AI 分析并生成报告。​==
==详细步骤​==
==构建该浏览器插件主要分为两大部分，分别是：​==
==构建通过coze构建对创作者内容通过不同维度分析的工作流​==
==构建浏览器拓展并调用通过coze发布的agent的API​==
==构建内容分析的coze工作流​==
==工作流​==
==此工作流为相当简单的工作流，主要包含1个模块：大模型接受输入并输出分析结果（此处不用工作流也可以，直接使用agent），具体如下：​==
==​==
==​==
==Prompt​==
==此处分享我的Prompt，大家可以直接复制使用：​==
==​==
==💡==
==​==
==​==
==​==
[](https://www.notion.soundefined)
==评论（0）==
==跳转至首条评论==
==0 字==