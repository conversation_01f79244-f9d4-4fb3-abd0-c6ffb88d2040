{
  "openapi": "3.1.0",
  "info": {
    "title": "Notion Search API Integration",
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "https://api.notion.com/v1"
    }
  ],
  "paths": {
    "/search": {
      "post": {
        "summary": "Search in Notion",
        "operationId": "searchNotion",
        "security": [
          {
            "NotionAPIKey": []
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/SearchRequest"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SearchResponse"
                }
              }
            }
          },
          "400": {
            "description": "Invalid JSON body error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Error"
                }
              }
            }
          },
          "429": {
            "description": "Rate limited error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Error"
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "SearchRequest": {
        "type": "object",
        "properties": {
          "query": {
            "type": "string"
          },
          "filter": {
            "type": "object",
            "properties": {
              "value": {
                "type": "string"
              },
              "property": {
                "type": "string"
              }
            },
            "required": ["value", "property"]
          },
          "sort": {
            "type": "object",
            "properties": {
              "direction": {
                "type": "string",
                "enum": ["ascending", "descending"]
              },
              "timestamp": {
                "type": "string"
              }
            },
            "required": ["direction", "timestamp"]
          }
        },
        "required": ["query"]
      },
      "SearchResponse": {
        "type": "object",
        // Include detailed properties based on the sample response provided
      },
      "Error": {
        "type": "object",
        "properties": {
          "object": {
            "type": "string"
          },
          "status": {
            "type": "integer"
          },
          "code": {
            "type": "string"
          },
          "message": {
            "type": "string"
          }
        }
      }
    },
    "securitySchemes": {
      "NotionAPIKey": {
        "type": "apiKey",
        "in": "header",
        "name": "Authorization"
      }
    }
  }
}
