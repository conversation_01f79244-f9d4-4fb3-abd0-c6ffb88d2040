---
Updated: 2023-03-09T12:05
tags:
  - AI->-Programming
  - AI->-ToDO
Created: 2023-03-09T10:53
---
### 写在前面
开始之前我们先来了解几个概念或语言。
本文内容很长，图片很多，推荐大家在 PC 端浏览。文章涉及的内容很多，有些点没讲到，大家可以看源码，我也是一个正在成长的程序员，见解浅显之处希望大家见谅！
本文需要有 Kotlin 基础，没有的朋友可以先去看一看 Kotlin 的语法，推荐大家去 [Kotlin 语言中文站](https://www.kotlincn.net/docs/reference/basic-syntax.html)学习。本文对于界面的实现不会进行讲解，布局文件和资源文件会在代码仓库准备好。

> 代码仓库：[https://github.com/githinkcn/KotlinApp](https://github.com/githinkcn/KotlinApp)
### 1. 什么是 Kotlin

> Kotlin 是一个用于现代多平台应用的静态编程语言，由 JetBrains 开发。Kotlin 可以编译成 Java 字节码，也可以编译成 JavaScript，方便在没有 JVM 的设备上运行。Kotlin 已正式成为 Android 的官方支持开发语言。此外，Kotlin 还是一门融合了面向对象与函数式编程的语言，支持泛型、安全的空判断，并且 Kotlin 与 Java 可以做到完全的交互。
### 2. 什么是 MVP 模式

> 简称 MVP，全称 Model-View-Presenter。MVP 是从经典的模式 MVC 演变而来，它们的基本思想有相通的地方：
> 
> Controller/Presenter 负责逻辑的处理，Model 提供数据，View 负责显示。
MVP 模式的核心思想：**MVP 把 Activity 中的 UI 逻辑抽象成 View 接口，把业务逻辑抽象成 Presenter 接口，Model 类还是原来的 Model。**
使用 MVP 组织代码架构，并对代码实施分层管理的好处如下。
- 分离了视图逻辑和业务逻辑，降低了耦合。当我们的 App 界面发生变化的时候我们只需要修改 View 层，其他层无需改动。
- Activity 只处理生命周期的任务，代码变得更加简洁。其实 Activity 并不是一个标准的 MVC 模式中的 Controller，它的首要职责是加载应用的布局和初始化用户界面，并接受并处理来自用户的操作请求，进而作出响应。随着界面及其逻辑的复杂度不断提升，Activity 类的 职责不断增加，以致变得庞大臃肿。当我们将其中复杂的逻辑处理移至另外的一个类（Presneter）中时，Activity 其实就是 MVP 模式中 View，它负责 UI 元素的初始化，建立 UI 元素与 Presenter 的关联（Listener 之类），同时自己也会处理一些简单的逻辑（复杂的逻辑交由 Presenter 处理）。
- 视图逻辑和业务逻辑分别抽象到了 View 和 Presenter 的接口中去，提高代码的可阅读性。
- Presenter 被抽象成接口，可以有多种具体的实现，可以方便进行单元测试。由于业务逻辑都在 Presenter 里，我们完全可以写一个 PresenterTest 的实现类继承 Presenter 的接口，现在只要在 Activity 里把 Presenter 的创建换成 PresenterTest，就能进行单元测试了，测试完再换回来即可。
- 把业务逻辑抽到 Presenter 中去，避免后台线程引用着 Activity 导致 Activity 的资源无法被系统回收从而引起内存泄露和 OOM。
通过以上几个概念我们已经了简单了解了什么是 MVP 模式，什么是 Kotlin 语言，下面我们就用一张思维导图来看一下我们要做的 App MVP 架构的数据流向。
![[85d4eb70-e63c-11e8-b46a-91431ad2fddb]]
### 一、开发环境概览
- Android Studio 版本：3.2.1
- Kotlin 版本：1.3.0
- JDK 版本：1.8.0_161
- SDK 版本：28
这里推荐使用[网易 MuMu](http://mumu.163.com/) Android 模拟器，此为全平台模拟器，安装简单，配置简单。IDE 连接模拟器：
```Plain
# Win
adb connect 127.0.0.1:7555
# Mac（貌似只能横屏）
adb connect 127.0.0.1:5555
```
**注意：**在命令行中使用 adb 命令，需要将 SDK 安装目录中的 platform-tools 文件夹配置到环境变量中。
### 二、项目预览
![[2d61b500-e753-11e8-aced-9f9ab4b76894]]
![[38d47670-e753-11e8-82d1-3560fc946d87]]
![[9cd644f0-e753-11e8-aced-9f9ab4b76894]]
![[a675cd00-e753-11e8-8b45-2bd295c11632]]
### 三、搭建多模块 Android 项目
### 1. 什么是 Android 模块化开发
单独开发每个模块，用集成的方式把他们组合起来，就能拼出一个 App。App 可以理解成很多功能模块的组合，而且有些功能模块是通用的、必备的，像自动更新、反馈、推送，都可以提炼成模块，和搭积木很像，由一个壳包含很多个模块。
### 2. 模块化的好处
- 对业务进行模块化拆分后，为了使各业务模块间解耦，各个模块都是独立的，它们之间没有依赖关系。每个模块负责的功能不同，业务逻辑不同，模块间业务解耦。模块功能比较单一，可在多个项目中使用。
- 一个模块实际上也是一个完整的项目，可以进行单独编译，调试。
- 每个团队负责不同的模块，提升开发，测试效率。
### 3. 创建项目
### **3.1 打开 Android Studio 新建一个项目（即主模块）**
![[42875810-e756-11e8-aced-9f9ab4b76894]]
配置页面需要配置一些信息（下图为原配置）
![[9ba7cfb0-e756-11e8-98da-277a8dc4f6ec]]
修改配置之后
![[ec239a00-e756-11e8-98da-277a8dc4f6ec]]
我们的 App 名称就是 `kotlinApp`，Package name 需要修改成“你域名的倒序 + project 的 name”，注意要勾选上 `Include Kotlin support`，毕竟我们要使用 Kotlin 来开发嘛。配置完成之后下一步。
此处是设置最小支持的 SDK，这里我们默认配置直接下一步。
![[02ddf8c0-e758-11e8-82d1-3560fc946d87]]
这里我们选择 Empty Activity，创建一个空的 Activity，下一步。
![[5bf9b570-e758-11e8-82d1-3560fc946d87]]
这里配置 Activity 的名称、Layout 的名称，默认配置，下一步等待依赖下载和编译完成即可进入开发界面。
![[d531a920-e758-11e8-aced-9f9ab4b76894]]
以上就是创建一 Android 项目的步骤，可能有点偏基础，我们创建的这个 App，在开发界面上应该可以看到项目目录，App 这个目录即为我们App的主模块。
![[877c9980-e814-11e8-89ee-f776175382b4]]
此时你运行此项目就可以在模拟器上看到 HelloWorld。
### **3.2 创建子模块**
下面就让我们添加几个模块，为每个模块赋予职责。
**创建公共底层模块（Common Module）**
选中 App 目录右键选择 `new -> module`。
![[cce156f0-e814-11e8-89ee-f776175382b4]]
选择 `Android Library`，下一步。
![[2b3549f0-e815-11e8-89ee-f776175382b4]]
进入到配置我们模块的页面，修改好我们的模块名和包名即可。
![[77db7b30-e815-11e8-89ee-f776175382b4]]
创建完后的项目目录为下图这样。
![[cd062790-e815-11e8-89ee-f776175382b4]]
以上就是创建自模块的具体步骤和相关配置，下面就请大家按照这个方法创建以下子模块：**中间件模块（Provider module）**、**用户模块（Mine module）**，创建完应该就是这个样子。
![[ded9f5a0-e81a-11e8-89ee-f776175382b4]]
可以看到我们创建了这些模块已经在项目目录中显示出来了，并且也给我生成了相应的 Gradle 文件，为了统一我们也把主模块的模块名改为大写。
**配置模块间的依赖**
- Common：为底层依赖所有的模块都将依赖它，第三方依赖基本上都会写在 Common 模块的 Gradle 的文件中。
- Provider：为中间层依赖于 Common。
- Mine、App：为业务模块依赖于 Provider，App 模块中需要引入 Mine 模块。
![[5f790630-e81e-11e8-89ee-f776175382b4]]
实现模块间的依赖有两种形式：一种是使用代码的形式在配置文件中配置，另一种是使用 IDE 去配置模块间的依赖。
首先说一下在 AS 3.0 之后就使用 API、implementation 作为 compile 的替代。
- API：和 compile 的作用一样，当前 module 会暴露其依赖的其他 module 内容。
- implementation：只在内部使用了该 module，不会向外部暴露其依赖的 module 内容。
这个在后面的配置中使用之后大家就会明白了。
**第一种：代码配置**
配置 Provider 依赖 Common。
![[22f15940-e820-11e8-89ee-f776175382b4]]
配置 Mine 依赖 Provider。
![[1f10f550-e821-11e8-89ee-f776175382b4]]
配置 App 依赖 Provider，引入 Mine 模块 。