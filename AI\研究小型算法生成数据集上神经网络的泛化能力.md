---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-23T11:38
tags:
  - AI->-Theory
URL: https://github.com/openai/grok
Created: 2024-03-23T11:15
---
[![](https://opengraph.githubassets.com/2cb8164c0e85d0d25b61f959814ee36703521ecd31ab8892481bda40cd26cd96/openai/grok)](https://opengraph.githubassets.com/2cb8164c0e85d0d25b61f959814ee36703521ecd31ab8892481bda40cd26cd96/openai/grok)
## ==Create list==
==Create a list to organize your starred repositories.==
==Name .==
==32 remaining==
==Description .==
==160 remaining==
==.==
==Create==
==**Tip:**== ==type== ==`:`== ==to add emoji to the name or description.==
==Beta Lists are currently in beta.== [==Share feedback and report bugs.==](https://github.com//github/feedback/discussions/categories/lists)
==**== [==grok==](https://github.com/openai/grok) ==** Public==
[![](https://avatars.githubusercontent.com/u/14957082?s=48&v=4)](https://avatars.githubusercontent.com/u/14957082?s=48&v=4)
- ==Unwatch Stop ignoring Watch 153==
    
    ### ==Notifications==
    
    ==Get push notifications on== [==iOS==](https://apps.apple.com/app/apple-store/id1477376905?ct=watch-dropdown&mt=8&pt=524675) ==or== [==Android==](https://play.google.com/store/apps/details?id=com.github.android&referrer=utm_campaign%3Dwatch-dropdown%26utm_medium%3Dweb%26utm_source%3Dgithub)==.==
    
- [==Fork 460 Fork your own copy of openai/grok==](https://github.com/openai/grok/fork)
==Notification settings==
### ==License==
[==MIT license==](https://github.com/openai/grok/blob/main/LICENSE)
[==3.8k stars==](https://github.com/openai/grok/stargazers) [==460 forks==](https://github.com/openai/grok/forks)
==153 watching==
[==**1**==](https://github.com/openai/grok/branches) [==Branch==](https://github.com/openai/grok/branches) [==**0**==](https://github.com/openai/grok/tags) [==Tags==](https://github.com/openai/grok/tags) [==Activity==](https://github.com/openai/grok/activity) [==Custom properties==](https://github.com/openai/grok/custom-properties)
==Public repository==
[==Open in github.dev==](https://github.dev/) [==Open in a new github.dev tab==](https://github.dev/) [==Open in codespace==](https://github.com/codespaces/new/openai/grok?resume=1)
==This commit does not belong to any branch on this repository, and may belong to a fork outside of the repository.==
[==**1**==](https://github.com/openai/grok/branches) [==Branch==](https://github.com/openai/grok/branches)
[==**0**==](https://github.com/openai/grok/tags) ==Tags==
==t==
## ==Add file==
## ==Add file==
## ==Folders and files==
==Name==
==Name==
==Last commit message==
==Last commit date==
## ==Latest commit==
[==yburda==](https://github.com/openai/grok/commits?author=yburda)
[![](https://avatars.githubusercontent.com/u/8129115?v=4&size=40)](https://avatars.githubusercontent.com/u/8129115?v=4&size=40)
[==Merge pull request==](https://github.com/openai/grok/commit/3d64b1d8c1d595dd8ebdb7771998823f1b14c7b3) [==#17==](https://github.com/openai/grok/pull/17) [==from eltociear/patch-1==](https://github.com/openai/grok/commit/3d64b1d8c1d595dd8ebdb7771998823f1b14c7b3)
[==3d64b1d==](https://github.com/openai/grok/commit/3d64b1d8c1d595dd8ebdb7771998823f1b14c7b3) ==·==
## ==History==
[==5 Commits==](https://github.com/openai/grok/commits/main/)
[==grok==](https://github.com/openai/grok/tree/main/grok)
[==grok==](https://github.com/openai/grok/tree/main/grok)
[==Initial commit==](https://github.com/openai/grok/commit/43efed280af24a8837b05fd9c97a3d14f295666f)
[==nbs==](https://github.com/openai/grok/tree/main/nbs)
[==nbs==](https://github.com/openai/grok/tree/main/nbs)
[==Initial commit==](https://github.com/openai/grok/commit/43efed280af24a8837b05fd9c97a3d14f295666f)
[==scripts==](https://github.com/openai/grok/tree/main/scripts)
[==scripts==](https://github.com/openai/grok/tree/main/scripts)
[==Update visualize_metrics.py==](https://github.com/openai/grok/commit/2c5c20b77f94a8b2d5d958866eb8332ec89a359b)
[==.gitignore==](https://github.com/openai/grok/blob/main/.gitignore)
[==.gitignore==](https://github.com/openai/grok/blob/main/.gitignore)
[==Initial commit==](https://github.com/openai/grok/commit/43efed280af24a8837b05fd9c97a3d14f295666f)
[==LICENSE==](https://github.com/openai/grok/blob/main/LICENSE)
[==LICENSE==](https://github.com/openai/grok/blob/main/LICENSE)
[==Initial commit==](https://github.com/openai/grok/commit/43efed280af24a8837b05fd9c97a3d14f295666f)
[==README.md==](https://github.com/openai/grok/blob/main/README.md)
[==README.md==](https://github.com/openai/grok/blob/main/README.md)
[==Add a link to the paper==](https://github.com/openai/grok/commit/921fee7153fb9986bd4713be96177d0db99eb1b1)
[==setup.py==](https://github.com/openai/grok/blob/main/setup.py)
[==setup.py==](https://github.com/openai/grok/blob/main/setup.py)
[==Initial commit==](https://github.com/openai/grok/commit/43efed280af24a8837b05fd9c97a3d14f295666f)
## ==Repository files navigation==
- [==README==](https://github.com/openai/grok#)
- [==MIT license==](https://github.com/openai/grok#)
## ==OpenAI Grok Curve Experiments==
## ==Paper==
==This is the code for the paper== [==Grokking: Generalization Beyond Overfitting on Small Algorithmic Datasets==](https://arxiv.org/abs/2201.02177) ==by Alethea Power, Yuri Burda, Harri Edwards, Igor Babuschkin, and Vedant Misra==
## ==Installation and Training==
==pip install -e .  
./scripts/train.py  
==
## ==About==
==No description, website, or topics provided.==
### ==Resources==
[==Readme==](https://github.com/openai/grok#readme-ov-file)
### ==License==
[==MIT license==](https://github.com/openai/grok#MIT-1-ov-file)
[==Activity==](https://github.com/openai/grok/activity)
[==Custom properties==](https://github.com/openai/grok/custom-properties)
### ==Stars==
[==**3.8k**==](https://github.com/openai/grok/stargazers) [==stars==](https://github.com/openai/grok/stargazers)
### ==Watchers==
[==**153**==](https://github.com/openai/grok/watchers) [==watching==](https://github.com/openai/grok/watchers)
### ==Forks==
[==**460**==](https://github.com/openai/grok/forks) [==forks==](https://github.com/openai/grok/forks)
[==Report repository==](https://github.com/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Fopenai%2Fgrok&report=openai+%28user%29)
## [==Releases==](https://github.com/openai/grok/releases)
==No releases published==
## [==Packages==](https://github.com/orgs/openai/packages?repo_name=grok)
==No packages published==
## [==Contributors 4==](https://github.com/openai/grok/graphs/contributors)
- [](https://github.com/aletheap)[==**aletheap**==](https://github.com/aletheap) [==Alethea Power==](https://github.com/aletheap)
- [](https://github.com/vedant)[==**vedant**==](https://github.com/vedant) [==Vedant Misra==](https://github.com/vedant)
- [](https://github.com/yburda)[==**yburda**==](https://github.com/yburda)[](https://github.com/yburda)
- [](https://github.com/eltociear)[==**eltociear**==](https://github.com/eltociear) [==Ikko Eltociear Ashimine==](https://github.com/eltociear)
## ==Languages==
- [==Python 98.1%==](https://github.com/openai/grok/search?l=python)
- [==Jupyter Notebook 1.4%==](https://github.com/openai/grok/search?l=jupyter-notebook)
- [==Shell 0.5%==](https://github.com/openai/grok/search?l=shell)