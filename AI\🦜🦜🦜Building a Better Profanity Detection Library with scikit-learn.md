---
Updated: 2024-08-28T20:07
tags:
  - AI->-Security&Profanity
URL: https://towardsdatascience.com/building-a-better-profanity-detection-library-with-scikit-learn-3638b2f2c4c2
Created: 2024-08-26T11:58
---
Memo
some package we need to installed
tested some of existing package
better_profanity
Some other way - use fugashi
Finally we have to use Deep Learning
Merge and decrease Model Size
We learned
# Memo
## some package we need to installed
```SQL
\#package need to be installed
pip install langdetect
pip install fugashi
pip install ipadic
```
## tested some of existing package
```SQL
pip install profanity-check <= Too old
\#profanity-check, got old version error. so giveup
ImportError: cannot import name 'joblib' from 'sklearn.externals'
ModuleNotFoundError: No module named 'sklearn.svm.classes'"
cd ~/miniconda3/envs/mom/lib/python3.10/site-packages/profanity_check
\#going to uninstall it
pip install --user pipx
python -m pipx ensurepath
```
## better_profanity
```SQL
   !pip install better_profanity
   https://pypi.org/project/better-profanity/
   from better_profanity import profanity
   
   text = "You p1ec3 of sHit."
   censored_text = profanity.censor(text)
   print(censored_text)  # 输出: You **** of ****.
```
  
  
```SQL
https://pypi.org/project/profanity-filter/
https://github.com/rominf/profanity-filter
```
  
/opt/workspace/mom/model/ProfanityDetect.py  
```SQL
https://www.kaggle.com/c/jigsaw-toxic-comment-classification-challenge/code
/opt/workspace/mom/hate-speech-and-offensive-language
https://www.kaggle.com/code/hawkeoni/pytorch-simple-bert
https://zenn.dev/ken_11/articles/0e2e231f321c5d
https://github.com/cl-tohoku/bert-japanese
```
## Some other way - use fugashi
```SQL
https://taku910.github.io/mecab/\#install-unix
# download mecab and dict file
tar zxfv mecab-X.X.tar.gz
cd mecab-X.X
./configure 
make
make check
sudo make install
tar zxfv mecab-ipadic-2.7.0-XXXX.tar.gz
mecab-ipadic-2.7.0-XXXX
./configure --enable-utf8-only
make
sudo make install
\#setup fugashi
https://github.com/polm/fugashi?tab=readme-ov-file
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xbb in position 2: invalid start byte

\#looks fugashi not working and we accept cursor suggestion
pip install mecab-python3
>>> import MeCab
>>> wakati = MeCab.Tagger("-Owakati")
>>> wakati.parse("pythonが大好きです").split()
['python', 'が', '大好き', 'です']
```
## Finally we have to use Deep Learning
```SQL
Summary
1. For english, we can use better_profanity, 它也支持customized ward list
   from better_profanity import profanity
   
   text = "You p1ec3 of sHit."
   censored_text = profanity.censor(text)
   print(censored_text)  # 输出: You **** of ****.
2. 但处理日语最大问题是如何分词，目前尝试fugashi和MeCAB都不好
3. 所以还是要训练模型，而且token一定要懂日语的
我们采用了Berd基本模型+BertJapaneseTokenizer
Step 1: 准备数据
Step 2: 下载预训练模型
https://huggingface.co/tohoku-nlp/bert-base-japanese-whole-word-masking
Step 3: 我们使用BertJapaneseTokenizer日语token产生x，Y，训练3个周期   
我们借鉴了https://www.kaggle.com/code/hawkeoni/pytorch-simple-bert
```
  
  
```SQL
(base) [raysheng@MONSTER data]$ ls -lrt
total 66796
-rw-r--r-- 1 <USER> <GROUP>    21205 Aug 26 14:39 output.txt  《- 只是从wiki爬下来原始数据
-rw-r--r-- 1 <USER> <GROUP>     3984 Aug 26 14:51 dirtywordlist.txt 《= 早期的dirty word列表
-rw-r--r-- 1 <USER> <GROUP>     7938 Aug 26 22:01 japanese_words.txt 《= 一般的日语用词用来生成句子
-rw-r--r-- 1 <USER> <GROUP> 18704824 Aug 26 22:53 data_sentences.csv 《= 夹杂英文全部和日语早期脏词的20万数据
-rw-r--r-- 1 <USER> <GROUP>    36585 Aug 26 23:18 sentences.csv 《- 只是生成数据data_sentences.csv子集才400条
-rw-r--r-- 1 <USER> <GROUP>    31934 Aug 27 11:03 kinshi.csv 《= 从robert拿来的
-rw-r--r-- 1 <USER> <GROUP>     5921 Aug 27 11:15 wordlist.txt 《- 包含挑选后的所有en和jp的脏词
-rw-r--r-- 1 <USER> <GROUP>     2955 Aug 27 11:22 japanese_dirtywordlist.txt 《- 仅仅日语脏词
-rw-r--r-- 1 <USER> <GROUP> 15433230 Aug 27 11:25 japanese_sentences.csv 《- 仅包含日语的所有脏词的20万条
-rw-r--r-- 1 <USER> <GROUP> 34138039 Aug 27 11:28 merged_sentences.csv 《- 是data_sentences.csv + japanese_sentences.csv
```
##   
Merge and decrease Model Size  
### We learned
```Python
\#Load , as we are using non standard model, we can't use 
\#AutoModelForSequenceClassification to load it
\#before we do that. pls check config.json
#   "num_labels": 1
# you need to install safetensors
# import importlib.util
# print(importlib.util.find_spec("safetensors") is not None)
I just find if I use non quantized model, although checkpoint file size is double of quantized model, but GPU usage is half of quantized model. 
1. Quantization overhead: Quantized models often require additional operations to dequantize values during inference, which can increase memory usage. As explained in the Hugging Face quantization documentation:
"Quantization can introduce accuracy loss since fewer bits limit the precision and range of values. However, researchers have extensively demonstrated that weights and activations can be represented using 8-bit integers (INT8) without incurring significant loss in accuracy."
This dequantization process may be contributing to the increased GPU usage.
Tensor layouts: Quantized models may use different memory layouts that are optimized for integer operations but potentially less efficient in terms of overall memory usage on GPUs.
Caching and optimization: The GPU drivers and CUDA runtime may be able to optimize the non-quantized model more effectively, leading to more efficient memory usage.
Framework-specific implementations: Depending on which quantization method you're using (e.g. bitsandbytes, GPTQ, etc.), there may be implementation-specific reasons for the increased memory usage.
To get a more definitive answer, you may want to profile your model's memory usage in more detail. Tools like nvidia-smi with the --query-gpu=memory.used option or PyTorch's memory profiling tools can give you a more granular view of memory usage during inference.
It's also worth noting that while the quantized model may be using more GPU memory, it should theoretically be faster for inference due to the reduced precision calculations. If you're not seeing a speed improvement, you might want to ensure you're using the quantized operations correctly and that your hardware supports the specific quantization method you're using efficiently.
```
/opt/workspace/mom/model/merge_prune.py
```SQL
# this one we didnt try
https://github.com/arcee-ai/mergekit
# we merge and reduce size to just 400MB and 600MB load size into GPU after half still working well
(base) [root@MONSTER:/opt/workspace/mom/model/profanity_classifier]# ls -lrt
total 432392
-rwxrwxrwx 1 <USER> <GROUP>       784 Aug 28 14:30 config.json
-rwxrwxrwx 1 <USER> <GROUP>      1365 Aug 28 14:30 tokenizer_config.json
-rw-r--r-- 1 <USER> <GROUP>       125 Aug 28 14:30 special_tokens_map.json
-rw-r--r-- 1 <USER> <GROUP> 442495988 Aug 28 14:30 model.safetensors
-rwxrwxrwx 1 <USER> <GROUP>    257706 Aug 28 14:30 vocab.txt
\#We tested prune and it not well. Even weight file size increased double. and accuracy dramtically downgraded.
def prune_bert_model(model, amount=0.3):
    for name, module in model.named_modules():
        # Prune linear layers
        if isinstance(module, nn.Linear):
            prune.l1_unstructured(module, name='weight', amount=amount)
            prune.l1_unstructured(module, name='bias', amount=amount)
        
        # Prune embedding layers
        elif isinstance(module, nn.Embedding):
            prune.l1_unstructured(module, name='weight', amount=amount)
    
    return model
def print_sparsity(model):
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear) or isinstance(module, nn.Embedding):
            weight = module.weight
            bias = getattr(module, 'bias', None)
            
            weight_sparsity = 100. * float(torch.sum(weight == 0)) / float(weight.nelement())
            print(f'Sparsity in {name}.weight: {weight_sparsity:.2f}%')
            
            if bias is not None:
                bias_sparsity = 100. * float(torch.sum(bias == 0)) / float(bias.nelement())
                print(f'Sparsity in {name}.bias: {bias_sparsity:.2f}%')
\#now we start to try quantization
import torch
import torch.nn as nn
import torch.nn.utils.prune as prune
import torch.nn.functional as F
from transformers import AutoModelForCausalLM, AutoTokenizer
from transformers import AutoTokenizer, AutoModelForSequenceClassification
##\#Save
# load original model
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
model_path = 'model/profanity_classifier_v1'
tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModelForSequenceClassification.from_pretrained(model_path)
model.to(device).eval()
config = model.config # to check the number of labels
\#quantize it
replace_linear_with_target_and_quantize(model, 
                             W8A16LinearLayer, 
                                   ["lm_head"])
								   
\#Save to 
merged_model_path = 'model/profanity_classifier'
model.eval()  # Set to evaluation mode
model.save_pretrained(merged_model_path)
model.config.save_pretrained(merged_model_path)    
tokenizer.save_pretrained(merged_model_path)  
\#Load , as we are using non standard model, we can't use 
\#AutoModelForSequenceClassification to load it
\#before we do that. pls check config.json
#   "num_labels": 1
# you need to install safetensors
# import importlib.util
# print(importlib.util.find_spec("safetensors") is not None)
from transformers import BertConfig, PreTrainedModel, BertModel
import os
from safetensors import safe_open
from safetensors.torch import load_file
class BertClassifier(PreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.bert = BertModel(config)
        self.classifier = nn.Linear(config.hidden_size, config.num_labels)
    def forward(self, input_ids, attention_mask=None, token_type_ids=None, position_ids=None, head_mask=None, labels=None):
        outputs = self.bert(input_ids,
                            attention_mask=attention_mask,
                            token_type_ids=token_type_ids,
                            position_ids=position_ids,
                            head_mask=head_mask)
        cls_output = outputs[1]
        logits = self.classifier(cls_output)
        logits = torch.sigmoid(logits)
        
        loss = None
        if labels is not None:
            loss_fct = nn.BCELoss()
            loss = loss_fct(logits.view(-1), labels.view(-1))
model_path = 'model/profanity_classifier'
   
tokenizer = AutoTokenizer.from_pretrained(model_path)
config = BertConfig.from_pretrained(model_path)        
# Create an instance of your model with the loaded config
model = BertClassifier(config).to(device)
replace_linear_with_target_and_quantize(model, 
                             W8A16LinearLayer, 
                                   ["lm_head"])
# Load the safetensors file
checkpoint_path = 'model/profanity_classifier/model.safetensors'
state_dict = load_file(checkpoint_path)
# Load the state dict into your model
model.load_state_dict(state_dict)
# Inference 
def test_model_inference(model,tokenizer):
    def predict_profanity(text):
        encoded = tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=120,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
        
        input_ids = encoded['input_ids'].to(device)
        attention_mask = encoded['attention_mask'].to(device)
        
        with torch.no_grad():
           _, output = model(input_ids, attention_mask=attention_mask)
        
        probability = output.item()
        return probability
    def test_sentences():
        sentences = [
            "This is a normal sentence.",
            "Go to hell, you scum!",
            "おまえはぼけ。",
            "あなたはダサいです。",
            "これから選手厚さ庭兄セーター受け取るまだ文章お腹暖かい",
            "お腹暖かい",
        ]
        
        for sentence in sentences:
            prob = predict_profanity(sentence)
            print(f"Sentence: '{sentence}'")
            print(f"Probability of containing profanity: {prob:.4f}")
            print("Classification:", "Profane" if prob > 0.7 else "Clean")
            print()
    test_sentences()
test_model_inference(model,tokenizer)
```
```Python
import torch
import torch.nn as nn
import torch.nn.utils.prune as prune
import torch.nn.functional as F
from transformers import BertJapaneseTokenizer, BertModel,BertConfig, PreTrainedModel
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from safetensors import safe_open
from safetensors.torch import load_file
import json
import shutil
import os
def replace_linear_with_target(module, 
                               target_class, module_name_to_exclude):
    for name, child in module.named_children():
        if isinstance(child, nn.Linear) and not \
          any([x == name for x in module_name_to_exclude]):
            old_bias = child.bias
            new_module = target_class(child.in_features, 
                                      child.out_features, 
                                      old_bias is not None, 
                                      child.weight.dtype)
            setattr(module, name, new_module)
            if old_bias is not None:
              getattr(module, name).bias = old_bias
        else:
            # Recursively call the function for nested modules
            replace_linear_with_target(
                child, target_class, module_name_to_exclude)
def replace_linear_with_target_and_quantize(module, 
                               target_class, module_name_to_exclude):
    for name, child in module.named_children():
        if isinstance(child, nn.Linear) and not \
        any([x == name for x in module_name_to_exclude]):
            old_bias = child.bias
            old_weight = child.weight
            new_module = target_class(child.in_features, 
                                      child.out_features, 
                                      old_bias is not None, 
                                      child.weight.dtype)
            setattr(module, name, new_module)
            getattr(module, name).quantize(old_weight)
            
            if old_bias is not None:
              getattr(module, name).bias = old_bias
        else:
            # Recursively call the function for nested modules
            replace_linear_with_target_and_quantize(child, 
                     target_class, module_name_to_exclude)       

def w8_a16_forward(weight, input, scales, bias=None):
    
    casted_weights = weight.to(input.dtype)
    output = F.linear(input, casted_weights) * scales
    
    if bias is not None:
        output = output + bias
      
    return output
    
class W8A16LinearLayer(nn.Module):
    def __init__(self, in_features, out_features, 
                 bias=True, dtype=torch.float32):
        super().__init__()
        
        
        self.register_buffer(
            "int8_weights",
            torch.randint(
                -128, 127, (out_features, in_features), dtype=torch.int8
            )
        )
        
        self.register_buffer("scales", 
                             torch.randn((out_features), dtype=dtype))
        
        if bias:
            self.register_buffer("bias", 
                                 torch.randn((1, out_features), 
                                             dtype=dtype))
        
        else:
            self.bias = None
    def quantize(self, weights):
        w_fp32 = weights.clone().to(torch.float32)
        scales = w_fp32.abs().max(dim=-1).values / 127
        scales = scales.to(weights.dtype)
        int8_weights = torch.round(weights
                        /scales.unsqueeze(1)).to(torch.int8)
        self.int8_weights = int8_weights
        self.scales = scales
    
    def forward(self, input):
        return w8_a16_forward(self.int8_weights, 
                              input, self.scales, self.bias)         

class BertClassifier(PreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.bert = BertModel(config)
        self.classifier = nn.Linear(config.hidden_size, config.num_labels)
    def forward(self, input_ids, attention_mask=None, token_type_ids=None, position_ids=None, head_mask=None, labels=None):
        outputs = self.bert(input_ids,
                            attention_mask=attention_mask,
                            token_type_ids=token_type_ids,
                            position_ids=position_ids,
                            head_mask=head_mask)
        cls_output = outputs[1]
        logits = self.classifier(cls_output)
        logits = torch.sigmoid(logits)
        
        loss = None
        if labels is not None:
            loss_fct = nn.BCELoss()
            loss = loss_fct(logits.view(-1), labels.view(-1))
        
        return loss, logits
    
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
########## Add num_labels and classifier_dropout(if used but now not used) ##########
# with open('model/profanity_classifier/config.json', 'r') as f:
#     config = json.load(f)
# config['num_labels'] = 1  # Assuming binary classification
# config['classifier_dropout'] = 0.1  # Add dropout rate if used in your classifier
# with open('model/profanity_classifier/config.json', 'w') as f:
#     json.dump(config, f, indent=2)
##########
def prune_bert_model(model, amount=0.3):
    for name, module in model.named_modules():
        # Prune linear layers
        if isinstance(module, nn.Linear):
            prune.l1_unstructured(module, name='weight', amount=amount)
            prune.l1_unstructured(module, name='bias', amount=amount)
        
        # Prune embedding layers
        elif isinstance(module, nn.Embedding):
            prune.l1_unstructured(module, name='weight', amount=amount)
    
    return model
def print_sparsity(model):
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear) or isinstance(module, nn.Embedding):
            weight = module.weight
            bias = getattr(module, 'bias', None)
            
            weight_sparsity = 100. * float(torch.sum(weight == 0)) / float(weight.nelement())
            print(f'Sparsity in {name}.weight: {weight_sparsity:.2f}%')
            
            if bias is not None:
                bias_sparsity = 100. * float(torch.sum(bias == 0)) / float(bias.nelement())
                print(f'Sparsity in {name}.bias: {bias_sparsity:.2f}%')
                
def merge_and_save_model(bert_model_name, checkpoint_path, merged_model_path, if_quantized=False):
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    
    # Load pretrained model and tokenizer
    tokenizer = BertJapaneseTokenizer.from_pretrained(bert_model_name)
    pretrained_bert = BertModel.from_pretrained(bert_model_name).to(device)
    # Create BertClassifier with pretrained BERT
    config = pretrained_bert.config
    config.num_labels = 1  # Assuming binary classification and looks num_labels already in pretrained model
    model = BertClassifier(config).to(device)
    # Load pretrained BERT weights
    model.bert.load_state_dict(pretrained_bert.state_dict())
    # Load fine-tuned checkpoint
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'], strict=False)
    # ############## Prune model ##############
    # # Print initial sparsity
    # print_sparsity(model)
    # # Apply pruning
    # pruned_model = prune_bert_model(model, amount=0.3)
    # # Print sparsity after pruning
    # print_sparsity(pruned_model)
    # model = pruned_model
    # ############## Prune model ##############
    
    # Create directory for the merged model
    os.makedirs(merged_model_path, exist_ok=True)
    # Copy necessary files from the pretrained model
    for file in ['config.json', 'tokenizer_config.json', 'vocab.txt']:
        shutil.copy(os.path.join(bert_model_name, file), merged_model_path)
    if if_quantized:
        ############## Quantize model ##############
        replace_linear_with_target_and_quantize(model, W8A16LinearLayer, ["lm_head"])
        # quantized_model = torch.quantization.quantize_dynamic(
        #     model, {torch.nn.Linear}, dtype=torch.qint8
        # )
        ############## Quantize model ##############
 
    # Save the merged model
    model.eval()  # Set to evaluation mode
    model.save_pretrained(merged_model_path)
    model.config.save_pretrained(merged_model_path)    
    tokenizer.save_pretrained(merged_model_path)  
    print(f"Merged model saved to {merged_model_path}")
def load_quantized_model(model_path,if_quantized):
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')    
    if if_quantized:
        config = BertConfig.from_pretrained(model_path)        
        # Create an instance of your model with the loaded config
        quantized_model = BertClassifier(config).to(device)
        replace_linear_with_target_and_quantize(quantized_model, 
                             W8A16LinearLayer, 
                                   ["lm_head"])
        # Load the safetensors file
        checkpoint_path = model_path + '/model.safetensors'
        state_dict = load_file(checkpoint_path)
        # Load the state dict into your model
        quantized_model.load_state_dict(state_dict)
    else:
        quantized_model = AutoModelForSequenceClassification.from_pretrained(model_path)
       
    return quantized_model
def test_model_inference(if_quantized):
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    model_path = 'model/profanity_classifier'
        
    tokenizer = AutoTokenizer.from_pretrained(model_path)    
    model = load_quantized_model(model_path,if_quantized)
    model.to(device).eval()
    config = model.config # to check the number of labels
    def predict_profanity(text):
        encoded = tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=120,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
        
        input_ids = encoded['input_ids'].to(device)
        attention_mask = encoded['attention_mask'].to(device)
        
        with torch.no_grad():
            if if_quantized:
                _, output = model(input_ids, attention_mask=attention_mask)
            else:
                outputs = model(input_ids, attention_mask=attention_mask)
        if if_quantized:
            probability = output.item()            
        else:
            logits = outputs.logits
            probability = torch.sigmoid(logits).item()
        return probability
    def test_sentences():
        sentences = [
            "This is a normal sentence.",
            "You are an idiot!",
            "I love this beautiful day.",
            "Go to hell, you scum!",
            "The quick brown fox jumps over the lazy dog.",
            "You p1ec3 of sHit.",
            "That's bullshit!",
            "おまえはぼけ。",
            "あなたはダサいです。",
            "変態人！",
            "バカヤローおまえ！",
            "これから選手厚さ庭兄セーター受け取るまだ文章お腹暖かい",
            "お腹暖かい",
            "淫売心配集まるうまい踊る天気",
            "ちくしょう、パンクだ。"
        ]
        
        for sentence in sentences:
            prob = predict_profanity(sentence)
            print(f"Sentence: '{sentence}'")
            print(f"Probability of containing profanity: {prob:.4f}")
            print("Classification:", "Profane" if prob > 0.7 else "Clean")
            print()
    test_sentences()
# Uncomment and modify the following line to merge and save the model
# merge_and_save_model('/opt/aibase/bert-base-japanese-whole-word-masking', 'model/bert_classifier_checkpoint.pt', 'model/profanity_classifier', if_quantized=True)
# Uncomment the following line to test model inference
test_model_inference(False)
```
Fast scan code
```Python
import torch
import torch.nn as nn
import torch.nn.utils.prune as prune
import torch.nn.functional as F
from transformers import AutoModelForCausalLM, AutoTokenizer
from transformers import AutoTokenizer, AutoModelForSequenceClassification
##\#Save
# load original model
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
model_path = 'model/profanity_classifier_v1'
tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModelForSequenceClassification.from_pretrained(model_path)
model.to(device).eval()
config = model.config # to check the number of labels
\#quantize it
replace_linear_with_target_and_quantize(model, 
                             W8A16LinearLayer, 
                                   ["lm_head"])
								   
\#Save to 
merged_model_path = 'model/profanity_classifier'
model.eval()  # Set to evaluation mode
model.save_pretrained(merged_model_path)
model.config.save_pretrained(merged_model_path)    
tokenizer.save_pretrained(merged_model_path)  
\#Load , as we are using non standard model, we can't use 
\#AutoModelForSequenceClassification to load it
\#before we do that. pls check config.json
#   "num_labels": 1
# you need to install safetensors
# import importlib.util
# print(importlib.util.find_spec("safetensors") is not None)
from transformers import BertConfig, PreTrainedModel, BertModel
import os
from safetensors import safe_open
from safetensors.torch import load_file
class BertClassifier(PreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.bert = BertModel(config)
        self.classifier = nn.Linear(config.hidden_size, config.num_labels)
    def forward(self, input_ids, attention_mask=None, token_type_ids=None, position_ids=None, head_mask=None, labels=None):
        outputs = self.bert(input_ids,
                            attention_mask=attention_mask,
                            token_type_ids=token_type_ids,
                            position_ids=position_ids,
                            head_mask=head_mask)
        cls_output = outputs[1]
        logits = self.classifier(cls_output)
        logits = torch.sigmoid(logits)
        
        loss = None
        if labels is not None:
            loss_fct = nn.BCELoss()
            loss = loss_fct(logits.view(-1), labels.view(-1))
model_path = 'model/profanity_classifier'
   
tokenizer = AutoTokenizer.from_pretrained(model_path)
config = BertConfig.from_pretrained(model_path)        
# Create an instance of your model with the loaded config
model = BertClassifier(config).to(device)
replace_linear_with_target_and_quantize(model, 
                             W8A16LinearLayer, 
                                   ["lm_head"])
# Load the safetensors file
checkpoint_path = 'model/profanity_classifier/model.safetensors'
state_dict = load_file(checkpoint_path)
# Load the state dict into your model
model.load_state_dict(state_dict)
# Inference 
def test_model_inference(model,tokenizer):
    def predict_profanity(text):
        encoded = tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=120,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
        
        input_ids = encoded['input_ids'].to(device)
        attention_mask = encoded['attention_mask'].to(device)
        
        with torch.no_grad():
           _, output = model(input_ids, attention_mask=attention_mask)
        
        probability = output.item()
        return probability
    def test_sentences():
        sentences = [
            "This is a normal sentence.",
            "Go to hell, you scum!",
            "おまえはぼけ。",
            "あなたはダサいです。",
            "これから選手厚さ庭兄セーター受け取るまだ文章お腹暖かい",
            "お腹暖かい",
        ]
        
        for sentence in sentences:
            prob = predict_profanity(sentence)
            print(f"Sentence: '{sentence}'")
            print(f"Probability of containing profanity: {prob:.4f}")
            print("Classification:", "Profane" if prob > 0.7 else "Clean")
            print()
    test_sentences()
test_model_inference(model,tokenizer)
```
  
[![](https://miro.medium.com/v2/resize:fit:350/1*n5OWj4WEPkGexXO28_yteg.png)](https://miro.medium.com/v2/resize:fit:350/1*n5OWj4WEPkGexXO28_yteg.png)