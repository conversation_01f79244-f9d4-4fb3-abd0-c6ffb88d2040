---
Updated: 2023-12-13T12:21
tags:
  - AI->-MoE
URL: https://baoyu.io/translations/llm/mixture-of-experts-explained
Created: 2023-12-13T02:15
---
发布于：2023 年 12 月 11 日
自从 Mixtral 8x7B（见[发布公告](https://mistral.ai/news/mixtral-of-experts/)和[模型说明](https://huggingface.co/mistralai/Mixtral-8x7B-v0.1)）发布以来，“混合专家模型”（Mixture of Experts，简称 MoEs）这类 Transformer 成了开放 AI 领域的焦点话题。在这篇博客中，我们将深入探讨 MoEs 的基础构架、训练方式，以及在实际应用中需要权衡的各种因素。
现在，让我们一起深入探索！
## 要点速览
MoEs：
- 相较于密集型模型，**预训练速度更快**
- 拥有比同等参数的模型**更快的推理速度**
- 对**显存要求高**，因为需要将所有专家模型都加载到内存中
- 虽然在微调方面存在挑战，但近期关于 MoE 的**指令调优研究**[显示出了光明前景](https://arxiv.org/pdf/2305.14705.pdf)
现在，让我们进一步了解细节！
## 什么是混合专家模型 (MoE) ？
模型的规模对于提升其质量至关重要。在有限的计算资源下，相较于用更多步骤训练一个小型模型，训练一个大型模型即便步骤更少效果通常更好。
混合专家模型 (MoE) 让模型以远低于传统密集模型的计算成本进行预训练，这意味着你可以在相同的计算预算下显著扩大模型或数据集的规模。特别是在预训练阶段，MoE 模型能比其同等规模的密集型模型更快地达到相同的性能水平。
那么，MoE 究竟是什么呢？在 Transformer 模型的背景下，MoE 主要由两个部分组成：
- **稀疏 MoE 层** 代替了传统的密集前馈网络 (FFN) 层。MoE 层包含若干“专家”（如 8 个），每个专家都是一个独立的神经网络。实际上，这些专家通常是 FFN，但它们也可以是更复杂的网络，甚至可以是 MoE 本身，形成一个层级结构的 MoE。
    
- 一个**门控网络或路由器**，用于决定哪些 Token 分配给哪个专家。例如，在下图中，“More”这个 Token 被分配给第二个专家，而“Parameters”这个 Token 被分配给第一个网络。值得注意的是，一个 Token 可以被分配给多个专家。如何高效地将 Token 分配给合适的专家，是使用 MoE 技术时需要考虑的关键问题之一。这个路由器由一系列可学习的参数构成，它与模型的其他部分一起进行预训练。
    
![[00_switch_transformer.png]]
Switch Layer
来自 [Switch Transformers 论文](https://arxiv.org/abs/2101.03961) 的 MoE 层示例
那么，简单回顾一下，MoE（混合专家模型）的设计思路是这样的：在 Transformer 模型中，我们将每一个 FFN（前馈网络）层替换为 MoE 层，由一个门控网络和若干“专家”组成。
虽然 MoE 为我们带来了一些优势，比如更高效的预训练和相较于密集模型更快的推理速度，但同时它也带来了一些挑战：
- **训练：** 混合专家模型（MoEs）在预训练阶段的计算效率极高，但在微调时往往难以适应新场景，容易造成过拟合现象。
    
- **推理：** 尽管 MoE 模型可能包含大量参数，但在推理过程中只有部分参数被使用，这使得它的推理速度远快于参数数量相同的密集模型。但这也导致了一个问题：所有参数都需加载到内存中，因此对内存的需求相当大。比如，对于 Mixtral 8x7B 这样的 MoE，我们需要足够的 VRAM 来支持一个有 47B 参数的密集型模型。为什么是 47B 而不是 8 x 7B = 56B 呢？原因在于 MoE 模型中，只有 FFN 层被视为独立的“专家”，其他部分的模型参数则是共享的。此外，如果每个 Token 只使用两个“专家”，推理速度（以 FLOPs 计算）就相当于使用一个 12B 模型（而不是 14B），因为它实现了 2x7B 的矩阵乘法，但部分层是共享的（接下来会进一步解释这一点）。
    
现在我们已经初步了解了 MoE 是什么，接下来让我们看看哪些研究成果促成了 MoE 的诞生。
## MoEs 简史
MoEs（Mixture of Experts，混合专家模型）的概念最早出现在 1991 年的论文《自适应局部专家混合》中。这一理念与集成方法相似，目的是通过监督程序管理一个由不同网络构成的系统，每个网络处理训练样本的一部分。每个单独网络或“专家”，都在输入空间的不同区域有其特长。那么，如何选出这些专家呢？由一个门控网络决定每个专家的权重。在训练过程中，同时对专家和门控网络进行训练。
在 2010 至 2015 年间，两个不同的研究领域推动了 MoE 的进一步发展：
- **将专家作为组件**：在传统的 MoE 结构中，系统由一个门控网络和多个专家组成。MoEs 作为整体模型已在 SVM（支持向量机）、高斯过程等方法中得到应用。[Eigen、Ranzato 和 Ilya](https://arxiv.org/abs/1312.4314) 的研究将 MoEs 作为更深层网络的一部分进行探索。这意味着 MoE 可以作为多层网络中的一层，使模型在大规模和高效率之间达到平衡。
    
- **条件计算**：传统网络会将所有输入数据通过每一层。在此期间，Yoshua Bengio 探索了一种基于输入 Token 动态激活或停用网络组件的方法。
    
这些研究促进了在自然语言处理（NLP）领域对混合专家模型的探索。具体来说，[Shazeer 等人](https://arxiv.org/abs/1701.06538)（2017 年的研究，团队成员包括 Geoffrey Hinton 和 Jeff Dean）将这一理念应用到了一个 137B LSTM（当时的 NLP 主要架构，由 Schmidhuber 发明）上，通过引入稀疏性概念，即使在大规模应用中也能保持快速的推理速度。这项工作主要关注翻译领域，但也面临着高通信成本和训练不稳定等挑战。
![[01_moe_layer.png]]
MoE layer in LSTM
《极大规模神经网络》论文中的 MoE 层
MoEs 的应用使得训练具有数万亿参数的模型成为可能，比如公开的 1.6T 参数的 Switch Transformers 等。除此之外，MoEs 在计算机视觉领域也有所探索，不过本篇博客将重点讨论 NLP 领域的应用。
## 稀疏性是什么？
稀疏性基于条件计算的概念。不同于密集模型中所有参数对所有输入都有效，稀疏性让我们能够只激活系统的部分区域。
深入探讨 Shazeer 在翻译领域对混合专家模型（MoE）的研究。条件计算（即网络的某些部分仅针对特定样本激活）的概念使得在不增加计算量的情况下扩大模型规模成为可能，从而在每层 MoE 中使用了数千名专家。
这种方法也带来了挑战。比如，虽然大批量处理通常能提高性能，但在 MoE 中，当数据通过活跃的专家时，实际的批量大小会减小。例如，如果我们的批量输入包含 10 个 Token，**可能有五个 Token 由一个专家处理，另外五个 Token 分别由五个不同的专家处理，这导致批量大小不均匀，资源利用率低下**。下文中的 [优化 MoE 性能](https://baoyu.io/translations/llm/mixture-of-experts-explained#making-moes-go-brrr) 一节将讨论更多挑战及其解决方案。
那我们该如何解决这些问题呢？通过一个学习型的门控网络 (G)，决定将输入的哪些部分分配给哪些专家 (E)：
y = \sum_{i=1}^{n} G(x)_i E_i(x)y=i=1∑n​G(x)i​Ei​(x)
在这种设置中，所有专家都参与处理所有输入——这是一种加权乘法过程。但如果 G 的值为 0 呢？这种情况下，就无需计算相应专家的操作，从而节约了计算资源。那么，典型的门控函数是什么样的呢？在传统设置中，我们通常使用一个简单的网络配合 softmax 函数。这个网络会学习如何选择最合适的专家处理输入。
G_\sigma(x) = \text{Softmax}(x \cdot W_g)Gσ​(x)=Softmax(x⋅Wg​)
Shazeer 的研究还探索了其他类型的门控机制，如带噪声的 Top-K 门控。这种方法加入了一定的（可调节的）噪声，然后只保留最高的 k 个值。具体来说：
1. 添加噪声
H(x)_i = (x \cdot W_{\text{g}})_i + \text{StandardNormal()} \cdot \text{Softplus}((x \cdot W_{\text{noise}})_i)H(x)i​=(x⋅Wg​)i​+StandardNormal()⋅Softplus((x⋅Wnoise​)i​)
1. 仅保留前 k 个值
\text{KeepTopK}(v, k)_i = \begin{cases} v_i & \text{if } v_i \text{ is in the top } k \text{ elements of } v, \\ -\infty & \text{otherwise.} \end{cases}KeepTopK(v,k)i​={vi​−∞​if vi​ is in the top k elements of v,otherwise.​
1. 我们使用 softmax 函数。
G(x) = \text{Softmax}(\text{KeepTopK}(H(x), k))G(x)=Softmax(KeepTopK(H(x),k))
这种稀疏性带来了一些有趣的特性。如果使用较低的 k 值（比如一到两个），我们可以比激活许多专家时更快地进行训练和推理。为什么不只选择最顶尖的专家呢？最初的假设是，为了让门控学习如何路由到不同的专家，需要路由到一个以上的专家，因此至少需要选择两个专家。[Switch Transformers](https://baoyu.io/translations/llm/mixture-of-experts-explained#switch-transformers) 章节将重新审视这一决策。
我们为什么要加入噪声？这是为了实现负载均衡！
## 为多专家系统（MoEs）负载均衡 tokens
正如之前所讨论的，如果所有的 tokens 都被发送到少数几个受欢迎的专家，这将导致训练效率低下。在标准的多专家系统训练中，门控网络倾向于主要激活相同的几位专家。这会形成自我加强的循环，因为得到优先训练的专家会被更频繁地选择。为了减轻这种情况，引入了一种**辅助损失**来鼓励平等对待所有专家。这种损失确保所有专家获得大致相同数量的训练样本。后续章节还将探讨“专家容量”的概念，这涉及到一个专家能处理的 tokens 数量上限。在 `transformers` 中，这种辅助损失可以通过 `aux_loss` 参数来调节。
## MoEs 和 Transformers
Transformers 模型展示了一个明显的趋势：增加参数的数量可以显著提高性能。Google 的 [GShard](https://arxiv.org/abs/2006.16668) 项目正是在这方面进行了深入探索，试图将 Transformers 模型扩展到超过 6000 亿个参数。
在 GShard 中，编码器和解码器里的部分 FFN (Feed-Forward Network) 层被 MoE (Mixture of Experts) 层替代，并采用了一种称为 top-2 的门控机制。下图显示了这种设计在编码器部分的应用。这种设计对大规模计算尤其有利：当模型扩展到多个设备时，MoE 层在这些设备间共享，而其他层则在每个设备上独立存在。关于如何高效利用 MoE 层的更多细节，可以参见[“让 MoEs 高效运行”](https://baoyu.io/translations/llm/mixture-of-experts-explained#making-moes-go-brrr)一节。
来自 GShard 论文的 MoE Transformer 编码器示例
为了在大规模应用中保持效率和均衡的负载，GShard 团队在设计上做了一些创新，除了引入了类似前一节提到的辅助损失机制外，还包括：
- **随机路由机制**：在 top-2 设计中，我们始终选择表现最优的专家，但第二选择的专家则根据其权重以一定概率被选中。
- **专家处理能力限制**：我们可以设定一个专家能处理的 Token 数量的上限。如果两个专家的处理能力都已达到上限，那么这个 Token 就会被认为是多余的，并通过残差连接传递到下一层，或在某些情况下被直接丢弃。这一概念在 MoEs 的应用中非常关键。为什么这样做？因为在模型编译时所有的张量形状都是静态确定的，但我们无法预先知道每个专家将处理多少 Token，因此需要设定一个固定的处理能力上限。
GShard 论文还探讨了适用于 MoEs 的并行计算模式，这些内容对于理解 MoEs 的高效运行非常有价值，但具体细节超出了本文的讨论范围。
**注意：** 在模型推理过程中，只有部分专家会被激活。同时，一些计算过程如自注意力机制会被所有 Token 共享。因此，尽管一个拥有 8 个专家的 470 亿参数模型听起来庞大，但实际上它的计算需求相当于一个 120 亿参数的密集型模型。如果采用 top-2 机制，模型会涉及约 140 亿参数，但由于注意力等操作是共享的，实际上模型真正使用的参数量仍然是 120 亿。
## Switch Transformers
尽管混合专家模型（MoEs）充满潜力，但它们在训练和微调时面临稳定性挑战。[Switch Transformers](https://arxiv.org/abs/2101.03961) 这项研究深入剖析了这些问题，并发布了一个具有 2048 个专家和 1.6 万亿参数的 MoE 模型，可以通过 Hugging Face 运行。相较于 T5-XXL，Switch Transformers 的预训练速度提高了四倍。
Switch Transformer 论文中的 Switch Transformer 层
与 GShard 相似，作者将 FFN 层替换为 MoE 层。Switch Transformers 提出了一种处理两种不同 token 的新型 Transformer 层，包含四个专家。
不同于最初至少使用两个专家的设想，Switch Transformers 采用了更简洁的单专家策略。这种策略的影响包括：
- 简化了路由计算
- 每个专家处理的批量至少减少了一半
- 减少了通信成本
- 保持了模型质量
此外，Switch Transformers 还探讨了专家容量的概念。专家容量的计算公式是：
\text{Expert Capacity} = \left(\frac{\text{tokens per batch}}{\text{number of experts}}\right) \times \text{capacity factor}Expert Capacity=(number of expertstokens per batch​)×capacity factor
每批 token 数量除以专家数量，再乘以容量因子。按此计算方式，可以均匀分配批次中的 token 给每个专家。如果容量因子大于 1，可以为 token 分配不均的情况提供缓冲。但容量增加会带来更高的设备间通信成本，这是一个需要权衡的问题。特别地，Switch Transformers 在较低的容量因子（1-1.25）下表现优异。
Switch Transformer 的研究者还对之前章节提到的负载均衡损失进行了简化。在训练过程中，每个 Switch 层的辅助损失会加入到总模型损失中，这种做法促进了均匀的路由分配，并可以通过超参数进行调整。
研究者们还尝试了一种选择性的精确度方法，例如在训练专家系统时使用 `bfloat16` 格式，而在其他计算过程中则采用全精度。降低精度能够显著减少处理器间的通信成本、计算成本以及存储数据的内存需求。但初期实验中，无论是专家系统还是门控网络都采用 `bfloat16` 进行训练，结果训练过程变得更加不稳定。特别是路由器计算部分，由于其涉及到指数函数，因此更高的精度显得尤为重要。为了缓解这种不稳定性，路由过程最终也采用了全精度处理。
采用选择性精度处理不仅能保持质量，还能提高模型的处理速度。
这个[在线笔记本](https://colab.research.google.com/drive/1aGGVHZmtKmcNBbAwa9hbu58DDpIuB5O4?usp=sharing)展示了如何对 Switch Transformers 进行微调以进行内容总结，不过建议您先了解其[微调部分](https://baoyu.io/translations/llm/mixture-of-experts-explained#fine-tuning-moes)。
Switch Transformers 采用了编码器 - 解码器的配置，实现了 T5 的多专家模型（MoE）版本。[GLaM](https://arxiv.org/abs/2112.06905) 论文则进一步探索了如何通过训练与 GPT-3 质量相当的模型来扩大这些模型的规模，同时只消耗 1/3 的能量（是的，得益于训练多专家模型所需的较低计算量，可以将碳排放量减少多达一个数量级）。作者主要关注于仅解码器模型的研究，以及基于少样本和一次样本的评估，而非进行微调。他们采用了 Top-2 路由和更大的容量因子，并且探究了如何根据计算需求在训练和评估过程中调整容量因子这一指标。
## 如何利用路由器 Z-loss 稳定模型训练
我们之前讨论过的平衡损失可能会引起训练稳定性的问题。为了稳定稀疏模型，我们可以采用多种方法，但这可能会牺牲模型的质量。例如，引入 dropout 虽然能增强稳定性，却会削弱模型的效果。而增加乘法运算组件虽然能提高模型质量，但又会降低其稳定性。
在 [ST-MoE](https://arxiv.org/abs/2202.08906) 研究中提出的路由器 z-loss 通过对门控网络输入的大数值 logits 施加惩罚，显著提高了训练的稳定性，同时又不会影响模型的质量。这种方法通过降低数值的绝对大小来减少舍入误差，这对于像门控这样的指数函数来说非常重要。为了更深入了解，建议阅读相关论文。
## 专家在学习中的角色和专长
ST-MoE 的研究者发现，编码器的专家倾向于专注于特定的 Token 组或基础概念。例如，可能形成专门处理标点符号或专有名词的专家。而解码器的专家则在专业化方面表现得较为平均。此外，作者还在多语言环境中进行了训练。虽然人们可能会认为每个专家会专注于一种特定语言，但实际情况却恰恰相反：由于 Token 的路由和负载均衡，没有任何一个专家专门对某一特定语言进行专研。
ST-MoE 论文中的表格展示了不同 Token 组被分配给哪些专家。
## 增加专家数量对预训练的影响
增加更多的专家可以提高样本效率和加速训练过程，但增益逐渐减少（特别是在达到 256 或 512 个专家后），并且在推理过程中需要更多的 VRAM。在大规模应用中研究的 Switch Transformers 的特性，在小规模应用中也得到了验证，即便是每层只有 2、4 或 8 个专家。
## 微调 MoE 技术

> Mixtral 软件已经在 transformers 4.36.0 版本中得到支持，您可以通过运行 `pip install "transformers==4.36.0 --upgrade"` 命令进行安装。
密集型模型和稀疏型模型在过拟合上表现出明显不同的特点。稀疏型模型更易于过拟合，因此我们可以尝试在专家系统内部应用更强的正则化手段，例如不同层次的 dropout 率——对密集层和稀疏层分别设置不同的 dropout 率。
在微调过程中，一个关键的决策是是否采用辅助损失。ST-MoE 的研究人员尝试关闭辅助损失，并发现即使高达 11% 的 Token 被丢弃，模型的质量也几乎不受影响。这表明 Token 丢弃可能是一种有效的防止过拟合的正则化策略。
Switch Transformers 的研究发现，在预训练阶段达到固定的困惑度时，稀疏模型在下游任务中的表现通常不及密集型模型，特别是在逻辑推理较多的任务，如 SuperGLUE 上。然而，在知识密集型的任务，比如 TriviaQA 上，稀疏模型的表现却出奇地好。研究还发现，在微调阶段使用较少数量的专家有助于模型表现。此外，模型在小型任务中表现不佳，但在大型任务中则表现良好，这也证明了其泛化能力的问题。
从图中可以看出，在小型任务（左图）中，稀疏模型在验证集上明显过拟合。而在大型任务（右图）中，MoE 的表现却相当不错。这些图像出自 ST-MoE 的研究论文。
另一个尝试是冻结所有非专家层的权重，结果如预期那样导致了性能大幅下降，因为 MoE 层占据了网络的大部分。相反，仅冻结 MoE 层的参数几乎能达到更新所有参数的效果。这种方法可以加速微调过程，同时减少内存使用。
通过仅冻结 MoE 层，我们不仅能加快训练速度，还能保持模型的质量。这些发现同样源于 ST-MoE 的研究论文。
在调整稀疏型多专家系统（MoEs）时，我们需要特别关注它们独特的微调超参数配置。比如，这类稀疏模型通常更适合较小的批量大小和较高的学习率。
微调后的稀疏模型在采用较低的学习率和较大的批量大小时，其性能会有所提升。此图片来源于 ST-MoE 论文。
你可能会对微调 MoEs 的挑战感到有些沮丧。然而，令人兴奋的是，2023 年 7 月的一篇新论文[MoEs Meets Instruction Tuning](https://arxiv.org/pdf/2305.14705.pdf)展示了一些有趣的实验：
- 单任务微调
- 多任务指令微调
- 在多任务指令微调后进行单任务微调
研究者对比了微调后的 MoE 和 T5 等效模型，发现后者性能更优。但当微调 Flan T5（T5 指令等效模型）MoE 时，MoE 的表现显著提高。不仅如此，Flan-MoE 相比 MoE 的提升幅度，甚至超过了 Flan T5 相比 T5 的提升，这表明 MoEs 可能从指令微调中获益更大，尤其是在任务数量更多的情况下。这与先前建议关闭辅助损失功能的讨论相反，实际上，这种损失可以帮助防止过拟合。
与稠密模型相比，稀疏模型在指令微调方面有更显著的收益。这张图片来自 MoEs Meets Instruction Tuning 论文。
## 何时选择稀疏 MoEs 和稠密模型？
在多机器、高吞吐量的场景中，专家系统是非常有效的。如果预训练的计算预算有限，那么稀疏模型将是更佳的选择。对于 VRAM 较少、吞吐量低的情况，稠密模型则更为合适。
**注意：** 我们不能直接比较稀疏和稠密模型之间的参数数量，因为这两种模型代表的是完全不同的概念。
## 加速 MoEs 的运行
在最初的多专家系统（MoE）研究中，MoE 层被设计成分支结构，这导致计算速度较慢，因为 GPU 本身并不适合这种设计。同时，由于设备间需要传输信息，网络带宽成为了性能瓶颈。下面我们将探讨一些方法，以提高这些模型在预训练和推理阶段的实用性，使 MoEs 运行更加高效。
### 并行处理技术
简要介绍一下并行处理技术：
- **数据并行：** 相同的权重在所有核心上复制，数据则在核心之间分配。
- **模型并行：** 模型在各核心之间分配，数据在所有核心上复制。
- **模型和数据并行：** 我们可以在核心间分配模型和数据。需要注意的是，不同核心处理的是不同批次的数据。
- **专家并行：** 将不同的专家部署在不同的处理单元上。如果与数据并行结合，每个核心将配备一个不同的专家，数据则在所有核心间分配。
在专家并行模式下，不同的处理单元部署了不同的专家，每个处理单元处理不同批次的训练样本。对于非 MoE 层，专家并行的行为类似于数据并行。对于 MoE 层，序列中的 tokens 被发送到拥有相应专家的处理单元。
这幅来自 Switch Transformers 论文的插图展示了不同并行处理技术下如何在各核心间分配数据和模型。
### 容量因子和通信成本
提高容量因子（CF）可以增加模型质量，但同时也会增加通信成本和激活内存的需求。如果全到全的通信速度较慢，那么使用较小的容量因子将是更好的选择。一个较好的初始设置是使用 top-2 路由，1.25 的容量因子，并且每个核心配置一个专家。在评估阶段，可以调整容量因子以减少计算量。
### 服务技巧

> 你可以把 [mistralai/Mixtral-8x7B-Instruct-v0.1](https://ui.endpoints.huggingface.co/new?repository=mistralai%2FMixtral-8x7B-Instruct-v0.1&vendor=aws&region=us-east-1&accelerator=gpu&instance_size=2xlarge&task=text-generation&no_suggested_compute=true&tgi=true&tgi_max_batch_total_tokens=1024000&tgi_max_total_tokens=32000) 部署到推理终端。
MoE 的一个主要问题是它的参数特别多。如果是在本地环境中使用，可能会更倾向于使用一个体积更小的模型。下面，我们来看看几种有助于优化服务的技巧：
- Switch Transformers 的研究者们早期就做了一些模型蒸馏的实验。通过将 MoE 模型蒸馏成更密集的形式，他们能够保留大约 30-40% 的稀疏性优势。因此，蒸馏不仅加快了模型的预训练速度，还能在实际应用中使用更小的模型。
- 最新的一些方法对路由机制进行了改进，能将整个句子或特定任务直接指派给某个专家，从而提取出适合服务的子网络。
- 专家聚合（MoE）技术：这种方法通过合并不同专家的权重，在推理阶段有效减少了模型的参数数量。
### 关于高效训练的更多讨论
FasterMoE（2022 年 3 月）深入分析了 MoE（专家混合体）在高效分布式系统中的表现。研究不仅探讨了不同并行处理策略的理论极限，还包括了如何倾斜专家的受欢迎程度、减少延迟的精细通信调度，以及一种新型的拓扑感知门控机制。这种机制通过选择延迟最低的专家来进行决策，从而实现了高达 17 倍的速度提升。
Megablocks（2022 年 11 月）致力于探索高效的稀疏预训练技术。他们提出了一种新的 GPU 核心，能够处理 MoE 中的动态性。这一创新方法不会丢失任何 Token，并且能够高效地适应现代硬件，带来了显著的速度提升。那么，它的独特之处在哪里呢？与传统的 MoE 使用批量矩阵乘法不同（这种方法假设所有专家的形状和 Token 数量都一样），Megablocks 则通过块稀疏运算来表达 MoE 层，这使得它能够适应不均匀的任务分配。
适用于不同大小专家和不同数量 Token 的块稀疏矩阵乘法，来自 [Megablocks](https://arxiv.org/abs/2211.15841)。
## 开源 MoEs（多专家系统）
目前，有几个开源项目专注于训练 MoEs（多专家系统）：
- Megablocks: [点击这里](https://github.com/stanford-futuredata/megablocks)
- Fairseq: [点击这里](https://github.com/facebookresearch/fairseq/tree/main/examples/moe_lm)
- OpenMoE: [点击这里](https://github.com/XueFuzhao/OpenMoE)
在开放访问 MoEs 领域，以下是一些值得关注的项目：
- [Switch Transformers (Google)](https://huggingface.co/collections/google/switch-transformers-release-6548c35c6507968374b56d1f): 一系列基于 T5 的 MoEs，包含 8 至 2048 个专家。其中最大的模型拥有 1.6 万亿个参数。
- [NLLB MoE (Meta)](https://huggingface.co/facebook/nllb-moe-54b): NLLB 翻译模型的 MoE 版本。
- [OpenMoE](https://huggingface.co/fuzhao): 一个社区项目，发布了基于 Llama 的 MoEs。
- [Mixtral 8x7B (Mistral)](https://huggingface.co/mistralai): 一款高效的 MoE，性能超越 Llama 2 70B，且推理速度更快。该项目还发布了一款 instruct-tuned 模型，详细内容可参见[这篇公告博客](https://mistral.ai/news/mixtral-of-experts/)。
## 激动人心的工作方向
当前的一些有趣研究方向包括：
- 将稀疏 MoE 还原为参数更少但数量相近的密集模型。
- MoEs 的量化。例如 [QMoE](https://arxiv.org/abs/2310.16795) (2023 年 10 月) 通过将 MoEs 量化到每个参数不到 1 位，将原本需要 3.2TB 加速器的 1.6T Switch Transformer 压缩至只需 160GB。
简而言之，一些引人注目的研究领域包括：
- 将 Mixtral 蒸馏成一个密集模型。
- 探索专家合并技术及其对推理时间的影响。
- 对 Mixtral 应用极端量化技术。
## 精选资源推荐
- [《适应性局部专家混合》(1991 年)](https://www.cs.toronto.edu/~hinton/absps/jjnh91.pdf) - 探讨如何结合多个局部专家的智慧来改进决策过程。
- [《深度专家混合中的因式分解表示学习》(2013 年)](https://arxiv.org/abs/1312.4314) - 研究在深度学习中如何更有效地表达和处理信息。
- [《离谱大的神经网络：稀疏门控的混合专家层》(2017 年)](https://arxiv.org/abs/1701.06538) - 揭示了大型神经网络如何通过特殊的结构实现更高效的学习。
- [《GShard：利用条件计算和自动分片技术扩展巨型模型》(2020 年 6 月)](https://arxiv.org/abs/2006.16668) - 展示了如何通过创新技术管理和运行超大规模的 AI 模型。
- [《GLaM：使用混合专家高效扩展语言模型》(2021 年 12 月)](https://arxiv.org/abs/2112.06905) - 探索在语言处理领域中，大型模型如何通过混合专家系统提升效能。
- [《Switch Transformers：使用简单高效的稀疏技术实现万亿参数模型的扩展》(2022 年 1 月)](https://arxiv.org/abs/2101.03961) - 介绍了一种新型的转换器架构，能够处理前所未有的参数量级。
- [《ST-MoE：设计稳定且可迁移的稀疏专家模型》(2022 年 2 月)](https://arxiv.org/abs/2202.08906) - 讨论如何在保持模型稳定性的同时，实现有效的知识迁移。
- [《FasterMoE：对大规模动态预训练模型的训练进行建模和优化》(2022 年 4 月)](https://dl.acm.org/doi/10.1145/3503221.3508418) - 分析和改善大规模预训练模型训练过程的关键策略。
- [《MegaBlocks：利用混合专家实现高效的稀疏训练》(2022 年 11 月)](https://arxiv.org/abs/2211.15841) - 展示了在高效训练大型 AI 模型时，混合专家技术的强大作用。
- [《混合专家与指令调整的结合：大型语言模型的成功秘诀》(2023 年 5 月)](https://arxiv.org/abs/2305.14705) - 揭示了结合指令调整和混合专家技术，如何显著提升大型语言模型的性能。
- [Mixtral-8x7B-v0.1](https://huggingface.co/mistralai/Mixtral-8x7B-v0.1), [Mixtral-8x7B-Instruct-v0.1](https://huggingface.co/mistralai/Mixtral-8x7B-Instruct-v0.1) - 提供了两个实际的模型案例，供读者进一步探索。