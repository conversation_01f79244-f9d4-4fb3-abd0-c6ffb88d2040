---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-18T11:07
tags:
  - AI->-Frontend
  - AI->-Programming
Created: 2023-05-27T10:44
---
  
```JavaScript
<<< Reference >>
download flutter
https://docs.flutter.dev/get-started/install
add your flutter path in to windows environment PAHT
#各种控件演示
https://gallery.flutter.dev/#/
dart is included in flutter when you do install flutter
https://dart.dev/get-dart
download andriod studio
https://developer.android.com/studio
afterwards from andriod studio, install google usb driver and android sdk command-line tools, android sdk
build tools
C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin
C:\Users\<USER>\AppData\Local\Android\Sdk
=============================================
Setup path
1 search env
2. setup path
F:\ResearchDirection\AI\flutter\sdk\flutter\bin
C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools

=============================================
After done all above, start dos prompt and run flutter doctor
flutter doctor
flutter doctor --android-licenses
C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools>flutter doctor
Doctor summary (to see all details, run flutter doctor -v):
[✓] Flutter (Channel stable, 3.10.1, on Microsoft Windows [Version 10.0.22621.1702], locale en-US)
[✓] Windows Version (Installed version of Windows is version 10 or higher)
[✓] Android toolchain - develop for Android devices (Android SDK version 33.0.2)
[✓] Chrome - develop for the web
[✓] Visual Studio - develop for Windows (Visual Studio Enterprise 2022 17.5.3)
[✓] Android Studio (version 2022.2)
[✓] VS Code (version 1.78.2)
[✓] VS Code (version 1.79.0-insider)
[✓] Connected device (3 available)
[✓] Network resources
• No issues found!
Download and install java 11
https://docs.oracle.com/en/java/javase/11/
In the VSCode , Dart extention setup. 
SDK -> setup Flutter and SDK paths
Setup VM
https://developer.android.com/studio/intro/update?hl=zh-cn\#sdk-manager
https://developer.android.com/tools/sdkmanager
sdkmanager --list
sdkmanager --update
sdkmanager --licenses
\#Setup Android devices. enable dev ope
https://developer.android.com/studio/debug/dev-options?hl=zh-cn
You need to install 
SDK Platform
	Android 13.0
SDK Tools
	Android SDK Build-Tool 34-rc4
	Android SDK Command-line Tools(latest)
	Android Emulator
	Android Emulator hypervisor driver
	Android SDK Platform-Tools
	Google USB Driver
	Intel X86 Emulator Accelerator(HAXM Installer)
\#looks you only can do samsuny test in below site or you have to buy one
https://developer.samsung.com/remotetestlab/devices/101/galaxy-mobile
\#install extension in vs code
Android 
ADB Interface for VS Code
Andoird Emulator Launcher
and setup adb and emulator tool path
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
C:\Users\<USER>\AppData\Local\Android\Sdk\emulator
# 有时觉得Dart一行才80个字符太短，调整方法是打开settings.json
把所有的editor.rules改成120
"editor.rulers": [
        
        120
    ],

升级我的开发环境
1. flutter upgrade
2. open Andriod Studio and upgrade
(有时此升级也会升级 Andriod Gradle Plugin (AGP) version)
```
```JavaScript
Start new flutter project
in vcs, ctr+shift+P type flutter:new project
or in console
flutter create myapp
Start create new package
flutter create --template=package hi_download
type st
can create stateless /stateful widget
```
```JavaScript
# use this command will help to fix code
dart fix --dry-run .
dart fix --apply .
\#Run app in mobile
https://marketplace.visualstudio.com/items?itemName=vinicioslc.adb-interface-vscode
First connect your device trough USB
Run ADB:📱 Disconnect from any devices
And run ADB:📱 Reset connected devices port to :5555
And Then ADB:📱 Connect to device IP enter your device (settings > status > ip address) IP address and be fine

\#Dart provided Dev tools 
Ctl+Shirt+P
>Dart:
In side
https://developer.samsung.com/remotetestlab/docs/70/tests-on-devices
启动虚拟手机，
然后下载RDB，运行会监听在一个Port上
然后在虚拟手机界面的左边选项里点connect，
会连上。然后开始上传apk，运行。
\#check this if you face pairing issue
https://stackoverflow.com/questions/70944858/android-studio-bumblebee-wifi-pairing-issue
Looks you have to connect real mobile via android studio for debugging
1. File -> Setting -> Build,Execution, Deployment -> Debugger -> Enable adb mDNS for wireless debugging
disable and enable again
2. Pair using wifi -> use mobile developper option and scan QR code
once pair done , then you can start debugging
```
  
  
```JavaScript
<<< Make APK packag >>>
Assigning an App Icon
Create or Choose Your Icon: First, you need to create or choose the icon that you want to use for your app. The icon should be a square image with dimensions of 1024x1024 pixels for best results.
让白色背景变为透明
https://imglarger.com/BackgroundRemover
Install the Flutter Launcher Icons Package: In your pubspec.yaml file, add the flutter_launcher_icons package to your dev_dependencies:
ask ChatGPT or access below link to get latest version
https://pub.dev/packages/flutter_launcher_icons
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: "^0.13.0"
Note: The version of flutter_launcher_icons might be different based on when you are doing this. Always try to use the latest version.
Configure the Icon: Still in your pubspec.yaml file, add the configuration for your icon:
flutter_icons:
  android: "launcher_icon" 
  ios: true
  image_path: "images/brainicon.png"
Here, the image_path is the location of your icon image file in your project. Adjust it according to your needs.
Run the Package: In your terminal, run the following command:
flutter pub get
dart run flutter_launcher_icons:main
These commands will generate the necessary icons in the Android and iOS project directories.
Generating APK
Open the Terminal: Open the terminal in your IDE or command prompt and navigate to the root of your Flutter project.
Run the build command: Enter the following command to generate a release build:
flutter build apk --release
This command generates a release build for Android. By default, the APK will be unsigned.
Locate the APK file: Once the build completes, you'll find the APK at the following location:
<app dir>/build/app/outputs/flutter-apk/app-release.apk
That's it! You have assigned an app icon and generated an APK for your Flutter app.
Please note, the APK generated using the above command is unsigned. If you want to distribute your app to the Play Store, you will need to sign the APK with your private key.

# build as web app
https://dev.classmethod.jp/articles/flutter-getting-start-web-page/
flutter build web
cd build/web
python -m http.server
python3 -m http.server 8000
or
install node.js and npm
npm install -g http-server
http-server -p 8000
or
dart pub global activate http_server
dart pub global run http_server:http_server
```
  
```JavaScript
Distributing your app on the Google Play Store involves several steps. Here's a high-level overview of the process:
1. **Review the App Publishing Guidelines**: Before you start, make sure you review the Google Play policies and agreements. This includes ensuring your app complies with the content policies of the Play Store.
2. **Create a Google Play Developer Account**: You'll need to create a Google Play Developer Account. There's a one-time $25 registration fee charged for a Google Play Developer account.
3. **Create a Signed APK or App Bundle**: Google Play requires that your APK is signed with a digital certificate before it can be installed on a device. A digital certificate consists of a public key and a private key, both of which are needed for signing your APK. To sign your APK, you can use Android Studio's `Generate Signed Bundle / APK` option or use the `jarsigner` command-line tool that comes with the JDK. In addition to APKs, Google also recommends using Android App Bundles (.aab files), which can reduce the size of your app on users' devices.
4. **Create a Store Listing**: This is where you'll provide details about your app such as the title, description, category, contact details, etc. You'll also need to upload screenshots of your app, and you can provide a promotional video if you have one.
5. **Set Up Pricing and Distribution Details**: Here you can choose whether your app will be free or paid, select the countries where it will be distributed, and specify whether your app is an application or a game.
6. **Release Your App**: Once all the above steps are complete, you can prepare your app for release. There are three types of release you can choose from:
    - **Internal Test**: This is used for testing your app with a limited number of users to get feedback.
    - **Closed Track (Alpha/Beta Testing)**: This is used for testing pre-release versions of your app with a wider set of users to get feedback.
    - **Production Track**: This is the final release of your app to the general public.
For each release, you'll need to create a new release, upload the APK or App Bundle that you signed earlier, and fill out the release details.
7. **Submit the App**: Finally, you can review all the details and submit your app. It may take a few days for your app to be reviewed. Once approved, your app will be published on the Google Play Store.
For more details, you can refer to the official Android documentation on how to prepare and release your app for distribution on the Google Play Store.
```
  
```JavaScript
<<< Some git notes >>>
在VS Code里把一个从其他github repo推送到你自己的github空间里，你可以遵循以下步骤：
克隆项目: 在你的本地环境中克隆你想要复制的项目。这可以通过在命令行中运行以下命令完成：

git clone <其他github repo的URL>
创建新的GitHub仓库: 在你的GitHub账户中创建一个新的仓库。在创建时，不要选择初始化README，.gitignore文件或许可证，因为这些文件可能已经在你刚刚克隆的仓库中存在。
更改远程URL: 切换到你的本地克隆仓库，然后在命令行中运行以下命令，将远程仓库URL更改为你的新GitHub仓库的URL：
git remote set-url origin <你的github repo的URL>
推送到新仓库: 现在你可以将你的更改推送到新的GitHub仓库。这可以通过运行以下命令完成：
git push -u origin master
or
git push -u origin main
（如果你正在使用的分支不是master分支，那么将master替换为你正在使用的分支的名称）
以上步骤都可以在VS Code的终端中完成，VS Code的终端可以通过顶部菜单的终端(Terminal) -> 新建终端(New Terminal)打开。
sometimes it needs authentication, then use below way ,as you are using mobile authentication key, we have to use token here
To clone to my owner github repo
1. create personal token
*********************************************************************************************
2. create one repo in github and change remote repo to my repo
git remote set-url origin https://github.com/netcaster1/VisualGLM-6B
3. push to my repo
git push -u origin main  <- main or master
Username for 'https://github.com': <EMAIL>
Password for 'https://<EMAIL>@github.com':  <<previous token created>>
Enumerating objects: 252, done.
\#to check current remote url
git remote -v
F:\ResearchDirection\AI\flutter\flutter_network>git remote -v
origin  https://github.com/netcaster1/flutter_network (fetch)
origin  https://github.com/netcaster1/flutter_network (push)

\#refresh from imooc -> netcaster
rm -rf xxxx
git clone immoc-github
git remote set-url origin https://github.com/netcaster1/chatgpt_flutter.git
git push -f origin main
another way
https://git.imooc.com/coding-672/chatgpt_flutter.git
git pull origin main
From https://git.imooc.com/coding-672/chatgpt_flutter
 * branch            main       -> FETCH_HEAD
   7047f9b..2c7fa74  main       -> origin/main
error: Your local changes to the following files would be overwritten by merge:
        lib/main.dart
        lib/pages/bottom_navigator.dart
        pubspec.lock
        pubspec.yaml
Please commit your changes or stash them before you merge.
Aborting
Updating 7047f9b..2c7fa74

merge then manually
git stash <- save changed
git pull  <- pull 
git stash pop <- apply change to new one
git remote set-url origin https://github.com/netcaster1/chatgpt_flutter.git
git push origin main
```
  
```JavaScript
download git repo from imooc
git clone https://git.imooc.com/coding-672/login_sdk.git
git clone https://git.imooc.com/coding-672/chat_message.git
此时发现只有.git folder exists,遇到的问题可能是由于远程仓库的默认分支（通常是 master 或 main）不存在，因此在 clone 时无法检出。
solution:
git branch -a   # 查看远程仓库的所有分支
git checkout branch_name # 从中选择一个分支进行检出
in my case: It works. after execute checkout, files coming out.
F:\ResearchDirection\AI\flutter\login_sdk>git branch -a
  remotes/origin/main
F:\ResearchDirection\AI\flutter\login_sdk>git checkout main
Switched to a new branch 'main'
branch 'main' set up to track 'origin/main'.
dart fix --apply .
flutter pub get
flutter clean
flutter build apk

login UI
Registration
course-flag：fg
userName： netcaster
password：12345#
imoocId： 7809138
id: 7809138
orderId： 8329  #订单ID后四位
courseFlag：fg
https://api.devio.org/uapi/swagger-ui.html#/Account/registrationUsingPOST
curl -X POST "https://api.devio.org/uapi/user/registration?userName=netcaster&password=12345%23&imoocId=7809138&orderId=8329&courseFlag=fg" -H "accept: */*" -H "course-flag: fg"
{
  "code": 0,
  "msg": "registration success."
}
Login：
course-flag ： fg
userName： netcaster
password：12345#
```