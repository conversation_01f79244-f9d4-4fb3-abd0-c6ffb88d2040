---
DocFlag:
  - Reference
  - Tested
Updated: 2024-06-01T09:53
tags:
  - AI->-Fine-Tuning
  - AI->-Model
  - AI->-Nvidia
  - AI->-Performance
  - AI->-Theory
URL: https://github.com/mit-han-lab/qserve
Created: 2024-05-10T23:26
---
  
```Python
(QServe) [raysheng@MONSTER:/opt/workspace]$ python -c "import torch; print(torch._C._GLIBCXX_USE_CXX11_ABI)"
False
(QServe) [raysheng@MONSTER:/opt/workspace]$ pip list|grep cuda
nvidia-cuda-cupti-cu12    12.1.105
nvidia-cuda-nvrtc-cu12    12.1.105
nvidia-cuda-runtime-cu12  12.1.105
(QServe) [raysheng@MONSTER:/opt/workspace/researcher]$ python -V
Python 3.10.14
(QServe) [raysheng@MONSTER:/opt/workspace/researcher]$ pip list|grep torch
torch                     2.3.0
torchvision               0.18.0

so we need to download
https://github.com/Dao-AILab/flash-attention/releases/download/v2.5.8/flash_attn-2.5.8+cu122torch2.3cxx11abiFALSE-cp310-cp310-linux_x86_64.whl
\#Check
python
import flash_attn

==========================================================
Dateset
https://huggingface.co/datasets/allenai/WildChat?not-for-all-audiences=true
\#add token
huggingface-cli login
*************************************

\#Must set group size 128 as group size -1 is for per channel quantization
# For L40S, better to use group quantization so I only downloaded checkpoint for group
export MODEL_PATH=./qserve_checkpoints/Yi-34B-QServe-g128 # Please set the path accordingly
GLOBAL_BATCH_SIZE=128 \
python qserve_benchmark.py \
  --model $MODEL_PATH \
  --benchmarking \
  --precision w4a8kv4 \
  --group-size 128
  
export MODEL_PATH=./qserve_checkpoints/Llama-3-8B-Instruct-QServe-g128
  
python qserve_e2e_generation.py \
  --model $MODEL_PATH \
  --ifb-mode \
  --precision w4a8kv4 \
  --quant-path $MODEL_PATH \
  --group-size 128
  
```
  
  
编写了一个Console  
/opt/workspace/qserve/flashChatCli.py
```Python
import argparse
import re
import time
from typing import List, Tuple
import os
from qserve import EngineArgs, LLMEngine, SamplingParams
from qserve.conversation import get_conv_template_name, get_conv_template
from termcolor import colored, cprint
USER_COLOR = "blue"
LLM_COLOR = "yellow"
STATS_COLOR = "magenta"

def extract_assistant_response(text):
    pattern = r"<\|start_header_id\|>assistant<\|end_header_id\|>\n\n(.*?)<\|eot_id\|>"
    match = re.findall(pattern, text, re.DOTALL)
    if match:
        return match[-1].strip()
    return ""

def process_user_input(engine: LLMEngine, conv_template):
    """Continuously process user input and handle the outputs."""
    request_id = 0
    conv = get_conv_template(conv_template)
    sampling_params = SamplingParams(
        temperature=0.0, top_p=1.0, stop_token_ids=[128001, 128009], max_tokens=4096
    )
    while True:
        user_input_lines = []
        while True:
            print(colored("User: ", USER_COLOR, attrs=["bold"]), end="", flush=True)
            user_input = input()
            if user_input == "":
                break
            user_input_lines.append(user_input)
        user_input = "\n".join(user_input_lines)
        if user_input.lower() == "exit":
            break
        elif user_input.lower() == "/reset":
            conv = get_conv_template(conv_template)
            print(colored("Conversation reset.", USER_COLOR, attrs=["bold"]))
            continue
        conv.append_message(conv.roles[0], user_input)
        conv.append_message(conv.roles[1], "")
        prompt = conv.get_prompt()
        succeeded = engine.add_request(str(request_id), prompt, sampling_params)
        if succeeded:
            request_id += 1
        print(colored("LLM is thinking...", color=LLM_COLOR, attrs=["bold"]), end="\r", flush=True)
        start_time = time.time()
        while engine.has_unfinished_requests():
            requests_outputs = engine.step()
            for request_output in requests_outputs:
                if request_output["finished"]:
                    response_text = extract_assistant_response(request_output["text"])
                    print(colored("\nLLM: ", USER_COLOR, attrs=["bold"]), end="", flush=True)
                    cprint(response_text, LLM_COLOR)
                    conv.messages[-1][-1] = response_text
        end_time = time.time()
        elapsed_time_ms = (end_time - start_time) * 1000
        print(colored("Response time: ", STATS_COLOR,attrs=["bold"]), end="", flush=True)
        cprint(f"{elapsed_time_ms:.2f} ms", "green")

def initialize_engine(args: argparse.Namespace) -> LLMEngine:
    """Initialize the LLMEngine from the command line arguments."""
    engine_args = EngineArgs.from_cli_args(args)
    return LLMEngine.from_engine_args(engine_args)

def main(args: argparse.Namespace):
    """Main function that sets up and runs the user input processing."""
    engine = initialize_engine(args)
    conversation_template = get_conv_template_name(args.model)
    process_user_input(engine, conversation_template)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="CLI console for interacting with LLM using qserve"
    )
    parser = EngineArgs.add_cli_args(parser)
    args = parser.parse_args()
    args.ifb_mode = True
    main(args)
```
#   
To Call  
  
  
```Python
\#default will call llama3
python flashChatCli.py 
python flashChatCli.py --model ./qserve_checkpoints/Llama-3-8B-Instruct-QServe-g128 --quant-path ./qserve_checkpoints/Llama-3-8B-Instruct-QServe-g128
\#yi seems still not fine
python flashChatCli.py --model ./qserve_checkpoints/Yi-34B-QServe-g128 --quant-path ./qserve_checkpoints/Yi-34B-QServe-g128
```
# QServe: _**W4A8KV4**_ Quantization and System Co-design for Efficient LLM Serving
[[Paper](https://arxiv.org/abs/2405.04532)] [[LMQuant Quantization Algorithm Library](https://github.com/mit-han-lab/lmquant)] [[Website](https://hanlab.mit.edu/projects/qserve)]
**QServe: Efficient and accurate LLM serving system** on GPUs with W4A8KV4 quantization (4-bit weights, 8-bit activations, and 4-bit KV cache). Compared with leading industry solution TensorRT-LLM, QServe achieves **1.2x-1.4x higher throughput** when serving Llama-3-8B, and **2.4x-3.5x higher throughput** when serving Qwen1.5-72B, on L40S and A100 GPUs. QServe also allows users to achieve A100-level throughput on **3x cheaper** L40S GPUs.
[![](https://github.com/mit-han-lab/qserve/raw/main/assets/figures/teaser.png)](https://github.com/mit-han-lab/qserve/raw/main/assets/figures/teaser.png)
[![](https://github.com/mit-han-lab/qserve/raw/main/assets/figures/efficiency.png)](https://github.com/mit-han-lab/qserve/raw/main/assets/figures/efficiency.png)
## Introduction
Quantization can accelerate large language model (LLM) inference. Going beyond INT8 quantization, the research community is actively exploring even lower precision, such as INT4. Nonetheless, state-of-the-art INT4 quantization techniques only accelerate low-batch, edge LLM inference, failing to deliver performance gains in large-batch, cloud-based LLM serving. We uncover a critical issue: existing INT4 quantization methods suffer from significant runtime overhead (20-90%) when **dequantizing either weights or partial sums** on GPUs. To address this challenge, we introduce **QoQ**, a W4A8KV4 quantization algorithm with 4-bit weight, 8-bit activation, and 4-bit KV cache. QoQ stands for **quattuor-octo-quattuor**, which represents 4-8-4 in Latin. QoQ is implemented by the **QServe** inference library that achieves measured speedup. The key insight driving QServe is that the efficiency of LLM serving on GPUs is critically influenced by **operations on low-throughput CUDA cores**. Building upon this insight, in QoQ algorithm, we introduce progressive quantization that can allow low dequantization overhead in W4A8 GEMM. Additionally, we develop SmoothAttention to effectively mitigate the accuracy degradation incurred by 4-bit KV quantization. In the QServe system, we perform compute-aware weight reordering and take advantage of register-level parallelism to reduce dequantization latency. We also make fused attention memory-bound, harnessing the performance gain brought by KV4 quantization. As a result, QServe improves the maximum achievable serving throughput of Llama-3-8B by **1.2×** on A100, **1.4×** on L40S; and Qwen1.5-72B by **2.4×** on A100, **3.5×** on L40S, compared to TensorRT-LLM. Remarkably, QServe on L40S GPU can achieve even higher throughput than TensorRT-LLM on A100. Thus, QServe effectively reduces the dollar cost of LLM serving by **3×**.
**The current release supports:**
- Blazingly fast system support for **QoQ W4A8KV4** quantization (Algorithim release: [LMQuant](https://github.com/mit-han-lab/lmquant));
- Pre-quantized QServe model zoo with **W4A8KV4 QoQ** for mainstream LLMs;
- **Fully PyTorch-based** runtime and user interface for LLM serving, with **TensorRT-LLM-level efficiency** and **PyTorch-level flexibility**;
- Full support for **in-flight batching** and **paged attention**;
- Efficient **fused** CUDA kernels for **W4A8**/W8A8 GEMM and **KV4**/KV8 attention;
- Easy-to-use examples on speed benchmarking and **large-scale end-to-end content generation** (with W4A8KV4, in-flight batching and paged attention).
## News
- [2024/05] 🔥 QServe is publicly released! Check our paper [here](https://arxiv.org/abs/2405.04532).
## Contents
- [QServe:](https://github.com/mit-han-lab/qserve#qserve-w4a8kv4-quantization-and-system-co-design-for-efficient-llm-serving) [_**W4A8KV4**_](https://github.com/mit-han-lab/qserve#qserve-w4a8kv4-quantization-and-system-co-design-for-efficient-llm-serving) [Quantization and System Co-design for Efficient LLM Serving](https://github.com/mit-han-lab/qserve#qserve-w4a8kv4-quantization-and-system-co-design-for-efficient-llm-serving)
    - [Introduction](https://github.com/mit-han-lab/qserve#introduction)
    - [News](https://github.com/mit-han-lab/qserve#news)
    - [Contents](https://github.com/mit-han-lab/qserve#contents)
    - [Installation](https://github.com/mit-han-lab/qserve#installation)
    - [QServe Model Zoo](https://github.com/mit-han-lab/qserve#qserve-model-zoo)
    - [Usage and Examples](https://github.com/mit-han-lab/qserve#usage-and-examples)
    - [Results](https://github.com/mit-han-lab/qserve#results)
        - [Accuracy Evaluation](https://github.com/mit-han-lab/qserve#accuracy-evaluation)
        - [Efficiency Benchmarks](https://github.com/mit-han-lab/qserve#efficiency-benchmarks)
    - [Reference](https://github.com/mit-han-lab/qserve#reference)
    - [Related Projects](https://github.com/mit-han-lab/qserve#related-projects)
    - [Acknowledgement](https://github.com/mit-han-lab/qserve#acknowledgement)
## Installation
1. Clone this repository and navigate to the corresponding folder:
```Plain
git clone https://github.com/mit-han-lab/qserve
cd qserve
```
1. Install QServe
```Plain
conda create -n QServe python=3.10 -y
conda activate QServe
pip install --upgrade pip  # enable PEP 660 support
pip install -e .
pip install flash-attn --no-build-isolation
```
We recommend starting an interactive python CLI interface and run
```Plain
import flash_attn
```
to check whether FlashAttention-2 is installed successfully. If not, we recommend downloading pre-built wheels from [here](https://github.com/Dao-AILab/flash-attention/releases/tag/v2.5.8). Please notice:
- PyTorch version needs to exactly match with the version specified in the `.whl` name;
- Check out both `cxx11abiTRUE` and `cxx11abiFALSE` wheels if one of them does not work;
- It's recommended to match CUDA version specified in the `.whl` filename, but minor mismatches (e.g. 12.1 vs 12.2, or even 11.8 vs 12.2) usually do not matter.
1. Compile the CUDA kernels.
```Plain
cd kernels
python setup.py install
```
1. If you want to clone our model zoo, please make sure that `git-lfs` is installed.
## QServe Model Zoo
We provide pre-quantized checkpoints for multiple model families. For example, for Llama-3-8B model, please run the following commands to download:
```Plain
# git lfs install  # install git lfs if not already
mkdir -p qserve_checkpoints && cd qserve_checkpoints
git clone https://huggingface.co/mit-han-lab/Llama-3-8B-QServe
```
For other models, please refer to the detailed support list for the links to download:
|   |   |   |
|---|---|---|
|Models|W4A8-per-channel|W4A8-g128|
|Llama3|✅ [8B](https://huggingface.co/mit-han-lab/Llama-3-8B-QServe)/70B|✅ [8B](https://huggingface.co/mit-han-lab/Llama-3-8B-QServe-g128)/70B|
|Llama3-Instruct|✅ [8B](https://huggingface.co/mit-han-lab/Llama-3-8B-Instruct-QServe)/70B|✅ [8B](https://huggingface.co/mit-han-lab/Llama-3-8B-Instruct-QServe-g128)/70B|
|Llama2|✅ [7B](https://huggingface.co/mit-han-lab/Llama-2-7B-QServe)/[13B](https://huggingface.co/mit-han-lab/Llama-2-13B-QServe)/70B|✅ [7B](https://huggingface.co/mit-han-lab/Llama-2-7B-QServe-g128)/[13B](https://huggingface.co/mit-han-lab/Llama-2-13B-QServe-g128)/70B|
|Vicuna|✅ [7B](https://huggingface.co/mit-han-lab/vicuna-7b-v1.5-QServe)/[13B](https://huggingface.co/mit-han-lab/vicuna-13b-v1.5-QServe)/30B|✅ [7B](https://huggingface.co/mit-han-lab/vicuna-7b-v1.5-QServe-g128)/[13B](https://huggingface.co/mit-han-lab/vicuna-13b-v1.5-QServe-g128)/30B|
|Mistral|✅ [7B](https://huggingface.co/mit-han-lab/Mistral-7B-v0.1-QServe)|✅ [7B](https://huggingface.co/mit-han-lab/Mistral-7B-v0.1-QServe-g128)|
|Yi|✅ [34B](https://huggingface.co/mit-han-lab/Yi-34B-QServe)|✅ [34B](https://huggingface.co/mit-han-lab/Yi-34B-QServe-g128)|
|Qwen|✅ 72B|✅ 72B|
For flagship datacenter GPUs such as the A100, it is recommended to use QServe-per-channel, while for inference datacenter GPUs like the L40S, QServe-per-group is the recommended approach.
If you are interested in generating the quantized checkpoints on your own, please follow the instructions in [LMQuant](https://github.com/mit-han-lab/lmquant) to run QoQ quantization and dump the fake-quantized models. We then provide checkpoint converter to real-quantize and pack the model into QServe format:
```Plain
python checkpoint_converter.py --model-path <hf-model-path> --quant-path <fake-quant-model-path> --group-size -1 --device cpu
# <fake-quant-model-path> is a directory generated by LMQuant, including model.pt and scale.pt
```
We also provide a [script](https://github.com/mit-han-lab/qserve/blob/main/scripts/ckpt_converter/convert.sh) to run the checkpoint converter. The final model will be automatically stored under `qserve_checkpoints`.
## Usage and Examples
We support both offline benchmarking and online generation (in-flight-batching) in QServe.
1. Offline speed benchmarking (Batched input sequences, fixed context length = 1024 and generation length = 512). We take Llama-3-8B (per-channel quant) as an example here. Please make sure that you have already downloaded the QoQ-quantized QServe model.
```Plain
export MODEL_PATH=./qserve_checkpoints/Llama-3-8B-QServe # Please set the path accordingly
GLOBAL_BATCH_SIZE=128 \
python qserve_benchmark.py \
  --model $MODEL_PATH \
  --benchmarking \
  --precision w4a8kv4 \
  --group-size -1
```
If you hope to use larger batch sizes such as 256, you may need to change `NUM_GPU_PAGE_BLOCKS` to a larger value than the automatically-determined value on A100. For example:
```Plain
export MODEL_PATH=./qserve_checkpoints/Llama-3-8B-QServe # Please set the path accordingly
GLOBAL_BATCH_SIZE=256 \
NUM_GPU_PAGE_BLOCKS=6400 \
python qserve_benchmark.py \
  --model $MODEL_PATH \
  --benchmarking \
  --precision w4a8kv4 \
  --group-size -1
```
1. This is an online demonstration of batched generation, showcasing in-flight batching, paged attention of W4A8KV4 QoQ LLMs. We will randomly sample a set of safety-moderated conversations from the [WildChat](https://huggingface.co/datasets/allenai/WildChat) dataset and process them efficiently through in-flight batching.
  
```Plain
export MODEL_PATH=./qserve_checkpoints/Llama-3-8B-Instruct-QServe # Please set the path accordingly
python qserve_e2e_generation.py \
  --model $MODEL_PATH \
  --ifb-mode \
  --precision w4a8kv4 \
  --quant-path $MODEL_PATH \
  --group-size -1
```
1. Argument list in QServe
    
    Below are some frequently used arguments in QServe interface:
    
- - `-model`: Path to the folder containing hf model configs. Can be the same as `-quant-path` if you directly download the models from QServe model zoo.
- - `-quant-path`: Path to the folder containing quantized LLM checkpoints.
- - `-precision`: The precision for GEMM in QServe, please choose from the following values: `w4a8kv4`, `w4a8kv8`, `w4a8` (means `w4a8kv8`), `w8a8kv4`, `w8a8kv8`, `w8a8` (means `w8a8kv8`). Default: `w4a8kv4`.
- - `-group-size`: Group size for weight quantization, -1 means per-channel quantization. QServe only supports -1 or 128. Please make sure your group size matches the checkpoint.
- - `-max-num-batched-tokens`: Maximum number of batched tokens per iteration. Default: 262144.
- - `-max-num-seqs`: Maximum number of sequences per iteration. Default: 256. Remember to increase it if you want larger batch sizes.
- - `-ifb-mode`: Enable in-flight batching mode. Suggest to activate in e2e generation.
- - `-benchmarking`: Enable speed profiling mode. Benchmark settings aligned with TensorRT-LLM.
    
    Environment variables in QServe:
    
- `GLOBAL_BATCH_SIZE`: Batch size used in offline speed benchmarking.
    
- `NUM_GPU_PAGE_BLOCKS`: Number of pages to be allocated on GPU. If not specified, it will be automatically determined based on available GPU memory. Note that the current automatic GPU page allocation algorithm is very conservative. It is recommended to manually set this value to a larger number if you observe that GPU memory utilization is relatively low.
    
1. One-line scripts:
We also provide sample scripts in QServe.
- End to end generation: `./scripts/run_e2e.sh`;
- Speed benchmarking: `./scripts/benchmark/benchmark_a100.sh` or `./scripts/benchmark/benchmark_l40s.sh`.
These scripts are expected to be executed in the QServe project folder (not in the `scripts` folder). Please note that `git-lfs` is needed for downloading QServe benchmark config files from huggingface before running the benchmark scripts.
## Results
We evaluate QServe W4A8KV4 quantization on a wide range of mainstream LLMs. QServe consistently outperforms existing W4A4 or W4A8 solutions from the accuracy perspective, while providing State-of-the-Art LLM serving efficiency.
### Efficiency Benchmarks
When serving the large language models Llama-3-8B and Qwen1.5-72B on L40S and A100 GPUs, QServe demonstrates superior performance, achieving **1.2x-1.4x higher throughput** compared to the leading industry solution, TensorRT-LLM, for Llama-3-8B, and a **2.4x-3.5x higher throughput** for Qwen1.5-72B. It is also able to **deliver higher throughput** and **accomodate the same batch size** on **L40S** compared with TensorRT-LLM on **A100** for six of eight models benchmarked, effectively saving the dollar cost of LLM serving by around 3x.
Benchmarking setting: the criterion is maximum achieveable throughput on NVIDIA GPUs, and the input context length is 1024 tokens, output generation length is 512 tokens. For all systems that support paged attention, we enable this feature. In-flight batching is turned off in the efficiency benchmarks.
|   |   |   |   |   |   |   |   |   |
|---|---|---|---|---|---|---|---|---|
|L40S (48G)|Llama-3-8B|Llama-2-7B|Mistral-7B|Llama-2-13B|Llama-30B|Yi-34B|Llama-2-70B|Qwen-1.5-72B|
|TRT-LLM-FP16|1326|444|1566|92|OOM|OOM|OOM|OOM|
|TRT-LLM-W4A16|1431|681|1457|368|148|313|119|17|
|TRT-LLM-W8A8|2634|1271|2569|440|123|364|OOM|OOM|
|Atom-W4A4|--|2120|--|--|--|--|--|--|
|QuaRot-W4A4|--|805|--|413|133|--|--|15|
|QServe-W4A8KV4|**3656**|**2394**|**3774**|**1327**|**504**|**869**|**286**|**59**|
|Throughput Increase*|**1.39x**|**1.13x**|**1.47x**|**3.02x**|**3.41x**|**2.39x**|**2.40x**|**3.47x**|
|   |   |   |   |   |   |   |   |   |
|---|---|---|---|---|---|---|---|---|
|A100 (80G)|Llama-3-8B|Llama-2-7B|Mistral-7B|Llama-2-13B|Llama-30B|Yi-34B|Llama-2-70B|Qwen-1.5-72B|
|TRT-LLM-FP16|2503|1549|2371|488|80|145|OOM|OOM|
|TRT-LLM-W4A16|2370|1549|2403|871|352|569|358|143|
|TRT-LLM-W8A8|2396|2334|2427|1277|361|649|235|53|
|Atom-W4A4|--|1160|--|--|--|--|--|--|
|QuaRot-W4A4|--|1370|--|289|267|--|--|68|
|QServe-W4A8KV4|**3005**|**2908**|**2970**|**1741**|**749**|**803**|**419**|**340**|
|Throughput Increase*|**1.20x**|**1.25x**|**1.22x**|**1.36x**|**2.07x**|**1.23x**|**1.17x**|**2.38x**|
The absolute token generation throughputs of QServe and baseline systems (Unit: tokens/second. `--` means unsupported). All experiments were conducted under the same device memory budget. Throughput increase of QServe is calculated with regard to the best baseline in each column. It is recommended to use QServe-per-channel on high-end datacenter GPUs like A100 and QServe-per-group is recommended on inference GPUs like L40S.
Max throughput batch sizes used by QServe:
|   |   |   |   |   |   |   |   |   |
|---|---|---|---|---|---|---|---|---|
|Device|Llama-3-8B|Llama-2-7B|Mistral-7B|Llama-2-13B|Llama-30B|Yi-34B|Llama-2-70B|Qwen-1.5-72B|
|L40S|128|128|128|75|32|64|24|4|
|A100|256|190|256|128|64|196|96|32|
We recommend direcly setting the `NUM_GPU_PAGE_BLOCKS` environmental variable to `25 * batch size`, since in our benchmarking setting we have a context length of 1024 and generation length of 512, which corresponds to 24 pages (each page contains 64 tokens). We leave some buffer by allocating one more page for each sequence.
### Accuracy Evaluation
QServe also maintains high accuracy thanks to the QoQ algorithm provided in our [LMQuant](https://github.com/mit-han-lab/lmquant) quantization library.
Below is the WikiText2 perplexity evaluated with 2048 sequence length. The lower is the better.
|   |   |   |   |   |   |   |   |   |   |   |
|---|---|---|---|---|---|---|---|---|---|---|
|Models|Precision|Llama-3 8B|Llama-2 7B|Llama-2 13B|Llama-2 70B|Llama 7B|Llama 13B|Llama 30B|Mistral 7B|Yi 34B|
|FP16||6.14|5.47|4.88|3.32|5.68|5.09|4.10|5.25|4.60|
|SmoothQuant|W8A8|6.28|5.54|4.95|3.36|5.73|5.13|4.23|5.29|4.69|
|GPTQ-R|W4A16 g128|6.56|5.63|4.99|3.43|5.83|5.20|4.22|5.39|4.68|
|AWQ|W4A16 g128|6.54|5.60|4.97|3.41|5.78|5.19|4.21|5.37|4.67|
|QuaRot|W4A4|8.33|6.19|5.45|3.83|6.34|5.58|4.64|5.77|NaN|
|Atom|W4A4 g128|7.76|6.12|5.31|3.73|6.25|5.52|4.61|5.76|4.97|
|QoQ|W4A8KV4|6.89|5.75|5.12|3.52|5.93|5.28|4.34|5.45|4.74|
|QoQ|W4A8KV4 g128|6.76|5.70|5.08|3.47|5.89|5.25|4.28|5.42|4.76|
- SmoothQuant is evaluated with per-tensor static KV cache quantization.
## Reference
If you find QServe useful or relevant to your research and work, please kindly cite our paper:
```Plain
@article{lin2024qserve,
  title={QServe: W4A8KV4 Quantization and System Co-design for Efficient LLM Serving},
  author={Lin*, Yujun and Tang*, Haotian and Yang*, Shang and Zhang, Zhekai and Xiao, Guangxuan and Gan, Chuang and Han, Song},
  journal={arXiv preprint arXiv:2405.04532},
  year={2024}
}
```
## Team
The QServe serving library is maintained by the following research team:
- [Haotian Tang](http://kentang.net/), system lead, MIT EECS;
- [Shang Yang](https://ys-2020.github.io/), system lead, MIT EECS;
- [Yujun Lin](https://yujunlin.com/), algorithm lead, MIT EECS;
- [Zhekai Zhang](https://hanlab.mit.edu/team/zhekai-zhang), system evaluation, MIT EECS;
- [Guangxuan Xiao](https://guangxuanx.com/), algorithm evaluation, MIT EECS;
- [Chuang Gan](https://people.csail.mit.edu/ganchuang), advisor, UMass Amherst and MIT-IBM Watson AI Lab;
- [Song Han](https://songhan.mit.edu/), advisor, MIT EECS and NVIDIA.
## Related Projects
The following projects are highly related to QServe. Our group has developed full-stack application-algorithm-system-hardware support for efficient large models, receiving **9k+ GitHub stars** and **over 1M Huggingface community downloads**.
You are also welcome to check out [MIT HAN LAB](https://hanlab.mit.edu/) for other exciting projects on **Efficient Generative AI**!
- [**Algorithm**] [LMQuant: Large Foundation Models Quantization](https://github.com/mit-han-lab/lmquant)
    
- [**Algorithm**] [AWQ: Activation-aware Weight Quantization for LLM Compression and Acceleration](https://github.com/mit-han-lab/llm-awq)
    
- [**System**] [TinyChat: Efficient and Lightweight Chatbot with AWQ](https://github.com/mit-han-lab/llm-awq/tree/main/tinychat)
    
- [**Application**] [VILA: On Pretraining of Visual-Language Models](https://github.com/Efficient-Large-Model/VILA)
    
- [**Algorithm**] [SmoothQuant: Accurate and Efficient Post-Training Quantization for Large Language Models](https://github.com/mit-han-lab/smoothquant)
    
- [**Algorithm**] [StreamingLLM: Efficient Streaming Language Models with Attention Sinks](https://github.com/mit-han-lab/streaming-llm)
    
- [**Hardware**] [SpAtten: Efficient Sparse Attention Architecture with Cascade Token and Head Pruning](https://arxiv.org/abs/2012.09852)
    
## Acknowledgement
We thank Julien Demouth, Jun Yang, and Dongxu Yang from NVIDIA for the helpful discussions. QServe is also inspired by many open-source libraries, including (but not limited to) [TensorRT-LLM](https://github.com/NVIDIA/TensorRT-LLM), [vLLM](https://github.com/vllm-project/vllm), [vLLM-SmoothQuant](https://github.com/vllm-project/vllm/pull/1112), [FlashAttention-2](https://github.com/Dao-AILab/flash-attention), [LMDeploy](https://github.com/InternLM/lmdeploy), [TorchSparse++](https://github.com/mit-han-lab/torchsparse), [GPTQ](https://arxiv.org/abs/2210.17323), [QuaRot](https://arxiv.org/abs/2404.00456) and [Atom](https://arxiv.org/abs/2310.19102).