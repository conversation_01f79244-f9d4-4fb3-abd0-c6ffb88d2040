---
Updated: 2024-02-19T10:23
tags:
  - AI->-CV
  - AI->-Stable-Diffusion
  - AI->-Theory
  - AI->-Video
Created: 2024-02-19T10:23
---
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sVzuOaiaP2wuTR6LiaA7kxCicYwwQBULK1hq9Micos5Cibz9AlNZqnfQnicag/0?wx_fmt=jpeg)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sVzuOaiaP2wuTR6LiaA7kxCicYwwQBULK1hq9Micos5Cibz9AlNZqnfQnicag/0?wx_fmt=jpeg)
==**O**====penAI Sora文生视频（图像看作单帧视频）一放出就炸翻整个AI 圈，也是ChatGPT掀起GenAI热潮时隔一年后，OpenAI再次史诗级的更新。OpenAI 随后公布的技术综述[文献1]，难掩其勃勃雄心：视频生成模型作为世界模拟器。==
==笔者春节前原计划整理一下对Google Lumiere 文生视频的认知，多个因素遗憾推迟。对比看两者大的技术方向均选择了扩散模型，却也有许多关键细节不同。恰好可以借着 OpenAI 技术综述来提纲挈领，一起梳理一下，为什么笔者觉得这是又一史诗级的更新。==
==一、Spacetime Latent Patches 潜变量时空碎片, 建构视觉语言系统==
==在“==[==**ChatGPT是第一个真正意义的人工通用智能**==](https://mp.weixin.qq.com/s?__biz=MzI2MjU4MDYwOA==&mid=2247484251&idx=1&sn=a3ed8006077c2d21e1c7829c11f021c2&scene=21#wechat_redirect)==”中，笔者总结过大语言模型借助Embedding将人类的语言 “编码”成自己的语言，然后通过注意力Attention从中提取各种丰富的知识和结构，加权积累与关联生成自己的语言，然后“编码”回人类的语言。==
==与ChatGPT首先引入Token Embedding 思路一致，针对视觉数据的建模方法则作为构建Sora最重要的第一步。碎片Patch已经被证明是一个有效的视觉数据表征模型，且高度可扩展表征不同类型的视频和图像。将视频压缩到一个低维的潜变量空间，然后将其拆解为时空碎片Spacetime Latent Patches。====**笔者觉得时空碎片是时空建模的关键，统一了时空分割的"语言"**====。==
==有了时空碎片这一统一的语言，Sora 自然解锁了多种技能：1. 自然语言理解，采用DALLE3 生成视频文本描述，用GPT丰富文本prompts ，作为合成数据训练Sora,  架起了GPT 与 Sora语言空间的更精确关联，等于在Token与Patch 之间统一了“文字”；2. 图像视频作为prompts，用户提供的图像或视频可以自然的编码为时空碎片Patch，用于各种图像和视频编辑任务 -- 静态图动画、扩展生成视频、视频连接或编辑等。==    
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sYoSowySWEAicJ2t1CPKzvicHG4PCwuhHkwVs959p7sgr3SiamjRCrbxVg/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sYoSowySWEAicJ2t1CPKzvicHG4PCwuhHkwVs959p7sgr3SiamjRCrbxVg/640?wx_fmt=png)
==二、扩散模型与Diffusion Transformer,组合成强大的信息提取器==
==OpenAI 讲Sora 是一个Diffusion Transformer，这来自伯克利学者的工作Diffusion Transformer (DiT)："采用Transformer的可扩展扩散模型 Scalable diffusion models with transformers"[文献2]，整体架构如下：==
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sL6uPOicqWUXeWichjvfHcxySfRAiboNDPU5dia5saic99g0syzxrUMjricEw/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sL6uPOicqWUXeWichjvfHcxySfRAiboNDPU5dia5saic99g0syzxrUMjricEw/640?wx_fmt=png)
==Diffusion Transformer (DiT)架构。左：我们训练调节的潜DiT模型。输入潜变量被分解成几个patch并由几个DiT块处理。右：DiT块的细节。我们对标准Transformer的变体进行了实验，这些变体通过自适应层归一化、交叉注意力和额外的输入token做调节。自适应层归一化效果最好。==
==扩散模型的工作原理是通过连续添加高斯噪声来破坏训练数据，然后通过逆转这个加噪过程来学习恢复数据。训练后可以使用扩散模型来生成数据，只需通过学习到的去噪过程来传递随机采样的噪声。扩散模型是一种潜变量模型，逐渐向数据添加噪声，以获得近似的后验q(x1:T|x0)，其中x1，...，xT是与x0具有相同维度的潜变量。==
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sfZDG4f7naShlFxzLnegsSwLkxFYlbwmUQ23cb7DW7gYG9WIFUgGYaA/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sfZDG4f7naShlFxzLnegsSwLkxFYlbwmUQ23cb7DW7gYG9WIFUgGYaA/640?wx_fmt=png)
==图像渐进地转化为纯高斯噪声。训练扩散模型的目标是学习逆过程，即训练pθ(xt−1|xt)。通过沿着这个过程链向后遍历，可以生成新的数据。==
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sic9qg1KZfD4TWp44pOh5iasth26Jr70fajXO1ibjgGUjHwHr09qmvaZ8Q/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sic9qg1KZfD4TWp44pOh5iasth26Jr70fajXO1ibjgGUjHwHr09qmvaZ8Q/640?wx_fmt=png)
==从信息熵的角度可以这样理解：结构化信息信息熵低，多轮加高斯噪音，提高其信息熵，逐步掩盖原来的结构信息。本就无序的非结构化部分，信息熵很高，添加少量高斯噪音，甚至不用添加高斯噪音，已然很无序。==
==在此视角下，学习到的内容其实是原来结构化信息（如图像）的“底片”。类似化学上的酸碱中和，本来很酸的地方，得放更多的碱，现在我们学到了放碱的分布和节奏，反过来，剔除碱的分布，酸的分布就被还原了。==
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sOibPa3xKuCUzU6VfyJqkzIqDHdXU5xnw97Heia4fa4akm9hfK3fqx4DQ/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sOibPa3xKuCUzU6VfyJqkzIqDHdXU5xnw97Heia4fa4akm9hfK3fqx4DQ/640?wx_fmt=png)
==基础的扩散模型，过程中不降维、无压缩，还原度比较高。学习过程中的概率分布作为潜变量参数化，训练获取其近似分布，用KL散度计算概率分布之间的距离[文献3]。Diffusion Transformer (DiT) 因为引入Transformer 做多层多头注意力和归一化，因而引入了降维和压缩，diffusion方式下的底片信息提取过程，原理与==[==**LLM的重整化**==](https://mp.weixin.qq.com/s?__biz=MzI2MjU4MDYwOA==&mid=2247484547&idx=1&sn=65f4168165b6d852106c91a10caade58&scene=21#wechat_redirect)==无异。==
==三、DiT应用于潜变量时空碎片，学习获得海量视频中时空碎片的动态关联==
==与“LLM在其高维语言空间中通过Transformer提取人类语言中无数的结构与关联信息”类似，====**Sora是个基于扩散模型的Transformer，被用于从高维的时空碎片张成的空间中，观察并提取丰富的时空碎片之间的关联与演化的动态过程**====。如果把前者对应人类读书，后者就是人类的视觉观察。==
==遗憾的是OpenAI的技术综述没有提供技术细节，不过笔者觉得大家可以参照Google Lumiere的技术原理来大胆推演一下。视频其实是记录了时空信息的载体: 时空碎片patch可以看作是三维空间的点集(x,y,z)的运动(t)或者说其实是个四维时空模型(x,y,z,t)。Sora和Lumiere之类的生成模型的第一步都是如何从中提取出相应的关键信息。==
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sSBm3p19AyDZiaVjdGcvVRcm0EPCcZBwrUhADaxX6icq0q3YOBrJsGnfA/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sSBm3p19AyDZiaVjdGcvVRcm0EPCcZBwrUhADaxX6icq0q3YOBrJsGnfA/640?wx_fmt=png)
==Lumiere STUNet架构。将预训练的T2I U-Net架构(Ho et al.， 2022a)“膨胀”到一个时空UNet(STUNet)，在空间和时间上对视频进行上下采样。(a)STUNet激活图的示例；颜色表示不同时序模块产生的特征：(b)基于卷积的块，由预训练的T2I层和因子化时空卷积组成，以及(c)在最粗的U-Net级别上基于注意力的块，其中预训练的T2I层和时间注意力。由于视频表征在最粗的级别上被压缩，我们使用有限的计算开销堆叠几个时间注意力层。==
==谷歌Lumiere: A Space-Time Diffusion Model for Video Generation [文献4]也选择了扩散模型，堆叠了归一化与注意力层，类似Sora的DiT，但细节如时长、分辨率、长宽比等的处理方式不同。细节决定成败，====**OpenAI 称Sora摒弃了“其他文生视频调整视频大小、裁剪或修剪到标准大小的通常做法”**====，以可变时长、原始分辨率与长宽比训练视频生成获得重要优势，如采样灵活性，改进的创作与成帧。==
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4snD4GmmLA5Ul9AvhbKNoQibOGiaZic9DibRO5N08FFpD1qzwPiaPoRsQ2Piaw/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4snD4GmmLA5Ul9AvhbKNoQibOGiaZic9DibRO5N08FFpD1qzwPiaPoRsQ2Piaw/640?wx_fmt=png)
==四、Sora 或Lumiere 视频学习与生成的技术背后蕴含的原理分析==
==读完Sora的技术综述， 笔者第一感觉 Sora其实是在时空潜变量碎片上学习到了可视层面或者表面意义上的SSM（State Space Model）, 从而在视频生成上展现出强大的涌现能力：人和景物在三维空间移动一致性；长程时间相关性与对象持久性，如事物被遮挡后重现；事物与周边世界的互动性；仿真数字世界等等。====**OpenAI认为持续扩大视频模型的规模，将可以用来模拟整个物理和数字世界，毕竟它们纯粹是尺度的现象（they are purely phenomena of scale）**====。==    
==让我们回顾一下“==[==**Transformer 的后浪来了**==](https://mp.weixin.qq.com/s?__biz=MzI2MjU4MDYwOA==&mid=2247486712&idx=1&sn=d7027e3bb9ae523e35c56bc51110b951&scene=21#wechat_redirect)==”中笔者总结过的SSM整体思维模型：==
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4s1CC5LOXSV1v5nWPPyd50NAft70F75nRCOHX4ognl1FFALmSqs0OqTA/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4s1CC5LOXSV1v5nWPPyd50NAft70F75nRCOHX4ognl1FFALmSqs0OqTA/640?wx_fmt=png)
==1.状态空间对事物的表征和刻画：状态空间的高维度，某时刻的信息，即某时刻的事物的能量的概率分布，是众多维度的联合概率分布，各维度都可能具有连续性和非线性，如何用线性系统近似，并最大努力消除非线性的影响非常关键；不同层次的潜变量空间，对信息的提取，和粗颗粒度逐层抽象，都需要类似重整化群 RG中的反复归一化，以消除“近似非线性处理”对整体概率为 1 的偏离。 关于重整化群信息提取的原理，请参考笔者梳理的“==[==**大模型认知框架**==](https://mp.weixin.qq.com/s?__biz=MzI2MjU4MDYwOA==&mid=2247486218&idx=1&sn=b5f8a4ca808e330202fa5b5142f75c89&scene=21#wechat_redirect)==”，此处不再赘述。这里Sora采用的 Diffusion Transformer (DiT)架构与谷歌Lumiere 采用的Space-Time UNet (STUNet) 都具备注意力与归一化，神经网路架构差异看起来主要来自是否采用“调整视频大小、裁剪或修剪到标准大小的通常做法”。==    
==2.状态空间的动态性：即从时间的维度，研究整个状态空间的变迁。这个变迁是状态空间的大量非时间维度的信息逐层提取，叠加时间这一特殊维度的（状态-时间）序列sequence。不管是高维度低层次的细颗粒度的概率分布的时间变化，还是低维度高层次的粗颗粒度概率分布的时间变化，都是非线性时变系统，用线性时不变（LTI）的模型都是无法很好刻画的。==
         ==Sora的具体做法技术综述中没有透露。==
==Lumiere 的处理中可以窥见端倪。这里可以有多种建模的方式，最自然的方式就是 ((x,y,z), t )的方式，将事物整体的演化看成时间序列，但此种方式往往存在数字视频采样频率不足导致的运动模糊与运动混淆问题。比如高速运转的轮子有时候看起来像在倒转。==
==Nyquist-Shannon采样定理告诉我们，对于模拟信号 ，如果希望同时看到信号的各种特性，采样频率应该大于原始模拟信号的最大频率的两倍，否则将发生混叠即相位或频率模糊。因而Lumiere采用了自监督时间超分辨率(TSR)与空间超分辨率(SSR)技术[文献5]，将事物的运动建模成多维度两两组合的模型：（x,y), …,(x,t),(y,t),(z,t)。==
==小的时空碎片会在视频序列的各个维度上重复出现，特别是空间和时间维度之间进行交换时，因而可以对其在时间域与空间域的表征做关联分析，慢逆时针有可能是快顺时针的假象，也可能就是慢逆时针。即使时域无法分辨，空域可以调整频率，看到更模糊或者没有特别变化的表征。==
         ==当物体快速移动时，x-t和y-t切片中的Patch看起来是高分辨率x-y切片(传统帧)的低分辨率版本。在t方向上增加这些x-t和y-t切片的分辨率与增加视频的时间分辨率是一样的。因此，空间x-y视频帧提供了如何在同一视频中增加x-t和y-t切片的时间分辨率的示例。==
==即将t看成第四维度，可以用x-y高分辨率训练修正x-t, y-t。同理，当物体移动非常缓慢时，x-t和y-t切片中的Patch呈现为x-y帧中Patch的拉伸版本，表明这些时间切片可以为如何提高视频帧的空间分辨率提供示例。即时间切片，反过来提升空间分辨率。如果SSM 学到了物理规律（如运动方程），直接输出高频帧理论上也应当可行。==    
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sHhKAhXWYQ8xDIuz1PAhIabDQW4Q5gDhstqibU777bTaFhdCDfg3m13Q/640?wx_fmt=png)](https://mmbiz.qpic.cn/sz_mmbiz_png/zNPOIJqb1Fbeiam7NohaWEvKGYzgoNN4sHhKAhXWYQ8xDIuz1PAhIabDQW4Q5gDhstqibU777bTaFhdCDfg3m13Q/640?wx_fmt=png)
==“跨维”递归的一维图示。1D对象向右移动。当适当的采样时间(T=1)，时间切片类似于空间切片(1D“帧”)。然而，当时间采样率过低(T=2)时，时间切片是空间切片的欠采样(混叠 aliasing)版本。因此，空间帧提供了消除时间混叠的示例。==
==3.状态空间时间序列的非马尔可夫性：思考attention 的价值，时序数据上的 attention 注意到了什么？诸如趋势, 周期性, 一次性事件等。非时间维度子空间内的 attention，注意到的是范畴内与范畴间的关系， 即某个时刻的状态空间。状态空间的时序，研究的是状态空间的动力学，外在驱动“力”或因素导致的状态的“流动”，即状态空间t时刻与 t-n时刻之间的关系，注意到的是其时间依赖规律，往往不具备马尔可夫性。==
         ==对此==
==“==[==**Transformer 的后浪来了**==](https://mp.weixin.qq.com/s?__biz=MzI2MjU4MDYwOA==&mid=2247486712&idx=1&sn=d7027e3bb9ae523e35c56bc51110b951&scene=21#wechat_redirect)==”以及笔者早前的“==[==**薛定谔的小板凳与深度学习的后浪**==](https://mp.weixin.qq.com/s?__biz=MzI2MjU4MDYwOA==&mid=2247483889&idx=1&sn=b191bac01b87880fd2fa50f9a1a2ced1&scene=21#wechat_redirect)==”中都做了相应的阐释。非马尔可夫性其实是世界的常态，事实上时延系统基本都是非马尔可夫的。时间维度的注意力与状态空间选择性非常关键。==         
         ==OpenAI 对Sora 视频生成模型的技术综述文章取了“视频生成模型作为世界模拟器video generation models as world simulators”的题目，可见其宏大的愿景。既然模拟世界，就绕不开万事万物的长程时间关联或者因果关系，非马尔可夫性不可避免会制造棘手的麻烦。==
==五、Sora的前景与未来==    
==**Sora 和 Lumiere 等文生视频模型其实就是大模型从侧重空间关联转向了加强时间关联**====。也就是从笔者上图中“非时间维度子空间”的信息提取，转向侧重学习和表征“状态空间的动态性”及处理“非马尔可夫性”。通过海量视频中对时空碎片的动态关联的学习，目前看文生视频大模型可以学到可视层面或者表面意义上的SSM，此种意义上，SAMBA之类的SSM模型应该可以殊途同归。==
==然而仅仅从时空碎片的表象上是获取不到足够捕获其内在规律的信息的，未来人类科学家们可以将他们长期殚精竭虑探索的领域，包括但不局限于物理化学生物等等学科的范畴，可视化为图像或视频，交给视频生成大模型去学习，辅助发现其中蕴含的潜在规律。==
==**Sora 开了一个好头，或者说史诗级的把视频生成模型泛化成了物理引擎**====。把LLM GPT加视频生成模型Sora推到实时，就接近或达到人类的感知水平了。今后重要任务是处理好感知到概念体系的认知跨越，也就是处理好生成过程采样和变分推断的合理性。==
==普林斯顿和DeepMind 科学家已经开始用随机图来解释大模型涌现出来的的新能力，与笔者去年9月整理过的：“==[==**范畴的相变与知识的形成**==](http://mp.weixin.qq.com/s?__biz=MzI2MjU4MDYwOA==&mid=2247486292&idx=1&sn=0148e951c2fd92a317d012e2bf3b3952&chksm=ea49bbbbdd3e32ad7b2c2cb1f587ff9f0e2172f9207c60c4972cce193cab9202c73773fec00a&scene=21#wechat_redirect)==”不谋而合。处理好大模型感知到认知的跨越，不仅仅使得AI4Science领域迎来重大突破，Artificial Super Intelligence人工超级智能也将指日可待。==
==[文献1]https://openai.com/research/video-generation-models-as-world-simulators==
==[文献2]Scalable diffusion models with transformers, https://arxiv.org/abs/2212.09748==
==[文献3]https://ml.cs.tsinghua.edu.cn/~fanbao/Application-DPM.pdf==
==[文献4]Lumiere: A Space-Time Diffusion Model for Video Generation https://arxiv.org/pdf/2401.12945.pdf==
==[文献5]Across Scales & Across Dimensions: Temporal Super-Resolution using Deep Internal Learning https://arxiv.org/abs/2003.08872==