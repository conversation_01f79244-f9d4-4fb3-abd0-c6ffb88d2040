---
Updated: 2024-03-17T17:13
tags:
  - AI->-Voice
Created: 2024-03-17T17:13
---
[![](https://picx.zhimg.com/v2-6e6f4e9a207e97ce6e8d37f914dc0093_r.jpg?source=172ae18b)](https://picx.zhimg.com/v2-6e6f4e9a207e97ce6e8d37f914dc0093_r.jpg?source=172ae18b)
==之前一直是以回答问题的方式普及说话人识别的相关知识，我没有去写过和说话人识别相关的文章，这主要是因为不想写得过于简单了事，另一方面找不到很新很好的点子能铺开分享。我想了好久，但是呢，最后还是决定把PLDA算法拿出来作为我的处女座文章，展开详细地讲讲。原因有几点哈，==
1. ==PLDA，是我入门以来，接触过的第二大块算法，是导师手把手带我推导的，所以我对PLDA挺有怀念之情；后来，有一位对我来说很重要的老师，他带着我重新深入理解一遍，那时特别有感触；==
2. ==我看过不少网上中文博客对于PLDA的分析，但很多都是搬运face recognition的PLDA来套到Kaldi的PLDA上，这是不对的；==
3. ==face recognition的PLDA，在网上，已有比较多的分析和研究，但对于Kaldi所用的PLDA，即two-covariance PLDA的分析和推导很少，也不全面；==
4. ==two-covariance PLDA算法分为两部分，一个是训练，第二个是打分，这两部分的公式推导都很复杂，原论文只放出每部分的最终结果，不好理解；==
5. ==现在2021年末，讲PLDA多少是炒冷饭了>_<，但我发现PLDA在cross-domain上的效果真的很不错，无论是搭配tdnn+softmax或者是resnet+amsoftmax，效果还是很出彩，推荐尝试。==
==好了，下面就开始讲正文吧，我会先说说为什么会有好几种PLDA，为什么Kaldi的PLDA和MSR等工具的PLDA是不同的。==
==我们常见的PLDA，是出自《Probabilistic Linear Discriminant Analysis for Inferences About Identity》，由Simon J.D. Prince和James H. Elder合著的，我愿称之为 standard PLDA，==[==https://wiki.inf.ed.ac.uk/twiki/pub/CSTR/ListenSemester2201112/prince-iccv07-plda.pdf==](https://link.zhihu.com/?target=https%3A//wiki.inf.ed.ac.uk/twiki/pub/CSTR/ListenSemester2201112/prince-iccv07-plda.pdf)==。==
==Kaldi所实现的PLDA则是，出自另一篇《Probabilistic Linear Discriminant Analysis》，由Sergey Ioffe所著，==[==https://ravisoji.com/assets/papers/ioffe2006probabilistic.pdf==](https://link.zhihu.com/?target=https%3A//ravisoji.com/assets/papers/ioffe2006probabilistic.pdf)==。==
==除上面两篇之外，还有不少PLDA的变体，例如Kenny提出的HT-PLDA，MAK老师写的Mixture PLDA等。==
==这些PLDA都是遵循了把向量映射到一个可区分空间中计算相似性，在该空间拉开类间距离和缩小类内距离：==
[![](https://pic4.zhimg.com/v2-b2b17a3c3f7d32ad02ee0915b802a273_b.jpg)](https://pic4.zhimg.com/v2-b2b17a3c3f7d32ad02ee0915b802a273_b.jpg)
==standard PLDA，是生成模型，考虑了eigenvoice、eigenchannels和残差因子，用EM算法训练参数时要依次对本征音和本征信道进行重估。这训练方式类似于联合因子分析（可叫joint-型plda），所以会存在JFA的相似问题，即洗炼后的信道因子会带走了部分说话人因子的信息。而实际上参考minDCF的评价公式，说话人因子信息更为重要，其代表类间信息，能直接降低FAR错误率，提升安全性。兼顾本征音和本征信道的standard PLDA，能有效降低FRR错误率，提升体验性，但低质量的target属实没接受意义，而且FAR不能得到足够的保证。==
==standard PLDA的映射关系如下：====  
  
====x_{ij} = \mu + Fh_i + Gw_{ij} + \epsilon_{ij} \\ h_i \sim N(0, \mathbf{I}) \\ w_{ij} \sim N(0, \mathbf{I}) \\ \epsilon_{ij} \sim N(0, \mathbf{\Lambda}^{-1})==
==其中，==
- ==x_{ij} 是指PLDA训练集中的第 i 个说话人的第 j 条矢量；==
- ==\muF 是身份空间的低维矩阵，是类间空间，代表EigenVoice本征音，表示各种身份的载荷矩阵，它的每列包含类间子空间的基底；==
- ==h_i 是说话人因子，是隐变量；==
- ==G 是误差空间的低维矩阵，是类内空间，代表EigenChannles本征信道，表示同一身份不同信道变化的残差载荷矩阵，它的每列相当于类内子空间的基底，是协方差特征向量；==
- ==w_{ij} 是信道因子，是隐变量；==
- ==\epsilon_{ij} 是残留噪声项，表示尚未解释的部分，该项均值为零，方差为对角矩阵Σ，符合高斯分布；==
- ==\mathbf{\Lambda} 是对角矩阵；==
- ==\mu + Fh_i ，代表了信号成分，描述了个体间的差异，对应着FAR；==
- ==Gw_{ij} + \epsilon_{ij} ，代表了噪声成分，描述了个体内的差异，对应着FRR。==
==standard PLDA的类间和类内空间矩阵是可预设降维的映射空间，所以能像LDA一样实现降低维度。从上面的分析看，噪声空间可能会削弱了说话人识别的效果，随后有人提出了弱化对噪声成分的建模，并增强信号成分，提出了simplified PLDA。公式如下：==
==x_{ij} = \mu + Fy_i + \epsilon_{ij} \\ y_i \sim N(0, \mathbf{I}) \\ \epsilon_{ij} \sim N(0, \mathbf{\Lambda}_f^{-1})====  
  
====simplified PLDA和standard PLDA挺相似的，也具有对向量作降维映射能力。虽然simplified PLDA增强对类间空间进行优化，但是simplified PLDA在跨信道上的同一说话人收敛能力下降了。==
==以上的PLDA，能被称之为standard PLDA系列，因子之间存在较强的相关性，需要联合看待处理。而对于Kaldi的PLDA，则是跳出了说话人因子和信道因子的范式，而是采用类间因子和类内因子，分别对应类间方差和类内方差，所以能称之为two-covariance PLDA。==
==Kaldi的PLDA是so-called two-covariance PLDA，具体理解可先假设全部数据中的任一样本 z_{ij} ，其是第 i 类的第 j 个样本，均值是：====  
  
====\mu = \frac{1}{N}\sum_{i=1}^I\sum_{j=1}^{n_i}z_{ij}==
==其中，第 i 类有 n_i 个样本，全部数据共有 I 个类别，共有 N 个样本；==
==而全部数据的均值矢量 \mu 是固定的，我们先提前对每个数据移除均值矢量，则任意一个样本符合以下高斯分布：==
==z_{ij} \sim N(0, \Phi)====  
  
====接着，假设存在类间因子 x ，该隐变量表征每个类别的虚拟类中心，则其满足：==
==x \sim N(0, \Phi_b)====  
  
====那第 i 类的某一样本 m ，该类有 n_i 个样本，则其是会满足：==
==m \sim N(0, \Phi_b + \frac{\Phi - \Phi_b}{n_i}) = N(0, \Phi_b + \frac{\Phi_w}{n_i})====  
  
====（这里我用m表示某类内的某一样本，而不再是用 z_{ij} ，因为m不是指特定样本，我能把它的类内方差抽象理解为 \frac{\Phi - \Phi_b}{n_i} ，如果是特定 z_{ij} 则不能在这里简单地除以 n_i 来表示单样本的类内方差）==
==能从上面分布得知，假设还存在类内因子 y ，该隐变量表征着某类别内的任一样本分布，满足：==
==y \sim N(0, \frac{\Phi_w}{n})====  
  
====综上，样本 m 能由类间因子和类内因子表示为：==
==m = x + y \\ x \sim N(0, \Phi_b) \\ y \sim N(0, \frac{\Phi_w}{n})====  
  
====x 和 y 的分布，是分别对应类间方差 \Phi_{between} 和类内方差 \Phi_{within} ，所以该PLDA被称之为two-covariance PLDA。==
==该PLDA相比于基于standard PLDA系列，优势在于能根据spk2utt划分出每个说话人的样本，能有效兼顾类间和类内的关系。但由于不再存在 F 和 G 的子空间，所以该PLDA不能实现向量降维，要搭配PCA和LDA等back-end模型提前降维。==
==目前来说，说话人识别的PLDA主要是以上两种，所以在谈Kaldi的PLDA时，不要把它和standard PLDA混在一起谈，更不要尝试硬套公式和代码，这都是不合理的~==
==接下来，是本文的重头戏，我们要如何训练PLDA呢？==
==在论文《Probabilistic Linear Discriminant Analysis》中，是先讲解inference再到learning，我这里反过来，先讲怎样去训练参数。Kaldi的PLDA模型主要参数是类间方差和类内方差，借助EM算法迭代收敛后，保存用于打分阶段的。这里我参考了《Probabilistic Linear Discriminant Analysis》和《A Note on Kaldi's PLDA Implementation》，我把后者的训练推导以我理解过的，更详细地方式展开顺一遍，读者若有不解的地方可以留言备注，==[==https://arxiv.org/pdf/1804.00403.pdf==](https://link.zhihu.com/?target=https%3A//arxiv.org/pdf/1804.00403.pdf)==。==
==我们这里先从上一节样本 m 的表示方法开始。==
==由于 m=x+y ，会有 y = m -x ，所以能理解为PLDA在做参数重估时只有一个隐变量，我们可以先只关注 x 的分布，之后能同理可得到 y 的分布。==
==假设PLDA的参数为 \theta ，对于给定的可观察变量 m ，根据极大似然估计有====  
  
====\theta = argmax log P(m,x|\theta)====  
  
====表示存在最优解参数 \theta ，其能使得以上的似然函数最大。由于上式有隐变量，且高斯分布存在多个凸函数簇，无法直接解得全局最优解，也就是没法获得闭式解。但由于琴生不等式的成立，我们可用EM算法去进行逼近估算。==
==根据EM算法，对于完全数据的似然概率 Q ，E步有期望：==
==Q = E(logP(m,x|\theta))==
==根据贝叶斯公式和高斯概率密度函数，上式可转换为：==
==logP(m,x|\theta) = log(P(m|x,\theta)P(x|\theta)) \\ = log(P(m|x,\theta)) + log(P(x|\theta)) \\ = log(N(m-x|0,\frac{\Phi_w}{n})) + log(N(x|0,\Phi_b)) \\ = C_1 - \frac{1}{2}(m - x -0)^T(\frac{\Phi_w}{n})^{-1}(m - x -0) + C_2 - \frac{1}{2}(x -0)^T\Phi_b^{-1}(x -0) \\ = \frac{-1}{2}(x^T\Phi_b^{-1}x + (m-x)^Tn\Phi_w^{-1}(m-x))==
==之后要做微分，所以提前把 C_1 和 C_2 省掉，并把右项进一步展开：==
==\frac{-1}{2}(x^T\Phi_b^{-1}x + (m-x)^Tn\Phi_w^{-1}(m-x)) \\ = \frac{-1}{2}(x^T\Phi_b^{-1}x + x^Tn\Phi_w^{-1}x - 2n\Phi_w^{-1}x^Tm + m^Tn\Phi_w^{-1}m) \\ = \frac{-1}{2}(x^T(\Phi_b^{-1} + n\Phi_w^{-1})x - 2n\Phi_w^{-1}x^Tm + m^Tn\Phi_w^{-1}m) \\ = \frac{-1}{2}(x^T(\Phi_b^{-1} + n\Phi_w^{-1})x - 2n\Phi_w^{-1}x^Tm + m^Tn\Phi_w^{-1}m) \ \ \ \ \ \ \ \ [1]\\ = \frac{-1}{2}(\Phi_b^{-1} + n\Phi_w^{-1})(x^Tx - 2\frac{n\Phi_w^{-1}}{\Phi_b^{-1} + n\Phi_w^{-1}}x^Tm + m^T\frac{n\Phi_w^{-1}}{\Phi_b^{-1} + n\Phi_w^{-1}}m) \ \ \ \ \ \ \ \ [2]==
==截止到现在都很顺利，之后的推导需要一个数学技巧。上式的展开是基于 P(m,x|\theta) ，从另一个角度看， m 和 x 都是满足高斯分布，高斯分布的联合概率依然是高斯分布，所以肯定存在：==
==P(m,x|\theta) \Rightarrow P(z|\theta) \sim N(\omega, \hat{\Phi})==
==根据高斯概率密度函数，又会有:==
==logP(m,x|\theta) \Rightarrow logP(z|\theta) = log(N(\omega, \hat{\Phi})) \\ = \frac{-1}{2}(z - \omega)^T\hat{\Phi}^{-1}(z-\omega) \\ = \frac{-1}{2}\hat{\Phi}^{-1}(z^Tz - 2z^Tw + w^Tw) \ \ \ \ \ \ \ \ [3]==
==对比[2]和[3]式，小括号内的完全平方差是能对应起来，同时[2]的第一项对应着[3]的方差，所以能直接求解出方差为（这是个小技巧）：==
==\hat{\Phi} = (\Phi_b^{-1} + n\Phi_w^{-1})^{-1}==
==接着把 \hat{\Phi} 的结果代回去[3]中，可得：==
==\frac{-1}{2}(z - \omega)^T\hat{\Phi}^{-1}(z-\omega) \\ = \frac{-1}{2}(z - \omega)^T(\Phi_b^{-1} + n\Phi_w^{-1})(z-\omega) \\ = \frac{-1}{2}(z^T(\Phi_b^{-1} + n\Phi_w^{-1})z - 2(\Phi_b^{-1} + n\Phi_w^{-1})z^T\omega + \omega^T(\Phi_b^{-1} + n\Phi_w^{-1})\omega) \ \ \ \ \ \ \ \ [4]==
==我们又对比[1]和[4]式，基于第一项和第二项，能类比得到均值为（眼要够尖）：==
==n\Phi_w^{-1}m = (\Phi_b^{-1} + n\Phi_w^{-1})\omega \\ \therefore \omega = \frac{n\Phi_w^{-1}m}{\Phi_b^{-1} + n\Phi_w^{-1}}==
==截至为止，就可证论文《A Note on Kaldi's PLDA Implementation》中的公式(13)和(14)。==
==综上， P(m,x|\theta) 满足高斯分布：==
==P(m,x|\theta) \sim N(\frac{n\Phi_w^{-1}m}{\Phi_b^{-1} + n\Phi_w^{-1}}, (\Phi_b^{-1} + n\Phi_w^{-1})^{-1})==
==由于给定了已知 m ，所以 P(m,x|\theta) 的分布也是 P(x|\theta) 的分布：==
==P(x|\theta) \sim N(\frac{n\Phi_w^{-1}m}{\Phi_b^{-1} + n\Phi_w^{-1}}, (\Phi_b^{-1} + n\Phi_w^{-1})^{-1}) \ \ \ \ \ \ \ \ [5]==
==而隐变量 y=m-x ，同理可得， P(y|\theta) 的分布是：==
==P(y|\theta) \sim N(\frac{n\Phi_w^{-1}m}{\Phi_b^{-1} + n\Phi_w^{-1}} - m, (\Phi_b^{-1} + n\Phi_w^{-1})^{-1})==
==接下来，终于到EM算法了。==
==分别对隐变量 x 和 y 的对数似然概率计算E步，有：==
==E (logP(x|\theta)) = E(log(\frac{1}{\sqrt{2\pi\Phi_b}}exp(\frac{x^Tx}{-2\Phi_b}))) = E(log|\Phi_b|^{\frac{-1}{2}} + \frac{x^Tx}{-2\Phi_b}) \\ = \frac{-1}{2}log|\Phi_b| - \frac{E(x^Tx)}{2\Phi_b} \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ [6]==
==E (logP(y|\theta)) = \frac{-1}{2}log|\frac{\Phi_w}{n}| - \frac{E(y^Ty)}{2\frac{\Phi_w}{n}} \ \ \ \ \ \ \ \ [7]==
==然后，分别对隐变量 x 和 y 的对数似然概率计算M步，要使极值点最大，分别对各自的Q函数的方差求偏导。==
==对[6]式求偏导：==
==\frac{\partial E(logP(x|\theta))}{\partial \Phi_b} = \frac{-1}{2\Phi_b} + \frac{E(x^Tx)}{2\Phi_b^2}==
==令上式等于0，可得新的 \hat\Phi_b ，==
==\hat\Phi_b = E(x^Tx) = D(x) + E^2(x) \\ = (\Phi_b^{-1} + n\Phi_w^{-1})^{-1} + (\frac{n\Phi_w^{-1}m}{\Phi_b^{-1} + n\Phi_w^{-1}})^2==
==其中，随着PLDA迭代训练的推进， x 的均值和方差不再是初始的0和 \Phi_b 了，而是[5]中的迭代中间值，把[5]式的分布代入到上面即可得下一轮迭代的结果。==
==同理，对于隐变量 y 的[7]式求偏导，可得新的 \hat\Phi_w ，==
==\hat\Phi_w = E(y^Ty) = D(y) + E^2(y) \\ = (\Phi_b^{-1} + n\Phi_w^{-1})^{-1} + (\frac{n\Phi_w^{-1}m}{\Phi_b^{-1} + n\Phi_w^{-1}} - m)^2==
==可证论文《A Note on Kaldi's PLDA Implementation》中的公式(17)和(18)。到这里为止，PLDA的EM训练就全部完成了~~==
==在《A Note on Kaldi's PLDA Implementation》论文中，只讲述了PLDA的训练推导，却少了打分的部分。后面，我会基于最原始的《Probabilistic Linear Discriminant Analysis》论文，进行two-covariance PLDA的打分推导，同样是全公式推导；推导过程中也会稍微结合Kaldi PLDA的ivector-plda-scoring进行分析；链接如下==[==说话人识别中PLDA的打分==](https://zhuanlan.zhihu.com/p/428734919)==。==