---
Updated: 2024-08-29T13:24
tags:
  - AI->-Agent
  - AI->-DevPlatform
  - AI->-Flowise
  - AI->-<PERSON><PERSON><PERSON><PERSON>
  - AI->Automation
URL: https://github.com/FlowiseAI/Flowise
Created: 2024-08-10T17:51
Reference:
---
Memo
initial Setup
Use <PERSON><PERSON><PERSON> To Analyze flow
Google Authentication
Build The workflow
Make Flowise → Make → Gmail → Flowise
Upgrade
Tricks
Flowise - Build LLM Apps Easily
Drag & drop UI to build your customized LLM flow
⚡Quick Start
🐳 Docker
Docker Compose
Docker Image
👨‍💻 Developers
Prerequisite
Setup
🔒 Authentication
🌱 Env Variables
📖 Documentation
🌐 Self Host
💻 Cloud Hosted
🙋 Support
🙌 Contributing
📄 License
  
# Memo
## initial Setup
```JavaScript
git clone https://github.com/FlowiseAI/Flowise.git
npx flowise start --FLOWISE_USERNAME=user --FLOWISE_PASSWORD=1234
sudo npm -i -g pnpm
cd Flowise
pnpm install
pnpm build
\#change .env
https://github.com/FlowiseAI/Flowise/blob/main/CONTRIBUTING.md\#-env-variables
You can specify the following variables in the .env file inside packages/server
Create .env file and specify the VITE_PORT (refer to .env.example) in packages/ui
(base) ray@jethome:/opt/workspace/app/Flowise/packages/ui$ grep '^[^#]' .env
VITE_PORT=8301
Create .env file and specify the PORT (refer to .env.example) in packages/server
(base) ray@jethome:/opt/workspace/app/Flowise/packages/server$ grep '^[^#]' .env
PORT=8300
CORS_ORIGINS=*
DATABASE_TYPE=postgres
DATABASE_PORT=5432
DATABASE_HOST="************"
DATABASE_NAME=flowise
DATABASE_USER=flowusr
DATABASE_PASSWORD="flow\#345"
FLOWISE_USERNAME=admin
FLOWISE_PASSWORD=test123

pnpm dev
pnpm start

#### DB prepareation
create database flowise;
create role flowrole;
grant connect on database flowise to flowrole;
create user flowusr password 'flow\#345';
grant flowrole to flowusr;
create tablespace flowtbsp location '/opt/postgresql/data/flowtbs';
alter role flowrole set default_tablespace='flowtbsp';
alter role flowrole set search_path=flowsc,public;
\c flowise postgres
create schema flowsc;
alter schema flowsc owner to flowusr;
alter database flowise set search_path=flowsc,public;
\#open pg_hba.conf
local   flowise         flowusr                                 md5
pg_ctl reload
postgres@jethome:/opt/postgresql/conf$ psql -U flowusr -h ************ -p 5432 -W -d flowise
Password:
psql (15.8 (Ubuntu 15.8-1.pgdg22.04+1))
Type "help" for help.
flowise=>
\#create extention
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION vector;
\#rerun 
pnmp start
flowise=# \dt
                  List of relations
 Schema |           Name            | Type  |  Owner
--------+---------------------------+-------+---------
 flowsc | apikey                    | table | flowusr
 flowsc | assistant                 | table | flowusr
 flowsc | chat_flow                 | table | flowusr
 flowsc | chat_message              | table | flowusr
 flowsc | chat_message_feedback     | table | flowusr
 flowsc | credential                | table | flowusr
 flowsc | document_store            | table | flowusr
 flowsc | document_store_file_chunk | table | flowusr
 flowsc | lead                      | table | flowusr
 flowsc | migrations                | table | flowusr
 flowsc | tool                      | table | flowusr
 flowsc | upsert_history            | table | flowusr
 flowsc | variable                  | table | flowusr
(13 rows)
```
## Use LangSmith To Analyze flow
```JavaScript
LangSmith
RayEver
API Key: ***************************************************
And create dedicated openai key for Flowise
********************************************************************************************************************************************************************
```
## Google Authentication
```JavaScript
Client ID
729099167009-b3eji747bfetmfkmmc0sdob7ktcu6411.apps.googleusercontent.com
Client secret
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
```
## Build The workflow
```JavaScript
http://************:8300/agentflows
\#Trick: You can use UseItForPromptGenerate this existing agentflow to gerenete prompt from different works
1, KNow how to use ollama embedding
URL : http://************:8234
Model: quentinz/bge-large-zh-v1.5:latest
2. Know how to use pg as vectorDB
3. Must add it into supervisor system prompt
You are a supervisor tasked with managing a conversation between the following workers: {team_members}.
Given the following user request, respond with the worker to act next.
Each worker will perform a task and respond with their results and status.
When finished, respond with FINISH.
Select strategically to minimize the number of steps taken.
Make sure finally you must ask Email Content creator to create email and ask Email Sender to send email.
```
## Make Flowise → Make → Gmail → Flowise
[[Build Multi-Agent AI Teams with RAG & Custom Tools! (No-Code) FlowiseAI - YouTube - Leon van Zyl]]
```JavaScript
\#Example
http://************:8300/agentcanvas/c1983195-511e-4984-b810-4a9e0625db8f
\#Trick
When creat webhook as you dont know struction, you can use make auto redeem. while redeem structure is running, then use flowise to send dummy request to Make
\#how to trigger it after complete
import requests
API_URL = "http://************:8300/api/v1/prediction/c1983195-511e-4984-b810-4a9e0625db8f"
def query(payload):
    response = requests.post(API_URL, json=payload)
    return response.json()
    
output = query({
    "question": "Hey, how are you?",
})
print(output['text'])
```
## Upgrade
```JavaScript
#获取远程更新但不应用在您的本地仓库目录中,运行以下命令:
git fetch origin
#尝试合并远程的更新,但不会自动提交,给您机会检查更改。
git merge --no-commit --no-ff origin/main
#查看冲突文件
git status
#更新过程解决所有冲突后,使用以下命令将文件添加到暂存区
git add .
#提交更改
git commit -m "合并远程更新并保留本地修改"
git config pull.rebase true
git status 
git pull
--------------
if pnpm build get error
\#Here is the step
1 cleanup
git stash pop
rm -rf node_modules
pnpm store prune
pnpm clean
rm -f pnpm-lock.yaml
2, repull the new version
git status
git stash
git pull
3, install dependencies
pnpm install 
4. upgrade turbo
npx @turbo/codemod update
# if not working then we add --force flag
npx @turbo/codemod update --force
5 build
pnpm build
# if step5 fail with this error
flowise-api:build: Error: Cannot find module '/databank/app/Flowise/node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/bin/tsc'
We add this missing module manually
cd packages/components/
pnpm add typescript@5.5.4 --save-dev
pnpm i
pnpm clean
pnpm build-force
```
### 2.1.0
```
rsync -avl Flowise/ Flowise-BK/
cd Flowise
git status
git stash
git pull
sudo npm add -g pnpm  <= upgrade pnpm if required
pnpm i
pnpm build
npx @turbo/codemod update
rm -f pnpm-lock.yaml
pnpm install
pnpm add -Dw typescript
pnpm build   
pnpm start

```
### 2.1.1
```
git stash
git pull
pnpm i
pnpm build
pnpm start

```
### 2.3.1
```
nvm use 18
git pull
pnpm i

```

## Python SDK  and StreamMode
```python
from flowise import Flowise, PredictionData
def test_streaming():
    client = Flowise(
        base_url="http://**************:8300",
    )

    # Test streaming prediction
    completion = client.create_prediction(
        PredictionData(
            chatflowId="c1983195-511e-4984-b810-4a9e0625db8f",
            question="Tell me a joke!",
            streaming=True
        )
    )

    # Process and print each streamed chunk
    print("Streaming response:")
    for chunk in completion:
        # {event: "token", data: "hello"}
        print(chunk)


if __name__ == "__main__":
    test_streaming()

```
## Tricks
```JavaScript
Override parameter when http call
"overrideConfig":{
	"modelName": "gpt-4"
}
{
	"question": query
	"overrideConfig":{
		"modelName": "gpt-4"
		"sessionid":"senderid"  <= let flowise which conversation session is
	}
}

```
## Extract JSON from Any Unstructured Data (Beginner's Guide)
https://youtube.com/watch?v=e1E3BtD9Fu4&si=KlcOqbyNN9qddg_k

## AutoUpdate Knowledge Base
```
https://youtube.com/watch?v=v5c5FV9cLAw&si=Xn_nFhr1qWr5la4A

```

[![](https://opengraph.githubassets.com/efadc398ef2fef8446813694c5cdf1f6dae77bcafbc510deb0913127f15c3074/FlowiseAI/Flowise)](https://opengraph.githubassets.com/efadc398ef2fef8446813694c5cdf1f6dae77bcafbc510deb0913127f15c3074/FlowiseAI/Flowise)
[![](https://github.com/FlowiseAI/Flowise/raw/main/images/flowise.png?raw=true)](https://github.com/FlowiseAI/Flowise/raw/main/images/flowise.png?raw=true)
# ==Flowise - Build LLM Apps Easily==
[![](https://camo.githubusercontent.com/0d245d43b8be284879fed960e9a34248995e8701825724073aed44b901a985e6/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f72656c656173652f466c6f7769736541492f466c6f77697365)](https://camo.githubusercontent.com/0d245d43b8be284879fed960e9a34248995e8701825724073aed44b901a985e6/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f72656c656173652f466c6f7769736541492f466c6f77697365)
[![](https://camo.githubusercontent.com/e1a46aee43635cba76ce752d5db60df470a857417bf69a3d3b7466225c6f5867/68747470733a2f2f696d672e736869656c64732e696f2f646973636f72642f313038373639383835343737353838313737383f6c6162656c3d446973636f7264266c6f676f3d646973636f7264)](https://camo.githubusercontent.com/e1a46aee43635cba76ce752d5db60df470a857417bf69a3d3b7466225c6f5867/68747470733a2f2f696d672e736869656c64732e696f2f646973636f72642f313038373639383835343737353838313737383f6c6162656c3d446973636f7264266c6f676f3d646973636f7264)
[![](https://camo.githubusercontent.com/b83c0efcbdf7f66815ab5b493f500012c748a9600f2027beb5cd8bc784c60cd3/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f466c6f7769736541493f7374796c653d736f6369616c)](https://camo.githubusercontent.com/b83c0efcbdf7f66815ab5b493f500012c748a9600f2027beb5cd8bc784c60cd3/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f466c6f7769736541493f7374796c653d736f6369616c)
[![](https://camo.githubusercontent.com/26815bf0c0529277948d8b345af7afafda769883207c9fa96eefba9759b9acf2/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f466c6f7769736541492f466c6f776973653f7374796c653d736f6369616c)](https://camo.githubusercontent.com/26815bf0c0529277948d8b345af7afafda769883207c9fa96eefba9759b9acf2/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f466c6f7769736541492f466c6f776973653f7374796c653d736f6369616c)
[![](https://camo.githubusercontent.com/89098c03fd31396fe027943cd3e97817dcdab01481f76a7854f243d481cc9bc6/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f666f726b732f466c6f7769736541492f466c6f776973653f7374796c653d736f6369616c)](https://camo.githubusercontent.com/89098c03fd31396fe027943cd3e97817dcdab01481f76a7854f243d481cc9bc6/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f666f726b732f466c6f7769736541492f466c6f776973653f7374796c653d736f6369616c)
==English |== [==中文==](https://github.com/FlowiseAI/Flowise/blob/main/i18n/README-ZH.md) ==|== [==日本語==](https://github.com/FlowiseAI/Flowise/blob/main/i18n/README-JA.md) ==|== [==한국어==](https://github.com/FlowiseAI/Flowise/blob/main/i18n/README-KR.md)
### ==Drag & drop UI to build your customized LLM flow==
[![](https://github.com/FlowiseAI/Flowise/raw/main/images/flowise.gif?raw=true)](https://github.com/FlowiseAI/Flowise/raw/main/images/flowise.gif?raw=true)
## ==⚡Quick Start==
==Download and Install== [==NodeJS==](https://nodejs.org/en/download) ==>= 18.15.0==
1. ==Install Flowise==
    
    ==npm install -g flowise==
    
2. ==Start Flowise==
    
    ==npx flowise start==
    
    ==With username & password==
    
    ==npx flowise start --FLOWISE_USERNAME=user --FLOWISE_PASSWORD=1234==
    
3. ==Open== [==http://localhost:3000==](http://localhost:3000/)
## ==🐳 Docker==
### ==Docker Compose==
1. ==Go to== ==`docker`== ==folder at the root of the project==
2. ==Copy== ==`.env.example`== ==file, paste it into the same location, and rename to== ==`.env`==
3. ==`docker compose up -d`==
4. ==Open== [==http://localhost:3000==](http://localhost:3000/)
5. ==You can bring the containers down by== ==`docker compose stop`==
### ==Docker Image==
1. ==Build the image locally:==
    
    ==docker build --no-cache -t flowise .==
    
2. ==Run image:==
    
    ==docker run -d --name flowise -p 3000:3000 flowise==
    
3. ==Stop image:==
    
    ==docker stop flowise==
    
## ==👨‍💻 Developers==
==Flowise has 3 different modules in a single mono repository.==
- ==`server`====: Node backend to serve API logics==
- ==`ui`====: React frontend==
- ==`components`====: Third-party nodes integrations==
### ==Prerequisite==
- ==Install== [==PNPM==](https://pnpm.io/installation)
    
    ==npm i -g pnpm==
    
### ==Setup==
1. ==Clone the repository==
    
    ==git clone https://github.com/FlowiseAI/Flowise.git==
    
2. ==Go into repository folder==
    
    ==cd Flowise==
    
3. ==Install all dependencies of all modules:==
    
    ==pnpm install==
    
4. ==Build all the code:==
    
    ==pnpm build==
    
    ==Exit code 134 (JavaScript heap out of memory) If you get this error when running the above `build` script, try increasing the Node.js heap size and run the script again:==
    
    ```plain
    export NODE_OPTIONS="--max-old-space-size=4096"
    pnpm build 
    ```
    
5. ==Start the app:==
    
    ==pnpm start==
    
    ==You can now access the app on== [==http://localhost:3000==](http://localhost:3000/)
    
6. ==For development build:==
    
    - ==Create== ==`.env`== ==file and specify the== ==`VITE_PORT`== ==(refer to== ==`.env.example`====) in== ==`packages/ui`==
    - ==Create== ==`.env`== ==file and specify the== ==`PORT`== ==(refer to== ==`.env.example`====) in== ==`packages/server`==
    - ==Run==
        
        ==pnpm dev==
        
    
    ==Any code changes will reload the app automatically on== [==http://localhost:8080==](http://localhost:8080/)
    
## ==🔒 Authentication==
==To enable app level authentication, add== ==`FLOWISE_USERNAME`== ==and== ==`FLOWISE_PASSWORD`== ==to the== ==`.env`== ==file in== ==`packages/server`====:==
```plain
FLOWISE_USERNAME=user
FLOWISE_PASSWORD=1234
```
## ==🌱 Env Variables==
==Flowise support different environment variables to configure your instance. You can specify the following variables in the== ==`.env`== ==file inside== ==`packages/server`== ==folder. Read== [==more==](https://github.com/FlowiseAI/Flowise/blob/main/CONTRIBUTING.md#-env-variables)
## ==📖 Documentation==
[==Flowise Docs==](https://docs.flowiseai.com/)
## ==🌐 Self Host==
==Deploy Flowise self-hosted in your existing infrastructure, we support various== [==deployments==](https://docs.flowiseai.com/configuration/deployment)
- [==AWS==](https://docs.flowiseai.com/deployment/aws)
- [==Azure==](https://docs.flowiseai.com/deployment/azure)
- [==Digital Ocean==](https://docs.flowiseai.com/deployment/digital-ocean)
- [==GCP==](https://docs.flowiseai.com/deployment/gcp)
- ==Others==
    - [==Railway==](https://docs.flowiseai.com/deployment/railway)
        
        [![](https://camo.githubusercontent.com/d07713342bc583232f8752c33a6a24e5f367d73725183a63f2f5fdd7c00606a3/68747470733a2f2f7261696c7761792e6170702f627574746f6e2e737667)](https://camo.githubusercontent.com/d07713342bc583232f8752c33a6a24e5f367d73725183a63f2f5fdd7c00606a3/68747470733a2f2f7261696c7761792e6170702f627574746f6e2e737667)
        
    - [==Render==](https://docs.flowiseai.com/deployment/render)
        
        [![](https://camo.githubusercontent.com/8bd9b408ceaa8efda215a42c9519114dec33c0f129e4d0d0b0a6eb670398be63/68747470733a2f2f72656e6465722e636f6d2f696d616765732f6465706c6f792d746f2d72656e6465722d627574746f6e2e737667)](https://camo.githubusercontent.com/8bd9b408ceaa8efda215a42c9519114dec33c0f129e4d0d0b0a6eb670398be63/68747470733a2f2f72656e6465722e636f6d2f696d616765732f6465706c6f792d746f2d72656e6465722d627574746f6e2e737667)
        
    - [==HuggingFace Spaces==](https://docs.flowiseai.com/deployment/hugging-face)
        
        [![](https://camo.githubusercontent.com/e0665f2bd1a938c4279af046bf12c53b3c79bffc4fec32b98221f072e7e3acae/68747470733a2f2f68756767696e67666163652e636f2f64617461736574732f68756767696e67666163652f6261646765732f7261772f6d61696e2f6f70656e2d696e2d68662d7370616365732d736d2e737667)](https://camo.githubusercontent.com/e0665f2bd1a938c4279af046bf12c53b3c79bffc4fec32b98221f072e7e3acae/68747470733a2f2f68756767696e67666163652e636f2f64617461736574732f68756767696e67666163652f6261646765732f7261772f6d61696e2f6f70656e2d696e2d68662d7370616365732d736d2e737667)
        
    - [==Elestio==](https://elest.io/open-source/flowiseai)
        
        [![](https://camo.githubusercontent.com/20bc2e60414176e6de7a9244ba478f2b32997f0e3f21526022f7f32a35126914/68747470733a2f2f7075622d64613336313537633835343634383636393831336633663736633532366332622e72322e6465762f6465706c6f792d6f6e2d656c657374696f2d626c61636b2e706e67)](https://camo.githubusercontent.com/20bc2e60414176e6de7a9244ba478f2b32997f0e3f21526022f7f32a35126914/68747470733a2f2f7075622d64613336313537633835343634383636393831336633663736633532366332622e72322e6465762f6465706c6f792d6f6e2d656c657374696f2d626c61636b2e706e67)
        
    - [==Sealos==](https://cloud.sealos.io/?openapp=system-template%3FtemplateName%3Dflowise)
        
        [![](https://raw.githubusercontent.com/labring-actions/templates/main/Deploy-on-Sealos.svg)](https://raw.githubusercontent.com/labring-actions/templates/main/Deploy-on-Sealos.svg)
        
    - [==RepoCloud==](https://repocloud.io/details/?app_id=29)
        
        [![](https://camo.githubusercontent.com/5ab332be1c758ec9b4f0fba19fbd5777f3bb10bef842ed33729edcad616c35ed/68747470733a2f2f64313674307063343834367835322e636c6f756466726f6e742e6e65742f6465706c6f792e706e67)](https://camo.githubusercontent.com/5ab332be1c758ec9b4f0fba19fbd5777f3bb10bef842ed33729edcad616c35ed/68747470733a2f2f64313674307063343834367835322e636c6f756466726f6e742e6e65742f6465706c6f792e706e67)
        
## ==💻 Cloud Hosted==
==Coming soon==
## ==🙋 Support==
==Feel free to ask any questions, raise problems, and request new features in== [==discussion==](https://github.com/FlowiseAI/Flowise/discussions)
## ==🙌 Contributing==
==Thanks go to these awesome contributors==
[![](https://camo.githubusercontent.com/a67f051d3cb975101725daef9a95a7ef40ea6b5f900bcd78af7a4c534d6dde4c/68747470733a2f2f636f6e747269622e726f636b732f696d6167653f7265706f3d466c6f7769736541492f466c6f77697365)](https://camo.githubusercontent.com/a67f051d3cb975101725daef9a95a7ef40ea6b5f900bcd78af7a4c534d6dde4c/68747470733a2f2f636f6e747269622e726f636b732f696d6167653f7265706f3d466c6f7769736541492f466c6f77697365)
==See== [==contributing guide==](https://github.com/FlowiseAI/Flowise/blob/main/CONTRIBUTING.md)==. Reach out to us at== [==Discord==](https://discord.gg/jbaHfsRVBW) ==if you have any questions or issues.==
[![](https://camo.githubusercontent.com/702a568bf3fe97dc1af52c0edd432947664c1cd47c55f0d0b1a0d79a77f3e7e0/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d466c6f7769736541492f466c6f7769736526747970653d54696d656c696e65)](https://camo.githubusercontent.com/702a568bf3fe97dc1af52c0edd432947664c1cd47c55f0d0b1a0d79a77f3e7e0/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d466c6f7769736541492f466c6f7769736526747970653d54696d656c696e65)
## ==📄 License==
==Source code in this repository is made available under the== [==Apache License Version 2.0==](https://github.com/FlowiseAI/Flowise/blob/main/LICENSE.md)==.==