---
DocFlag:
  - Tested
Updated: 2024-06-07T15:15
tags:
  - AI->-Image
URL: https://ai.google.dev/gemma/docs/paligemma?hl=zh-cn
Created: 2024-06-07T02:40
---
  
  
```Python
https://www.kaggle.com/models/google/paligemma/keras/discussion?sort=undefined
cd /opt/aibase/paligemma-3b-mix-224
models--google--paligemma-3b-mix-224
huggingface-cli download google/paligemma-3b-mix-224 --local-dir .

#因为要使用PaliGemmaForConditionalGeneration， 所以要升级了
pip install -q -U git+https://github.com/huggingface/transformers.git

from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
from PIL import Image
import requests
import torch
model_id = "google/paligemma-3b-mix-224"
device = "cuda:0"
dtype = torch.bfloat16
model = PaliGemmaForConditionalGeneration.from_pretrained(
    model_id,
    torch_dtype=dtype,
    device_map=device,
    revision="bfloat16",
).eval()
processor = AutoProcessor.from_pretrained(model_id)

image = Image.open("./pdfs/imapages/test.png")
max_size = (224, 224)
quality=75
    # Open the PDF file
if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
    image.thumbnail(max_size, Image.Resampling.LANCZOS)
# Save the image to the specified folder
image_path = f"./pdfs/imapages/test.png"
image.save(image_path, format='PNG', optimize=True, quality=quality)
                    
# Instruct the model to create a caption in Spanish
prompt = "Pls describe the image"
model_inputs = processor(text=prompt, images=image, return_tensors="pt").to(model.device)
input_len = model_inputs["input_ids"].shape[-1]
with torch.inference_mode():
    generation = model.generate(**model_inputs, max_new_tokens=100, do_sample=False)
    generation = generation[0][input_len:]
    decoded = processor.decode(generation, skip_special_tokens=True)
    print(decoded)                 
                    
#!!! Looks it has to be FT before we can use it
# Instruct the model to create a caption in Spanish
prompt = "Pls describe the image in Japanese"
model_inputs = processor(text=prompt, images=image, return_tensors="pt").to(model.device)
input_len = model_inputs["input_ids"].shape[-1]
with torch.inference_mode():
    generation = model.generate(**model_inputs, max_new_tokens=100, do_sample=False)
    generation = generation[0][input_len:]
    decoded = processor.decode(generation, skip_special_tokens=True)
    print(decoded)
Output!!!    
unanswering does not require reading text in the image
    
```
  
  
[https://huggingface.co/blog/paligemma](https://huggingface.co/blog/paligemma)
https://github.com/merveenoyan/smol-vision/blob/main/Fine_tune_PaliGemma.ipynb
![[share.png]]
## PaliGemma
PaliGemma 是一种轻量级的开放_视觉语言模型_ (VLM)，其灵感来自 [PaLI-3](https://arxiv.org/abs/2310.09199)，并基于 [SigLIP 视觉模型](https://arxiv.org/abs/2303.15343)和 [Gemma 语言模型](https://arxiv.org/abs/2403.08295)等开放组件。PaliGemma 将图片和文本作为输入，可以回答有关图片的问题并提供详细信息和上下文，这意味着 PaliGemma 可以对图片进行更深入的分析，并提供有用的数据洞见，例如为图片和短视频添加说明、对象检测以及读取图片中嵌入的文字。
PaliGemma 模型分为两组：通用模型集和研究导向模型集：
- [PaliGemma](https://www.kaggle.com/models/google/paligemma) - 可针对各种任务进行微调的通用预训练模型。
- [PaliGemma-FT](https://www.kaggle.com/models/google/paligemma-ft) - 面向研究的模型，可根据特定研究数据集进行微调。
**重要提示**：除 `paligemma-3b-mix` 变体外，_大多数_ PaliGemma 模型都需要进行调参才能生成有用的结果。 请务必先对这些模型执行微调，并测试输出，然后再将其部署给最终用户。
主要优势包括：
- multiple_stop 支持同时理解图片和文字。
    
    ### 多模态理解
    
- build 可以针对各种视觉语言任务进行微调。
    
    ### 多功能基本模型
    
- explore 附带针对多种任务微调的检查点，可立即用于研究。
    
    ### 现成的探索
    
## 了解详情
### [查看模型卡片](https://ai.google.dev/gemma/docs/paligemma/model-card?hl=zh-cn)
PaliGemma 的模型卡片包含有关模型的详细信息、实现信息、评估信息、模型使用情况和限制等。
### [在 Kaggle 上查看](https://www.kaggle.com/models/google/paligemma)
在 Kaggle 上查看有关 PaliGemma 的更多代码、Colab 笔记本、信息和讨论。