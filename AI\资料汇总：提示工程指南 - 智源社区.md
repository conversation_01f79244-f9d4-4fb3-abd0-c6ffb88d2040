---
Updated: 2023-03-02T22:12
tags:
  - AI->-Prompt
  - AI->-Theory
Created: 2023-03-01T23:56
---
GitHub上的一个资料集[“Prompt Engineering Guide”](https://github.com/dair-ai/Prompt-Engineering-Guide)，收集了提示工程方面的论文、学习资料和工具。
## Papers
- Surveys:
    - [Pre-train, Prompt, and Predict: A Systematic Survey of Prompting Methods in Natural Language Processing](https://arxiv.org/abs/2107.13586)
    - [A Taxonomy of Prompt Modifiers for Text-To-Image Generation](https://arxiv.org/abs/2204.13988)
- Applications:
    - [Legal Prompt Engineering for Multilingual Legal Judgement Prediction](https://arxiv.org/abs/2212.02199)
    - [Investigating Prompt Engineering in Diffusion Models](https://arxiv.org/abs/2211.15462)
    - [Conversing with Copilot: Exploring Prompt Engineering for Solving CS1 Problems Using Natural Language](https://arxiv.org/abs/2210.15157)
    - [Piloting Copilot and Codex: Hot Temperature, Cold Prompts, or Black Magic?](https://arxiv.org/abs/2210.14699)
- Approaches/Techniques:
    - [Ask Me Anything: A simple strategy for prompting language models](https://paperswithcode.com/paper/ask-me-anything-a-simple-strategy-for)
    - [Large Language Models Are Human-Level Prompt Engineers](https://sites.google.com/view/automatic-prompt-engineer?pli=1)
    - [Promptagator: Few-shot Dense Retrieval From 8 Examples](https://arxiv.org/abs/2209.11755)
    - [Prompt Programming for Large Language Models: Beyond the Few-Shot Paradigm](https://www.arxiv-vanity.com/papers/2102.07350/)
- Collections:
    - [Papers with Code](https://paperswithcode.com/task/prompt-engineering)
    - [Prompt Papers](https://github.com/thunlp/PromptPapers#papers)
## Tools & Libraries
- [OpenAI Playground](https://beta.openai.com/playground)
- [GPTTools](https://gpttools.com/comparisontool)
- [Lexica](https://lexica.art/)
- [Prompt Base](https://promptbase.com/)
- [Playground](https://playgroundai.com/)
- [OpenPrompt](https://github.com/thunlp/OpenPrompt)
- [Visual Prompt Builder](https://tools.saxifrage.xyz/prompt)
- [Prompt Generator for OpenAI's DALL-E 2](http://dalle2-prompt-generator.s3-website-us-west-2.amazonaws.com/)
- [AI Test Kitchen](https://aitestkitchen.withgoogle.com/)
- [Prompt Engine](https://github.com/microsoft/prompt-engine)
- [PromptSource](https://github.com/bigscience-workshop/promptsource)
## Datasets
- [PartiPrompts](https://parti.research.google/)
- [Real Toxicity Prompts](https://allenai.org/data/real-toxicity-prompts)
- [P3 - Public Pool of Prompts](https://huggingface.co/datasets/bigscience/P3)
- [WritingPrompts](https://github.com/dair-ai/Prompt-Engineering-Guide/blob/main/WritingPrompts)
- [Midjourney Prompts](https://huggingface.co/datasets/succinctly/midjourney-prompts)
- [Awesome ChatGPT Prompts](https://huggingface.co/datasets/fka/awesome-chatgpt-prompts)
- [Stable Diffusion Dataset](https://huggingface.co/datasets/Gustavosta/Stable-Diffusion-Prompts)
## Blog, Guides, Tutorials and Other Readings
- [Prompt Engineering by co:here](https://docs.cohere.ai/docs/prompt-engineering)
- [Prompt Engineering by Microsoft](https://microsoft.github.io/prompt-engineering/)
- [Start with an Instruction](https://beta.openai.com/docs/quickstart/start-with-an-instruction)
- [DALLE Prompt Book](https://dallery.gallery/the-dalle-2-prompt-book/)
- [DALL·E 2 Prompt Engineering Guide](https://docs.google.com/document/d/11WlzjBT0xRpQhP9tFMtxzd0q6ANIdHPUBkMV-YB043U/edit#)
- [Language Models and Prompt Engineering: Systematic Survey of Prompting Methods in NLP](https://youtube.com/watch?v=OsbUfL8w-mo&feature=shares)
- [Learn Prompting](https://learnprompting.org/)
- [Prompt Engineering Topic by GitHub](https://github.com/topics/prompt-engineering)
- [Prompt Engineering Template](https://docs.google.com/spreadsheets/d/1-snKDn38-KypoYCk9XLPg799bHcNFSBAVu2HVvFEAkA/edit#gid=0)