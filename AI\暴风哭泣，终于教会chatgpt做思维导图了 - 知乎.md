---
Updated: 2023-03-02T22:12
tags:
  - AI->-Chatbot
  - AI->-Prompt
Created: 2023-03-01T23:59
---
## **在**
教了chatgpt两天并熬到了凌晨四点还在教它
换了无数种具体要求的说法
甚至还给它喂了无数我做的思维导图
给它文本让它自己做发现它的问题一点点给它纠正
几乎是手把手的教它做思维导图
## **之后**
## **我终于总结出来了针对chatgpt的这三条制作规则了！！！**
亲测能够有效防止它再给我做出一堆垃圾来（除非文本太难，它看不懂，分析不出来里面的逻辑性，这种还是只能我自己上了）。
下面是我总结的那三条制定规则，我放在这里了大家可以复制，希望大家都能使用这个训练chatgpt做思维导图，这样它做出来的思维导图才能越来越好，训练它的人更多，它的制作水平也就越高：
1.如果没有给你主题的情况下，要先浏览文本确定这一片的文本是在讲什么，制定一个主题。
2.对这一主题下的文本的每句话都进行分析，分析每一句话的含义，分析这句话和主题的关系，这句话是主题的什么，体现了主题的什么。然后找出其中的联系，并把总结出来的能表示这种联系的词汇作为主题的分支，再把作为内容的原句子放在这一分支后面。如果原文本中写出了它们的联系，就提取原文本中的词作为分支。同理，对每一个子主题相关的文本的每句话也要都进行分析，分析这句话是子主题的什么，找到其中的联系，把总结出来的表示这种联系的词汇作为这个子主题的分支，再把作为内容的原句子放在这一分支后面。同样的，如果原文本中写出了它们的联系，就提取原文本中的词作为分支。
3.分析句子的时候又不能只看这个句子和主题的联系。还要看它和分支之间的联系。要找到句子和句子之间的联系，有的时候文本会给一个总结性的句子，在它之后的句子都是对这句话的展开解释说明，这个时候就不能把那些用来解释的句子和总结性的句子并列，就要把前者放在后者之下，作为后者的一个分支。
![[v2-85d49140364c6271e8f35e6a596d1d73_720w.webp]]
再对比一下对chatgpt没有要求让它自由发挥制作的思维导图：
可以看出让chatgpt自己做的思维导图明显不如要求它按照那三条规则制定出来的思维导图更加条例清晰，第二级分支还好，到了第三级的分组chatgpt就完全不去判断内容的性质了，直接把所有的句子一提取然后一股脑的丢进二级分支里面去了。
介于有朋友说我这生成的是大纲不是思维导图，那我教一下大家怎么把这种文本格式的思维导图转换成正常格式的思维导图吧：
首先，需要一台电脑（手机亲测复制出来的没有格式，不能用），一个叫做幕布的思维导图软件，再在电脑中打开chatgpt的网页，把内容复制下来：
打开幕布，新建一个主题，把复制的内容粘贴到里面，可以看到是带着格式一起粘贴过去的
之后把标题挪到上面放标题的位置，再点右上角的思维导图
点开后出现的就是思维导图形式的大纲了
再更新一下，有朋友问我关于训练chatgpt后训练效果能不能保留的问题，这些我也想过，我当时训练它的时候就问了它很多问题，下面我把它的回答放到这里。
再放一下关于回复一个朋友投喂的截图，想投喂它就也要给它发文本形式的思维导图，也就是markdown形式的大纲吧，可以用一些支持markdown编辑的软件做好了后再复制下来发给它。我自己用的不是支持markdown编辑的思维导图软件，是一个能间隔重复记忆的笔记软件，但是也可以带着格式把大纲复制下来直接发给chatgpt。
再更新一下吧，这几天浏览的人已经有些少了。为了能让更多人看到叶大的文章，避免有些后来看到这篇文章的人没看评论区错过我说的内容，我就把置顶评论的内容再搬到文章里吧：
大家可以看一下叶大
[@叶峻峣](https://www.zhihu.com/people/4c592f496dc33822b560b382907ff1d0)
写的这篇文章
我产生教ai制作思维导图的想法就是在看了叶大教它制作anki卡片的想法之后，因为我自己更习惯记忆内容体系化，所以看了anki卡片可以自动化制卡后就立刻想到思维导图也实现自动化生成用在remnote（也是看叶大主页知道的，大家感兴趣可以去了解一下）上进行间隔重复记忆。
叶大这篇文章是写自己总结出来的让ai制卡的prompt是怎么一步步产生的。Prompt 是为了让语言模型实现预期任务所提供的指令和背景信息。Prompt 工程是开发和优化 Prompt 的实践，可以帮助我们更有效地利用语言模型完成各种任务。我的总结的这三条针对ai的制作思维导图的规则其实就算是一个prompt，我也经历了和叶大类似的摸索过程，修改补充了很多次。完整的prompt其实共包括下面这些：
指令：希望模型执行的具体任务或指示
背景：补充的外部或上下文信息，可以引导模型做出更好的反应
输入数据：想要解决的输入或问题
输出指示：输出的类型或格式
我在给ai命令的时候其实就无意中已经包括了这些了，在文章中我以文字放出来的只有那三条规则，只能算是其中的一部分，但是有的仔细看了我的截图的朋友应该可以发现我还限定了输出格式什么的，我在跟ai交流的时候还是包含了prompt的完整内容的。而叶大的文章里放出来的部分就是一个非常标准的prompt了。