---
Updated: 2024-05-22T12:24
tags:
  - AI->-Graph
Created: 2024-05-21T17:47
---
Reference
Setup
数据导入
数据查询
  
# Reference
```Python
# document
https://neo4j.com/docs/python-manual/current/
# installation package including jdk
G:\ResearchDirection\AI\Neo4j
```
# Setup
![[Neo4j-7cbf30e6-Created-2024-05-21.txt]]
```Python
https://console.neo4j.io/
Username: neo4j
Password: BW_VBpJ0oOLW_vNmPKoJG6DBy6NF-1wcKg157yWySys
import dotenv
import os
from neo4j import GraphDatabase
load_status = dotenv.load_dotenv("Neo4j-7cbf30e6-Created-2024-05-21.txt")
if load_status is False:
    raise RuntimeError('Environment variables not loaded.')
URI = os.getenv("NEO4J_URI")
AUTH = (os.getenv("NEO4J_USERNAME"), os.getenv("NEO4J_PASSWORD"))
with GraphDatabase.driver(URI, auth=AUTH) as driver:
    driver.verify_connectivity()
```
  
```Python
Local Host version
G:\ResearchDirection\AI\Neo4j\neo4j-community-5.19.0-windows\neo4j-community-5.19.0
1. Open a console and navigate to the installation directory.
2. Start the server:
   * Windows, use: bin\neo4j-admin server console
   * Linux/Mac, use: ./bin/neo4j-admin server console
3. In a browser, open http://localhost:7474/
4. Shutdown the server by typing Ctrl-C in the console.
```
  
# 数据导入
```Python
To specify the URL for your CSV file in a Neo4j Cypher query, you need to use 
the file:// protocol followed by the path to the file relative to the default import directory 
of your Neo4j installation. The import directory is typically set to <neo4j-home>/import/ in your Neo4j configuration.
NOTE！！！ data file is not in client side but in server side
G:\ResearchDirection\AI\Neo4j\neo4j-community-5.19.0-windows\neo4j-community-5.19.0\import
import dotenv
import os
from neo4j import GraphDatabase
uri = "neo4j://************:7687"
AUTH = ("neo4j", "qapl1209ws")
driver = GraphDatabase.driver(uri, auth=AUTH)
with GraphDatabase.driver(uri, auth=AUTH) as driver:
    driver.verify_connectivity()
    
          
            
def load_data(driver):
    with GraphDatabase.driver(URI, auth=AUTH) as driver:    
        with driver.session(database="neo4j") as session:
            session.run("MATCH ()-[r]->() DELETE r")
            session.run("MATCH (n) DELETE n")
            print("Loading user data ...")
            session.run("""
                LOAD CSV WITH HEADERS FROM 'file:///userdb_neo4j.csv' AS row
                MERGE (u:User {userID: row.userID, username: row.username})
                MERGE (s:Stock {stockCode: row.stockCode, stockName: row.stockName})
                MERGE (u)-[:SELECTED]->(s)
                """)
def query_data(driver):
    with GraphDatabase.driver(URI, auth=AUTH) as driver:    
        with driver.session(database="neo4j") as session:
            print("Querying data ...")
            result = session.run("""
                MATCH (u:User)-[:SELECTED]->(s:Stock)
                RETURN u.username, s.stockName
                """)
            for record in result:
                print(f"{record['u.username']} selected {record['s.stockName']}")
            
            
try:
    load_data(driver)
    query_data(driver)
finally:
    driver.close()         

    
```
  
# 数据查询
```Python
from neo4j import GraphDatabase
# Database Connection URI and Authentication
uri = "neo4j://************:7687"
auth = ("neo4j", "qapl1209ws")
driver = GraphDatabase.driver(uri, auth=auth)
def recommend_stocks(driver):
    while True:
        userid = input("请输入要为哪位用户推荐股票，输入其ID即可: ")
        m = int(input("为该用户推荐多少只股票呢？ "))
        if userid == "0":
            break
        
        with driver.session() as session:
            # 计算与当前用户相似的用户
            result = session.run("""
                MATCH (u1:User {userID: $userid})-[:SELECTED]->(s:Stock)<-[:SELECTED]-(u2:User)
                WHERE u1 <> u2
                WITH u1, u2, COUNT(DISTINCT s) AS stocksInCommon
                WHERE stocksInCommon >= 1
                MERGE (u1)-[sim:SIMILARITY]-(u2)
                SET sim.stocksInCommon = stocksInCommon
                
                WITH u1
                MATCH (u1)-[sim:SIMILARITY]-(u2:User)
                WITH u1, u2, sim
                ORDER BY sim.stocksInCommon DESC LIMIT 3
                MATCH (s:Stock)<-[:SELECTED]-(u2)
                WHERE NOT (s)<-[:SELECTED]-(u1)
                WITH s, COUNT(*) AS recommendationCount
                RETURN s.stockName AS StockName, s.stockCode as StockCode, recommendationCount
                ORDER BY recommendationCount DESC, StockName
                LIMIT $m
            """, parameters={'userid': userid, 'm': m})
            print("Recommended stocks:")
            stocks = []
            for record in result:
                stocks.append([record["StockName"], record["StockCode"], record["recommendationCount"]])
            if not stocks:
                print("No recommendations found.")
            else:
                df = pd.DataFrame(stocks, columns=["Stock Name", "Stock Code", "Number of Recommendations"])
                print(df.to_string(index=False))
            print()
try:
    recommend_stocks(driver)
finally:
    driver.close()
```