---
Updated: 2024-07-29T17:35
tags:
  - AI->-Tools
Created: 2024-07-29T17:35
---
[![](https://miro.medium.com/v2/resize:fit:926/1*zHMUkIe5JmjgYLlxBPjxsg.png)](https://miro.medium.com/v2/resize:fit:926/1*zHMUkIe5JmjgYLlxBPjxsg.png)
[![](https://miro.medium.com/v2/resize:fill:55:55/1*oxS7GUko4Gu2H4Cw8UaehQ.png)](https://miro.medium.com/v2/resize:fill:55:55/1*oxS7GUko4Gu2H4Cw8UaehQ.png)
[==Nero Un Chi Hin 阮智軒==](https://medium.com/@NeroHin?source=post_page-----a2cfdd892cbc--------------------------------)
==12 min read==
==Feb 18, 2024==
# ==前言 / 為甚麼？==
==過年前看到群創光電智能推進處(Text&AIGC) 發表了== [==Bailong-instruct 7B==](https://huggingface.co/INX-TEXT/Bailong-instruct-7B)==，號稱比上個月發表的 MediaTek-Research 發表的 Breeze-7B 有更好的表現（但 2/17 的== [==Discussion==](https://huggingface.co/INX-TEXT/Bailong-instruct-7B/discussions/3#65d03dac5c5797eabf61a117) ==中有討論一番）。==
[![](https://miro.medium.com/v2/resize:fit:700/1*zHMUkIe5JmjgYLlxBPjxsg.png)](https://miro.medium.com/v2/resize:fit:700/1*zHMUkIe5JmjgYLlxBPjxsg.png)
==但無論結果如何看到台灣本土繁中模型發表還是很興奮的，於是就趁春節時用 LM Studio 來驗證一下效能如何；但因為模型太新了（而且==[==唐鳳==](https://huggingface.co/audreyt)==委員這次也還沒幫忙轉檔）目前還沒有 GGUF 版本可以下載、於是在網上找到了一下要怎麼把 HuggingFace Model 轉成 GGUF 格式的教學後，決定也來貢獻一下開源社群 XD==
[![](https://miro.medium.com/v2/resize:fit:700/1*CVY9jZ6BT4KJYb1zC1GF4A.png)](https://miro.medium.com/v2/resize:fit:700/1*CVY9jZ6BT4KJYb1zC1GF4A.png)
# ==本文架構==
- ==甚麼是 GGUF==
- ==動手來轉檔、量化和上傳==
- ==總結==
# ==甚麼是 GGUF？==
==GGUF 格式全名為（GPT-Generated Unified Format），而提到 GGUF 就不得不提到它的前身== [==GGML==](https://github.com/ggerganov/ggml)==（GPT-Generated Model Language），後者是專門為了 Machine Learning 設計的 Tensor Library、最早可以追溯到 2022/10；目的是為了有一個單文件（Single File Sharing）的格式並且易於在不同架構的 GPU 和 CPU 上可以 inference，但在後續的開發上遇到靈活性不足、相容性及難以維護的問題。==
# ==為甚麼要轉換 GGUF 格式==
==在傳統的 Deep Learning Model 開發中大多使用 PyTorch 來進行開發，但因為在部署時會面臨相依 Lirbrary 太多、版本管理的問題於才有了 GGML、GGMF、GGJT 等格式，而在開源社群不停的迭代後 GGUF 就誕生了。==
==GGUF 其實是基於 GGJT 的格式上進行優化的，並解決了 GGML 當初面臨的問題，包括：==
1. ==可擴展性：輕鬆為 GGML 架構下的工具添加新功能，或者向 GGUF 模型添加新 Feature，不會破壞與現有模型的兼容性。==
2. ==對 mmap （==[==memory map==](https://man7.org/linux/man-pages/man2/mmap.2.html)==） 兼容性：模型可以使用 mmap 進行加載（原理解析可看==[==參考==](https://zhuanlan.zhihu.com/p/348102901)==），實現快速載入和儲存。(從 GGJT 開始導入，可參考== [==GitHub==](https://github.com/ggerganov/ggml/discussions/492)==)==
3. ==易於使用：模型可以使用少量程式碼輕鬆加載和儲存，無需相依的 Library，同時對於不同程式語言支援程度也高。==
4. ==模型資訊完整：載入模型所需的所有資訊都包含在模型文件中，不需額外撰寫設定檔。==
5. ==利於模型量化：GGUF 支援模型量化（4-bits, 8-bits, F16），在 GPU 越來越貴的情況下節省 vRAM 成本也是相當重要的。==
[![](https://miro.medium.com/v2/resize:fit:700/1*7ZCAGNy-UZou8sDMeAEodg.png)](https://miro.medium.com/v2/resize:fit:700/1*7ZCAGNy-UZou8sDMeAEodg.png)
# ==動手來轉檔、量化和上傳==
==整個轉檔的步驟如下：==
1. ==從 HuggingFace 下載 Model==
2. ==使用 llama.cpp 來進行轉檔==
3. ==使用 llama.cpp 來進行量化模型==
4. ==上傳轉檔及量化後的 GGUF 模型到 Huggingface Repo==
# ==從 HuggingFace 下載 Model==
==最直覺是用 git clone 來下載模型 XD 但是因為 LLM 每個一部分都按 GB 來計算，避免出現 OOM Error 的情況下簡單用 Python 寫一個 download.py 比較簡單。==
==先安裝== ==`huggingface_hub`====：==
==pip install huggingface_hub==
==再來使用== ==`huggingface_hub`== ==的== ==`snapshot_download`== ==來下載：==
==from huggingface_hub import snapshot_download==
==model_id = "INX-TEXT/Bailong-instruct-7B"====  
  
====snapshot_download(====  
  
====repo_id=model_id,====  
  
====local_dir="INX-TEXT_Bailong-instruct-7B",====  
  
====local_dir_use_symlinks=False,====  
  
====revision="main",====  
  
====use_auth_token="<YOUR_HF_ACCESS_TOKEN>")==
==cd 到== ==`[download.py](https://medium.com/@NeroHin/download.py)`== ==的 path 下執行==
==python download.py==
[![](https://miro.medium.com/v2/resize:fit:700/1*KvW3BdAyk9L1QCFWka7D2Q.png)](https://miro.medium.com/v2/resize:fit:700/1*KvW3BdAyk9L1QCFWka7D2Q.png)
# ==使用 llama.cpp 來進行轉檔==
[==llama.cpp==](https://github.com/ggerganov/llama.cpp) ==是 GGML 主要作者基於 Meta 最早 Open Souce LLM LLaMA 的 C/C++ 版本，目的是希望用 CPU 就可以 inference LLM；在社群眾人不懈的努力和貢獻下現今支援大多主流模型，包括（截止 2024/2/18）：==
- ==LLaMA 🦙==
- ==LLaMA 2 🦙🦙==
- [==Mistral 7B==](https://huggingface.co/mistralai/Mistral-7B-v0.1)
- [==Mixtral MoE==](https://huggingface.co/models?search=mistral-ai%2FMixtral)
- ==Falcon==
- [==Chinese LLaMA / Alpaca==](https://github.com/ymcui/Chinese-LLaMA-Alpaca) ==and== [==Chinese LLaMA-2 / Alpaca-2==](https://github.com/ymcui/Chinese-LLaMA-Alpaca-2)
- [==Vigogne (French)==](https://github.com/bofenghuang/vigogne)
- [==Koala==](https://bair.berkeley.edu/blog/2023/04/03/koala/)
- [==Baichuan 1 & 2==](https://huggingface.co/models?search=baichuan-inc%2FBaichuan) ==+== [==derivations==](https://huggingface.co/hiyouga/baichuan-7b-sft)
- [==Aquila 1 & 2==](https://huggingface.co/models?search=BAAI%2FAquila)
- [==Starcoder models==](https://github.com/ggerganov/llama.cpp/pull/3187)
- [==Refact==](https://huggingface.co/smallcloudai/Refact-1_6B-fim)
- [==Persimmon 8B==](https://github.com/ggerganov/llama.cpp/pull/3410)
- [==MPT==](https://github.com/ggerganov/llama.cpp/pull/3417)
- [==Bloom==](https://github.com/ggerganov/llama.cpp/pull/3553)
- [==Yi models==](https://huggingface.co/models?search=01-ai%2FYi)
- [==StableLM models==](https://huggingface.co/stabilityai)
- [==Deepseek models==](https://huggingface.co/models?search=deepseek-ai%2Fdeepseek)
- [==Qwen models==](https://huggingface.co/models?search=Qwen%2FQwen)
- [==PLaMo-13B==](https://github.com/ggerganov/llama.cpp/pull/3557)
- [==Phi models==](https://huggingface.co/models?search=microsoft%2Fphi)
- [==GPT-2==](https://huggingface.co/gpt2)
- [==Orion 14B==](https://github.com/ggerganov/llama.cpp/pull/5118)
- [==InternLM2==](https://huggingface.co/models?search=internlm2)
- [==CodeShell==](https://github.com/WisdomShell/codeshell)
==**多模態模型:**==
- [==LLaVA 1.5 models==](https://huggingface.co/collections/liuhaotian/llava-15-653aac15d994e992e2677a7e)
- [==BakLLaVA==](https://huggingface.co/models?search=SkunkworksAI%2FBakllava)
- [==Obsidian==](https://huggingface.co/NousResearch/Obsidian-3B-V0.5)
- [==ShareGPT4V==](https://huggingface.co/models?search=Lin-Chen%2FShareGPT4V)
- [==MobileVLM 1.7B/3B models==](https://huggingface.co/models?search=mobileVLM)
- [==Yi-VL==](https://huggingface.co/models?search=Yi-VL)
==透過以上 LLM 陣容來說可謂非常強大 XD 同時它也支援 macOS、Linux、Windows、Docker、FreeBSD，為日後小參數 LLM 在 Edge Device 運行打下良好基礎。==
[![](https://miro.medium.com/v2/resize:fit:700/1*gN94PiRifJBNGwM8BmdIWQ.png)](https://miro.medium.com/v2/resize:fit:700/1*gN94PiRifJBNGwM8BmdIWQ.png)
==首先我們先 clone llama.cpp 到本地：==
==git clone https://github.com/ggerganov/llama.cpp.git==
==因為 llama.cpp 轉換模型的程式是基於 python 開發，所以要安裝相依的 Library(建議開個 venv 來執行後續的轉檔):==
==20240703 更新：llama.cpp 有==[==公告==](https://github.com/ggerganov/llama.cpp/pull/7430) ==convert.py 棄用了，要改用 convert-hf-to-gguf.py～==
==cd llama.cpp====  
  
====pip install -r requirements.txt==
==安裝後來測些一下轉換程式能否運作：==
==python convert.py -h==
[![](https://miro.medium.com/v2/resize:fit:700/1*S_G6vx8n1lC-j1Dfmwzwhg.png)](https://miro.medium.com/v2/resize:fit:700/1*S_G6vx8n1lC-j1Dfmwzwhg.png)
==然後來把剛剛從 HuggingFace 下載的 Model 轉換成 GGUF 格式：==
==python convert.py INX-TEXT_Bailong-instruct-7B \====  
  
====--outfile bailong-instruct-7b-f16.gguf \====  
  
====--outtype f16==
==還記得上面提到為了節省成本可以考慮將 Model 量化嗎？接下來我們來操作一下量化的步驟。==
# ==使用 llama.cpp 來進行量化模型==
==根據 HuggingFace 上 TheBloke（大善人）開源的== [==Llama-2–13B-chat-GGUF==](https://huggingface.co/TheBloke/Llama-2-13B-chat-GGUF/tree/main) ==項目中有 14種不同的 GGUF 模型，當中數字是代表量化的 bits 設定；以下是參考他對於不同量化的推薦和說明：==
- ==q2_k：特定張量 (Tensor) 采用較高的精度設置，而其他的則保持基礎級別。==
- ==q3_k_l、q3_k_m、q3_k_s：這些變體在不同張量上使用不同級別的精度，從而達到性能和效率的平衡。==
- ==q4_0：這是最初的量化方案，使用 4 位精度。==
- ==q4_1和q4_k_m、q4_k_s：這些提供了不同程度的準確性和推理速度，適合需要平衡資源使用的場景。==
- ==q5_0、q5_1、q5_k_m、q5_k_s：這些版本在保證更高準確度的同時，會使用更多的資源並且推理速度較慢。==
- ==q6_k和q8_0：這些提供了最高的精度，但是因為高資源消耗和慢速度，可能不適合所有用戶。==
[![](https://miro.medium.com/v2/resize:fit:700/1*XXGCegG7QqJL8woleTadNw.png)](https://miro.medium.com/v2/resize:fit:700/1*XXGCegG7QqJL8woleTadNw.png)
==量化示意圖（==[==Source==](https://blog.gopenai.com/the-llm-revolution-boosting-computing-capacity-with-quantization-methods-b8666cdb4b6a)==）==
==如果追求較低成本和保持模型效能的情況推薦使用用 Q5_K_M，如果想更節省 RAM，則可以考慮 Q4_K_M。一般來說，帶有 K_M 的版本會比 K_S 的版本表現更佳。不過不建議使用 Q2_K 或 Q3_* 等版本，因為它們會顯著降低模型的整體性能。==
==使用 llama.cpp 量化操作很簡單：==
==./quantize ./models/Bailong-instruct-7B-f16.gguf ./models/Bailong-instruct-7B-v0.1-Q5_K_M.gguf q5_k_m==
[![](https://miro.medium.com/v2/resize:fit:700/1*xNgo1kEW0gT_fVG-_5UPkA.png)](https://miro.medium.com/v2/resize:fit:700/1*xNgo1kEW0gT_fVG-_5UPkA.png)
==使用 LM Stduio 來查看時可以看到== ==`Q8`== ==和== ==`Q5_K_M`== ==兩者的大小相差 2.45 GB，在有限的 GPU 資源上實在是節省不小。==
[![](https://miro.medium.com/v2/resize:fit:700/1*GXV7LU-A8Hbmfj-GJ7Na5Q.png)](https://miro.medium.com/v2/resize:fit:700/1*GXV7LU-A8Hbmfj-GJ7Na5Q.png)
# ==上傳轉檔及量化後的 GGUF 模型到 Huggingface Repo==
==同步驟一的邏輯，避免透過 git pull 來上傳大型的檔案下來寫一個== ==`upload.py`== ==:==
==from huggingface_hub import HfApi====  
  
====import os==
==api = HfApi()====  
  
====HF_ACCESS_TOKEN = "<YOUR_HF_WRITE_ACCESS_TOKEN>"====  
  
====model_id = "NeroUCH/Bailong-instruct-7B-GGUF"==
==api.create_repo(====  
  
====model_id,====  
  
====exist_ok=True,====  
  
====repo_type="model",====  
  
====use_auth_token=HF_ACCESS_TOKEN,====  
  
====)==
==for file in os.listdir():====  
  
====if file.endswith(".gguf"):====  
  
====model_name = file.lower()====  
  
====api.upload_file(====  
  
====repo_id=model_id,====  
  
====path_in_repo=model_name,====  
  
====path_or_fileobj=f"{os.getcwd()}/{file}",====  
  
====repo_type="model",====  
  
====use_auth_token=HF_ACCESS_TOKE)==
==（因為檔案太大，上傳每個檔案都要花上十幾二十分鐘QQ）==
[![](https://miro.medium.com/v2/resize:fit:700/1*u-IP8lGwGY8vot1QhizNfg.png)](https://miro.medium.com/v2/resize:fit:700/1*u-IP8lGwGY8vot1QhizNfg.png)
==最後我們到== [==NeroUCH/Bailong-instruct-7B-GGUF==](https://huggingface.co/NeroUCH/Bailong-instruct-7B-GGUF) ==來確認有沒有上傳成功！==
[![](https://miro.medium.com/v2/resize:fit:700/1*gkBscQnZTqB3phibWKFy-A.png)](https://miro.medium.com/v2/resize:fit:700/1*gkBscQnZTqB3phibWKFy-A.png)
# ==總結==
==呼，平常看到社群上大家熱心的上傳轉換好的模型時就暗暗決定有朝一日也要回饋社群 XD 現在也成功把繁中的模型轉換後方便讓大家使用真是個不錯的善報呢；而轉換好的 GGUF 模型參考==[==唐鳳==](https://huggingface.co/audreyt)==委員的建議可以使用以下的工具來運行：==
- [==llama.cpp==](https://github.com/ggerganov/llama.cpp)==：GGUF 的源項目。提供 CLI 和 Server 選項。==
- [==text-generation-webui==](https://github.com/oobabooga/text-generation-webui)==：最廣泛使用的網絡界面，具有許多功能和強大的擴展。支持 GPU 加速。==
- [==KoboldCpp==](https://github.com/LostRuins/koboldcpp)==：一個功能齊全的網絡界面，支持所有平台和 GPU 架構的 GPU 加速。特別適合故事講述。==
- [==GPT4All==](https://gpt4all.io/index.html)==：一個免費且開源的本地運行圖形用戶界面，支持 Windows、Linux 和 macOS，並支援 GPU 加速。==
- [==LM Studio==](https://lmstudio.ai/)==（推薦）：一個易於使用且功能強大的本地圖形用戶界面，適用於Windows和macOS（Silicon），支持GPU加速。==
- [==LoLLMS Web UI==](https://github.com/ParisNeo/lollms-webui)==：提供了許多獨特的功能，包括一個完整的模型庫，讓用戶輕鬆選擇模型，非常適合尋找特色功能的用戶。==
- [==Faraday.dev==](https://medium.com/@NeroHin/Faraday.dev)==：一個吸引人且易於使用的基於角色的聊天圖形用戶界面，適用於 Windows和 macOS（Silicon和Intel），支持GPU加速。==
- [==llama-cpp-python==](https://github.com/abetlen/llama-cpp-python)==：支持GPU加速、LangChain 和 OpenAI 兼容 API 服務器的 Python庫。==
- [==candle==](https://github.com/huggingface/candle)==：以 Rust 撰寫的機器學習框架，提供GPU支持和易於使用的特點，適合追求性能的開發者。==
# ==結尾 MurMur==
- ==謝謝您看到這裡，如果文章對您有幫助可以幫我拍個手！==
- ==也歡迎找我交流你的心得和想法～==
- ==LinkedIn:== [==https://www.linkedin.com/in/nerouch/==](https://www.linkedin.com/in/nerouch/)
- ==GitHub:== [==https://github.com/NeroHin==](https://github.com/NeroHin)
# ==參考==
- [==Quantize Llama models with GGUF and llama.cpp==](https://mlabonne.github.io/blog/posts/Quantize_Llama_2_models_using_ggml.html)
- [==Tutorial: How to convert HuggingFace model to GGUF format==](https://github.com/ggerganov/llama.cpp/discussions/2948)
- [==AI 筆記 — 電腦沒有獨立顯卡，只靠 CPU 也能跑大型語言模型嗎？==](https://blog.darkthread.net/blog/llama-cpp/)
- [==What is GGUF and GGML?==](https://medium.com/@phillipgimmi/what-is-gguf-and-ggml-e364834d241c)
- [==GGUF vs. GGML: Why GGUF Is a Better File Format==](https://deci.ai/blog/ggml-vs-gguf-comparing-formats-amp-top-5-methods-for-running-gguf-files/)