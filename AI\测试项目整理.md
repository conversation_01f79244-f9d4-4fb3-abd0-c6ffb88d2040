---
DocFlag:
  - Reference
Updated: 2023-12-07T14:53
tags:
  - AI->-Fine-Tuning
  - AI->-Model
  - AI->-Programming
Created: 2023-03-18T13:41
Score: OOOOO
---
```Go
<<Unix>>
base                  *  /home/<USER>/miniconda3
ChatGLM                  /home/<USER>/miniconda3/envs/ChatGLM    /opt/workspace/ai/ChatGLM-6B 
OpenChatKit              /home/<USER>/miniconda3/envs/OpenChatKit  /opt/workspace/openchat/OpenChatKit/
gpt2                     /home/<USER>/miniconda3/envs/gpt2    /opt/workspace/gpt2

<<Windows>>
base                     F:\ResearchDirection\AI\miniconda3
alpaca                   F:\ResearchDirection\AI\miniconda3\envs\alpaca   F:\ResearchDirection\AI\stanford_alpaca
visgpt                   F:\ResearchDirection\AI\miniconda3\envs\visgpt   F:\ResearchDirection\AI\visual-chatgpt
NonCanda 
cd /opt/install/ai or F:\ResearchDirection\wsl\ai
FACEGOOD-Audio2Face  Windows
PaddleBoBo     Unix
GPT3VoiceBot   Windows
ERNIE   			 Unix
MockingBird    Unix
cd /opt/workspace/ai  or F:\ResearchDirection\AI
PaddleBoBo Windows
Midjourney Just a place to save graph generated by midj 
stable-diffusion-webui-directml  directml version. Not used
ColossalAI   Unix 
phenaki-pytorch  Unix
Archive Just a place to save old result
go-openai Go-Library
stable-diffusion-webui Windows
gptcli  unix
promptEngieer Just save 技术论文
AI-Kanbo  Unix. Finetuning
autocut   Windows
speech2txt just a test.py 
data traning data . perhaps being used by PaddleBobo
```
Team
```Python
https://teams.microsoft.com/l/meetup-join/19%3akQm1qUI9IFXDFk6j_jMSj_00pZBlyDrjn9cSdZBQYwQ1%40thread.tacv2/1700525154462?context=%7b%22Tid%22%3a%222e890069-92f8-4045-85a4-c0d7628de1bb%22%2c%22Oid%22%3a%22f524ff65-2642-417f-ac73-b16597919eb1%22%7d
```