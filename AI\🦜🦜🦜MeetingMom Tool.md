---
DocFlag:
  - Tested
  - Testing
Updated: ""
tags:
  - AI/Programming
  - AI/Voice
Created: 2023-09-20T00:18
---
Highly Related Document
[[🦜🦜🦜AWS Project for Mom]]

---
旧Conda 环境的构建
Whisper Large 模型Pytorch调用方式
日语LLM的列表
初始对speaker不同声音比对
flask 内存不断增加问题
SSL Certification Setup
SBI Azure ML Install
Server Setup
Jupiter Service environment setup
SBI Azure OpenAI Model List
Use az command for bastion console access
Postgresql DB Setup
DB binary installation
MomDB installation
MomDB table setup
New bits mom development
保护Python Code
MOM_AI service
生成UML图
Improvement Accuracy
keypoint for improvement
possible points we can improve
Issues
github reference
训练微调
语言自动识别逻辑
Prompt
How to Mount one driver to Linux server
Data CleanUp - PDF with images
使用Lux下载视频,然后转音频
Convert MD file to Word
Ollama
Regarding Embedding to RAG
==========================

# Team Mail List
```
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>

https://pmt.sbigenai.com/admin
```
# Backup Reminder
vue version location
```
uat
/opt/app/mom/archive/20250415_vue
/opt/app/mom/model/archive/audiocore_20250415.pyc
/databank/backup <- includes all vue version + 1st react version and some old vue backup side


prd .env
(mom) mlusr@SBIHD-GenAI-dev-LVM-ML01:/opt/app/mom_vue$ cat .env
###############################################
#  Azure in Japan East
###############################################
# AZURE_OPENAI_API_KEY="********************************"
# AZURE_OPENAI_ENDPOINT = "https://sbihd-genai-dev-openai.openai.azure.com/"
# AZURE_DEPLOYMENT="SBIHD-GenAI-dev-OpenAI-gpt35-16k"
# AZURE_DEPLOYMENT_GPT4 = "SBIHD-GenAI-dev-OpenAI-gpt4o"
# # AZURE_DEPLOYMENT_GPT4 = "SBIHD-GenAI-dev-OpenAI-gpt4"
# # OPENAI_API_VERSION="2023-12-01-preview"
# OPENAI_API_VERSION = "2024-05-01-preview" # gpt4o
###############################################
#  Azure in Australia East
###############################################
AZURE_OPENAI_API_KEY="********************************"
AZURE_OPENAI_ENDPOINT = "https://sbihd-genai-dev-openai-gpt4-32k.openai.azure.com/"
AZURE_DEPLOYMENT="SBIHD-GenAI-dev-OpenAI-gpt35-16k"
AZURE_DEPLOYMENT_GPT4 = "SBIHD-GenAI-dev-OpenAI-gpt4-32k"
OPENAI_API_VERSION="2024-02-15-preview"
###############################################
MODEL_TEMPERATURE = 0.4
AUDIO_MODEL_VERSION = 2
WORKER_COUNT = 12
LESS_MEMORY = 0
POSITIVE_THRESHOLD = 0.3
MAX_SPEAKERS = 10
MAX_TOKENS=15000
SLEEP_TIME = 20
NOTIFICATION_SERVER = "http://*********:8281/playbook_runner/exec_trans?playbook_id=ai_mmw_ins_ai_mmw_user_notifications_mom"
NOTIFICATION_TOKEN = "eyJ2IjoxLCJhbGciOiJIUzUxMiJ9.MA.zhC4yrX4Y5hIw-mvh1SYm9sUYOjotEMRWSvCktlQdbIeuce9c6ZA2ZPbGdysR71d-vtIWfLYxKeiJdPCCEaTZQ"
EMBEDDING_URL = "http://127.0.0.1:5000/embed"
DBNAME = "momdb"
DBUSER = "momusr"
DBPWD = "momusr#345"
DBHOST = "********"
DBPORT = "5432"
TRANS_URL = "https://"
PROFANITY_CHECK_FAST = 0
CLAUDE_LLM_MODEL = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
CLAUDE_LLM_REGION = "us-west-2"
CLAUDE_MAX_TOKENS = 131072
IF_CLAUDE_LLM = 1
# 0 for dev environment, 1 for production environment
IF_PROD_ENV = 1
HIDE_DEVICE_SWITCH_LOGS=1
MAX_WORKERS=8
GPU_TASK_LIMIT=4
MODEL_CONCURRENT_LIMIT=2


```
# 旧Conda 环境的构建

```Python

conda create -n mom 
(mom) [raysheng@MONSTER:~]$ python -V
Python 3.10.12
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu117
pip install pyaudio SpeechRecognition
pip install git+https://github.com/openai/whisper.git
pip install python-dotenv
pip install langchain
pip install rich
pip install pyairtable
pip install webrtcvad
pip install 'flask[async]
\#install ffmpeg
https://phoenixnap.com/kb/ffmpeg-windows
\#test sample
https://www.bing.com/videos/riverview/relatedvideo?&q=%e4%bc%9a%e8%ad%b0+%e6%97%a5%e6%9c%ac%e8%aa%9e&&mid=47B3B5AF79CAC16E6BB847B3B5AF79CAC16E6BB8&&FORM=VRDGAR
Topic: Today we talk about how to use business Japanese in meeting
https://www.msn.com/ja-jp/video/news/%E7%B1%B3%E5%9B%BD%E5%8B%99%E9%95%B7%E5%AE%98%E3%81%A8%E4%B8%AD%E5%9B%BD-%E5%9B%BD%E5%AE%B6%E5%89%AF%E4%B8%BB%E5%B8%AD%E3%81%8C%E4%BC%9A%E8%AB%87-%E9%A6%96%E8%84%B3%E4%BC%9A%E8%AB%87%E5%AE%9F%E7%8F%BE%E3%81%B8%E3%81%AE-%E5%9C%B0%E3%81%AA%E3%82%89%E3%81%97-%E3%81%8B/vi-AA1gV21Z
https://www.bing.com/videos/riverview/relatedvideo?&q=%E4%BC%9A%E8%AD%B0%20Video&mid=045ADE09E9079DF0B9B8045ADE09E9079DF0B9B8&ajaxhist=0
Test Sample
https://www.youtube.com/watch?v=tyaKQ_NURqQ
https://www.youtube.com/watch?v=jAh5zVKmRe8
https://www.youtube.com/watch?v=aWpTyWZWOwY
https://github.com/openai/whisper
https://github.com/davabase/whisper_real_time/tree/master
\#airtable
https://airtable.com/app9AmBsPXS7UzmJS/tblBBOqUElDTb1YtS/viwjvB3rFXY7MPzKK?blocks=hide
    documents = []
    for line in transcription:
        content = line
        add_meetingtableindetail(meetingid, line, sentimentstr)
        if content.strip():  # 判断获取的页面内容是否为空，这里先去除了首尾的空白字符
            document = Document(page_content=Line, metadata={'Sentiment': 'meeting mom'})
            documents.append(document)
    return documents
import time
from rich.live import Live
from rich.table import Table
from rich.layout import Layout
layout=Layout()
layout.split_row(
   Layout(name="left"),
   Layout(name="right"), )
print(layout)
I would like to display the below table in the right column but I can't figure out how
table = Table()
table.add_column("Row ID")
table.add_column("Description")
table.add_column("Level")
with Live(table, refresh_per_second=4):  # update 4 times a second to feel fluid
    for row in range(12):
        time.sleep(0.4)  # arbitrary delay
        # update the renderable internally
        table.add_row(f"{row}", f"description {row}", "[red]ERROR")
```

![[mom.py]]
![[mom_web.py]]

```Python

<<Server side >>
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu117
pip install celery
pip install fastapi
pip install uvicorn
pip install python-dotenv
pip install redis
pip install git+https://github.com/m-bain/whisperx.git
https://hf.co/pyannote/speaker-diarization
https://github.com/m-bain/whisperX
https://huggingface.co/pyannote/segmentation
https://huggingface.co/pyannote/speaker-diarization
#!@!!!!!!!!
If you want to install in Jetson environment. Pls look at related to Notion memo
how to install torchaudio and CTranslate2
Japanese Model
\#Explanation
https://note.com/elyza/n/na405acaca130
\#Model download
https://huggingface.co/elyza/ELYZA-japanese-Llama-2-7b
https://huggingface.co/elyza/ELYZA-japanese-Llama-2-7b-instruct
https://huggingface.co/elyza/ELYZA-japanese-Llama-2-7b-fast
https://huggingface.co/elyza/ELYZA-japanese-Llama-2-7b-fast-instruct

ValueError: Tokenizer class LlamaTokenizer does not exist or is not currently imported.
For now to resolve this error, need to manually update the tokenizer_class to "LlamaTokenizer" in tokenizer_config.json.
You must install 
pip install transformers==4.33.2
/opt/workspace/miniconda3/envs/mlearn/lib/python3.8/site-packages/transformers/models/auto/tokenization_auto.py
\#Explanation
https://zenn.dev/elyza/articles/5e7d9373c32a98
### 指示:
与えられた文脈から、質問に対する答えを抜き出してください。
### 入力:
文脈：2006年秋からチャルマースでは建築家としても土木技師としてもダブルディグリーの機会を提供する新しい教育制度がある。このカリキュラムは建築と技術という名称で、300から360クレジットである。
質問：2006年秋からチャルマースでは建築家としても土木技師としてもダブルディグリーの機会を提供する新しい制度がある。何の制度か？
### 応答:
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
B_INST, E_INST = "[INST]", "[/INST]"
B_SYS, E_SYS = "<<SYS>>\n", "\n<</SYS>>\n\n"
DEFAULT_SYSTEM_PROMPT = "あなたは誠実で優秀な日本人のアシスタントです。"
text = "クマが海辺に行ってアザラシと友達になり、最終的には家に帰るというプロットの短編小説を書いてください。"
model_name = "elyza/ELYZA-japanese-Llama-2-7b-instruct"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name, torch_dtype="auto")
if torch.cuda.is_available():
    model = model.to("cuda")
prompt = "{bos_token}{b_inst} {system}{prompt} {e_inst} ".format(
    bos_token=tokenizer.bos_token,
    b_inst=B_INST,
    system=f"{B_SYS}{DEFAULT_SYSTEM_PROMPT}{E_SYS}",
    prompt=text,
    e_inst=E_INST,
)

with torch.no_grad():
    token_ids = tokenizer.encode(prompt, add_special_tokens=False, return_tensors="pt")
    output_ids = model.generate(
        token_ids.to(model.device),
        max_new_tokens=256,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=tokenizer.eos_token_id,
    )
output = tokenizer.decode(output_ids.tolist()[0][token_ids.size(1) :], skip_special_tokens=True)
print(output)
"""
承知しました。以下にクマが海辺に行ってアザラシと友達になり、最終的には家に帰るというプロットの短編小説を記述します。
クマは山の中でゆっくりと眠っていた。
その眠りに落ちたクマは、夢の中で海辺を歩いていた。
そこにはアザラシがいた。
クマはアザラシに話しかける。
「おはよう」とクマが言うと、アザラシは驚いたように顔を上げた。
「あ、こんにちは」アザラシは答えた。
クマはアザラシと友達になりたいと思う。
「私はクマと申します。」クマは...
"""

# fix llama return non standard json
pip install ujson
import ujson
data_str = '{\n    "Sentiment": "positive",\n    "Translate": "You are an expert who will judge people sentiment based on the blew content and \n    also tranlate the below content into English if it is not in English."\n}'
try:
    data_dict = ujson.loads(data_str)
    print("转换成功，结果如下：")
    print(data_dict)
except Exception as e:
    print(f"转换失败，错误信息：{e}")
```

Whisper Large 模型Pytorch调用方式

```Python
import torch
from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline
from datasets import load_dataset

device = "cuda:0" if torch.cuda.is_available() else "cpu"
torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32
model_id = "openai/whisper-large-v3"
model = AutoModelForSpeechSeq2Seq.from_pretrained(
    model_id, torch_dtype=torch_dtype, low_cpu_mem_usage=True, use_safetensors=True, use_flash_attention_2=True
)
model.to(device)
processor = AutoProcessor.from_pretrained(model_id)
pipe = pipeline(
    "automatic-speech-recognition",
    model=model,
    tokenizer=processor.tokenizer,
    feature_extractor=processor.feature_extractor,
    max_new_tokens=128,
    chunk_length_s=30,
    batch_size=16,
    return_timestamps=True,
    torch_dtype=torch_dtype,
    device=device,
)
result = pipe("土星猫文明.wav", generate_kwargs={"language": "japanese"})
print(result["text"])
```

日语LLM的列表
|   |   |   |   |   |   |
|---|---|---|---|---|---|
|モデル|スコア|開発元|パラメータ数|Open / Closed|商用利用|
|GPT-4|4.32|OpenAl|不明|Closed|-|
|GPT-3.5-turbo|3.81|OpenAl|不明|Closed|-|
|PaLM 2 (Bison)|3.30|Google|不明|Closed|-|
|GPT-3.5 (text-davinci-003)|2.76|OpenAl|175B|Closed|-|
|ELYZA-japanese-Llama-2-7b-instruct|2.61|ELYZA|7B|Open|$\bigcirc$|
|ELYZA-japanese-Llama-2-7b-fast-instruct|2.59|ELYZA|7B|Open|$\bigcirc$|
|line-japanese-large-Im-3.6b-instruction-sft|2.06|LINE|3.6B|Open|$\bigcirc$|
|stabilityai-japanese-stablelm-instruct-alpha-7b|1.92|Stability AI|7B|Open|$x$|
|matsuo-lab-weblab-10b-instruction-sft|1.87|東大松尾研|$10 \mathrm{~B}$|Open|$x$|
|rinna-japanese-gpt-neox-3.6b-instruction-ppo|1.67|rinna|3.6B|Open|$\bigcirc$|
|rinna-bilingual-gpt-neox-4b-instruction-ppo|1.57|rinna|4B|Open|0|
|   |   |   |   |   |   |   |   |   |
|---|---|---|---|---|---|---|---|---|
|モデル|Jcommon-senseqa|jnli|marc_ja|jsquad|jaqket_v2|xIsum_ja|xwinograd_ja|mgsm|
|ELYZA-japanese-Llama-2-7b-instruct|65.15|57.27|91.51|67.38|58.51|5.02|70.07|10.0|
|ELYZA-japanese-Llama-2-7b-fast-instruct|70.87|36.32|92.73|68.82|62.29|3.35|59.85|9.2|
|ELYZA-japanese-Llama-2-7b|75.60|50.66|87.57|71.43|58.85|4.16|71.85|7.6|
|ELYZA-japanese-Llama-2-7b-fast|71.49|45.69|86.59|70.85|64.26|2.51|60.90|8.0|
|Llama-2-7b-chat|55.59|29.54|90.41|59.34|17.96|2.34|66.11|9.2|
|$\leftarrow$ matsuo-lab-weblab-10b-instruction-sft|74.62|66.56|95.49|78.34|63.32|20.57|71.95|$z 0$|
|stabilityai-japanese-stablelm-instruct-alpha-7b|82.22|52.05|82.88|63.26|74.83|7.79|72.68|2.0|
|rinna-bilingual-gpt-neox-4b-instruction-ppo|48.79|48.23|96.09|54.16|57.65|5.03|65.07|2.4|
|rinna-japanese-gpt-neox-3.6b-instruction-ppo|44.06|54.19|89.61|51.62|50.95|6.63|69.13|4.4|
|line-japanese-large-Im-3.6b-instruction-sft|33.60|42.56|55.09|44.37|39.86|6.57|64.65|.4|
-----------------------------------------------------------------------------------------

# 初始对speaker不同声音比对

```JavaScript
# improve and try to identify speaker
pip install scikit-learn
pip install librosa
pip install fastdtw
from skimage.transform import resize
resized_mel_spectrogram = resize(mel1, (369, 396))
from scipy.spatial.distance import cityblock as manhattan
from scipy.spatial.distance import chebyshev
from scipy.spatial.distance import cosine
from scipy.spatial.distance import hamming
from scipy.spatial.distance import mahalanobis
from scipy.spatial.distance import jaccard
mels = [mel1, mel2, mel3, mel4, mel5, mel6, mel7, mel8, mel9]
n = len(mels)
distance_matrix = np.zeros((n, n))
for i in range(n):
    for j in range(i, n):
        # distance, _ = fastdtw(mels[i], mels[j], dist=euclidean)
        distance, _ = fastdtw(mels[i], mels[j], dist=euclidean)
        distance_matrix[i, j] = distance
        distance_matrix[j, i] = distance  # 矩阵是对称的
plt.imshow(distance_matrix, cmap='hot', interpolation='nearest')
plt.colorbar(label='DTW Distance')
plt.title("DTW Distance Matrix")
plt.xticks(np.arange(n), [f"mel{i+1}" for i in range(n)])
plt.yticks(np.arange(n), [f"mel{i+1}" for i in range(n)])
plt.show()
```

```JavaScript
https://stackoverflow.com/questions/73901352/how-might-i-persuade-kombu-to-use-simplejson-instead-of-the-json-module
https://stackoverflow.com/questions/56200907/can-i-control-the-start-finish-time-when-i-use-speech-recognition-in-python#:~:text=%23%20Create%20the%20recognizer_instance%3A%20r%20%3D%20sr.Recognizer%20%28%29,print%20%28%22Say%20something%21%22%29%20audio%20%3D%20r.listen%20%28source%3Dsource%2C%20timeout%3D5%29
sr.Recognizer()用于初始化语音识别对象。
energy_threshold用于设置声音的能量阈值。
pause_threshold用于设置语句之间的暂停时间阈值。
non_speaking_duration用于设置非语音时长。
phrase_threshold用于设置识别短语的最低时间。
assert self.pause_threshold >= self.non_speaking_duration >= 0
recorder = sr.Recognizer()
    recorder.energy_threshold = args.energy_threshold
    recorder.pause_threshold = 0.4
    recorder.non_speaking_duration = 0.3
    recorder.phrase_threshold = 3
```

# flask 内存不断增加问题

[https://www.v2ex.com/t/977017](https://www.v2ex.com/t/977017)

```JavaScript
import random
import string
def generate_api_key(length=32):
    letters_and_digits = string.ascii_letters + string.digits
    api_key = ''.join(random.choice(letters_and_digits) for i in range(length))
    return api_key
print(generate_api_key())

pip install fastapi uvicorn
from fastapi import FastAPI, Header, HTTPException
app = FastAPI()
# 假设这是有效的API Key
VALID_API_KEY = "your_valid_api_key_here"
@app.get("/api/resource/")
async def read_item(authorization: str = Header(None)):
    if authorization is None or authorization != VALID_API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API Key")
    return {"data": "Hello, you have access!"}

```

# SSL Certification Setup

```JavaScript
How to limit the max number of threads with sync endpoints?
https://github.com/tiangolo/fastapi/issues/4221\#issuecomment-982260467
https://pypi.org/project/fastapi-limiter/
https://juejin.cn/post/7098906924191580196
https://stackoverflow.com/questions/73195338/how-to-avoid-database-connection-pool-from-being-exhausted-when-using-fastapi-in
https://zhuanlan.zhihu.com/p/560472159
from anyio.lowlevel import RunVar
from anyio import CapacityLimiter
app = FastAPI()
@app.on_event("startup")
def startup():
    print("start")
    RunVar("_default_thread_limiter").set(CapacityLimiter(2))

pip install fastapi-limiter
raise Exception("You must call FastAPILimiter.init in startup event of fastapi!")
Exception: You must call FastAPILimiter.init in startup event of fastapi!

pip install slowapi
Exception: parameter `request` must be an instance of starlette.requests.Request
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
Limiter = Limiter(key_func=get_remote_address)
app = FastAPI(lifespan=lifespan)
app.state.limiter = Limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
@app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
@Limiter.limit("10/second")
async def create_chat_completion(request: ChatCompletionRequest):
# Add Https support
------------------------------------------------------------------------------------------------
https://dev.to/rajshirolkar/fastapi-over-https-for-development-on-windows-2p7d
https://github.com/FiloSottile/mkcert
sudo yum install nss-tools
or
sudo apt install libnss3-tools
git clone https://github.com/FiloSottile/mkcert && cd mkcert
go build -ldflags "-X main.Version=$(git describe --tags)"
mkcert -install
mkcert ************ ************ ::1
mkcert ************** ************** ::1
Created a new certificate valid for the following names 📜
 - "************"
 - "************"
 - "::1"
The certificate is at "./************+2.pem" and the key at "./************+2-key.pem" ✅
It will expire on 30 December 2025 🗓
mv ************+2.pem cert.pem
mv ************+2-key.pem key.pem
uvicorn.run(app, host=args.server_name, port=args.server_port, workers=1,
                ssl_keyfile="/opt/workspace/mom/privkey.pem", ssl_certfile="/opt/workspace/mom/fullchain.pem"
            )
python openai_api.py -c /opt/workspace/aibase/Qwen-14B-Chat-Int4 --server-port 8002 --server-name 0.0.0.0
Then you can install ChatGPT - Genie AI extension in VS Code
#对于自己是server，然后访问，会出现如下error
request to https://127.0.0.1:8002/v1/chat/completions failed, reason: unable to verify the first certificate
openssl s_client -connect xxx.xxx.xxx.xxx:443 -CAfile /etc/pki/CA/cacert.pem
openssl s_client -connect ************:8002 -CAfile /opt/workspace/mom/fullchain.pem
export NODE_TLS_REJECT_UNAUTHORIZED=0
https://stackoverflow.com/questions/68002512/ssl-certificate-generated-by-mkcert-is-not-a-full-chain
mkcert -CAROOT
export NODE_EXTRA_CA_CERTS="$(mkcert -CAROOT)/rootCA.pem"
# Solution !!!
@@@under centos/redhat
mkcert -CAROOT
cd /usr/share/pki/ca-trust-source/anchors
sudo cp -rp /home/<USER>/.local/share/mkcert/rootCA.pem raysheng-root-cacert.crt
sudo update-ca-trust
# confirm
trust list
https://medium.com/@superseb/get-your-certificate-chain-right-4b117a9c0fce
"genieai.openai.apiBaseUrl": "https://************:8002",
    "http.proxyStrictSSL": false
@@@under ubunto
# cd /usr/share/ca-certificates
# mkdir mylocal
# sudo cp -rp /home/<USER>/.local/share/mkcert/rootCA.pem local/jethome-root-ca.pem
# cp -p /etc/ca-certificates.conf /etc/ca-certificates.conf.bak
# echo "mylocal/mylocal-root-cacert.crt" >> /etc/ca-certificates.conf
# update-ca-certificates
# ls -l /etc/ssl/certs/ | grep mylocal-root-cacert
NOW WE ARE GOING TO SOLVE CAN CALL IT FROM LANGCHAIN
https://qiita.com/msi/items/9cb90271836386dafce3    !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Retrying langchain.chat_models.openai.ChatOpenAI.completion_with_retry.<locals>._completion_with_retry in 4.0 seconds as it raised APIConnectionError: 
Error communicating with OpenAI: HTTPSConnectionPool(host='************', port=8002):
 Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)'))).
\#Open browser
https://************:8002/v1/completions
cancel and in browser find/export certfication like below 
https://qiita.com/pm00/items/9983bac9f188e73cda20
\#test
openssl s_client -connect ************:8002 -servername ************

<<  SERVER SIDE >>
mkcert -CAROOT
cd /usr/share/pki/ca-trust-source/anchors
sudo cp -rp /home/<USER>/.local/share/mkcert/rootCA.pem raysheng-root-cacert.crt
sudo update-ca-trust
$ python -c "import certifi; print(certifi.where())"
/etc/ssl/certs/ca-certificates.crt
G:\ResearchDirection\AI\miniconda3\envs\mom\lib\site-packages\certifi\cacert.pem
/home/<USER>/miniconda3/envs/mom/lib/python3.10/site-packages/certifi/cacert.pem
$ python -c "import requests;print(requests.__version__)"
2.22.0
$ python -c "import requests; print(requests.certs.where())"
/etc/ssl/certs/ca-certificates.crt
G:\ResearchDirection\AI\miniconda3\envs\mom\lib\site-packages\certifi\cacert.pem
$ python -c "import ssl; print(ssl.get_default_verify_paths())"; 
(set -x; ls -l /usr/lib/ssl)
DefaultVerifyPaths(cafile=None, capath='/usr/lib/ssl/certs', openssl_cafile_env='SSL_CERT_FILE', openssl_cafile='/usr/lib/ssl/cert.pem', openssl_capath_env='SSL_CERT_DIR', openssl_capath='/usr/lib/ssl/certs')
/home/<USER>/miniconda3/envs/mom/ssl/cert.pem
(mom) [raysheng@MONSTER:/opt/workspace/mom]$ ls -lrt /home/<USER>/miniconda3/envs/mom/ssl/
total 264
-rw-rw-r-- 4 <USER> <GROUP>  12324 Aug 25 03:20 openssl.cnf.dist
-rw-rw-r-- 4 <USER> <GROUP>  12324 Aug 25 03:20 openssl.cnf
-rw-rw-r-- 4 <USER> <GROUP>    412 Aug 25 03:20 ct_log_list.cnf.dist
-rw-rw-r-- 4 <USER> <GROUP>    412 Aug 25 03:20 ct_log_list.cnf
-rw-r--r-- 4 <USER> <GROUP> 221470 Sep  6 23:16 cacert.pem
lrwxrwxrwx 1 raysheng raysheng     10 Sep 20 00:36 cert.pem -> cacert.pem
drwxr-xr-x 2 <USER> <GROUP>   4096 Sep 20 00:36 misc
(mom) [raysheng@MONSTER:/opt/workspace/mom]$ cd /home/<USER>/miniconda3/envs/mom/ssl/
(mom) [raysheng@MONSTER:~/miniconda3/envs/mom/ssl]$ rm -f cert.pem
(mom) [raysheng@MONSTER:~/miniconda3/envs/mom/ssl]$ ln -s /home/<USER>/miniconda3/envs/mom/lib/python3.10/site-packages/certifi/cacert.pem cert.pem
(mom) [raysheng@MONSTER:~/miniconda3/envs/mom/ssl]$ ls -lrt /home/<USER>/miniconda3/envs/mom/ssl/
total 268
-rw-rw-r-- 4 <USER> <GROUP>  12324 Aug 25 03:20 openssl.cnf.dist
-rw-rw-r-- 4 <USER> <GROUP>  12324 Aug 25 03:20 openssl.cnf
-rw-rw-r-- 4 <USER> <GROUP>    412 Aug 25 03:20 ct_log_list.cnf.dist
-rw-rw-r-- 4 <USER> <GROUP>    412 Aug 25 03:20 ct_log_list.cnf
-rw-r--r-- 4 <USER> <GROUP> 221470 Sep  6 23:16 cacert.pem
drwxr-xr-x 2 <USER> <GROUP>   4096 Sep 20 00:36 misc
lrwxrwxrwx 1 raysheng raysheng     82 Sep 30 19:32 cert.pem -> /home/<USER>/miniconda3/envs/mom/lib/python3.10/site-packages/certifi/cacert.pem
@@ubuntu
cat /usr/share/ca-certificates/local/jethome-root-ca.pem >> /home/<USER>/.local/lib/python3.8/site-packages/certifi-2023.7.22-py3.8.egg/certifi/cacert.pem
cd /opt/workspace/miniconda3/envs/mom/ssl/
cat /usr/share/ca-certificates/local/jethome-root-ca.pem >> cacert.pem

<< CLIENT SIDE>>
1. Download and import raysheng-root-ca ONLY into windows via certmgt.msc
2. add mom virutal environment ssl cacert.pm. raysheng-root-ca only
python -c "import ssl; print(ssl.get_default_verify_paths())"; 
DefaultVerifyPaths(cafile='G:\\ResearchDirection\\AI\\miniconda3\\Library\\ssl\\cacert.pem', capath=None, openssl_cafile_env='SSL_CERT_FILE', openssl_cafile='C:\\Program Files\\Common Files\\ssl/cert.pem', openssl_capath_env='SSL_CERT_DIR', openssl_capath='C:\\Program Files\\Common Files\\ssl/certs')
# Get the cert.pm path from above
G:\\ResearchDirection\\AI\\miniconda3\\Library\\ssl\\cacert.pem
add  raysheng-root-cacert.crt (openai-qwen.crt is not required)
3. C:\Users\<USER>\miniconda3\envs\mom\lib\site-packages\certifi\cacert.pem
python -c "import certifi; print(certifi.where())"
Add raysheng-root-ca only is enough
\#test
Test looks you only can test from python
openssl s_client -connect ************:8002 -servername ************
openssl s_client -connect **************:8002 -servername **************
Verify return code: 0 (ok)
# below is not required , remove it
# REQUESTS_CA_BUNDLE = "G:\ResearchDirection\AI\mom\openai-qwen.crt"
OPENAI_API_BASE = "https://************:8002/v1"
OPENAI_API_KEY="none"

In short.
Server side. rootca.pem need to add into os. python ssl and certif package
             start application with app cert and key pair genrated by mkcert
Client side. need to registe rootca.pem into certmgt.msc and add the same into 
certif and ssl cert.pem file
\#2024/3/6
后来发现，好像client段必须把raysheng-root-ca加到对应的python 下的certifi下面的cacert.pem文件里
/home/<USER>/miniconda3/envs/chatchat/lib/python3.11/site-packages/certifi

\#How to add official SSL cert in Azure
https://learn.microsoft.com/en-us/azure/app-service/configure-ssl-app-service-certificate?tabs=portal\#buy-and-configure-an-app-service-certificate
```

# SBI Azure ML Install

## Server Setup

```LaTeX
https://www.cherryservers.com/blog/install-cuda-ubuntu
https://developer.nvidia.com/cuda-downloads?target_os=Linux&target_arch=x86_64&Distribution=Ubuntu&target_version=22.04&target_type=deb_network
https://learn.microsoft.com/en-us/azure/virtual-machines/linux/n-series-driver-setup

https://qiita.com/kenmaro/items/3c9b9056d31af01e6f0d
https://gist.github.com/denguir/b21aa66ae7fb1089655dd9de8351a202
azusr@SBIHD-GenAI-dev-LVM-ML01:~$ lspci|grep -i nvidia
0001:00:00.0 3D controller: NVIDIA Corporation GV100GL [Tesla V100 PCIe 16GB] (rev a1)
azusr@SBIHD-GenAI-dev-LVM-ML01:~$ dpkg -l|grep nvidia
azusr@SBIHD-GenAI-dev-LVM-ML01:~$ dpkg -l|grep cuda
sudo apt update
sudo apt upgrade
sudo apt install ubuntu-drivers-common
sudo ubuntu-drivers install
sudo ubuntu-drivers devices
driver   : nvidia-driver-535 - distro non-free recommended
sudo apt install nvidia-driver-535
sudo reboot now
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.1-1_all.deb
sudo apt install -y ./cuda-keyring_1.1-1_all.deb
sudo apt update
sudo apt -y install cuda-toolkit-12-3
sudo apt install -y ubuntu-drivers-common
sudo ubuntu-drivers install
reboot
nvidia-smi
sudo apt-get install -y nvidia-kernel-open-545
sudo apt-get install -y cuda-drivers-545
nvidia-smi
sudo apt update
sudo apt full-upgrade
SBIHD-GenAI-dev-LVM-ML01
sudo groupadd -g 1001 devgroup
sudo useradd -u 1001 -g 1001 -m devusr
id devusr
sudo vi sudoers.d/01_devusr
# User rules for azusr
devusr ALL=(ALL) NOPASSWD:ALL
useid: azusr
passwd: azusr\#123wsx

\#cudnn install
https://developer.nvidia.com/cudnn
https://developer.nvidia.com/cudnn-downloads?target_os=Linux&target_arch=x86_64&Distribution=Ubuntu&target_version=22.04&target_type=deb_network
sudo apt-get -y install cudnn

\#environment path
export PATH="/usr/local/cuda/bin:$PATH"
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"

symdev -sid 0308 -sg SG_DB0102 list -wwn
sudo sh -c "echo 1 > /sys/block/sdba/device/rescan"
sudo sh -c "echo 1 > /sys/block/sdbd/device/rescan"
sudo multipathd -k
show maps
resize map TOGENS01-test
sudo pvs -o+dev_size
sudo pvresize /dev/mapper/360000970000497900098533030303343
sudo lvextend -l+100%FREE redovg/redolv
sudo xfs_growfs /DB0102
Node1
sudo pvs 
  /dev/emcpowerd POGENS21-archvg  lvm2 a--    1.46t      0
sudo pvs -o+pv_used,dev_size
sudo sfdisk -R /dev/emcpowerd
sudo pvs -o+pv_used,dev_size
!!Do in node2
sudo pvs 
  /dev/emcpowerd POGENS21-archvg  lvm2 a--    1.46t      0
sudo pvs -o+pv_used,dev_size
sudo sfdisk -R /dev/emcpowerd
sudo pvs -o+pv_used,dev_size

!!Do in node where disk mounted
sudo pvresize /dev/emcpowerd
sudo pvs -o+pv_used,dev_size
sudo lvextend -l+100%FREE redovg/redolv
sudo xfs_growfs /DB0102
sudo yum install sg3_utils -y
rescan-scsi-bus.sh
https://qiita.com/yushikmr/items/c3bddc1e21d19a848a19
\#disable auto mount
sudo systemd-mount --unmount /databank/
```

## Jupiter Service environment setup

```JavaScript
sudo useradd -u 1002 -g 1001 -m jupusr
./Miniconda3-latest-Linux-x86_64.sh
sudo mkdir /databank/env/jupiter
./Miniconda3-latest-Linux-x86_64.sh  -u
conda create -n jupiter python=3.11.7
conda activate jupiter
pip install jupyterlab
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
jupyter lab --generate-config
(jupiter) jupusr@SBIHD-GenAI-dev-LVM-ML01:~/.local$ cat start.sh
conda init
conda activate jupiter
jupyter lab --ip ********* --port 8888 --NotebookApp.token='' --NotebookApp.password='' &
\#create jupyter as service
https://techoverflow.net/2021/06/11/how-to-run-jupyter-lab-as-systemd-service/
azusr@SBIHD-GenAI-dev-LVM-ML01:/usr/lib/systemd/system$ cat jupyterlab.service
[Unit]
Description=Jupyter Lab server
After=network.target
[Service]
Type=simple
ExecStart=/usr/bin/env /databank/env/jupiter/envs/jupiter/bin/jupyter lab --ip ********* --port 8888 --NotebookApp.token='' --NotebookApp.password=''
WorkingDirectory=/databank/env/jupiter/home
User=jupusr
Group=devgroup
Restart=on-failure
RestartSec=5s
[Install]
WantedBy=multi-user.target
https://milestone-of-se.nesuke.com/sv-basic/linux-basic/arp-ip-neighbour/
\#failover vip
1, remove addition ip set from vm
2. create load balancer SBIHD-GenAI-dev-LVM-MLVIP
a. create frontend ip SBIHD-GenAI-dev-LVM-MLFIP *********
b. create SBIHD-GENAI-dev-MLPool (pointing to 2 ML server IP)
c. create probe SBIHD-GENAI-dev-LVM-ML-loadbalance-pg-probe  TCP:5432
b. create loadbalance rule SBIHD-GENAI-dev-LVM-ML-loadbalance-rule
Enable High availablity ports | session perssistence: clientip and protocol| Disable Floating IP

Loadbalancer -> Frontend IP(public ip) -> loadbalance rule(443:443) -> Bandpool(2 vm) and Health probe (clientip and protocol session persistence
   and enable outbound rules to provide backend pool members to access internet)   -> 
DONT FORGET YOU NEED TO ADD BELOW FIREWALL RULE
```

![[Notion/AI/🦜🦜🦜MeetingMom Tool/attachments/Untitled.png|Untitled.png]]

```JavaScript
if load balanace is for internel service, you need to specify load balance rule, above firewall rule is not required. 
But 自己是访问不了loadbalance ip的, 需要从其他server那边测试结果
```

SBI Azure OpenAI Model List

```LaTeX
SBIHD-GenAI-dev-OpenAI
SBIHD-GenAI-dev-OpenAI-gpt4
SBIHD-GenAI-dev-OpenAI-emb
SBIHD-GenAI-dev-OpenAI-gpt35
SBIHD-GenAI-dev-OpenAI-gpt35-16k
Project-SBIHD-GenAI-dev-OpenAI
# Found gpt4-32k not existing in base model list
# 不能从旧的系统里加了
goto ai.azure.com  => azure ai studio
https://ai.azure.com/build/deployments?wsid=/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/sbihd-genai-dev-rg/providers/Microsoft.MachineLearningServices/workspaces/project-sbihd-genai-dev-openai&tid=2e890069-92f8-4045-85a4-c0d7628de1bb
AI Project (Project-SBIHD-GenAI-dev-OpenAI) -> deployment -> Create
the reason is it is not available in Japan East. Only available in AustraliaEast/France/Sweden
So I create in the location and when create you have to assign new network but from orginal network not able to access
you need to add this original network to the new openai resource.
```

![[stamp2.login.microsoftonline.zip]]

```LaTeX
#绿色安装python
1. https://www.python.org/downloads/
2. unzip and download https://bootstrap.pypa.io/get-pip.py
3. modify determine_pip_install_arguments
   return ["install", "--upgrade", "--force-reinstall"] + args
     ||
		 V
   return ["install", "--upgrade", "--force-reinstall", "--trusted-host=pypi.python.org --trusted-host=pypi.org --trusted-host=files.pythonhosted.org"] + args
3. open cmd and enter folder
python get-pip.py
4. setup PATH
setx PATH "xxxx\python;xxxx\python\Scripts;%PATH%"
SETx PIP_DEFAULT_TIMEOUT 1000
setx AZURE_CLI_DISABLE_CONNECTION_VERIFICATION 1
setx ADAL_PYTHON_SSL_NO_VERIFY 1
setx REQUESTS_CA_BUNDLE C:\work\tools\python\Lib\site-packages\certifi
python -c "import requests;r=requests.get('https://www.google.com');print(r)"
\#access below and download certification
https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration
append this cert into above pem file
5. Setup library path
append below into pythonXX._pth
Lib\site-packages
6. update trusted-host
https://bobbyhadz.com/blog/python-connection-error-ssl-certificate-verify-failed
pip config set global.trusted-host "pypi.python.org pypi.org files.pythonhosted.org" --trusted-host=pypi.python.org --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
create pip.ini like below and put under xxxx\python
pip config -v list <= check if pip can locate your pip.ini file
7. pywintypes issue
same as step 5
add blew path into _pth file
Lib\site-packages\win32\lib
Lib\site-packages\win32
8. disable requests package using SSL
backup Lib\site-packages\requests\sessions.py
add this line blew line 777
verify = False
\#install az cli without admin
https://github.com/Azure/azure-cli/issues/15381
python -m pip install --upgrade pip
pip install azure-cli
```

## Use az command for bastion console access

```Python
\#open multi monitor
https://techeng-study.com/bastion-rdp-disable-multimonitor/
https://learn.microsoft.com/zh-cn/cli/azure/
https://jeffbrown.tech/azure-bastion-rdp-native-client/
\#az command reference
https://learn.microsoft.com/zh-cn/cli/azure/reference-index?view=azure-cli-latest
az config set core.allow_broker=true
az account clear
az login
az account show
AOAI_SBIHD-GenAI_Minutes_dev
"user": {
      "name": "<EMAIL>",
      "type": "user"
    }
az resource list
{
    "changedTime": "2024-02-13T07:40:17.215956+00:00",
    "createdTime": "2024-02-13T07:27:22.218162+00:00",
    "extendedLocation": null,
    "id": "/subscriptions/72c6de5d-6db7-4780-89e1-3cd938a56c1f/resourceGroups/poctest/providers/Microsoft.Compute/virtualMachines/RWindows01",
    "identity": null,
    "kind": null,
    "location": "eastus",
    "managedBy": null,
    "name": "RWindows01",
    "plan": null,
    "properties": null,
    "provisioningState": "Succeeded",
    "resourceGroup": "poctest",
    "sku": null,
    "tags": null,
    "type": "Microsoft.Compute/virtualMachines"
  },
az account set --subscription AOAI_SBIHD-GenAI_Minutes_dev
az extension add --name bastion
az extension add --name ssh
az group list
\#all kinds of usage 
https://qiita.com/Isato-Hiyama/items/106504a2ed49a74d990f
az network bastion rdp --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "SBIHD-GenAI-dev-WVM-shray" --configure
Bastion Host SKU must be Standard and Native Client must be enabled.
az network bastion rdp --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/SBIHD-GenAI-dev-RG/providers/Microsoft.Compute/virtualMachines/SBIHD-GenAI-dev-WVM-shray" --configure
\#connect with rdp/ssh
az network bastion rdp --name "RWindows01-vnet-bastion" --resource-group "poctest" --target-resource-id "/subscriptions/72c6de5d-6db7-4780-89e1-3cd938a56c1f/resourceGroups/poctest/providers/Microsoft.Compute/virtualMachines/RWindows01" --configure
az network bastion ssh --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/SBIHD-GENAI-DEV-RG/providers/Microsoft.Compute/virtualMachines/SBIHD-GenAI-dev-LVM-ML01" --auth-type password --user azusr
az network bastion ssh --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/SBIHD-GENAI-DEV-RG/providers/Microsoft.Compute/virtualMachines/SBIHD-GenAI-dev-LVM-ML02" --auth-type password --user azusr
\#create tunnel
az network bastion tunnel --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/SBIHD-GENAI-DEV-RG/providers/Microsoft.Compute/virtualMachines/SBIHD-GenAI-dev-LVM-ML01" --resource-port "22" --port "60001"
az network bastion tunnel --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/SBIHD-GENAI-DEV-RG/providers/Microsoft.Compute/virtualMachines/SBIHD-GenAI-dev-LVM-ML02" --resource-port "22" --port "60002"
az network bastion tunnel --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/SBIHD-GENAI-DEV-RG/providers/Microsoft.Compute/virtualMachines/SBIHD-GenAI-dev-LVM-ML01" --resource-port "22" --port "60003"
az network bastion tunnel --name "SBIHD-GenAI-dev-VNW-bastion" --resource-group "SBIHD-GenAI-dev-RG" --target-resource-id "/subscriptions/4ddddfa3-31bd-493f-ba9b-45e243e5d2dd/resourceGroups/SBIHD-GENAI-DEV-RG/providers/Microsoft.Compute/virtualMachines/SBIHD-GenAI-dev-LVM-ML02" --resource-port "22" --port "60004"
netstat -ano|grep LIST|grep 6000
https://bst-2b8ef494-c7a3-48ad-812f-59659602967b.bastion.azure.com/api/shareable-url/a6adf629-b1a3-459d-9f57-e1b4a3eef52f

shray
qapl1209ws\#123
```
## SMTP Relay server setup
```
<<server side>>
sudo apt install mailutils
# select interet suite
sudo apt install postfix

# we use app password for google
https://myaccount.google.com/apppasswords

vi /etc/postfix/sasl_passwd
[smtp.gmail.com]:587    <EMAIL>:ddfy ipoq tnwd uswr

chmod 600 /usr/local/etc/postfix/sasl_passwd

postmap /etc/postfix/sasl_passwd

#setup /etc/postfix/main.cf
mynetworks = *********/8 10.0.0.0/24
smtpd_relay_restrictions = permit_mynetworks reject_unauth_destination
myhostname = SBIHD-GenAI-dev-LVM-ML01.udt030gljfhulfmdpodvtjppvd.lx.internal.cloudapp.net
relayhost = [smtp.gmail.com]:587
smtp_use_tls = yes
smtp_sasl_auth_enable = yes
smtp_sasl_security_options =
smtp_sasl_password_maps = hash:/etc/postfix/sasl_passwd
smtp_tls_CAfile = /etc/ssl/certs/ca-certificates.crt


systemctl restart postfix.service

echo -e "Just for test" | mail -s "Test email" -r "<EMAIL>" "<EMAIL>"

sendmail -f <EMAIL> <EMAIL> <EMAIL> <EMAIL> << EOF
Subject: Test email sent from Azure SMTP Relay service
To: <EMAIL>
Cc: <EMAIL>, <EMAIL>

$(cat test.txt)
EOF

sendmail -f <EMAIL> <EMAIL> << EOF
Subject: Test email sent from Azure SMTP Relay service
To: <EMAIL>
Cc: <EMAIL>, <EMAIL>

$(cat test.txt)
EOF

<<client side>>
sudo nano /etc/postfix/main.cf
relayhost = [your_relay_server_ip]:25
sudo systemctl restart postfix

```
### replace sender info to customize one and make sure it put into queue
```
First create the header checks file:

sudo nano /etc/postfix/smtp_header_checks
Add this line:
/^From:.*/ REPLACE From: SBI GenAI Mom System <<EMAIL>>

Create the db file:

sudo postmap -r /etc/postfix/smtp_header_checks

Set proper permissions:

sudo chmod 644 /etc/postfix/smtp_header_checks
sudo chmod 644 /etc/postfix/smtp_header_checks.db

Edit main.cf:

sudo nano /etc/postfix/main.cf
Make sure these lines are there:
sender_canonical_maps = static:<EMAIL>
smtp_header_checks = regexp:/etc/postfix/smtp_header_checks

Restart Postfix:

sudo systemctl restart postfix

Test sending:

echo "This is a test email" | mail -s "Test Subject" <EMAIL>
The key was creating both the smtp_header_checks file and its compiled .db version. The error occurred because the file didn't exist. Would you like me to explain what each of these configuration lines does? CopyRetryRSThx,. Just in case, How to setup our relay server, if not able to send email. Pls put into queue but not remove?EditTo configure Postfix to queue emails instead of removing them when delivery fails, you'll need to modify the queue settings. Here's how to set it up:

Edit the Postfix Configuration:

sudo nano /etc/postfix/main.cf
Add or modify these settings:
# Maximum time before giving up and bouncing the message
maximal_queue_lifetime = 5d        # Keep messages in queue for 5 days

# Maximum time for active delivery attempts
maximal_backoff_time = 3h         # Maximum time between retries
minimal_backoff_time = 300s       # Minimum time between retries
queue_run_delay = 300s            # How often to try delivering queued mail

# Never delete queued messages
bounce_queue_lifetime = 5d        # How long to keep bounced messages
notify_classes = resource, software, bounce, delay, policy

# Set soft_bounce to prevent permanent delivery errors
soft_bounce = yes                 # Convert permanent errors into temporary errors

Check Queue Settings:

# View current queue
mailq

# Force queue processing
sudo postqueue -f

# View specific message in queue
sudo postcat -q queue_ID

Manage Queued Messages:

# Hold message in queue
sudo postsuper -h queue_ID

# Release held message
sudo postsuper -H queue_ID

# Delete specific message
sudo postsuper -d queue_ID
```
Send Test Email with Telnet
```
ubuntu@SBIHD-GenAI-uat-LVM-AP01:~$ telnet 10.0.3.51 25
Trying 10.0.3.51...
Connected to 10.0.3.51.
Escape character is '^]'.
220 SBIHD-GenAI-uat-LVM-AP01.ap-northeast-1.compute.internal ESMTP Postfix (Ubuntu)
HELO sbigenai.com
250 SBIHD-GenAI-uat-LVM-AP01.ap-northeast-1.compute.internal
MAIL FROM: <EMAIL>
250 2.1.0 Ok
RCPT TO: <EMAIL>
250 2.1.5 Ok
DATA
354 End data with <CR><LF>.<CR><LF>
From: Ray Sheng
TO: JBSHENG
Subject: TEST email
hi, it is a test
.
250 2.0.0 Ok: queued as D838E7E516
quit
221 2.0.0 Bye

```
### make postfix support OAuth2.0 
```
# Debian/Ubuntu
apt-get install postfix postfix-pcre libsasl2-modules

# CentOS/RHEL
yum install postfix cyrus-sasl cyrus-sasl-plain

在 /etc/postfix/sasl/smtpd.conf 创建或修改：
pwcheck_method: auxprop
auxprop_plugin: sql
mech_list: PLAIN LOGIN CRAM-MD5 DIGEST-MD5 NTLM XOAUTH2
sql_engine: mysql

修改 Postfix 主配置文件：
编辑 /etc/postfix/main.cf
# 启用 SASL 认证
smtp_sasl_auth_enable = yes
smtp_sasl_password_maps = hash:/etc/postfix/sasl_passwd
smtp_sasl_security_options = noanonymous
smtp_sasl_mechanism_filter = xoauth2

# TLS 设置
smtp_use_tls = yes
smtp_tls_security_level = encrypt
smtp_tls_CAfile = /etc/ssl/certs/ca-certificates.crt

# 中继设置
relayhost = [smtp.gmail.com]:587

配置凭证：
创建并编辑 /etc/postfix/sasl_passwd：
[smtp.gmail.com]:587    <EMAIL>:oauth2_token

生成数据库文件：
postmap /etc/postfix/sasl_passwd
chown root:root /etc/postfix/sasl_passwd*
chmod 600 /etc/postfix/sasl_passwd*

https://claude.ai/chat/83113ad0-bdb2-42a6-83c8-eb45b2f622cd

```



## Postgresql DB Setup

### DB binary installation

```LaTeX
./Miniconda3-latest-Linux-x86_64.sh
conda create -n mom python=3.10.12
conda activate mom
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu117
pip install -r requirements.txt
pip install langchain
sudo apt-get update
sudo apt-get install portaudio19-dev
pip install pyaudio SpeechRecognition
sudo apt remove postgresql postgresql-contrib
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/postgresql-pgdg.list > /dev/null
sudo apt update
sudo apt install postgresql
sudo apt upgrade
sudo apt install postgresql-server-dev-16
sudo dpkg -l|grep postgres
(mom) devusr@SBIHD-GenAI-dev-LVM-ML01:/etc/postgresql$ sudo dpkg -l|grep postgres
ii  postgresql                                16+257.pgdg22.04+1                      all          object-relational SQL database (supported version)
ii  postgresql-14                             14.11-1.pgdg22.04+1                     amd64        The World's Most Advanced Open Source Relational Database
ii  postgresql-16                             16.2-1.pgdg22.04+1                      amd64        The World's Most Advanced Open Source Relational Database
ii  postgresql-client-14                      14.11-1.pgdg22.04+1                     amd64        front-end programs for PostgreSQL 14
ii  postgresql-client-16                      16.2-1.pgdg22.04+1                      amd64        front-end programs for PostgreSQL 16
ii  postgresql-client-common                  257.pgdg22.04+1                         all          manager for multiple PostgreSQL client versions
ii  postgresql-common                         257.pgdg22.04+1                         all          PostgreSQL database-cluster manager
ii  postgresql-server-dev-16                  16.2-1.pgdg22.04+1                      amd64        development files for PostgreSQL 16 server-side programming
cd /opt
sudo ln -s /usr/lib/postgresql/16 postgresql
cd /databank/database
sudo mkdir pgdata
sudo chown -R postgres.postgres pgdata
cd /opt/postgresql
sudo ln -s /databank/database/pgdata/ data
sudo rsync -avl /var/lib/postgresql/16/main/ /databank/database/pgdata/
cd /etc/postgresql/16/
rm -rf main
ln -s /databank/database/config/ main
cd /opt/postgresql
ln -s /databank/database/config conf
cd /opt
sudo chown -R postgres.postgres postgresql
postgres@SBIHD-GenAI-dev-LVM-ML02:/opt/postgresql$ ls -lrt
total 8
drwxr-xr-x 2 <USER> <GROUP> 4096 Feb 13 11:55 bin
drwxr-xr-x 4 <USER> <GROUP> 4096 Feb 13 11:56 lib
lrwxrwxrwx 1 postgres postgres   26 Feb 15 05:23 data -> /databank/database/pgdata/
lrwxrwxrwx 1 postgres postgres   25 Feb 15 07:06 conf -> /databank/database/config
\#modify postgresql.conf and pg_hba.conf
```
### AI Portal DB
```
postgres=# create database aipdb;
CREATE DATABASE
postgres=# create role aiprole;
CREATE ROLE
postgres=# grant connect on database aipdb to aiprole;
GRANT
postgres=# create user aipusr password 'aipusruat#12';
CREATE ROLE
postgres=# grant aiprole to aipusr;
GRANT ROLE
postgres=# alter role aiprole set search_path=aipsc,public;
ALTER ROLE
postgres=# \c aipdb postgres
You are now connected to database "aipdb" as user "postgres".
aipdb=# create schema aipsc;
CREATE SCHEMA
aipdb=# alter schema aipsc owner to aipusr;
ALTER SCHEMA
aipdb=# alter database aipdb set search_path=aipsc,public;
ALTER DATABASE
aipdb=# create schema momsc;
CREATE SCHEMA
aipdb=# alter schema momsc owner to aipusr;
ALTER SCHEMA
aipdb=# alter role aiprole set search_path=aipsc,momsc,public;
ALTER ROLE
aipdb=# alter database aipdb set search_path=aipsc,momsc,public;
ALTER DATABASE

psql -U aipusr -h 127.0.0.1 -p 5499 -W -d aipdb
aipusruat#12


```
#### make aipusr able to talk to momusr tables
```
\c momdb
CREATE ROLE momdb_reader WITH NOLOGIN;
-- Grant connect permission to the role
GRANT CONNECT ON DATABASE momdb TO momdb_reader;

-- Grant schema usage permission
GRANT USAGE ON SCHEMA momsc TO momdb_reader;

-- Grant table permissions
GRANT SELECT ON momsc.mom_actions TO momdb_reader;
GRANT SELECT ON momsc.mom_audio_convert_tasks TO momdb_reader;
GRANT SELECT ON momsc.mom_audio_files TO momdb_reader;
GRANT SELECT ON momsc.mom_audio_pool TO momdb_reader;
GRANT SELECT ON momsc.mom_audio_speaker_texts TO momdb_reader;
GRANT SELECT ON momsc.mom_doc_structuring TO momdb_reader;
GRANT SELECT ON momsc.mom_doc_structuring_solution TO momdb_reader;
GRANT SELECT ON momsc.mom_meeting_minutes TO momdb_reader;
GRANT SELECT ON momsc.v_mom_meeting_status TO momdb_reader;
GRANT SELECT ON momsc.v_mom_meeting_users TO momdb_reader;
GRANT SELECT ON momsc.v_mom_speaker_alias TO momdb_reader;
GRANT momdb_reader TO aipusr;

add below line into pg_hba.conf
local   momdb           aipusr                                  trust

```
For testing
https://test.sbigenai.com/aiportal/home

Vector extension installation
drop extension vector;
set search_path to public;
create extension vector;

### MomDB installation

```JavaScript
create database momdb;
create role momrole;
grant connect on database momdb to momrole;
create user momusr password 'momusr\#345';
grant momrole to momusr;
create tablespace momtbsp location '/opt/postgresql/data/pg_tblspc';
alter role momrole set default_tablespace='momtbsp';
alter role momrole set search_path=momsc,public;
\c momdb postgres
create schema momsc;
alter schema momsc owner to momusr;
alter database momdb set search_path=momsc,public;
\#open pg_hba.conf
local maybedb maybe md5
pg_ctl reload
maybedb=# \c maybedb maybe
Password for user maybe:
\#add raesc schema
create schema raesc;
alter schema raesc owner to momusr;

\#as we can't specify db, so did some change
postgres=# alter role mayberole nosuperuser;
ALTER ROLE
postgres=# alter role mayberole with CreateDB;
ALTER ROLE
#只能显示的grant
postgres=# alter role maybe createdb;
ALTER ROLE
\#change pg_hba.conf
local all maybe md5
pg_ctl reload
psql -U momusr -h ********* -p 5432 -W -d momdb
psql -U momusr -h ************ -p 5432 -W -d momdb
postgres@SBIHD-GenAI-dev-LVM-ML02:/opt/postgresql$ psql -U momusr -h ******** -p 5432 -W -d momdb
Password:
psql (16.2 (Ubuntu 16.2-1.pgdg22.04+1))
SSL connection (protocol: TLSv1.3, cipher: TLS_AES_256_GCM_SHA384, compression: off)
Type "help" for help.
momdb=> \conninfo
You are connected to database "momdb" as user "momusr" on host "********" at port "5432".
SSL connection (protocol: TLSv1.3, cipher: TLS_AES_256_GCM_SHA384, compression: off)
momdb=> show search_path;
  search_path
---------------
 momsc, public
(1 row)
momdb=> select * from testme;
 fd
----
(0 rows)
momdb=> \dt
        List of relations
 Schema |  Name  | Type  | Owner
--------+--------+-------+--------
 momsc  | testme | table | momusr
(1 row)
```

MomDB table setup

```LaTeX
DROP TABLE mm_audio_convert_task;
DROP TABLE mm_audio_text;
DROP TABLE mm_main ;
create table mom_audio_convert_tasks (
  meeting_id varchar(64) not null,
  audio_file_name varchar(128) not null,
  audio_file_size int not null,
  audio_sample_rate int not null,
  audio_file_hash bigint not null,
  convert_status varchar(8) not null default 'new',
  convert_fail_reason varchar(128) ,
  created_by varchar(32) not null,
  created_at timestamp with time zone not null default current_timestamp,
  updated_by varchar(32) not null,
  updated_at timestamp with time zone not null default current_timestamp,
  primary key (meeting_id)
);
new|failed|completed
create table mom_audio_files (
  audio_file_hash bigint not null,
  audio_file_body bytea not null,
  created_by varchar(32) not null,
  created_at timestamp with time zone not null default current_timestamp,
  updated_by varchar(32) not null,
  updated_at timestamp with time zone not null default current_timestamp,
  primary key (audio_file_hash)
);
create table mom_audio_speaker_texts (
  meeting_id varchar(64) not null,
  -- maybe the spoken time offset, eg. 03:02:00
  spoken_at varchar(64) not null,
  speaker_id varchar(64) not null,
  speaker_text varchar(512) not null,
  speaker_alias varchar(64),
  created_by varchar(32) not null,
  created_at timestamp with time zone not null default current_timestamp,
  updated_by varchar(32) not null,
  updated_at timestamp with time zone not null default current_timestamp,
  primary key (meeting_id, spoken_at)
);
create table mom_actions (
  meeting_id varchar(64) not null,
  action_seq int not null default 1,
  assignee varchar(32),
  action varchar(512) not null,
  completed boolean not null default false,
  created_by varchar(32) not null,
  created_at timestamp with time zone not null default current_timestamp,
  updated_by varchar(32) not null,
  updated_at timestamp with time zone not null default current_timestamp,
  primary key (meeting_id, action_seq)
);
create table mom_meeting_minutes (
  meeting_id varchar(64) not null,
  meeting_name varchar(128) not null,
  meeting_room varchar(64),
  meeting_time timestamp with time zone not null,
  ai_summary text,
  created_by varchar(32) not null,
  created_at timestamp with time zone not null default current_timestamp,
  updated_by varchar(32) not null,
  updated_at timestamp with time zone not null default current_timestamp,
  lang varchar(2) not null default 'JP',
  primary key (meeting_id)
);

INSERT INTO mom_meeting_minutes (meeting_id, meeting_name, meeting_room, meeting_time, ai_summary, created_by, updated_by)
VALUES ('1', 'Project Kickoff', 'Room A1', '2024-03-05 10:00:00+00', 'This is a summary generated by AI.', 'admin', 'admin');
SELECT * FROM momsc.mom_meeting_minutes
ORDER BY meeting_id ASC 
INSERT INTO mom_meeting_minutes (meeting_id, meeting_name, meeting_room, meeting_time, ai_summary, created_by, updated_by)
VALUES ('2', 'Project Kickoff', 'Room A2', '2024-03-06 10:00:00+00', 'This is a summary generated by AI.', 'admin', 'admin');

SELECT
  mact.meeting_id,
  mact.convert_task_id,
  mact.audio_file_name,  
  mact.audio_sample_rate,
  maf.audio_file_body
FROM
  mom_audio_convert_tasks mact
JOIN
  mom_audio_files maf
ON
  mact.audio_file_hash = maf.audio_file_hash
WHERE
  mact.convert_status = 'todo'
ORDER BY
  mact.meeting_id,
  mact.convert_task_id;

SELECT
  mact.meeting_id,
  mact.convert_task_id,
  mact.audio_file_name,
  convert_status,
  mact.audio_sample_rate
FROM
  mom_audio_convert_tasks mact
JOIN
  mom_audio_files maf
ON
  mact.audio_file_hash = maf.audio_file_hash
WHERE
  mact.convert_status = 'todo'
ORDER BY
  mact.meeting_id,
  mact.convert_task_id;
```

PGVector Usage

```JavaScript
https://github.com/pgvector/pgvector
https://github.com/pgvector/pgvector-python
alter table mom_audio_speaker_texts alter column embedding type vector(768);
alter table mom_actions alter column embedding type vector(768);
alter table mom_meeting_minutes alter column embedding type vector(768);

create index on mom_audio_speaker_texts using hnsw (embedding vector_l2_ops);
create index on mom_audio_speaker_texts using hnsw (embedding vector_cosine_ops);
create index on mom_actions using hnsw (embedding vector_l2_ops);
create index on mom_actions using hnsw (embedding vector_cosine_ops);
create index on mom_meeting_minutes using hnsw (embedding vector_l2_ops);
create index on mom_meeting_minutes using hnsw (embedding vector_cosine_ops);

CREATE INDEX ON items USING hnsw (embedding vector_l2_ops);
CREATE INDEX ON items USING hnsw (embedding vector_cosine_ops);
CREATE INDEX ON items USING ivfflat (embedding vector_l2_ops) WITH (lists = 1000);
CREATE INDEX ON vtest USING ivfflat (v vector_cosine_ops) WITH(lists = 1000);
SELECT embedding <-> '[3,1,2]' AS distance FROM items;
SELECT 1 - (embedding <=> '[3,1,2]') AS cosine_similarity FROM items;
cur.execute('SELECT * FROM items ORDER BY embedding <-> %s LIMIT 5', (embedding,))
cur.fetchall()

-- check index progress
SELECT phase, tuples_done, tuples_total FROM pg_stat_progress_create_index;

def query(question, limit=64):
    vec = model.encode(question)  # 生成一个一次性的编码向量，默认查找最接近的64条记录
    item = 'ARRAY[' + ','.join([str(f) for f in vec.tolist()]) + ']::VECTOR(768)'
    cursor = connect('postgres:///').cursor()
    cursor.execute("""SELECT id, txt, vec <-> %s AS d FROM sentences ORDER BY 3 LIMIT %s;""" % (item, limit))
    for id, txt, distance in cursor.fetchall():
        print("%-6d [%.3f]\t%s" % (id, distance, txt))
      
```

## New bits mom development

```LaTeX
https://github.com/m-bain/whisperX
conda create --name whisperx python=3.10
conda activate whisperx
conda install pytorch==2.0.0 torchaudio==2.0.0 pytorch-cuda=11.8 -c pytorch -c nvidia
pip install git+https://github.com/m-bain/whisperx.git
pip install git+https://github.com/m-bain/whisperx.git --upgrade
speechbrain
https://github.com/speechbrain/speechbrain
https://huggingface.co/speechbrain/spkrec-ecapa-voxceleb
pip install git+https://github.com/speechbrain/speechbrain.git@develop
pip install git+https://github.com/speechbrain/speechbrain.git
\#document
https://speechbrain.readthedocs.io/en/latest/index.html
\#example
https://note.com/eurekachan/n/n50b7fb912f23
#可能分类要用到
https://speechbrain.readthedocs.io/en/latest/API/speechbrain.processing.html
https://speechbrain.readthedocs.io/en/latest/API/speechbrain.processing.diarization.html
import torchaudio
from speechbrain.inference.speaker import EncoderClassifier
classifier = EncoderClassifier.from_hparams(source="speechbrain/spkrec-ecapa-voxceleb")
signal, fs =torchaudio.load('tests/samples/ASR/spk1_snt1.wav')
embeddings = classifier.encode_batch(signal)
\#install whisper
# doc https://github.com/openai/whisper
https://huggingface.co/openai/whisper-large-v3
pip install git+https://github.com/openai/whisper.git 
pip install flash-attn --no-build-isolation --upgrade
https://qiita.com/syoyo/items/940500db4ccb940a6af3
whisper test_dar.wav --language Japanese --model medium
===  模型下载的路径
(mom) (base) [raysheng@MONSTER whisper]$ pwd
/home/<USER>/.cache/whisper
(mom) (base) [raysheng@MONSTER whisper]$ ls
medium.pt
\#openai large-v3 huggingface version
git clone https://huggingface.co/openai/whisper-large-v3
#程序如下。result['text']包含所以wav转text后文本，result['segments']包含分段后内容
import whisper
def get_segments_embedingtable(audio_file, model, audio, result):
    for segment in result.get('segments', []):      
            speaker = Segment(segment['start'], segment['end'])
            waveform, sample_rate = audio.crop(audio_file, speaker)
          
            # # 将裁剪后的音频片段保存到文件
            # crop_file = "crop_segment.wav"
            # sf.write(crop_file, waveform.squeeze().numpy(), sample_rate)
                  
            embedding = model.encode_batch(waveform)
            segment['embedding'] = embedding.squeeze(0)  
return result  
whisper.available_models()
model_test = whisper.load_model("large", device="cuda", download_root="/opt/workspace/mom/model",).float()
result = model_test.transcribe("./test.wav", language="ja", temperature=0.0 , verbose=True, condition_on_previous_text=False)
result = get_segments_embedingtable(temp_file_path_preprocessed, emb_model, emb_audio, result)  
for segment in result['segments']:
    segment['embedding'] = segment['embedding'].tolist()
print(result["text"])
result.get("segments")
```

### 保护Python Code

```Python
https://pyarmor.readthedocs.io/en/latest/
https://blog.csdn.net/weixin_43207777/article/details/108351862
pip install pyarmor
pyarmor gen bits_momx.py
python bits_momx.py
\#you can call api from encrypted package
it will generate dist/audiocore.py and cp dist/pyarmor_runtime_000000 ..
from dist.audiocore import setup_model

python -m py_compile bits_momx.py
bits_logger.pyc
```

### MOM_AI service

```JavaScript
root@SBIHD-GenAI-dev-LVM-ML01:/usr/lib/systemd/system# cat mom_ai.service
[Unit]
Description=MOM_AI for LangChain ChatChat
After=pg16.service
[Service]
User=devusr
Environment="PATH=/opt/app/miniconda3/envs/mom/bin:/usr/bin:$PATH"
ExecStart=/opt/app/miniconda3/envs/mom/bin/python bits_momx.py
WorkingDirectory=/opt/app/mom
Restart=on-failure
RestartSec=5s
OOMScoreAdjust=-500
[Install]
WantedBy=multi-user.target
```

MOM_EMB Service

```JavaScript
Description=MOM_EMBEDDING Service made by GenAI team.
After=pg16.service
[Service]
User=devusr
Environment="PATH=/opt/app/miniconda3/envs/mom/bin:/usr/bin:$PATH"
ExecStart=/opt/app/miniconda3/envs/mom/bin/python bits_momx_emb.pyc
WorkingDirectory=/opt/app/mom
Restart=on-failure
RestartSec=5s
OOMScoreAdjust=-500
[Install]
WantedBy=multi-user.target
```

### 生成UML图

```Python
Ask Claude ->
使用UML的序列图来表示程序中所有函数之间的调用关系，包括函数的参数：
```

![[bits_momx.png]]

## Improvement Accuracy

### keypoint for improvement

```JavaScript
1. Segment
2. Alignment
3. Embedding
# 比较 
https://chat.openai.com/share/4d81850e-e0b5-4953-804a-940a9f159d8f
- dvector
https://github.com/wq2012/SpectralCluster => use d-vector
https://github.com/yistLin/dvector?tab=readme-ov-file
https://github.com/yistLin/dvector/releases
https://github.com/philipperemy/deep-speaker
https://share.weiyun.com/V2suEUVh
- xvector
speechbrain--spkrec-ecapa-voxceleb using xvector
https://github.com/manojpamk/pytorch_xvectors?tab=readme-ov-file\#downloading
https://drive.google.com/file/d/1gbAWDdWN_pkOim4rWVXUlfuYjfyJqUHZ/view
- svector
https://arxiv.org/pdf/2008.04659v1.pdf
4. Clustering - Spectral Clustering
5. Labels
```

![[Notion/AI/🦜🦜🦜MeetingMom Tool/attachments/Untitled 1.png|Untitled 1.png]]

### possible points we can improve

```Python
1 convert_status -> resize varchat(12)
2. 声音mono化 不要
3. update combined file in certain meeting id, if hashcode not null. 必须删除原来的在update新的  <= fixed
4. Long Content summary
https://www.xiaoiluo.com/article/long-summarize
https://juejin.cn/post/7238110426147504184
https://book.st-hakky.com/data-science/langchain-long-document/
momdb=> select meeting_id,convert_status,audio_file_order from mom_audio_convert_tasks where audio_file_order = 0;
  meeting_id   | convert_status | audio_file_order
---------------+----------------+------------------
 Infra meeting | new            |                0
 1             | completed      |                0
 AWS meeting   | completed      |                0
select meeting_id,convert_status,audio_file_order from mom_audio_convert_tasks where audio_file_order = 0;
update mom_audio_convert_tasks set convert_status='in-queue' where meeting_id='1' and audio_file_order=0;
Error transcribing audio: [Errno 2] No such file or directory: 'ffmpeg'
select ai_summary from mom_meeting_minutes where meeting_id = '';
```

### Issues

```Python
Whisper did not predict an ending timestamp, which can happen if audio is cut off in the middle of a word. Also make sure WhisperTimeStampLogitsProcessor was used during generation
https://github.com/huggingface/transformers/issues/20225
https://github.com/huggingface/transformers/pull/24476
https://github.com/huggingface/transformers/issues/23231
https://github.com/huggingface/transformers/issues/21467
https://github.com/huggingface/transformers/issues/22053
https://github.com/blbadger/transformers/commit/f0ac2d721ca629150b8b8baee90c98ef81ad30c0
https://github.com/Vaibhavs10/insanely-fast-whisper/issues/158
https://huggingface.co/openai/whisper-large-v2/blob/main/generation_config.json\#L124
Temporary solution
1. use openai/whisper-large-v2
2. use openai/whisper-large-v3 but
modify /opt/workspace/mom/model/whisper-large-v3/generation_config.json
  "no_timestamps_token_id": 50363, <= update original 50364 -> 50363
  "no_speech_token_id": 50363,  <= add this but look useless
  

比较下来发现sample_rate越大正确性越好，好像同downmix为mono无关
emb_audio = Audio(sample_rate=22050, mono="downmix")
vs
emb_audio = Audio()
vs
emb_audio = Audio(sample_rate=22050)
vs
emb_audio = Audio(sample_rate=16000)
\#change to deep-speaker embedding
比较下来感觉识别不同speaker个数大大减少
git clone https://github.com/philipperemy/deep-speaker.git
cd deep-speaker
pip install .
import random
import numpy as np
from deep_speaker.audio import read_mfcc
from deep_speaker.batcher import sample_from_mfcc
from deep_speaker.constants import SAMPLE_RATE, NUM_FRAMES
from deep_speaker.conv_models import DeepSpeakerModel
from deep_speaker.test import batch_cosine_similarity
# Reproducible results.
np.random.seed(123)
random.seed(123)
# Define the model here.
emb_model = DeepSpeakerModel()
emb_model.m.load_weights('/opt/app/mom/model/speech_embeddings/dvector/ResCNN_triplet_training_checkpoint_265.h5', by_name=True)
------------------------------------------------------------------------------------------------
# pyannote 的speaker区分比较下来效果不太好，比DeepSpearker区分还要少些
https://github.com/pyannote/pyannote-audio
-- download model https://huggingface.co/pyannote/speaker-diarization-3.1
-- it will download into /home/<USER>/.cache/torch/pyannote
from pyannote.audio import Pipeline
pipeline = Pipeline.from_pretrained(
    "pyannote/speaker-diarization-3.1",
    use_auth_token="*************************************")
# send pipeline to GPU (when available)
import torch
pipeline.to(torch.device("cuda"))
# apply pretrained pipeline
diarization = pipeline("audio.wav")
# print the result
for turn, _, speaker in diarization.itertracks(yield_label=True):
    print(f"start={turn.start:.1f}s stop={turn.end:.1f}s speaker_{speaker}")
  
---------------------------------------------------------------------------------
https://stackoverflow.com/questions/65298241/what-does-this-tensorflow-message-mean-any-side-effect-was-the-installation-su


----------------------------------------------------------------------------------
1. added m4a support 
2. POSITIVE_THRESHOLD = 0.4 -> 0.5 \#for clustering failback_threshold
3. 
    num_samples = embeddings_array.shape[0] + 1  # 10 -> 1
  
    multi_stage = MultiStageClusterer(
        main_clusterer=main_clusterer,
        fallback_threshold=failback_threshold,
        L=num_samples ,
        U1=num_samples * 2, \#4 -> 2
        U2=num_samples * 4,  \#8 -> 4
        deflicker=Deflicker.Hungarian)
  
########  目前最佳设定如下  
num_samples = embeddings_array.shape[0] + 1
multi_stage = MultiStageClusterer(
    main_clusterer=main_clusterer,
    fallback_threshold=0.2,
    L=num_samples ,
    U1= 20 ,
    U2= num_samples ,
    deflicker=Deflicker.Hungarian)
  
-----------------
1. 并行取得embedding
2. 出现重复词语时候修复  <= Done
3. Remove unrelated items from summary and action plan  <= Done
How to use MapReduceDocumentsChain
https://api.python.langchain.com/en/latest/chains/langchain.chains.combine_documents.map_reduce.MapReduceDocumentsChain.html\#langchain.chains.combine_documents.map_reduce.MapReduceDocumentsChain
  the correct way to call
  map_prompt = PromptTemplate(input_variables=["agenda", "docs"], template=map_template)
  ...
 final_summary = map_reduce_chain.run({"input_documents": text_chunks, "agenda": meeting_agenda})
 
So far large v3 looks better. 
4. Handle if meeting agenda is null , we can use meeting_name <= Done
5. get main lang and handover ai to process <= Done
   a. Get summary and action plan, we need to seperated based on Language
   b. in audiocore.py , these lang='ja'
   failback_result = model_failback.transcribe(temp_file_path, language="ja", temperature=0.4, verbose=False, condition_on_previous_text=False)
6. when resummary fail, put summarized_fail_resaon <= Done
7. based on speaker_classification_granularity , fine/coarse , decide ai logic  <= Done
8. when successful, overwrite previous failure reason
9. in-queue, in-progress, completed, failed
10. Ask resize spoken_at and spoken_end size to 11  <- done
11. Notification <- done
12. support mp4 format <- Done
pip install moviepy
\#ERROR: invalid memory alloc request size 1073741824
http://www.fuzzy.cz/en/articles/storing-files-in-a-postgresql-database/

-------------------------------------------------------------------------------------
https://github.com/linto-ai/whisper-timestamped/issues/17
[2024-05-01 13:50:58,966] ERROR: prompt: Audio Transcription, error: Error transcribing audio: Argument \#4: Padding size should be less than the corresponding input dimension, but got: padding (3, 3) at dimension 2 of input [1, 128, 3]
\#it is fixed. the reason is the segment just 0.02 sec. Have to merge to next audio segment to fix it
            # 检查片段长度是否小于1秒
            if end - start < 1:
                if i < len(result['chunks']) - 1:
                    # 如果不是最后一个片段,将其与下一个片段合并
                    next_chunk = result['chunks'][i + 1]
                    chunk['timestamp'] = (start, next_chunk['timestamp'][1])
                    chunk['text'] += ' ' + next_chunk['text']
                    end =  next_chunk['timestamp'][1]
                    del result['chunks'][i + 1]
                else:
                    # 如果是最后一个片段,直接丢弃
                    del result['chunks'][i]
                    continue

------------------------------------------------------------------------------------
transformer Issue

(mom) ray@jethome:/opt/app/mom$ pip list|grep trans
ctranslate2               4.5.0
s3transfer                0.10.3
sentence-transformers     3.3.0
transformers              4.46.2
(mom) ray@jethome:/opt/app/mom$ pip list|grep trans
ctranslate2               4.5.0
s3transfer                0.10.3
sentence-transformers     3.3.0
transformers              4.39.0



<<issue 1>>
https://github.com/openai/whisper/discussions/2094
                result = audio_pipeline[whisper_instance_id](
                    temp_file_path_preprocessed,
                    generate_kwargs=generate_kwargs,
                    batch_size=worker_count 
                )
				
   You have passed language=ja, but also have set `forced_decoder_ids` to [[1, None], [2, 50359]] which creates a conflict. `forced_decoder_ids` will be ignored in favor of language=ja.
   
Solution:
1. fix the code so that able to handle both version
2. make transformer version same as prod in AWS
pip install transformers==4.39.0
pip install ipadic
   
<<issue 2>>
https://github.com/pytorch/pytorch/issues/121834
not fully confirmed but it could be torch issue. but my dev cannot change pytorch
                result = audio_pipeline[whisper_instance_id](
                    temp_file_path_preprocessed,
                    generate_kwargs=generate_kwargs,
                    batch_size=worker_count 
                )

The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.
WARNING:py.warnings:/opt/workspace/miniconda3/envs/mom/lib/python3.10/site-packages/torch/nn/modules/conv.py:306: UserWarning: Plan failed with a cudnnException: CUDNN_BACKEND_EXECUTION_PLAN_DESCRIPTOR: cudnnFinalize Descriptor Failed cudnn_status: CUDNN_STATUS_NOT_SUPPORTED (Triggered internally at /opt/pytorch/aten/src/ATen/native/cudnn/Conv_v8.cpp:919.)
  return F.conv1d(input, weight, bias, self.stride,

pip install torch==2.3.1 torchvision==0.18.1 torchaudio==2.3.1 --index-url https://download.pytorch.org/whl/cu118
  
Solution:   
N/A
  Just user warning message 
The warning message indicates that CUDNN attempted to use an optimized execution plan for a convolution operation but couldn't create it. When this happens, PyTorch automatically falls back to a different, slightly less optimized implementation. Your model will still run correctly, just potentially with a very slight performance impact.

```

### github reference

```Python
https://huggingface.co/jonatasgrosman/wav2vec2-large-xlsr-53-japanese
https://huggingface.co/NTQAI/wav2vec2-large-japanese
https://huggingface.co/esnya/japanese_speecht5_tts
\#Speed up with all kinds of ways
pip install git+https://github.com/huggingface/transformers
https://github.com/Vaibhavs10/insanely-fast-whisper
pip list|grep trans  <= make sure version is latest one 
pip list|grep flash
pip list|grep accele
pip list|grep opti
(mom) [raysheng@MONSTER model]$ pip list|grep acce
accelerate                    0.23.0
(mom) [raysheng@MONSTER model]$ pip list|grep opti
optimum                       1.13.2
(mom) [raysheng@MONSTER model]$ pip list|grep flash
flash-attn                    2.5.6
(mom) [raysheng@MONSTER model]$ pip list|grep trans
ctranslate2                   4.0.0
transformers                  4.39.0.dev0
\#lang chain document
https://python.langchain.com/docs/use_cases/summarization
https://smith.langchain.com/hub/aim-notes?organizationId=9a8ea6c7-221b-4ad9-a2e7-4003a75bab13
\#ConvTasNet github and pretrained model
https://github.com/JusperLee/Conv-TasNet?tab=readme-ov-file
https://huggingface.co/JusperLee/Conv-TasNet/tree/main

#声音处理的天花板们
speech2Text
https://huggingface.co/spaces/hf-audio/open_asr_leaderboard
可惜number 1不支持日语

Text2Speech
https://huggingface.co/blog/arena-tts

#日语文字特征向量模型
https://huggingface.co/pkshatech/GLuCoSE-base-ja
```

询问AI的prompt

```Python
以下两组值是不同模型对相同一批声音文件的speaker标注，数字是随机产生的，数字本身并不重要，重要的是两个模型是否都辨识不同的speaker，请观察这两个模型在辨识不同speaker有没有歧义
模型A：
['55', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '59', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '60', '46', '38', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '3', '68', '7', '63', '41', '61', '43', '47', '39', '49', '51', '3', '3', '3', '3', '3', '3', '67', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '57', '48', '0', '0', '0', '0', '0', '0', '0', '0', '0', '33', '0', '0', '52', '58', '64', '40', '54', '37', '0', '0', '0', '0', '0', '73', '0', '69', '0', '0', '0', '70', '0', '0', '0', '45', '0', '28', '0', '65', '0', '0', '0', '0', '50', '0', '74', '0', '4', '71', '0', '0', '0', '0', '1', '18', '0', '0', '0', '0', '0', '2', '44', '25', '0', '0', '0', '0', '0', '0', '66', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '16', '0', '0', '0', '0', '0', '0', '0', '16', '0', '0', '56', '0', '0', '0', '0', '42', '20', '23', '27', '24', '29', '72', '62', '34', '0', '0', '0', '0', '0', '31', '35', '0', '30', '19', '0', '32', '0', '53', '26', '1', '4', '12', '9', '36', '0', '0', '13', '11', '21', '22', '5', '0', '0', '0', '17', '16', '0', '0', '8', '0', '0', '0', '0', '0', '0', '14', '15', '6', '0', '2', '0', '0', '7', '0', '10', '0']
模型B：
['127', '0', '0', '0', '0', '0', '66', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '79', '0', '0', '8', '116', '128', '99', '124', '113', '108', '0', '5', '0', '91', '0', '75', '114', '82', '69', '0', '0', '80', '0', '119', '110', '0', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '66', '8', '135', '97', '83', '78', '107', '85', '95', '129', '96', '0', '0', '0', '0', '8', '0', '92', '39', '14', '14', '14', '14', '14', '14', '56', '14', '14', '16', '16', '111', '84', '14', '14', '2', '4', '32', '2', '12', '14', '14', '105', '106', '63', '68', '131', '109', '71', '70', '88', '30', '121', '125', '14', '12', '112', '115', '3', '118', '130', '14', '13', '13', '76', '12', '3', '2', '102', '15', '122', '7', '101', '30', '15', '4', '32', '93', '77', '133', '73', '14', '74', '15', '14', '98', '57', '14', '14', '14', '14', '14', '81', '120', '134', '14', '14', '14', '14', '7', '14', '117', '14', '29', '14', '14', '14', '94', '1', '67', '14', '126', '12', '14', '14', '14', '14', '14', '1', '14', '6', '100', '14', '62', '28', '14', '14', '14', '14', '14', '14', '14', '38', '53', '14', '28', '6', '59', '132', '40', '37', '58', '54', '49', '90', '87', '34', '19', '46', '18', '1', '64', '31', '89', '43', '123', '45', '103', '26', '48', '44', '21', '55', '36', '33', '22', '104', '86', '51', '10', '65', '72', '41', '14', '14', '35', '27', '50', '14', '47', '14', '7', '2', '24', '29', '7', '14', '23', '42', '20', '9', '52', '61', '60', '25', '11', '17', '16']


Attach source code and ask
用UML表示出上面所有函数之间的调用关系，要包含函数的参数
maybe you need to add , pls use sequenceDiagram 
```

# 训练微调

[[(553) Fine tuning Whisper for Speech Transcription - YouTube - Trelis Research]]

```Python
import whisper
import datetime
def format_timestamp(seconds):
    return str(datetime.timedelta(seconds=seconds)).replace(".", ",")
def write_vtt(result, file_path):
    with open(file_path, "w", encoding="utf-8") as vtt_file:
        vtt_file.write("WEBVTT\n\n")
        for chunk in result["chunks"]:
            start = format_timestamp(chunk["timestamp"][0])
            end = format_timestamp(chunk["timestamp"][1])
            text = chunk["text"].replace("\n", " ")
            vtt_file.write(f"{start} --> {end}\n{text}\n\n")
# 加载Whisper模型
model = whisper.load_model("base")
# 音频文件路径
audio_path = "path/to/your/audio/file.wav"
# 调用Whisper模型进行语音识别
result = model.transcribe(audio_path)
# 生成的VTT文件路径
vtt_path = "path/to/save/subtitle.vtt"
# 将识别结果写入VTT文件
write_vtt(result, vtt_path)
print("字幕文件已生成：", vtt_path)
```

# 语言自动识别逻辑

```Python
https://discuss.huggingface.co/t/language-detection-with-whisper/26003
git clone https://huggingface.co/openai/whisper-tiny.en
def detect_language(model: WhisperForConditionalGeneration, tokenizer: WhisperTokenizer, input_features,
                    possible_languages: Optional[Collection[str]] = None) -> List[Dict[str, float]]:
    # hacky, but all language tokens and only language tokens are 6 characters long
    language_tokens = [t for t in tokenizer.additional_special_tokens if len(t) == 6]
    if possible_languages is not None:
        language_tokens = [t for t in language_tokens if t[2:-2] in possible_languages]
        if len(language_tokens) < len(possible_languages):
            raise RuntimeError(f'Some languages in {possible_languages} did not have associated language tokens')
    language_token_ids = tokenizer.convert_tokens_to_ids(language_tokens)
    # 50258 is the token for transcribing
    logits = model(input_features,
                   decoder_input_ids = torch.tensor([[50258] for _ in range(input_features.shape[0])])).logits
    mask = torch.ones(logits.shape[-1], dtype=torch.bool)
    mask[language_token_ids] = False
    logits[:, :, mask] = -float('inf')
    output_probs = logits.softmax(dim=-1).cpu()
    return [
        {
            lang: output_probs[input_idx, 0, token_id].item()
            for token_id, lang in zip(language_token_ids, language_tokens)
        }
        for input_idx in range(logits.shape[0])
    ]
  
    waveform, sample_rate = torchaudio.load(str(audio_path))
    input_features = processor(waveform.squeeze().numpy(), sampling_rate=sample_rate,
                               return_tensors="pt").input_features
    language = detect_language(model, tokenizer, input_features, {'en', 'zh'})
  
processor = WhisperProcessor.from_pretrained(“openai/whisper-tiny.en”)
model = WhisperForConditionalGeneration.from_pretrained(“openai/whisper-tiny.en”)
tokenizer = WhisperTokenizer.from_pretrained(“openai/whisper-tiny.en”)
input_features = {“Hola”,“como”,“estás”}
detect_language(model, tokenizer,input_features)

decode_options["language"]

\#You must add language option otherwise
result = pipe("wav/0422TeamMeeting_amp.wav")
ValueError: Multiple languages detected when trying to predict the most likely target language for transcription. It is currently not supported to transcribe to different languages in a single batch. Please make sure to either force a single language by passing `language='...'` or make sure all input audio is of the same language.
\#As whisper model is not good and try some other models
https://huggingface.co/docs/transformers/main/en/model_doc/mms\#language-identification-lid
# Try facebook model
/opt/workspace/mom/bits_momx_jupyter.ipynb
facebook/mms-lid-126
git clone https://huggingface.co/facebook/mms-lid-126
processor.id2label.values()
https://colab.research.google.com/github/r3gm/InsightSolver-Colab/blob/main/Massively_Multilingual_Speech_MMS.ipynb
from transformers import Wav2Vec2ForSequenceClassification, AutoFeatureExtractor
import torch
from pyannote.audio import Audio
from pyannote.core import Segment
from pydub import AudioSegment
from typing import Optional, Collection
lang_model_id = '/opt/workspace/mom/model/mms-lid-126'
lang_processor = AutoFeatureExtractor.from_pretrained(lang_model_id)
lang_model = Wav2Vec2ForSequenceClassification.from_pretrained(lang_model_id)
def detect_language(model: Wav2Vec2ForSequenceClassification, input_features, possible_languages: Optional[Collection[str]] = None) -> List[Dict[str, float]]:
    language_tokens = list(model.config.id2label.values())
    if possible_languages is not None:
        # 获取想要的语言对应的token ids
        language_token_ids = [i for i, lang in enumerate(language_tokens) if lang in possible_languages]
        if len(language_token_ids) < len(possible_languages):
            raise RuntimeError(f'Some languages in {possible_languages} did not have associated language tokens')
    else:
        language_token_ids = list(range(len(language_tokens)))
    # if less_memory == 0:
    #     input_features = input_features.to("cuda")
    with torch.no_grad():
        logits = model(input_features['input_values'].squeeze(1)).logits
      
    mask = torch.ones(logits.shape[-1], dtype=torch.bool)
    mask[language_token_ids] = False
    logits[:, mask] = -float('inf')
    output_probs = logits.softmax(dim=-1).cpu()
  
    # 将结果转换为List[Dict[str, float]]格式
    result: List[Dict[str, float]] = []
    for lang_id in language_token_ids:
        result.append({language_tokens[lang_id]: output_probs[0, lang_id].item()})
    return result
 
# 假设 `outputs` 是模型输出的语言概率分布张量
interested_languages = {"jpn", "eng"}  # 感兴趣的语言列表
audio_file = 'crop_segment.wav'
sample_rate=16000
emb_audio = Audio(sample_rate, mono="downmix")
file_duration = emb_audio.get_duration(audio_file)
start = 0
# end = 5
speaker = Segment(start, file_duration)
waveform, sample_rate = emb_audio.crop(audio_file, speaker)
inputs = lang_processor(waveform, sampling_rate=16000, return_tensors="pt")
language_probs = detect_language(lang_model, inputs,interested_languages)
print(language_probs)  # 输出类似 {"ja": 0.8, "zh": 0.1, "ko": 0.1} 的字典
# Try Japanese fine tuning model
https://huggingface.co/Ivydata/whisper-base-japanese/tree/main
----------------------------------------------------------------------
!! So far we use openai/whisper-small as language detect model
```

[TestforMom](https://www.notion.so/TestforMom-b62975d5cfa14b58bfe670fb62f5a332?pvs=21)

```Python
1. transcription_results = transcribe_audio_worker(temp_file_path, speaker_classification_granularity)
上面函数要多加一个main_lang,会议的主要语言来决定AI处理使用哪个模型
2. result = get_segments_embedingtable(temp_file_path_preprocessed, emb_model, emb_audio, result)  调用时后要传入那种方式

To do
copy bits_momx.py and audiocore.py to office env
double check 会议的main_lang 字符串，然后对 audiocore.py里对于method处理进行调整
```

# Prompt

```Python
\#Meeting agenda.
9:00 AM STATE OF THE BUSINESS
9:10 AM FINANCIAL REVIEW & 2024 BUDGET UPDATE
10:50 AM REGULATORY & LEGAL
10:25 AM BREAK
11:05 AM BOARD TRAINING
\#Instruction
Based on the meet memos and agenda provided above, please read and summarize the meeting content with business and professonal language in English. 
please memorize action plan or task list based on the summary included speaker id and action items. Please make sure you will use English to output the results!! 
Please use business and professional words while doing summary and action list and provide details.
All information is quite important and dont miss any keypoint related to the meeting agenda.
```

```
Meeting Agenda:
HD生成AI室のR&D運営方針
Meeting memos:
At attached file
Based on the provided meeting agenda and the detailed notes with json format, 
{
  "speaker_alias_display_jp":{说话人的名字},
  "speaker_text":{说话的内容},
  "importance_level":{枚举类型, 'ignore':表示我们可以忽略这段话, 'normal':表示总结时候要考虑这段话, 'must':表示非常重要,总结时候一定要概况这段话}
}
review and summarize the meeting content. Please perform the following tasks:
1. Content Summary:
a. Write the summary in Japanese.
b. 内容的取舍,请根据'importance_level', 'ignore':表示我们必须忽略这段话, 'normal':表示总结时候要考虑这段话, 'must':表示非常重要,总结时候一定要概况这段话的内容
2. Action Plan and Task List:
a. Identify actions and tasks discussed in the meeting.
b. List each action or task next to the specific 'speaker_alias_display_jp' as mentioned in the notes without guessing or changing the speaker identifiers.
Instructions for Extraction:
Extract names and content strictly based on the 'speaker_alias_display_jp' and 'speaker_text' format used in the notes.
Do not infer, assume, or modify speaker names or numbers; use them exactly as they appear in the notes.
Use professional and business-appropriate language throughout the summary. If No actions or tasks can be
identified from the given information, Please include 'No Action' in the return message.
Meeting Summary:
```

# How to Mount one driver to Linux server

```JavaScript
https://kb.uconn.edu/space/IKB/***********/Setting+up+OneDrive+on+Linux
curl https://rclone.org/install.sh | sudo bash
rclone config
mkdir OneDrive
vi mountonedriver.sh
chmod a+x mountonedriver.sh
./mountonedriver.sh
cd /home/<USER>/OneDrive/
```
-- to renew token of onedriver (usually happen after password change)
rclone config reconnect onedrive:
and it will generate one url and do copy/paste access this link to complete authentication

# Data CleanUp - PDF with images

[[🦜🦜🦜Working with PDFs- The best tools for extracting text, tables and images]]
Another Read PDF code

```Python
https://github.com/CosmosShadow/gptpdf/tree/main
https://github.com/adithya-s-k/omniparse
```

[[Demystifying PDF Parsing 04- OCR-Free Large Multimodal Model-Based Method]]
[[VikParuchuri-marker- Convert PDF to markdown quickly with high accuracy]]

Or use mathpix

[[🦜🦜🦜opendatalab-MinerU- A one-stop, open-source, high-quality data extraction tool, supports PDF-webpage-e-book extraction.一站式开源高质量数据提取工具，支持PDF-网页-多格式电子书提取。]]
https://github.com/opendatalab/MinerU

## pdfprocessor类
/opt/workspace/app/cursor/jina/backend/PdfImage.py

# 使用Lux下载视频,然后转音频

```JavaScript
lux -m --audio-only https://youtu.be/XEzRZ35urlk
ffmpeg -i sbiglobal.webm -c:v libx264 -preset fast -profile:v main -level 3.1 -c:a aac -b:a 128k -movflags +faststart sbiglobal.mp4
```

# Convert MD file to Word

```Python
import markdown
from docx import Document
from docx.shared import Pt
from docx.enum.style import WD_STYLE_TYPE
from bs4 import BeautifulSoup
def md_to_word(md_file, docx_file):
    # Read the Markdown file
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    # Convert Markdown to HTML, enabling the tables extension
    html = markdown.markdown(md_content, extensions=['tables'])
    # Create a new Word document
    doc = Document()
    # Define styles
    styles = doc.styles
    style_normal = styles['Normal']
    style_normal.font.name = 'Arial'
    style_normal.font.size = Pt(11)
    style_heading1 = styles['Heading 1']
    style_heading1.font.name = 'Arial'
    style_heading1.font.size = Pt(16)
    style_heading1.font.bold = True
    style_heading2 = styles['Heading 2']
    style_heading2.font.name = 'Arial'
    style_heading2.font.size = Pt(14)
    style_heading2.font.bold = True
    # Parse the HTML content
    soup = BeautifulSoup(html, 'html.parser')
    for element in soup.find_all(['h1', 'h2', 'p', 'table']):
        if element.name == 'h1':
            doc.add_paragraph(element.text, style='Heading 1')
        elif element.name == 'h2':
            doc.add_paragraph(element.text, style='Heading 2')
        elif element.name == 'p':
            doc.add_paragraph(element.text, style='Normal')
        elif element.name == 'table':
            # Handle table
            rows = element.find_all('tr')
            cols = max(len(row.find_all(['th', 'td'])) for row in rows)
          
            tbl = doc.add_table(rows=len(rows), cols=cols)
            tbl.style = 'Table Grid'
          
            for i, row in enumerate(rows):
                cells = row.find_all(['th', 'td'])
                for j, cell in enumerate(cells):
                    tbl.cell(i, j).text = cell.text.strip()
    # Save the document
    doc.save(docx_file)
# Usage
md_to_word('../pdfs/financial-report2.md', '../pdfs/output.docx')
print("Conversion completed. Check 'output.docx'.")
```

# Ollama

## Setup

```Python
# reference
https://www.reddit.com/r/ollama/comments/1c4zg15/does_anyone_know_how_to_change_where_your_models/
https://github.com/ollama/ollama/tree/main
https://www.gpu-mart.com/blog/custom-llm-models-with-ollama-modelfile
# install
curl -fsSL https://ollama.com/install.sh | sh

(base) [root@MONSTER:/etc/systemd/system]# cat /etc/systemd/system/ollama.service
[Unit]
Description=Ollama Service
After=network-online.target
[Service]
ExecStart=/usr/local/bin/ollama serve
User=ollama
Group=ollama
Restart=always
RestartSec=3
Environment="PATH=/opt/postgresql/bin:/home/<USER>/.juliaup/bin:/home/<USER>/.modular/pkg/packages.modular.com_mojo/bin:/home/<USER>/miniconda3/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/share/Modules/bin:/usr/lib64/ccache:/usr/local/bin:/usr/bin:/usr/local/sbin:/usr/sbin:/usr/local/cuda/bin:/usr/local/go/bin"
Environment="OLLAMA_HOST=0.0.0.0:8234"
Environment="OLLAMA_ORIGINS=*"
Environment="OLLAMA_KEEP_ALIVE=-1"
Environment="OLLAMA_NUM_PARALLEL=4"
Environment="OLLAMA_FLASH_ATTENTION=1"
Environment="OLLAMA_KV_CACHE_TYPE=q4_0"
Environment="OLLAMA_LOAD_TIMEOUT=50"

[Install]
WantedBy=default.target


\#Donwload
ollama pull llama3.1

#### Default location
/usr/share/ollama
After 0.5.8
/home/<USER>/.ollama

#### Default Libaray
/usr/local/lib/ollama

#### Binary location
/usr/local/bin/ollama

\#run
ollama run llama3.1
ollama run llama3.1:70b
ollama ps
\#to top model
crl+x or exit after 5min or restart ollama service
\#customize it
export OLLAMA_HOST=0.0.0.0:8234
ollama show llama2:latest --modelfile > myllama2.modelfile
ollama create myllama2 --file myllama2.modelfile
ollama run myllama2
```

## Customize timeout ! it is not required after 3.10

```Python
# solution to load large llm with ollama
https://github.com/ollama/ollama/issues/4131
\#brew install cmake
\#brew install go
git clone https://github.com/ollama/ollama.git
modify /opt/workspace/ollama/llm/server.go  -- find stallDuration
 	stallDuration := 50 * time.Minute            // If no progress happens 
 	finalLoadDuration := 50 * time.Minute        // After we hit 100%, give the runner more time to come online 
go generate ./...
sudo go build .
create symbolic link under 
lrwxrwxrwx 1 root   root        28 Aug  1 18:59 ollama -> /opt/workspace/ollama/ollama
(base) [raysheng@MONSTER:/usr/local/bin]$
```

## copy model to the other name
ollama cp qwen2.5 qwen2.5-coder-14b-instruct

## import other model into ollama
https://github.com/ollama/ollama/blob/main/docs/modelfile.md

```JavaScript
# import gguf into ollma
1. create Modelfile
cat /databank/workspace/aibase/Qwen2-Math/Modelfile
FROM /opt/workspace/aibase/Qwen2-Math/Qwen2-Math-7B-Instruct-Q4_K_M.gguf
2. import gguf
ollama create qwen2-math -f /databank/workspace/aibase/Qwen2-Math/Modelfile
```

## upgrade
https://github.com/ollama/ollama/blob/main/docs/development.md

<span style="background:#40a9ff">backup /etc/systemd/system/ollama.service as it will be overwritten</span>
```
# Upgrade
#We dont use this way as it will overwrite systemctl service file
#and not fit for my customize version

!!!backup /etc/systemd/system/ollama.service as it will be overwritten.
curl -fsSL https://ollama.com/install.sh | sh

#we use
sudo systemctl stop ollama
git clone https://github.com/ollama/ollama.git

#for alreay ollama previous version existing
1. confirm current version
git describe --tag
git log --oneline
1. check what kind of changes made in local
2. git stash
backup llm/server.go (I changed the timeout value)
4. git pull
backup this new llm/server.go to server.go.new
5. git describe --tag
6. git stash apply <- if required.
diff server.go server.go.new (confirm my change is there)
sudo go generate ./...
sudo go build .

update it if you want to include version information
[13:05:06]ray@jethome64:/opt/app/ollama/version$ ls
version.go
[13:05:06]ray@jethome64:/opt/app/ollama/version$ cat version.go
package version

var Version string = "0.4.2"


```
 since v0.5.5
 ```
 make clean
 make -j 8
 go build .
```
since v0.5.12
https://github.com/ollama/ollama/blob/main/docs/development.md
Looks not working
<span style="background:#ff4d4f">CUDA error: the provided PTX was compiled with an unsupported toolchain.</span>
```shell
# make -f Makefile.sync clean sync
cmake -B build -S .
cmake --build build -j8
sudo cmake --build build --target install
go build .

Or Just
cmake -B build 
cmake --build build -j8
sudo cmake --build build --target install
go build .

```

We also can download prebuild version for jetpack6
https://github.com/ollama/ollama/blob/main/docs/linux.md
### switch between versions
```
git tag -l
git reset --hard tags/v0.4.2
git describe --tags
git pull origin main
git describe --tags

```

## Change max context windows

```
> ollama run llama3.1
> /set parameter num_ctx 16384
> /save llama3.1
> then restart it and check 
> llama_new_context_with_model: n_ctx in journalctl log
```
Since 0.5.13
OLLAMA_CONTEXT_LENGTH 
The default context length can now be set with a new OLLAMA_CONTEXT_LENGTH environment variable. For example, to set the default context length to 8K, use: OLLAMA_CONTEXT_LENGTH=8192 ollama serve
##  OS permission issue fix
   if get chmod permission error. what you do is
   1. make sure
   drwxr-x---   4 <USER> <GROUP>  4096 Jul 31 21:48 ollama
   2.
   vi /etc/group
  ollama:x:982:root,raysheng 
```
```


  ## Run models in hugggingface 
  ollama run hf.co/{username}/{repository}

## Ollama - Meta Llama 3.2 vision models (11B & 90B)
https://ollama.com/blog/llama3.2-vision
```
Get started 
Download Ollama 0.4, then run:
ollama run llama3.2-vision
 
To run the larger 90B model: 
ollama run llama3.2-vision:90b
 
To add an image to the prompt, drag and drop it into the terminal, or add a path to the image to the prompt on Linux.
 
Note: Llama 3.2 Vision 11B requires least 8GB of VRAM, and the 90B model requires at least 64 GB of VRAM.
python
```
```python
import ollama

response = ollama.chat(
    model='llama3.2-vision',
    messages=[{
        'role': 'user',
        'content': 'What is in this image?',
        'images': ['image.jpg']
    }]
)

print(response)
```

curl
```c
curl http://localhost:11434/api/chat -d '{
  "model": "llama3.2-vision",
  "messages": [
    {
      "role": "user",
      "content": "what is in this image?",
      "images": ["<base64-encoded image data>"]
    }
  ]
}'
```

## Other QA

```
https://github.com/ollama/ollama/blob/main/docs/faq.md

```
## API document
```
https://github.com/ollama/ollama/blob/main/docs/api.md

```

## K/V Context Cache
[[Bringing KV Context Quantisation to Ollama]]
```
Experimental: new flag to set KV cache quantization to 4-bit (q4_0), 8-bit (q8_0) or 16-bit (f16). This reduces VRAM requirements for longer context windows.
To enable for all models, use OLLAMA_FLASH_ATTENTION=1 OLLAMA_KV_CACHE_TYPE=q4_0 ollama serve
Note: in the future flash attention will be enabled by default where available, with kv cache quantization available on a per-model basis
Thank you @sammcj for the contribution in in #7926
```
![[Pasted image 20241206221741.png]]
# Regarding Embedding to RAG

```JavaScript
1. word embedding
2. sentence embedding
3. LLM embedding
https://github.com/mcgill-nlp/llm2vec
pip install llm2vec
pip install flash-attn --no-build-isolation
```

# 量化local AI模型
考虑
[[microsoftVPTQ VPTQ, A Flexible and Extreme low-bit quantization algorithm]]


# Gitlab related
## Git Lab Setup
```
# gitlab access token:
glpat-pMozs3zxsP4_wGJoLmK-

# gitlab doc-hub access token
https://**************:8243/sbi-ai-portal/doc-hub
**************************

# get project related info
curl -k -H "PRIVATE-TOKEN:**************************" "https://**************:8243/api/v4/projects?search=doc-hub"

# clone the project
git  -c http.sslVerify=false clone https://ray:**************************@**************:8243/sbi-ai-portal/doc-hub.git

gitlab-remote://**************:8243/sbi-ai-portal?project=6&ref=main

# after clone
1. settings.json
       "gitlab.ignoreCertificateErrors": true,
       "http.proxyStrictSSL": false,
       "gitlab.instanceUrl": "https://**************:8243",
       "gitlab.token": "**************************"
   
2. disable sslVerify
git config --global http.sslVerify false
or
git config --global http.https://**************:8243.sslVerify false

# Remove global SSL verification setting
git config --global --unset http.sslVerify

# Or remove for specific host
git config --global --unset http.https://**************:8243.sslVerify

3. git pull to confirm
   
```

## Git Command

```JavaScript
<<< Some git notes >>>
在VS Code里把一个从其他github repo推送到你自己的github空间里，你可以遵循以下步骤：
克隆项目: 在你的本地环境中克隆你想要复制的项目。这可以通过在命令行中运行以下命令完成：

git clone <其他github repo的URL>
创建新的GitHub仓库: 在你的GitHub账户中创建一个新的仓库。在创建时，不要选择初始化README，.gitignore文件或许可证，因为这些文件可能已经在你刚刚克隆的仓库中存在。
更改远程URL: 切换到你的本地克隆仓库，然后在命令行中运行以下命令，将远程仓库URL更改为你的新GitHub仓库的URL：
git remote set-url origin <你的github repo的URL>
推送到新仓库: 现在你可以将你的更改推送到新的GitHub仓库。这可以通过运行以下命令完成：
git push -u origin master
or
git push -u origin main
（如果你正在使用的分支不是master分支，那么将master替换为你正在使用的分支的名称）
以上步骤都可以在VS Code的终端中完成，VS Code的终端可以通过顶部菜单的终端(Terminal) -> 新建终端(New Terminal)打开。
sometimes it needs authentication, then use below way ,as you are using mobile authentication key, we have to use token here
To clone to my owner github repo
1. create personal token
*********************************************************************************************
2. create one repo in github and change remote repo to my repo
git remote set-url origin https://github.com/netcaster1/VisualGLM-6B
3. push to my repo
git push -u origin main  <- main or master
Username for 'https://github.com': <EMAIL>
Password for 'https://<EMAIL>@github.com':  <<previous token created>>
Enumerating objects: 252, done.
\#to check current remote url
git remote -v
F:\ResearchDirection\AI\flutter\flutter_network>git remote -v
origin  https://github.com/netcaster1/flutter_network (fetch)
origin  https://github.com/netcaster1/flutter_network (push)

\#refresh from imooc -> netcaster
rm -rf xxxx
git clone immoc-github
git remote set-url origin https://github.com/netcaster1/chatgpt_flutter.git
git push -f origin main
another way
https://git.imooc.com/coding-672/chatgpt_flutter.git
git pull origin main
From https://git.imooc.com/coding-672/chatgpt_flutter
 * branch            main       -> FETCH_HEAD
   7047f9b..2c7fa74  main       -> origin/main
error: Your local changes to the following files would be overwritten by merge:
        lib/main.dart
        lib/pages/bottom_navigator.dart
        pubspec.lock
        pubspec.yaml
Please commit your changes or stash them before you merge.
Aborting
Updating 7047f9b..2c7fa74

merge then manually
git stash <- save changed
git pull  <- pull 
git stash pop <- apply change to new one
git remote set-url origin https://github.com/netcaster1/chatgpt_flutter.git
git push origin main


# Show all settings
git config --global http.sslVerify false
git config --global credential.helper store
git config --list
git config --list --show-origin

#stash
# First, see what's in each stash
git stash show -p stash@{0}
git stash show -p stash@{1}

# If stash@{1} is your latest changes, apply it
git stash apply stash@{1}

# If you want to clean up stashes after confirming
git stash drop stash@{0}  # drops the first stash
git stash drop stash@{1}  # drops the second stash

# Or clear all stashes
git stash clear

download git repo from imooc
git clone https://git.imooc.com/coding-672/login_sdk.git
git clone https://git.imooc.com/coding-672/chat_message.git
此时发现只有.git folder exists,遇到的问题可能是由于远程仓库的默认分支（通常是 master 或 main）不存在，因此在 clone 时无法检出。
solution:
git branch -a   # 查看远程仓库的所有分支
git checkout branch_name # 从中选择一个分支进行检出
in my case: It works. after execute checkout, files coming out.
F:\ResearchDirection\AI\flutter\login_sdk>git branch -a
  remotes/origin/main
F:\ResearchDirection\AI\flutter\login_sdk>git checkout main
Switched to a new branch 'main'
branch 'main' set up to track 'origin/main'.
dart fix --apply .
flutter pub get
flutter clean
flutter build apk

login UI
Registration
course-flag：fg
userName： netcaster
password：12345#
imoocId： 7809138
id: 7809138
orderId： 8329  #订单ID后四位
courseFlag：fg
https://api.devio.org/uapi/swagger-ui.html#/Account/registrationUsingPOST
curl -X POST "https://api.devio.org/uapi/user/registration?userName=netcaster&password=12345%23&imoocId=7809138&orderId=8329&courseFlag=fg" -H "accept: */*" -H "course-flag: fg"
{
  "code": 0,
  "msg": "registration success."
}
Login：
course-flag ： fg
userName： netcaster
password：12345#


```

# Migration to AWS

idea is tar miniconda3 and all pg and project file to remote and create symlink 

## some python libaray issue after migration
```
(mom) mlusr@SBIHD-GenAI-dev-LVM-ML01:/opt/app/mom$ python modeltest.py
Traceback (most recent call last):
  File "/databank/app/mom/modeltest.py", line 33, in <module>
    import psycopg2
  File "/home/<USER>/miniconda3/envs/mom/lib/python3.10/site-packages/psycopg2/__init__.py", line 51, in <module>
    from psycopg2._psycopg import (                     # noqa
ImportError: libpq.so.5: cannot open shared object file: No such file or directory

sudo apt-get install libpq5 libpq-dev
```

## database migration
```bash

sudo systemctl stop mom_ai.service
sudo systemctl stop mom_emb.service
sudo systemctl stop pg16.service
tar zcvf postgresql_backup_$(date +%Y%m%d_%H%M%S).tar.gz --dereference bin lib pgvector data conf

sudo systemctl start pg16.service
sudo systemctl start mom_emb.service
sudo systemctl start mom_ai.service

sudo systemctl status pg16.service
sudo systemctl status mom_emb.service
sudo systemctl status mom_ai.service

cd /usr/share/postgresql/16
backup above all in above folder

scp -P 60001 -rp Key4ML.pem azusr@127.0.0.1:/tmp
#setup config file and make sure config  file and keyfile only owner can access
Host jumpserver
    HostName ************
    User ubuntu
    IdentityFile ~/.ssh/Key4ML.pem
    ForwardAgent yes

Host ml01
    HostName ********
    User ubuntu
    ProxyJump jumpserver
    IdentityFile ~/.ssh/Key4ML.pem

scp -rp xxx ml01:/tmp
cd /databank/database/16
mkdir share
tar zxvf /databank/install/migration/pgshare.tar.gz
cd /usr/share
sudo mkdir postgresql
cd postgresql/
sudo ln -s /databank/database/16/share 16
ubuntu@SBIHD-GenAI-dev-LVM-ML01:/usr/share/postgresql$ ls
16
ubuntu@SBIHD-GenAI-dev-LVM-ML01:/usr/share/postgresql$ ls -lrt
total 0
lrwxrwxrwx 1 root root 27 Nov  6 16:42 16 -> /databank/database/16/share


postgres@SBIHD-GenAI-dev-LVM-ML01:/opt/postgresql$ pg_ctl start -D /opt/postgresql/data
waiting for server to start....2024-11-06 16:43:52.580 JST [41731] FATAL:  could not load server certificate file "/etc/ssl/certs/ssl-cert-snakeoil.pem": No such file or directory
2024-11-06 16:43:52.581 JST [41731] LOG:  database system is shut down
 stopped waiting
pg_ctl: could not start server
Examine the log output.
<<solution>>
sudo apt-get install ssl-cert
# confirm postgresql.conf setup
# OR Option 2: Point to correct certificate locations
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'
# confirm permission
sudo ls -l /etc/ssl/certs/ssl-cert-snakeoil.pem
sudo ls -l /etc/ssl/private/ssl-cert-snakeoil.key
sudo chown postgres:postgres /etc/ssl/certs/ssl-cert-snakeoil.pem
sudo chown postgres:postgres /etc/ssl/private/ssl-cert-snakeoil.key
sudo chmod 600 /etc/ssl/private/ssl-cert-snakeoil.key
sudo ls -l /etc/ssl/certs/ssl-cert-snakeoil.pem
sudo ls -l /etc/ssl/private/ssl-cert-snakeoil.key

postgres@SBIHD-GenAI-dev-LVM-ML01:~$ ls -lrt /etc/ssl/private/ssl-cert-snakeoil.key
ls: cannot access '/etc/ssl/private/ssl-cert-snakeoil.key': Permission denied

#let us move key to some other place
sudo mv /etc/ssl/private/ssl-cert-snakeoil.key /databank/database/16/ssl

2024-11-06 16:56:32.494 JST [42091] LOG:  listening on IPv4 address "0.0.0.0", port 5432
2024-11-06 16:56:32.495 JST [42091] FATAL:  could not create lock file "/var/run/postgresql/.s.PGSQL.5432.lock": No such file or directory
2024-11-06 16:56:32.502 JST [42091] LOG:  database system is shut down

mkdir -p /opt/postgresql/run
chown postgres:postgres /opt/postgresql/run
chmod 2775 /opt/postgresql/run
Then update postgresql.conf:
unix_socket_directories = '/opt/postgresql/run'
cd /var/run
sudo ln -s /opt/postgresql/run postgresql

#for test
pg_ctl start -D /opt/postgresql/data
pg_ctl stop -m f



```

## Mom_ai migration issue
```

Nov 06 18:13:40 SBIHD-GenAI-dev-LVM-ML01 python[46327]: /opt/app/miniconda3/envs/mom/lib/python3.10/site-packages/pydub             /utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
Nov 06 18:13:40 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg,              but may not work", RuntimeWarning)
Nov 06 18:13:47 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Special tokens have been added in the vocabulary, make sure the              associated word embeddings are fine-tuned or trained.
Nov 06 18:14:13 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Special tokens have been added in the vocabulary, make sure the associated wtuned or trained.
Nov 06 18:14:13 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Special tokens have been added in the vocabulary, make sure the associated wtuned or trained.
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Traceback (most recent call last):
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 1426, in <module>
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 1359, in main
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 1326, in update_in_progress_to_
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 597, in scan_convert_status_mat
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "/opt/app/miniconda3/envs/mom/lib/python3.10/site-packages/psycopg2/_n connect
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:     conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]: psycopg2.OperationalError: connection to server at "********", port 5432 faionf entry for host "********", user "momusr", database "momdb", SSL encryption
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]: connection to server at "********", port 5432 failed: FATAL:  no pg_hba.conf7", user "momusr", database "momdb", no encryption


```
Soltuon
```
sudo apt-get install ffmpeg

# edit pg_hba.conf
host    all             all             ********/24             md5

```
# NodeJS debug in prod mode
```
NODE_ENV=production DEBUG=* npx next start -p 8505
NODE_ENV=development npx next start  -p 8505

```

# Speech Text Priority
```
relevance_rating 

https://**************:8243/sbi-ai-portal/interface-hub/-/blob/main/arpep_db/pg/reference/momdb-ddl.sql?ref_type=heads

create table mom_audio_speaker_texts (
  meeting_id varchar(64) not null,
  spoken_at varchar(12) not null,
  spoken_end varchar(12) not null,
  speaker_id varchar(64) not null,
  speaker_text varchar(512) not null,
  speaker_text_modified varchar(512),
  speaker_alias varchar(64),
  speaker_alias_modified boolean not null default false,
  relevance_rating varchar(16) not null default 'normal',
  embedding vector(768),
  created_by int not null,
  created_at timestamp with time zone not null default current_timestamp,
  updated_by int not null,
  updated_at timestamp with time zone not null default current_timestamp,
  primary key (meeting_id, spoken_at)
);
comment on table mom_audio_speaker_texts is 'meeting minute audio speaker texts table';
comment on column mom_audio_speaker_texts.spoken_at is 'when material_type is audio, format: hh:mm:ss with time zone; when transcript, line no';
comment on column mom_audio_speaker_texts.spoken_end is 'when material_type is audio, format: hh:mm:ss with time zone; when transcript, length of speaker_text';
comment on column mom_audio_speaker_texts.speaker_alias is 'int-(user_id) for internal users; ext-(user_id) for external users';
comment on column mom_audio_speaker_texts.relevance_rating is 'one of [vulgarity, irrelevant, normal, important, core], vulgarity and irrelevant can be ignored, important and core must be considered';




```

# Amazon Bedrock Claude
## Reference
https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters-anthropic-claude-text-completion.html
https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters-anthropic-claude-messages.html
https://docs.anthropic.com/en/api/claude-on-amazon-bedrock
https://medium.com/@woyera/how-to-use-claude-llm-models-through-aws-bedrock-0ec2a6a7612c
https://aws.amazon.com/cn/bedrock/claude/
https://github.com/anthropics/anthropic-sdk-python/blob/main/examples/bedrock.py


## Api reference code
https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html

## Source location
/opt/workspace/app/aws-doc-sdk-examples/python/example_code/bedrock-runtime/models/anthropic_claude/converse.py


## Role and Policy Setup
### Create Policy
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "aws-marketplace:Subscribe"
            ],
            "Resource": "*",
            "Condition": {
                "ForAnyValue:StringEquals": {
                    "aws-marketplace:ProductId": [
                        "prod-5oba7y7jpji56",
                        "prod-m5ilt4siql27k",
                        "prod-cx7ovbu5wex7g"
                    ]
                }
            }
        },
        {
            "Effect": "Allow",
            "Action": [
                "aws-marketplace:Unsubscribe",
                "aws-marketplace:ViewSubscriptions"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": [
				"arn:aws:bedrock:ap-northeast-1:060795926728:inference-profile/apac.anthropic.claude-3-sonnet-20240229-v1:0",
				"arn:aws:bedrock:ap-northeast-1:060795926728:inference-profile/apac.anthropic.claude-3-5-sonnet-20240620-v1:0",
				"arn:aws:bedrock:ap-northeast-1:060795926728:inference-profile/apac.anthropic.claude-3-haiku-20240307-v1:0",
				"arn:aws:bedrock:ap-northeast-1:060795926728:inference-profile/apac.anthropic.claude-3-sonnet-20240229-v1:0",
				"arn:aws:bedrock:ap-northeast-1:060795926728:inference-profile/apac.anthropic.claude-3-5-sonnet-20240620-v1:0",
				"arn:aws:bedrock:ap-northeast-1:060795926728:inference-profile/apac.anthropic.claude-3-haiku-20240307-v1:0",
				"arn:aws:bedrock:ap-northeast-1:060795926728:inference-profile/apac.anthropic.claude-3-5-sonnet-20241022-v2:0",
				"arn:aws:bedrock:ap-northeast-1::foundation-model/anthropic.claude-3-haiku-20240307-v1:0",
				"arn:aws:bedrock:ap-northeast-2::foundation-model/anthropic.claude-3-haiku-20240307-v1:0",
				"arn:aws:bedrock:ap-southeast-1::foundation-model/anthropic.claude-3-haiku-20240307-v1:0",
				"arn:aws:bedrock:ap-southeast-2::foundation-model/anthropic.claude-3-haiku-20240307-v1:0",
				"arn:aws:bedrock:ap-south-1::foundation-model/anthropic.claude-3-haiku-20240307-v1:0",
				"arn:aws:bedrock:ap-northeast-1::foundation-model/anthropic.claude-3-5-sonnet-20240620-v1:0",
				"arn:aws:bedrock:ap-northeast-2::foundation-model/anthropic.claude-3-5-sonnet-20240620-v1:0",
				"arn:aws:bedrock:ap-southeast-1::foundation-model/anthropic.claude-3-5-sonnet-20240620-v1:0",
				"arn:aws:bedrock:ap-southeast-2::foundation-model/anthropic.claude-3-5-sonnet-20240620-v1:0",
				"arn:aws:bedrock:ap-northeast-1::foundation-model/anthropic.claude-3-5-sonnet-20241022-v2:0",
				"arn:aws:bedrock:ap-northeast-2::foundation-model/anthropic.claude-3-5-sonnet-20241022-v2:0",
				"arn:aws:bedrock:ap-northeast-3::foundation-model/anthropic.claude-3-5-sonnet-20241022-v2:0",
				"arn:aws:bedrock:ap-southeast-1::foundation-model/anthropic.claude-3-5-sonnet-20241022-v2:0",
				"arn:aws:bedrock:ap-southeast-2::foundation-model/anthropic.claude-3-5-sonnet-20241022-v2:0",
				"arn:aws:bedrock:ap-southeast-3::foundation-model/anthropic.claude-3-5-sonnet-20241022-v2:0",
				"arn:aws:bedrock:ap-south-1::foundation-model/anthropic.claude-3-5-sonnet-20240620-v1:0"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:ListFoundationModels"
            ],
            "Resource": "*"
        }
    ]
}
```
### Create IAM Role:
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "bedrock.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
#### attach previous policy to this role
#### directly grant policy to this user
#### create acccess key -> local code
- Access key ID: ********************
- Secret access key: iaDpBgRFxHS3R/fVHamuDCAdK/xYQrzWnTIs0wdI
- Region: ap-northeast-1

aws sts get-caller-identity  

## Programming and Code
pip install -U "anthropic[bedrock]"
from anthropic import AnthropicBedrock

client = AnthropicBedrock(
    # Authenticate by either providing the keys below or use the default AWS credential providers, such as
    # using ~/.aws/credentials or the "AWS_SECRET_ACCESS_KEY" and "AWS_ACCESS_KEY_ID" environment variables.
    aws_access_key="********************",
    aws_secret_key="iaDpBgRFxHS3R/fVHamuDCAdK/xYQrzWnTIs0wdI",
    # Temporary credentials can be used with aws_session_token.
    # Read more at https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp.html.
    # aws_session_token="<session_token>",
    # aws_region changes the aws region to which the request is made. By default, we read AWS_REGION,
    # and if that's not present, we default to us-east-1. Note that we do not read ~/.aws/config for the region.
    aws_region="ap-northeast-1",
)

message = client.messages.create(
    model="apac.anthropic.claude-3-5-sonnet-20240620-v1:0",
    max_tokens=4096,
    messages=[{"role": "user", "content": "Hello, world"}]
)
print(message.content)

```
<<output>>
[TextBlock(text='Hello! How can I assist you today? Feel free to ask any questions or let me know if you need help with anything.', type='text')]

```
## Bedrock Agent Usage
https://learn.deeplearning.ai/courses/serverless-agentic-workflows-with-amazon-bedrock/lesson/4/performing-calculations
![[Copy of Agent basics.ipynb]]

### Create Policy
```
name：AmazonBedrockAgentBedrockFoundationModelPolicy_3UPW6XNYU3U

{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "BedrockFullAccess",
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:CreateAgent",
                "bedrock:CreateAgentAlias",
                "bedrock:PrepareAgent",
                "bedrock:DeleteAgent",
                "bedrock:GetAgent",
                "bedrock:ListAgents",
                "bedrock:UpdateAgent"
            ],
            "Resource": [
                "*"
            ]
        }
    ]
}

OR

{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Sid": "BedrockFullAccess",
			"Effect": "Allow",
			"Action": [
				"bedrock:*"
			],
			"Resource": [
				"*"
			]
		}
	]
}
```
### Create Role
```
Name: AmazonBedrockExecutionRoleForAgents_TT39E4CY4MH
Attach previous policy to this new Role
```
### Assign Role to ServiceUser
Here we are not directly assign role to serveruser but pass role.
```
select serviceUser -> Find 'Add permissions' dropdown list -> Select ‘Create inline policy'

Name: Amazon_Agent_Role

{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Sid": "BedrockFullAccess",
			"Effect": "Allow",
			"Action": [
				"bedrock:InvokeModel",
				"bedrock:InvokeAgent",
				"bedrock:CreateAgent",
				"bedrock:CreateAgentAlias",
				"bedrock:CreateAgentActionGroup",
				"bedrock:GetAgentActionGroup",
				"bedrock:UpdateAgentActionGroup",
				"bedrock:PrepareAgent",
				"bedrock:DeleteAgent",
				"bedrock:GetAgent",
				"bedrock:GetAgentAlias",
				"bedrock:UpdateAgentAlias",
				"bedrock:ListAgents",
				"bedrock:UpdateAgent"
			],
			"Resource": [
				"*"
			]
		},
		{
			"Sid": "Stmt1464440182000",
			"Effect": "Allow",
			"Action": [
				"lambda:*"
			],
			"Resource": [
				"*"
			]
		},
		{
			"Sid": "IAMPassRole",
			"Effect": "Allow",
			"Action": "iam:PassRole",
			"Resource": [
				"arn:aws:iam::060795926728:role/service-role/AmazonBedrockExecutionRoleForAgents_TT39E4CY4MH",
				"arn:aws:iam::060795926728:role/service-role/agent_action_group_handler-role-rrgfw9b7"
			]
		}
	]
}
```

More Lazy One :) 
```
{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Sid": "BedrockFullAccess",
			"Effect": "Allow",
			"Action": [
				"bedrock:*"
			],
			"Resource": [
				"*"
			]
		},
		{
			"Sid": "Stmt1464440182000",
			"Effect": "Allow",
			"Action": [
				"lambda:*"
			],
			"Resource": [
				"*"
			]
		},
		{
			"Sid": "IAMPassRole",
			"Effect": "Allow",
			"Action": "iam:PassRole",
			"Resource": [
				"arn:aws:iam::060795926728:role/service-role/AmazonBedrockExecutionRoleForAgents_TT39E4CY4MH",
				"arn:aws:iam::060795926728:role/service-role/agent_action_group_handler-role-rrgfw9b7"
			]
		}
	]
}
```
### Code Sample
Blew is code location related to AWS Bedrock test code
/opt/app/mom/LLMCheckTest.ipynb
```python
import boto3
import json

AWS_KEY = "********************"
AWS_SECRET_KEY = "iaDpBgRFxHS3R/fVHamuDCAdK/xYQrzWnTIs0wdI"

bedrock_agent = boto3.client(service_name='bedrock-agent', 
region_name='ap-northeast-1', 
aws_access_key_id=AWS_KEY, 
aws_secret_access_key=AWS_SECRET_KEY)

roleArn="arn:aws:iam::060795926728:role/service-role/AmazonBedrockExecutionRoleForAgents_TT39E4CY4MH"

create_agent_response = bedrock_agent.create_agent(
    agentName='mugs-customer-support-agent',
    foundationModel='anthropic.claude-3-5-sonnet-20240620-v1:0',
    instruction="""You are an advanced AI agent acting as a front line customer support agent.""",
    agentResourceRoleArn=roleArn
)

agentId = create_agent_response['agent']['agentId']

from helper import *

wait_for_agent_status(
    agentId=agentId, 
    targetStatus='NOT_PREPARED'
)

bedrock_agent.prepare_agent(
    agentId=agentId
)

wait_for_agent_status(
    agentId=agentId, 
    targetStatus='PREPARED'
)

create_agent_alias_response = bedrock_agent.create_agent_alias(
    agentId=agentId,
    agentAliasName='MyAgentAlias',
)

agentAliasId = create_agent_alias_response['agentAlias']['agentAliasId']

wait_for_agent_alias_status(
    agentId=agentId,
    agentAliasId=agentAliasId,
    targetStatus='PREPARED'
)

bedrock_agent_runtime = boto3.client(service_name='bedrock-agent-runtime', region_name=region_name)

import uuid

message = "Hello, I bought a mug from your store yesterday, and it broke. I want to return it."

sessionId = str(uuid.uuid4())

invoke_agent_response = bedrock_agent_runtime.invoke_agent(
    agentId=agentId,
    agentAliasId=agentAliasId,
    inputText=message,
    sessionId=sessionId,
    endSession=False,
    enableTrace=True,
)

event_stream = invoke_agent_response["completion"]

for event in event_stream:
    print(event)

message = "Hello, I bought a mug from your store yesterday, and it broke. I want to return it."

sessionId = str(uuid.uuid4())
invoke_agent_and_print(
    agentAliasId=agentAliasId,
    agentId=agentId,
    sessionId=sessionId,
    inputText=message,
    enableTrace=True,
)


```
### Lambda Function 
#### Agent Side
```python
update_agent_action_group_response = bedrock_agent.update_agent_action_group(
    actionGroupName='customer-support-actions',
    actionGroupState='ENABLED',
    actionGroupId=actionGroupId,
    agentId=agentId,
    agentVersion='DRAFT',
    actionGroupExecutor={
        'lambda': lambda_function_arn
    },
    functionSchema={
        'functions': [
            {
                'name': 'customerId',
                'description': 'Get a customer ID given available details. At least one parameter must be sent to the function. This is private information and must not be given to the user.',
                'parameters': {
                    'email': {
                        'description': 'Email address',
                        'required': False,
                        'type': 'string'
                    },
                    'name': {
                        'description': 'Customer name',
                        'required': False,
                        'type': 'string'
                    },
                    'phone': {
                        'description': 'Phone number',
                        'required': False,
                        'type': 'string'
                    },
                }
            },            
            {
                'name': 'sendToSupport',
                'description': 'Send a message to the support team, used for service escalation. ',
                'parameters': {
                    'custId': {
                        'description': 'customer ID',
                        'required': True,
                        'type': 'string'
                    },
                    'purchaseId': {
                        'description': 'the ID of the purchase, can be found using purchaseSearch',
                        'required': True,
                        'type': 'string'
                    },
                    'supportSummary': {
                        'description': 'Summary of the support request',
                        'required': True,
                        'type': 'string'
                    },
                }
            },
            {
                'name': 'purchaseSearch',
                'description': """Search for, and get details of a purchases made.  Details can be used for raising support requests. You can confirm you have this data, for example "I found your purchase" or "I can't find your purchase", but other details are private information and must not be given to the user.""",
                'parameters': {
                    'custId': {
                        'description': 'customer ID',
                        'required': True,
                        'type': 'string'
                    },
                    'productDescription': {
                        'description': 'a description of the purchased product to search for',
                        'required': True,
                        'type': 'string'
                    },
                    'purchaseDate': {
                        'description': 'date of purchase to start search from, in YYYY-MM-DD format',
                        'required': True,
                        'type': 'string'
                    },
                }
            }
        ]
        
    }
)

actionGroupId = update_agent_action_group_response['agentActionGroup']['actionGroupId']

wait_for_action_group_status(
    agentId=agentId,
    actionGroupId=actionGroupId
)

prepare_agent_response = bedrock_agent.prepare_agent(
    agentId=agentId
)

wait_for_agent_status(
    agentId=agentId,
    targetStatus='PREPARED'
)

bedrock_agent.update_agent_alias(
    agentId=agentId,
    agentAliasId=agentAliasId,
    agentAliasName='test',
)

wait_for_agent_alias_status(
    agentId=agentId,
    agentAliasId=agentAliasId,
    targetStatus='PREPARED'
)

sessionId = str(uuid.uuid4())
message = """<EMAIL> - I bought a mug 10 weeks ago and now it's broken. I want a refund."""

invoke_agent_and_print(
    agentId=agentId,
    agentAliasId=agentAliasId,
    inputText=message,
    sessionId=sessionId,
    enableTrace=True
)

```
  #### Key Is permision setup in Lambda Function Side
```
Go into Lambda function
Select Configuration tab
Select Permission menu item
Scroll down to Resource-based Policy Statements and click Add Permissions button
Click AWS Service radio button
Choose Other from the Service dropdown
Enter anything for Statement ID
Enter bedrock.amazonaws.com for the Principal
Enter your Bedrock Agent's ARN as the Source ARN
For Example:  arn:aws:bedrock:ap-northeast-1:060795926728:agent/*
Select lambda:InvokeFunctionas the Action
Click Save
```

# Bedrock Knowledege Chatbot 

## Knowledege Setup

```json

{
  "type": "KNOWLEDGE_BASE",
  "knowledgeBaseConfiguration": {
    "knowledgeBaseId": "K0Y4Y1R5NY",
    "modelArn": "arn:aws:bedrock:ap-northeast-1::foundation-model/anthropic.claude-3-5-sonnet-20240620-v1:0",
    "retrievalConfiguration": {
      "vectorSearchConfiguration": {
        "numberOfResults": 5,
        "rerankingConfiguration": {
          "type": "BEDROCK_RERANKING_MODEL",
          "bedrockRerankingConfiguration": {
            "modelConfiguration": {
              "modelArn": "arn:aws:bedrock:ap-northeast-1::foundation-model/cohere.rerank-v3-5:0"
            }
          }
        }
      }
    },
    "generationConfiguration": {
      "inferenceConfig": {
        "textInferenceConfig": {
          "temperature": 0,
          "topP": 1,
          "maxTokens": 2048,
          "stopSequences": [
            "\nObservation"
          ]
        }
      },
      "guardrailConfiguration": {
        "guardrailId": "tne2t3i3gw2c",
        "guardrailVersion": "DRAFT"
      }
    },
    "orchestrationConfiguration": {
      "inferenceConfig": {
        "textInferenceConfig": {
          "temperature": 0,
          "topP": 1,
          "maxTokens": 2048,
          "stopSequences": [
            "\nObservation"
          ]
        }
      }
    }
  }
}

```
## Create agent
jethome64
/opt/app/mom/LLMCheckTest.ipynb

Agent -> ActionGroup（lambda function again) -> assign kb id -> Guardrail 
All Done.  Then create Agent Alias and Assign this working draft version

## Create lambda function
```javascript
// bedrock-chat-lambda/index.mjs
import { BedrockAgentRuntimeClient, InvokeAgentCommand } from "@aws-sdk/client-bedrock-agent-runtime";
import { randomUUID } from 'crypto';

export const handler = async (event) => {
  console.log('Handler started');
  
  try {
    console.log('Received event:', JSON.stringify(event, null, 2));
    
    const bedrockClient = new BedrockAgentRuntimeClient({ 
      region: "ap-northeast-1"
    });

    // Parse the body with session handling
    let userMessage, sessionId;
    let isNewSession = false;  // 初始化 isNewSession

    try {
      if (typeof event.body === 'string') {
        const parsedBody = JSON.parse(event.body);
        userMessage = parsedBody.message;
        sessionId = parsedBody.sessionId;
      } else if (event.body && event.body.message) {
        userMessage = event.body.message;
        sessionId = event.body.sessionId;
      }
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      throw new Error('Invalid request body format');
    }

    if (!userMessage) {
      throw new Error('Message is required');
    }

    // 只在需要生成新 sessionId 时设置 isNewSession 为 true
    if (!sessionId) {
      sessionId = randomUUID();
      isNewSession = true;
      console.log('Generated new sessionId:', sessionId);
    } else {
      console.log('Using existing sessionId:', sessionId);
    }

    const params = {
      agentId: "5FIBRD0OOP",
      agentAliasId: "OKYX0LAT0S",
      sessionId: sessionId,
      inputText: userMessage
    };

    console.log('Agent parameters:', JSON.stringify(params, null, 2));

    const command = new InvokeAgentCommand(params);
    const response = await bedrockClient.send(command);

    console.log('Agent response received');

    let fullResponse = '';
    for await (const chunk of response.completion) {
      if (chunk.chunk?.bytes) {
        const chunkText = new TextDecoder().decode(chunk.chunk.bytes);
        fullResponse += chunkText;
      }
    }

    console.log('Response processed successfully');

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*"
      },
      body: JSON.stringify({
        response: fullResponse,
        sessionId: sessionId,
        isNewSession: isNewSession  // 现在这个值只有在生成新 sessionId 时才为 true
      })
    };

  } catch (err) {
    console.error('Error details:', {
      name: err.name,
      message: err.message,
      stack: err.stack,
      code: err.$metadata?.httpStatusCode
    });

    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*"
      },
      body: JSON.stringify({
        error: err.message,
        type: err.name,
        code: err.$metadata?.httpStatusCode
      })
    };
  }
};
```
Requestion 
```
{
    "message": "三体里主角有哪些人",
    "sessionId": null
}
```
Response
```
{"response":"\n\n在《三体》小说系列中，有几个重要的角色和人物类型：\n\n1. 叶文洁：她是一个重要的角色，在小说中被提到。\n\n2. 汪淼：他似乎是一个主要角色，参与了一个与《三体》相关的聚会。\n\n3. 科学家和学者：小说中包括了一位著名学者，他以给东方哲学赋予现代科学内涵而闻名。\n\n4. 作家：有一位著名的前卫小说家。\n\n5. 商业和政府领导：包括一家大型软件公司的副总裁和国家电力公司的高层领导。\n\n6. 媒体人：有一位来自大型媒体的记者。\n\n7. 学生：提到了一位在读的理科博士生。\n\n8. 历史人物：在小说的虚拟游戏部分，出现了许多历史人物，如孔子、墨子、秦始皇、伽利略、牛顿和爱因斯坦等。\n\n虽然小说中可能还有其他重要角色，但这些是搜索结果中提到的主要人物类型。《三体》是一个复杂的科幻故事，涉及地球文明和外星文明的互动，以及人类在面对宇宙威胁时的反应和选择。\n\n\n","sessionId":"1f5dd358-d5da-4d14-9cae-2ce30cf4c01f","isNewSession":true}
```
### Setting is the key
timeout should be longer as well as memory
![[Pasted image 20250224204015.png]]

## Create API Gateway

Integration request to lambda function we just created and make sure lambda proxy integration is true
![[Pasted image 20250224205306.png]]

### CORS error
```error
Access to fetch at 'https://db5abihr70.execute-api.ap-northeast-1.amazonaws.com/test/chat' from origin 'http://************:8505' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.

```
Solution
```
Go to AWS API Gateway console
Select your API
Click on "Actions" dropdown
Select "Enable CORS"
In the "Access-Control-Allow-Origin" field, enter your frontend domain (http://************:8505) or * for all domains
Check these headers:
Access-Control-Allow-Methods: POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Origin
Access-Control-Allow-Credentials: true
Click "Enable CORS and replace existing CORS headers"
Deploy your API again
```
![[Pasted image 20250224213844.png]]

### Authentication and Usage Plan setup
```
# Setting up API Key Authentication in AWS API Gateway

## Option 1: Using AWS Console (Recommended for beginners)

1. **Create API Key**
   - Go to API Gateway console
   - Click on "API Keys" in the left sidebar
   - Click "Create API key"
   - Enter a name for your key
   - Click "Save"

2. **Create Usage Plan**
   - Go to "Usage Plans" in the left sidebar
   - Click "Create usage plan"
   - Enter plan details:
     
     Name: "chat-usage-plan"
     Description: "Usage plan for chat application"
     Throttling: 10 requests per second
     Burst: 20 requests
     Quota: 1000 requests per month
     
   - Click "Next"
   - Associate your API stage
   - Add the API key you created
   - Click "Done"

3. **Enable API Key Requirement**
   - Go to your API's resources
   - Select the POST method
   - Click "Method Request"
   - Set "API Key Required" to "true"
   - Click the checkmark to save
   - Deploy your API again

## Option 2: Using AWS CLI

1. **Create API Key**
   
   aws apigateway create-api-key \
     --name "chat-api-key" \
     --description "API Key for chat application" \
     --enabled
   
2. **Create Usage Plan**
   
   aws apigateway create-usage-plan \
     --name "chat-usage-plan" \
     --description "Usage plan for chat application" \
     --throttle \
         rateLimit=10 \
         burstLimit=20 \
     --quota \
         limit=1000 \
         offset=0 \
         period=MONTH
   

3. **Add API Stage to Usage Plan**
   
   aws apigateway update-usage-plan \
     --usage-plan-id YOUR_USAGE_PLAN_ID \
     --patch-operations \
       op=add,path=/apiStages,value=YOUR_API_ID:test
   

4. **Associate API Key with Usage Plan**
   
   aws apigateway create-usage-plan-key \
     --usage-plan-id YOUR_USAGE_PLAN_ID \
     --key-id YOUR_API_KEY_ID \
     --key-type API_KEY
   

## Frontend Integration

1. Get your API key from AWS Console after setup

2. Create `.env.local` in your frontend project root:
   
   NEXT_PUBLIC_API_KEY=your_api_key_here
   

3. Add API key to your fetch requests:
   
   headers: {
     'Content-Type': 'application/json',
     'x-api-key': process.env.NEXT_PUBLIC_API_KEY
   }
   

## Security Notes

- Never commit `.env.local` to version control
- Keep your API key secure
- Consider implementing rate limiting and monitoring in your usage plan
- For production use, consider implementing a more secure authentication method (like JWT)
- Monitor API usage through AWS CloudWatch
- Regularly rotate API keys for security

## Troubleshooting

1. If getting 403 errors:
   - Verify API key is correctly set in frontend
   - Check if API key is enabled in AWS
   - Verify Usage Plan is correctly associated

2. If getting CORS errors:
   - Ensure CORS is enabled in API Gateway
   - Add 'x-api-key' to Access-Control-Allow-Headers

3. If rate limiting issues:
   - Check Usage Plan throttling settings
   - Monitor usage in AWS Console
   - Consider adjusting limits based on your needs 
```
### Have manual Test
```
--------------------------------------------------------------------
New Conversation.
Request:
curl -X POST \
  -H "Content-Type: application/json" \
  -H "x-api-key: MU1nmEkdQ13DYcdBr7KjO9aipVhXsvqa2dDEF8Xi" \
  -H "Origin: window.location.origin" \
  -d '{"message":"Hello", "sessionId":""}' \
  https://db5abihr70.execute-api.ap-northeast-1.amazonaws.com/test/chat

curl -X POST \
  -H "Content-Type: application/json" \
  -H "x-api-key: Bg6yMWDpx38Dp2OmWNqEFazZFpoa1qWQ5CDnNuPR" \
  -H "Origin: window.location.origin" \
  -d '{"message":"Hello", "sessionId":""}' \
  https://yrqd369zd7.execute-api.ap-northeast-1.amazonaws.com/test/chat



Response:
{"response":
    "Hello! Welcome to our customer support. How can I assist you today? ","sessionId":"546e0a66-df08-4fe0-9a10-f30c5857b60a","isNewSession":true}
---------------------------------------------------------------
Existing Session:
curl -X POST \
  -H "Content-Type: application/json" \
  -H "x-api-key: MU1nmEkdQ13DYcdBr7KjO9aipVhXsvqa2dDEF8Xi" \
  -H "Origin: window.location.origin" \
  -d '{"message":"Hello", "sessionId":"546e0a66-df08-4fe0-9a10-f30c5857b60a"}' \
  https://db5abihr70.execute-api.ap-northeast-1.amazonaws.com/test/chat


Response:
{"response":
    "Hello! Welcome to our customer support. How can I assist you today? ","sessionId":"546e0a66-df08-4fe0-9a10-f30c5857b60a","isNewSession":false}
------------------------------------------------

```
## Modify Agent Step
Change Agent -> Create AgentAlias -> Change Lambda and Deploy

---

# VPC Endpoint setup
In AWS Network ACLs setup, we allow all outbound traffic from inside EC2 server to external network. But we blocked 1024-65535 inbinding traffic from external to inside.

But we need  domain like *.execute-api.ap-northeast-1.amazonaws.com
 able to pass ACLs rules connect to ec2 server. 

How we can setup in AWS
## Create endpoint
```
Type:  AWS services
Service: com.amazonaws.ap-northeast-1.execute-api
vpc: same as the ec2 instance
additionals setting:  Enable DNS name
subnet: same as ec2 instance
security group: same as ec2 instance
policy: full access

```
Endpoint policy
```
{
	"Statement": [
		{
			"Action": "execute-api:Invoke",
			"Effect": "Allow",
			"Principal": "*",
			"Resource": "*"
		}
	]
}
```



it will generate one ip endpoint and as long as it is same server, even different api gateway. it will point to same ip
```
ubuntu@SBIHD-GenAI-uat-LVM-AP01:~$ ping db5abihr70.execute-api.ap-northeast-1.amazonaws.com
PING db5abihr70.execute-api.ap-northeast-1.amazonaws.com (**********) 56(84) bytes of data.

ubuntu@SBIHD-GenAI-uat-LVM-AP01:~$ ping yrqd369zd7.execute-api.ap-northeast-1.amazonaws.com
PING yrqd369zd7.execute-api.ap-northeast-1.amazonaws.com (**********) 56(84) bytes of data.

```

## check network acls

no change, i think, 1024-65535 port should be blocked

## get ec2 instance public ip
ubuntu@SBIHD-GenAI-uat-LVM-AP01:~$ curl checkip.amazonaws.com
**************

## check security group
should be same as ec2,  But as api gateway is https.  so we need to add one rule
allow https 443 open source is ********/24  
![[Pasted image 20250411092803.png]]

API Gateway Resource Policy
```
{
   "Version": "2012-10-17",
   "Statement": [
     {
       "Effect": "Allow",
       "Principal": "*",
       "Action": "execute-api:Invoke",
       "Resource": "arn:aws:execute-api:ap-northeast-1:060795926728:yrqd369zd7/*"
     },
     {
       "Effect": "Allow",
       "Principal": "*",
       "Action": "execute-api:Invoke",
       "Resource": "arn:aws:execute-api:ap-northeast-1:060795926728:yrqd369zd7/*",
       "Condition": {
         "StringEquals": {
           "aws:SourceVpce": "vpce-0d9ce61e83f315408"
         }
       }
     }
   ]
 }

```

```
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": "execute-api:Invoke",
      "Resource": "arn:aws:execute-api:ap-northeast-1:060795926728:yrqd369zd7/*/POST/chat"
    }
  ]

```
<span style="background:#ff4d4f">SUMMARY!!!!!!!!!!</span>
<span style="background:#40a9ff">Restrict ChatBot API access. Only works in below case</span>
```
# Below 4 conditions, @@@@@Only aws:SourceIp works for amazon agent
aws:SourceIp, or aws:VpcSourceIp aws:SourceVpc or aws:SourceVpce

# get ec2 instance public ip with 'curl checkip.amazonaws.com'
**************

# Setup resource policy
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": "execute-api:Invoke",
      "Resource": "arn:aws:execute-api:ap-northeast-1:060795926728:yrqd369zd7/*/*/*"
    },
    {
      "Effect": "Deny",
      "Principal": "*",
      "Action": "execute-api:Invoke",
      "Resource": "arn:aws:execute-api:ap-northeast-1:060795926728:yrqd369zd7/*/*/*",
      "Condition": {
        "NotIpAddress": {
          "aws:SourceIp": [
            "**************/32",
            "**************/32",
            "**************/32",
            "**************/32"
          ]
        }
      }
    }
  ]
}

{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": "execute-api:Invoke",
      "Resource": "arn:aws:execute-api:ap-northeast-1:060795926728:db5abihr70/*/*/*"
    },
    {
      "Effect": "Deny",
      "Principal": "*",
      "Action": "execute-api:Invoke",
      "Resource": "arn:aws:execute-api:ap-northeast-1:060795926728:db5abihr70/*/*/*",
      "Condition": {
        "NotIpAddress": {
          "aws:SourceIp": [
            "**************/32",
            "**************/32",
            "**************/32",
            "**************/32"  <== this is my laptip public and it is the key
          ]
        }
      }
    }
  ]
}


# Test from external server and ec2 server with below command
curl -v -X POST https://yrqd369zd7.execute-api.ap-northeast-1.amazonaws.com/test/chat \
    -H "Content-Type: application/json" \
    -H "x-api-key: Bg6yMWDpx38Dp2OmWNqEFazZFpoa1qWQ5CDnNuPR" \
    -d '{"message":"test","sessionId":""}'

curl -v -X POST https://db5abihr70.execute-api.ap-northeast-1.amazonaws.com/test/chat \
	    -H "Content-Type: application/json" \
	    -H "x-api-key: MU1nmEkdQ13DYcdBr7KjO9aipVhXsvqa2dDEF8Xi" \
	    -d '{"message":"test","sessionId":""}'	
```

AWS 安全组 (Security Group - SG):
有状态 (Stateful): 这是最关键的一点！安全组是有状态的。这意味着如果您允许出站流量（例如，到目标服务器的 443 端口），安全组会自动允许相应的返回流量进入，而不需要显式的入站规则来允许这些临时端口上的返回流量。它会跟踪已建立的连接。
默认行为: 默认情况下，安全组允许所有出站流量，并拒绝所有入站流量（除非有明确的允许规则）。
规则类型: 安全组只有 "允许" (Allow) 规则，没有明确的 "拒绝" (Deny) 规则。如果您描述的“阻止”是指没有添加入站规则，那么这是默认行为。如果您试图用某种方式模拟“阻止”，这可能不符合 SG 的设计。
AWS 网络 ACL (Network Access Control List - NACL):
无状态 (Stateless): NACL 是无状态的。您必须为请求和响应流量都配置明确的允许规则。如果您在 NACL 中阻止了入站临时端口，那么即使出站流量被允许，返回流量也会被阻止。NACL 应用于子网级别。
---
![[Pasted image 20250411122711.png]]
Use firewall but NOT endpoint

---

# API Token Dashboard Monitoring

```
# 在目标服务器上创建固定的符号链接
ln -sf tokens_$(date +%Y-%m-%d).csv current_tokens.csv

# 修改CSV Exporter配置指向固定文件名
每日自动更新脚本 /usr/local/bin/update_csv_link.sh:
#!/bin/bash
# 每日更新CSV文件链接
TODAY=$(date +%Y-%m-%d)
CSV_PATH="/opt/app/mom/mom_token_logs/tokens_${TODAY}.csv"
LINK_PATH="/opt/app/mom/mom_token_logs/current_tokens.csv"

# 检查今天的文件是否存在
if [ -f "$CSV_PATH" ]; then
    ln -sf "$CSV_PATH" "$LINK_PATH"
    echo "$(date): Updated link to $CSV_PATH"
    
    # 重启CSV exporter以重新读取文件
    # systemctl restart csv-exporter
else
    echo "$(date): Warning - $CSV_PATH not found"
fi
设置Cron定时任务:
bash# 每天00:01执行更新
crontab -e
1 0 * * * /usr/local/bin/update_csv_link.sh >> /var/log/csv_link_update.log 2>&1

# docker-compose.yml
ubuntu@SBIHD-GenAI-dev-LVM-AP01:/opt/aws/grafana$ cat docker-compose.yml
version: '3.8'
services:
  grafana:
    image: grafana/grafana-enterprise:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3055:3000"
    environment:
      - GF_INSTALL_PLUGINS=yesoreyeram-infinity-datasource
      - GF_SECURITY_ADMIN_PASSWORD=grafana3055mom
    volumes:
      - grafana-storage:/var/lib/grafana
    networks:
      - monitoring

volumes:
  grafana-storage:

networks:
  monitoring:

bash# Start with Docker Compose
docker-compose up -d

# Check status
docker-compose ps

# Setup AWS and make able to acces 3055 port from outside
  and also allow ML server 8100 csv port access
# target server csv log

// csv_monitor.js
const express = require('express');
const fs = require('fs');
const path = require('path');
const app = express();

// Enable CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    next();
});

// Serve current day's CSV file
app.get('/current-tokens.csv', (req, res) => {
    const today = new Date().toISOString().split('T')[0];
    const filename = `tokens_${today}.csv`;
    const filepath = path.join(__dirname, '/mom_token_logs/', filename);
    
    if (fs.existsSync(filepath)) {
        res.sendFile(filepath);
    } else {
        res.status(404).send('Current day file not found');
    }
});

// Serve all files
app.use(express.static('.'));

app.listen(8100, () => {
    console.log('CSV server running on port 8100');
});

# startup 
https://claude.ai/public/artifacts/9a4114ae-67bb-4ec6-ad04-1f4050b9f409
sudo npm install -g pm2
npm install express
pm2 start csv_monitor.js --name "csv-api-server"
# 3. Enable auto-start on boot
pm2 startup
pm2 save

# 4. Check status
pm2 status
# View logs in real-time
pm2 logs csv-api-server

# Restart server
pm2 restart csv-api-server

# Stop server
pm2 stop csv-api-server

# Monitor performance
pm2 monit

# List all processes
pm2 list
Current CSV: http://********:8100/current-tokens.csv
Health check: http://********:8100/health

#Setup in Grafana

```



---

## Q/A

### Difference between invokeModle and converse
```
2. Amazon Bedrock 中的 invokeModel vs converse

方法定义和用途


invoke_model:
pythonCopy# 单次调用，每次请求都是独立的
response = bedrock_runtime.invoke_model(
    modelId='anthropic.claude-3-sonnet-20240229-v1:0',
    contentType='application/json',
    body=json.dumps({
        "prompt": "Human: Hello\nAssistant:",
        "max_tokens_to_sample": 2048,
    })
)

converse:
pythonCopy# 支持对话历史的调用
response = bedrock_runtime.converse(
    modelId='anthropic.claude-3-sonnet-20240229-v1:0',
    conversationId='unique-conversation-id',  # 可选
    body=json.dumps({
        "prompt": "Hello",
        "conversation_history": previous_messages,  # 之前的对话历史
    })
)



主要区别


对话历史管理：

invoke_model 不自动管理对话历史，需要手动管理
converse 可以自动管理对话历史，保持上下文连贯性


状态追踪：

converse 提供 conversationId 来追踪特定对话
invoke_model 每次调用都是独立的，没有状态追踪




实际应用示例

pythonCopyimport boto3
import json

bedrock = boto3.client('bedrock-runtime')

# 使用 invoke_model 的对话实现
def chat_with_invoke_model():
    conversation_history = []
    
    def send_message(message):
        conversation_history.append({"role": "user", "content": message})
        
        response = bedrock.invoke_model(
            modelId='anthropic.claude-3-sonnet-20240229-v1:0',
            body=json.dumps({
                "messages": conversation_history
            })
        )
        
        assistant_message = json.loads(response['body'].read())
        conversation_history.append({"role": "assistant", "content": assistant_message['content']})
        return assistant_message['content']
    
    return send_message

# 使用 converse 的对话实现
def chat_with_converse():
    conversation_id = "unique-id-" + str(uuid.uuid4())
    
    def send_message(message):
        response = bedrock.converse(
            modelId='anthropic.claude-3-sonnet-20240229-v1:0',
            conversationId=conversation_id,
            body=json.dumps({
                "message": message
            })
        )
        
        return json.loads(response['body'].read())['content']
    
    return send_message

# 使用示例
invoke_chat = chat_with_invoke_model()
converse_chat = chat_with_converse()

# 使用 invoke_model
response1 = invoke_chat("Hello!")
response2 = invoke_chat("What did I just say?")

# 使用 converse
response3 = converse_chat("Hello!")
response4 = converse_chat("What did I just say?")



----------------------------
from anthropic import AnthropicBedrock

client = AnthropicBedrock(
    # Authenticate by either providing the keys below or use the default AWS credential providers, such as
    # using ~/.aws/credentials or the "AWS_SECRET_ACCESS_KEY" and "AWS_ACCESS_KEY_ID" environment variables.
    aws_access_key="********************",
    aws_secret_key="iaDpBgRFxHS3R/fVHamuDCAdK/xYQrzWnTIs0wdI",
    # Temporary credentials can be used with aws_session_token.
    # Read more at https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp.html.
    # aws_session_token="<session_token>",
    # aws_region changes the aws region to which the request is made. By default, we read AWS_REGION,
    # and if that's not present, we default to us-east-1. Note that we do not read ~/.aws/config for the region.
    aws_region="ap-northeast-1",
)

message = client.messages.create(
    model="apac.anthropic.claude-3-5-sonnet-20240620-v1:0",
    max_tokens=4096,
    messages=[{"role": "user", "content": "Hello, world"}]
)
print(message.content)


https://github.com/anthropics/anthropic-sdk-python/blob/main/examples/bedrock.py

/home/<USER>/miniconda3/envs/mom/lib/python3.10/site-packages/anthropic/resources/messages.py

----------------------
<< Cursor Prompt >>

we are going to add below logic to the @bits_momx_claude.py 
3. in .env file We added below parameter
If IF_CLAUDE_LLM = 0, then we will use existing Azure OpenAI to process LLM related tasks, Otherwise we will use Amazon Bedrock Claude LLM
All bedrock claude related parameters we have defined in .env file, in the begginning, you need to 
load AWS_ACCESS_KEY/AWS_SECRET_KEY/AWS_REGION/CLAUDE_LLM_MODEL/IF_CLAUDE_LLM

4. Pls use similar way to call bedrock claude api like azure openai

5. example of how to call amazon bedrock claude api including output sample, Pls check @amazon bedrock 



---
summarize_meeting_v4

claude_client.messages.create 
max_tokens
```

### Problems when create bedrock agent
```
Problem 1
ValidationException
This agent cannot be prepared. The AgentCollaboration attribute is set to SUPERVISOR but no agent collaborators are added.
Solution
Disable Multi-Agent


Problem 2
AccessDeniedException: An error occurred (AccessDeniedException) when calling the CreateAgent operation: User: arn:aws:iam::060795926728:user/serviceUser is not authorized to perform: bedrock:CreateAgent on resource: arn:aws:bedrock:ap-northeast-1:060795926728:agent/*

```