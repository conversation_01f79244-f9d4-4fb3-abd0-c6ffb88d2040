---
DocFlag:
  - Tested
  - Testing
Updated: 2024-08-23T20:54
tags:
  - AI->-Programming
  - AI->-Prompt
  - AI->Automation
Created: 2023-04-28T18:52
---
Make Reference Doc
Webhook with ChatGPT
JSON + Webhook
  
# Make Reference Doc
```JavaScript
https://www.make.com/en/help/tools
\#Some of API
https://rapidapi.com/search/medium
```
```JavaScript
  Twitter =》 TweetsInbox in notion  → router   → router - > notion 
					                                |        | router +++++++++++++++ notion
																					|           |++++++++++++++++notion
                                         GPT ---------router --------------notion
                                                       |————————————gpt ------ notion

Smartthings/Switchbot/IFTTT/Hue/Ambi (We dont use google home)
Make GPS -> IFTTT Webhook -> SmartThing -> Lock Door
Make GPS -> IFTTT Webhook -> Switchbot 
Smarthings -> Barthroom Fan
Switchbot -> None (disable GPS)
Hue -> Door sense -> Door light (disable GPS)
IFTTT -> Temperature/Humnity sensor -> AmbiCloud -> AC
IFTTT -> Smarthing -> Door Sendor -> Switchbot/Hue
Plan
Make -> IFTTT -> Hue/Smartthings/Switchbot

```
```JavaScript
<<< Some of keys >>>
OPENAI_API_KEY
***************************************************
=======================================================================
https://console.cloud.google.com/apis/credentials?project=autogpt-netcaster
Client ID: ************-fkbhcntjbtfqtiuf4v3ju64revh93ems.apps.googleusercontent.com
Client Secret: 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

{"web":{"client_id":"************-fkbhcntjbtfqtiuf4v3ju64revh93ems.apps.googleusercontent.com","project_id":"autogpt-netcaster","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_secret":"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","redirect_uris":["https://www.integromat.com/oauth/cb/google-restricted","https://www.integromat.com/oauth/cb/google","https://www.integromat.com/oauth/cb/google-custom","https://www.integromat.com/oauth/cb/app"]}}

Make (in google api dashboard)
Client ID: ************-b3eji747bfetmfkmmc0sdob7ktcu6411.apps.googleusercontent.com
Client secret: 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
---------------------------------------------
https://ifttt.com/developers

---------------------------------------------
https://www.customjs.space/
https://rapidapi.com/technologycircle-gmbh-technologycircle-gmbh-default/api/customjs/details
---------------------------------------------
https://developer.twitter.com/en/portal/dashboard
V2 API
Client ID: eGUtdEl0aWRaUGJnWWY4a1o1QUk6MTpjaQ
Client Secret: iLXhLjYGo1euAeTwaNlYdkYbIpuWtMtNwnlZkGm-VagJhwHcsI
V1 API
API Key:  *************************
API Key Secret: NUWU64zBbiswZ5rFALIrRsL1dLY9Kstu3WPotfqJilCKXagQ7v
Bearer Token: AAAAAAAAAAAAAAAAAAAAABZ%2FnAEAAAAAtZs37S8fBcK4VtSWtWm622LBw34%3DJVgACv2aEsqkSjWcB8GUbeafQaYJY6CdkTfjJpXhtbps6zA5x7
User ID	128504265
Name	Ray Sheng
Screen name	netcaste
---------------------------------------------
Medium
https://medium.com/me/settings/security
MakeAuto: 2ee0f638ff9ea8d87a3a98a6bae2cab632afd950b81bd9085569dbc316b2856a7
API key
https://rapidapi.com/nishujain199719-vgIfuFHZxVZ/api/medium2/
headers: {
    'X-RapidAPI-Key': '**************************************************',
    'X-RapidAPI-Host': 'medium2.p.rapidapi.com'
  }
Check the dashboard
https://rapidapi.com/developer/analytics/MakeAuto
Document
https://medium.com/geekculture/medium-api-documentation-90a01549d8db\#7a2c
Github 
https://github.com/justdataplease/medium-sky
https://github.com/Medium/medium-api-docs    <= official site
https://github.com/FrenchTechLead/medium-stats-api
---------------------------------------------
Notion
https://www.notion.so/integrations/make-e65a098f-a364-4e6a-81ab-c06ad7297e11
https://www.notion.so/my-integrations
Integration in Notion
Make-Auto: **************************************************
https://www.notion.so/netcaster/Make-News-********************************?pvs=4
---------------------------------------------
mp.weixin.qq.com
---------------------------------------------
https://home.openweathermap.org/
<EMAIL>
\/X+z8&s)!8fu/.U
********************************
---------------------------------------------
Hue development
https://developers.meethue.com/develop/hue-api-v2/
https://qiita.com/akinko/items/58c650f99f25fc7e3cb5
Run this utl and get hue bridge ip
https://discovery.meethue.com/
https://************/clip/v2/resource/device
https://developers.meethue.com/develop/hue-api-v2/getting-started/   !!!!! Easy to understand
[
    {
        "success": {
            "username": "WUX9B9R1Qh99WtbJVVYUHxCYAlCXGdcu1ge3lEfs",
            "clientkey": "5AF615C8C18B7CDCDB4E09EA0843BA78"
        }
    }
]
https://<bridge ip address>/clip/v2/resource/light/<id>
{
            "id": "9fe8c5ea-fe51-4b8c-abbd-50a8f76aef95",
            "id_v1": "/lights/5",
            "product_data": {
                "model_id": "LCT007",
                "manufacturer_name": "Signify Netherlands B.V.",
                "product_name": "Hue color lamp",
                "product_archetype": "sultan_bulb",
                "certified": true,
                "software_version": "67.101.2",
                "hardware_platform_type": "100b-104"
            },
            "metadata": {
                "name": "bedroom light",
                "archetype": "sultan_bulb"
            },
            "identify": {},
            "services": [
                {
                    "rid": "09b1d971-2467-4630-908c-145b45c52bdb",
                    "rtype": "light"
                },
                {
                    "rid": "b9152185-022b-40e6-a101-ee4f99ce3367",
                    "rtype": "zigbee_connectivity"
                },
                {
                    "rid": "b36b4dce-3ea3-41f0-91ca-4ea3defd2893",
                    "rtype": "entertainment"
                }
            ],
            "type": "device"
        },
PUT/GET https://************/clip/v2/resource/light/09b1d971-2467-4630-908c-145b45c52bdb
{
    "on": {
        "on": true
    }
}

\#Spotify
https://developer.spotify.com/documentation/web-api/concepts/authorization
https://developer.spotify.com/dashboard

https://api.spotify.com
```
  
```JavaScript
<<< Gmail >>
Gmail filter doc
https://support.google.com/mail/answer/7190
-(from:(BUYMA) OR from:(*.jp) OR from:(Moncler) OR from:(ec-system) OR from:(出前館) OR from:(ユニクロ) OR from:(ビーエムメルマガ) OR from:(テレビアクセサリ) OR from:(VEIMIA) OR from:(科技紫微網) OR from:(Wall Street Journal))
```
  
```JavaScript
<<< Some Tutorial >>
https://www.bing.com/videos/search?q=make.com+notion&&view=detail&mid=77C2A965CF950A3CCB6277C2A965CF950A3CCB62&&FORM=VRDGAR&ru=%2Fvideos%2Fsearch%3Fq%3Dmake.com%2Bnotion%26FORM%3DHDRSC4
\#Flow control
https://www.make.com/en/help/tools/flow-control\#iterator-935250
Here is some sample to extract data from json
get( map(Sample JSON[]; email ; id ; 704) ; 1 )
map( 55.Books; Summary; Genre；Sci-Fi)   =》 Genre为Sci-Fi的Summary -》Array
https://www.make.com/en/use-cases/tweet-with-notion
Error handling
https://www.integromat.com/en/help/throw
\#Break and Sleep 出错的终止，可以等待一些时间，然后retry，retry此时schedule出设置, 最好设成顺序执行
https://www.youtube.com/watch?v=mZb3A-pSCcU
\#Commit and Ignore  Commit. 后续停止，mark成功。 ignore该模块mark成功，后续继续执行，一般当到末梢去执行
\#Rollback and Resume Resume: 替换值然后再执行   ，路由也有默认的failback出口，可以利用，只有支持ACID的模块才支持rollback，rollback系统Mark失败，rollback一旦触发，后续都不执行
https://www.youtube.com/watch?v=yGHUN7alk2k

1. Using JSONModule
BundleValidationError
Validation failed for 1 parameter(s).
Missing value of required parameter 'title'.
Origin
2. Using HTTP Module
3. Added Flow control
Validation failed due to missing title parameter
{{8.error.detail}}  contains Missing value of required parameter 'title'.
 
```
  
# Webhook with ChatGPT
![[Notion/AI/🦜🦜🦜Twitter-Gmail-Medium → Make.com → Notion/attachments/Untitled.png|Untitled.png]]
```Python
Upload file -> add uploaded file to a vector store -> call Assistant with vs id and file id
in one vs we can put multuple files.
```
![[Notion/AI/🦜🦜🦜Twitter-Gmail-Medium → Make.com → Notion/attachments/Untitled 1.png|Untitled 1.png]]
  
# JSON + Webhook
```JavaScript
https://www.youtube.com/watch?v=x4hL64Ugojg
```