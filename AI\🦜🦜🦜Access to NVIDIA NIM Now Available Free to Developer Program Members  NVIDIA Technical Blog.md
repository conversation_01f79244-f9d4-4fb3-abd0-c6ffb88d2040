---
Updated: 2025-01-31T13:43:00
tags:
  - AI->-Nvidia
  - AI->-Programming
  - AI->-<PERSON>ls
  - AI->-llama
URL: https://developer.nvidia.com/blog/access-to-nvidia-nim-now-available-free-to-developer-program-members/?ncid=em-anno-738750&nvweb_e=2g9KQAS-rLXD2aKxFjXR3o5f4mqEdRlXuMKJ20hQYCqboYU8YdOQKBmcVVxEN_UP0xB75VKzQE-vELieaoC0gA&mkt_tok=MTU2LU9GTi03NDIAAAGUn7T_92pFEItacnSER4tn0akdWOmWUYuNcS9ge1N9xVqCyXKJ9AGJpsdnqfvNGxTgDW8qLmBWJg0D3q_DAZUcJUpOi_oEj0ss9k7mnzUvEMT0Dq3GlD4
Created: 2024-07-30T13:11
---
# Test
```Python
\#Login
https://build.nvidia.com/explore/discover
\#Find model
And generate key
\#Run
from openai import OpenAI
client = OpenAI(
  base_url = "https://integrate.api.nvidia.com/v1",
  api_key = "**********************************************************************"
)
completion = client.chat.completions.create(
  model="meta/llama-3.1-405b-instruct",
  messages=[{"role":"user","content":"Write a limerick about the wonders of GPU computing."}],
  temperature=0.2,
  top_p=0.7,
  max_tokens=1024,
  stream=True
)
for chunk in completion:
  if chunk.choices[0].delta.content is not None:
    print(chunk.choices[0].delta.content, end="")
```
  
[![](https://developer-blogs.nvidia.com/wp-content/uploads/2024/07/nim-visual-featured.png)](https://developer-blogs.nvidia.com/wp-content/uploads/2024/07/nim-visual-featured.png)
==使用简单的 API 将预训练的 AI 基础模型集成到产品和体验中的能力显着提高了开发人员对 LLM 端点和应用程序开发框架的使用。==[==NVIDIA NIM==](https://developer.nvidia.com/nim) ==使开发人员和工程团队能够快速部署自己的 AI 模型端点，以便使用流行的开发工具和框架安全开发加速生成式 AI 应用程序。==
==开发人员表示，他们希望更轻松地访问 NIM 进行开发，因此我们很高兴能够为超过 500 万== [==NVIDIA 开发者计划==](https://developer.nvidia.com/developer-program)==成员提供免费的可下载的 NIM 微服务访问权限，用于开发、测试和研究。该计划的成员将获得全面的资源、培训、工具和专家社区，以帮助构建加速的应用程序和解决方案。==
==在这篇文章中，我们将简要介绍 NIM 微服务的技术概述，重点介绍一些可供下载和自托管部署的微服务，并提供入门实践资源。==
## ==什么是 NIM 微服务？==
==NIM 提供容器，用于自托管 GPU 加速的微服务，用于跨云、数据中心和工作站的预训练和定制 AI 模型。它们可以通过一个命令进行部署，并自动公开行业标准的 API，以便快速集成到应用程序、开发框架和工作流中。一个例子是基于==[==大型语言模型 （LLM）==](https://www.nvidia.com/en-us/glossary/large-language-models/) ==的 NIM 微服务的 OpenAI API 规范。==
==使用== [==NVIDIA TensorRT==](https://developer.nvidia.com/tensorrt) ==和== [==NVIDIA TensorRT-LLM==](https://github.com/NVIDIA/TensorRT-LLM) ==构建的优化推理引擎可提供低响应延迟和高吞吐量。在运行时，NIM 微服务会为基础模型、GPU 和系统的每种组合选择最佳推理引擎。NIM 容器还提供标准的可观测性数据源，并内置支持在 NVIDIA GPU 上使用 Kubernetes 进行自动缩放。有关 NIM 功能和架构的更多信息，请参阅==[==适用于 LLM 的 NVIDIA NIM==](https://docs.nvidia.com/nim/large-language-models/latest/introduction.html) ==文档。==
## ==下载适用于任何用例的 NIM 微服务==
==虽然任何人都可以注册== [==NVIDIA API 目录==](https://build.nvidia.com/explore/discover)==以获得免费积分，以通过 NVIDIA 托管的 NIM 端点访问模型，但 NVIDIA 开发者计划的成员可以免费访问最新的可下载的 NIM 微服务，包括 Meta 的== [==Llama 3.1 8B==](https://build.nvidia.com/meta/llama-3_1-8b-instruct)==、Mistral AI 的紧凑型== [==Mistral 7B Instruction==](https://build.nvidia.com/mistralai/mistral-7b-instruct-v03) ==等等。==
==开发人员计划成员可以在最多 2 个节点或 16 个 GPU 上使用 NIM 微服务。当准备好在生产中使用 NIM 时，组织可以注册免费的 90 天== [==NVIDIA AI Enterprise 许可证。==](https://www.nvidia.com/en-us/data-center/products/ai-enterprise/)==有关详细信息，请参阅==[==常见问题解答==](https://forums.developer.nvidia.com/t/nim-faq/300317)==。==
## ==开始使用可下载的 NIM 微服务==
==在== [==NVIDIA API 目录中==](https://build.nvidia.com/explore/discover#llama3-70b)==，选择一个微服务，然后选择== ==**Build with this NIM**== ==以下载 NIM 微服务并获取容器的 API 密钥。==
==如果您还不是该计划的成员，您将有机会加入 - 只需查找开发人员计划选项即可。有关更多信息，请参阅== [==入门==](https://docs.nvidia.com/nim/large-language-models/latest/getting-started.html) ==和== [==使用 NVIDIA NIM 部署生成式 AI 的简单指南==](https://developer.nvidia.com/blog/a-simple-guide-to-deploying-generative-ai-with-nvidia-nim/)==。==
[https://www.youtube.com/embed/087spL8hMvM?version=3&rel=1&showsearch=0&showinfo=1&iv_load_policy=1&fs=1&hl=en-US&autohide=2&wmode=transparent](https://www.youtube.com/embed/087spL8hMvM?version=3&rel=1&showsearch=0&showinfo=1&iv_load_policy=1&fs=1&hl=en-US&autohide=2&wmode=transparent)
==_视频 1.如何在 5 分钟内部署 NVIDIA NIM_==
==如果您想通过简单的部署在托管基础设施上获得 NIM 微服务的实践经验，请尝试使用== [==NVIDIA API 密钥的 NVIDIA Brev Launchable==](http://brev.dev/llama3-1-nim) ==来快速配置 GPU，下载 Llama 3.1 NIM 微服务，并通过 Jupyter 笔记本或一组端点与其交互。托管的 NIM 微服务也在== [==Hugging Face==](https://developer.nvidia.com/blog/nvidia-collaborates-with-hugging-face-to-simplify-generative-ai-model-deployments/) ==上可用。两种托管解决方案均按小时费率定价。==
[https://www.youtube.com/embed/XTCPiK9fFCo?version=3&rel=1&showsearch=0&showinfo=1&iv_load_policy=1&fs=1&hl=en-US&autohide=2&wmode=transparent](https://www.youtube.com/embed/XTCPiK9fFCo?version=3&rel=1&showsearch=0&showinfo=1&iv_load_policy=1&fs=1&hl=en-US&autohide=2&wmode=transparent)
==_视频 2.微调 Llama 3.1 并直接从您的笔记本电脑使用 NVIDIA NIM 进行部署_==
==有关详细信息，请参阅以下资源：==
- [==使用 NVIDIA NIM 无缝部署大量 LoRA 适配器==](https://developer.nvidia.com/blog/seamlessly-deploying-a-swarm-of-lora-adapters-with-nvidia-nim/)
- ==GitHub 存储库示例：==
    - [==/英伟达/GenerativeAI示例==](https://github.com/NVIDIA/GenerativeAIExamples)
    - [==/langchain-ai/langchain-nvidia==](https://github.com/langchain-ai/langchain-nvidia/blob/main/cookbook/langgraph_rag_agent_llama3_nvidia_nim.ipynb) ==用于具有 NVIDIA NIM 的 LLM RAG 代理==
    - [==/run-llama==](https://github.com/run-llama/llama_index/blob/main/docs/docs/examples/llm/nvidia_nim.ipynb)==，用于使用 NVIDIA NIM 的 LlamaIndex RAG 管道==
    - [==/NVIDIA/NeMo-Curator==](https://github.com/NVIDIA/NeMo-Curator/blob/main/tutorials/nemotron_340B_synthetic_datagen/synthetic_preference_data_generation_nemotron_4_340B.ipynb) ==用于使用 Nemotron-4 340B 生成合成偏好数据==
    - [==/NVIDIA/workbench-example-hybrid-rag==](https://github.com/nvidia/workbench-example-hybrid-rag) ==用于使用 RAG 和 NVIDIA AI Workbench 与文档聊天==
- [==Haystack RAG 管道，具有自行部署的 AI 模型和 NVIDIA NIM==](https://colab.research.google.com/github/deepset-ai/haystack-cookbook/blob/main/notebooks/rag-with-nims.ipynb)==（Colab 笔记本）==
==要与 NVIDIA 和 NIM 微服务社区互动，请参阅== [==NVIDIA NIM 开发者论坛==](https://forums.developer.nvidia.com/c/ai-data-science/nvidia-nim)==。我们期待收到您的来信，并迫不及待地想看看您构建了什么！==
## ==About the Authors==
[![](https://developer-blogs.nvidia.com/wp-content/uploads/2024/07/beth-noble-240x240-1-131x131.jpg)](https://developer-blogs.nvidia.com/wp-content/uploads/2024/07/beth-noble-240x240-1-131x131.jpg)
==**About Bethann Noble**====  
  
====Bethann Noble is a product marketing manager for enterprise software products at NVIDIA, including the NVIDIA AI Enterprise software platform with NVIDIA NIM. Previously, she held senior positions in marketing and product marketing at AI copilot startup Continual, AI-powered bot protection platform HUMAN Security, Cloudera, and IBM. Bethann has a bachelor’s degree in mathematics from the University of Texas at Austin.==
[==View all posts by Bethann Noble==](https://developer.nvidia.com/blog/author/bnoble/)
[![](https://developer-blogs.nvidia.com/wp-content/uploads/2024/07/Janaina-Pilomia-131x131.jpg)](https://developer-blogs.nvidia.com/wp-content/uploads/2024/07/Janaina-Pilomia-131x131.jpg)
==**About Janaina Pilomia**====  
  
====Janaina Pilomia is a senior product marketing manager for the NVIDIA Developer Program and website. A former developer herself, in over 20 years of DevRel related roles Janaina has built developer experiences, programs and products for mobile, embedded, cloud, and AI technologies.==
[==View all posts by Janaina Pilomia==](https://developer.nvidia.com/blog/author/jpilomia/)