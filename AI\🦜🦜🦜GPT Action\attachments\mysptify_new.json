{"openapi": "3.1.0", "info": {"title": "Spotify Web API", "version": "1.0.0", "description": "API for interacting with Spotify Web API, including authentication and artist data retrieval."}, "servers": [{"url": "https://api.spotify.com/v1", "description": "Spotify Web API server"}], "paths": {"/artists/{artist_id}": {"get": {"operationId": "getArtistData", "summary": "Retrieves data for a specific artist using their Spotify ID", "parameters": [{"name": "artist_id", "in": "path", "required": true, "description": "The Spotify ID of the artist", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArtistResponse"}}}}}, "security": [{"oauth2": []}]}}, "/me/player/next": {"post": {"tags": ["Player"], "operationId": "skip-users-playback-to-next-track", "summary": "<PERSON><PERSON> To <PERSON>\n", "description": "Skips to next track in the user’s queue.\n", "parameters": [{"name": "device_id", "required": false, "in": "query", "schema": {"title": "Device ID", "description": "The id of the device this command is targeting. If not supplied, the user's currently active device is the target.", "example": "0d1841b0976bae2a3a310dd74c0f3df354899bc8", "type": "string"}}], "responses": {"204": {"description": "Command sent"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "429": {"$ref": "#/components/responses/TooManyRequests"}}, "security": [{"oauth2": ["user-modify-playback-state"]}]}}, "/me/player/previous": {"post": {"tags": ["Player"], "operationId": "skip-users-playback-to-previous-track", "summary": "<PERSON>p <PERSON>\n", "description": "Skips to previous track in the user’s queue.\n", "parameters": [{"name": "device_id", "required": false, "in": "query", "schema": {"title": "Device ID", "description": "The id of the device this command is targeting. If\nnot supplied, the user's currently active device is the target.\n", "example": "0d1841b0976bae2a3a310dd74c0f3df354899bc8", "type": "string"}}], "responses": {"204": {"description": "Command sent"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "429": {"$ref": "#/components/responses/TooManyRequests"}}, "security": [{"oauth2": ["user-modify-playback-state"]}]}}}, "components": {"responses": {"Unauthorized": {"description": "Bad or expired token. This can happen if the user revoked a token or\nthe access token has expired. You should re-authenticate the user.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "Forbidden": {"description": "Bad OAuth request (wrong consumer key, bad nonce, expired\ntimestamp...). Unfortunately, re-authenticating the user won't help here.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "NotFound": {"description": "The requested resource cannot be found.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "BadRequest": {"description": "The request contains malformed data in path, query parameters, or body.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}, "TooManyRequests": {"description": "The app has exceeded its rate limits.\n", "content": {"application/json": {"schema": {"type": "object", "required": ["error"], "properties": {"error": {"$ref": "#/components/schemas/ErrorObject"}}}}}}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"clientCredentials": {"tokenUrl": "https://accounts.spotify.com/api/token", "scopes": {"app-remote-control": "Communicate with the Spotify app on your device.\n", "playlist-read-private": "Access your private playlists.\n", "playlist-read-collaborative": "Access your collaborative playlists.\n", "playlist-modify-public": "Manage your public playlists.\n", "playlist-modify-private": "Manage your private playlists.\n", "user-library-read": "Access your saved content.\n", "user-library-modify": "Manage your saved content.\n", "user-read-private": "Access your subscription details.\n", "user-read-email": "Get your real email address.\n", "user-follow-read": "Access your followers and who you are following.\n", "user-follow-modify": "Manage your saved content.\n", "user-top-read": "Read your top artists and content.\n", "user-read-playback-position": "Read your position in content you have played.\n", "user-read-playback-state": "Read your currently playing content and Spotify Connect devices information.\n", "user-read-recently-played": "Access your recently played items.\n", "user-read-currently-playing": "Read your currently playing content.\n", "user-modify-playback-state": "Control playback on your Spotify clients and Spotify Connect devices.\n", "ugc-image-upload": "Upload images to Spotify on your behalf.\n", "streaming": "Play content and control playback on your other devices.\n"}}}}}, "schemas": {"ErrorObject": {"type": "object", "x-spotify-docs-type": "ErrorObject", "required": ["status", "message"], "properties": {"status": {"type": "integer", "minimum": 400, "maximum": 599, "description": "The HTTP status code (also returned in the response header; see [Response Status Codes](/documentation/web-api/concepts/api-calls#response-status-codes) for more information).\n"}, "message": {"type": "string", "description": "A short description of the cause of the error.\n"}}}, "ArtistResponse": {"type": "object", "properties": {"external_urls": {"type": "object", "properties": {"spotify": {"type": "string"}}}, "followers": {"type": "object", "properties": {"href": {"type": "string", "nullable": true}, "total": {"type": "integer"}}}, "genres": {"type": "array", "items": {"type": "string"}}, "href": {"type": "string"}, "id": {"type": "string"}, "images": {"type": "array", "items": {"type": "object", "properties": {"height": {"type": "integer"}, "url": {"type": "string"}, "width": {"type": "integer"}}}}, "name": {"type": "string"}, "popularity": {"type": "integer"}, "type": {"type": "string"}, "uri": {"type": "string"}}}}}}