---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-18T11:14
tags:
  - AI->-Chatbot
  - AI->-Fine-Tuning
  - AI->-Model
  - AI->-Programming
  - AI->-ToDO
Created: 2023-03-14T21:04
---
[![](https://mmbiz.qpic.cn/mmbiz_jpg/5qv5QsBmI9Ds2wTFiaKxoyfecYhIiaVhp8UtdhdcAg8NDEiccKEGictMUibX7WvtADQoqNUvZhibg2efczVib2AP1xia2A/0?wx_fmt=jpeg)](https://mmbiz.qpic.cn/mmbiz_jpg/5qv5QsBmI9Ds2wTFiaKxoyfecYhIiaVhp8UtdhdcAg8NDEiccKEGictMUibX7WvtADQoqNUvZhibg2efczVib2AP1xia2A/0?wx_fmt=jpeg)
---
![[Notion/AI/🦜🦜🦜ChatGLM：千亿基座的对话模型启动内测，单卡版模型已全面开源/attachments/640|640]]
近日，由清华技术成果转化的公司智谱AI 开源了 GLM 系列模型的新成员——**中英双语对话模型 ChatGLM-6B，支持在单张消费级显卡上进行推理使用**。这是继此前开源 GLM-130B 千亿基座模型之后，智谱AI 再次推出大模型方向的研究成果。与此同时，基于千亿基座模型的 ChatGLM 也同期推出，初具问答和对话功能，现已开启邀请制内测（**内测申请网址 chatglm.cn**），后续还会逐步扩大内测范围。据悉，ChatGLM-6B 是一个开源的、支持中英双语问答的对话语言模型，并针对中文进行了优化。该模型基于 General Language Model（GLM）架构，具有 62 亿参数。结合模型量化技术，用户可以在消费级的显卡上进行本地部署（INT4 量化级别下最低只需 6GB 显存）。ChatGLM-6B 使用了和 ChatGLM 相同的技术，针对中文问答和对话进行了优化。经过约 1T 标识符的中英双语训练，辅以监督微调、反馈自助、人类反馈强化学习等技术的加持，62 亿参数的 ChatGLM-6B 虽然规模不及千亿模型，但大大**降低了推理成本，提升了效率**，并且已经能生成相当符合人类偏好的回答。模型开源地址：https://github.com/THUDM/ChatGLM-6B。具体来说，ChatGLM-6B 具备以下特点：
- **充分的中英双语预训练：**ChatGLM-6B 在 1:1 比例的中英语料上训练了 1T 的 token 量，兼具双语能力。
- **优化的模型架构和大小：**吸取 GLM-130B 训练经验，修正了二维 RoPE 位置编码实现，使用传统 FFN 结构。6B（62亿）的参数大小，也使得研究者和个人开发者自己微调和部署 ChatGLM-6B 成为可能。
- **较低的部署门槛：**FP16 半精度下，ChatGLM-6B 需要至少 13 GB 的显存进行推理，结合模型量化技术，这一需求可以进一步降低到 10GB（INT8）和 6GB（INT4），使得 ChatGLM-6B 可以部署在消费级显卡上。
- **更长的序列长度：**相比 GLM-10B（序列长度 1024），ChatGLM-6B 序列长度达 2048，支持更长对话和应用。
- **人类意图对齐训练：**使用了监督微调（Supervised Fine-Tuning）、反馈自助（Feedback Bootstrap）、人类反馈强化学习（RLHF）等方式，使模型初具理解人类指令意图的能力。输出格式为 markdown，方便展示。
基于以上特点，ChatGLM-6B 在一定条件下具备较好的对话与问答能力，以下是 ChatGLM-6B 的对话效果展示：**不过由于 ChatGLM-6B 模型的容量较小，不可避免的存在一些局限和不足**，包括：
- 相对较弱的模型记忆和语言能力。在面对许多事实性知识任务时，ChatGLM-6B 可能会生成不正确的信息，也不太擅长逻辑类问题（如数学、编程）的解答。
- 可能会产生有害说明或有偏见的内容：ChatGLM-6B 只是一个初步与人类意图对齐的语言模型，可能会生成有害、有偏见的内容。
- 较弱的多轮对话能力：ChatGLM-6B 的上下文理解能力还不够充分，在面对长答案生成和多轮对话的场景时，可能会出现上下文丢失和理解错误的情况。
同时，**智谱AI 还开启了 ChatGLM 线上模型的内测**。相比起 ChatGLM-6B，ChatGLM 线上模型的能力提升主要来源于独特的千亿基座模型 GLM-130B。它采用了不同于 BERT、GPT-3 以及 T5 的 GLM 架构，是一个包含多目标函数的自回归预训练模型。2022 年 11 月，斯坦福大学大模型中心对全球 30 个主流大模型进行了全方位的评测，GLM-130B 是亚洲唯一入选的大模型。在与 OpenAI、Google Brain、微软、英伟达、Meta AI 的各大模型对比中，评测报告显示 GLM-130B 在准确性和公平性指标上与 GPT-3 175B（davinci）接近或持平，鲁棒性、校准误差和无偏性优于 GPT-3 175B（如下图）。  
基于千亿基座的 ChatGLM 线上模型目前在 chatglm.cn 进行邀请制内测，  
**用户需要使用邀请码进行注册，也可以填写基本信息申请内测**。由 ChatGLM 生成的对话效果展示：  
整体而言，  
**ChatGLM 距离国际顶尖大模型研究和产品还有一定差距，GLM 团队也在博客中坦言了这一点**，并表示将持续研发并开源更新版本的 ChatGLM 和相关模型。欢迎大家下载 ChatGLM-6B，基于它进行研究和（非商用）应用开发。GLM 团队希望能和开源社区研究者和开发者一起，推动大模型研究和应用在中国的发展。博客链接请见：https://chatglm.cn/blog**关于智谱AI**智谱AI由清华大学计算机系的技术成果转化而来，致力于打造新一代认知智能通用模型，提出了Model as a Service（MaaS）的市场理念。公司于2021年合作研发了双语千亿级超大规模预训练模型GLM-130B，并主导构建了高精度通用知识图谱，把两者有机融合为数据与知识双轮驱动的认知引擎，并基于此千亿基座模型打造 ChatGLM （chatglm.cn）。此外，智谱AI也推出了认知大模型平台Bigmodel.ai，形成AIGC产品矩阵，包括高效率代码模型CodeGeeX、高精度文图生成模型CogView等，提供智能API服务。通过认知大模型链接物理世界的亿级用户、赋能元宇宙数字人、成为具身机器人的基座，赋予机器像人一样“思考”的能力。官网请见（zhipuai.cn）**｜点击关注我 👇 记得标星｜**
---
---
https://github.com/THUDM/ChatGLM-6B
https://github.com/THUDM/GLM
```Go
Location: /opt/workspace/ai/ChatGLM-6B
https://huggingface.co/THUDM/chatglm-6b

Create environment.yml based on requirement.txt file
(base) [raysheng@MONSTER:/opt/workspace/ai/ChatGLM-6B]$ cat environment.yml
name: ChatGLM
channels:
  - defaults
  - conda-forge
dependencies:
  - python
  - pip
  - protobuf>=3.19.5,<3.20.1
  - pip:
    - transformers>=4.26.1
    - icetk
    - cpm_kernels

conda env create -f environment.yml
# To activate this environment, use
#
#     $ conda activate ChatGLM
#
# To deactivate an active environment, use
#
#     $ conda deactivate
git lfs install
\#download pretrained data
git clone https://huggingface.co/THUDM/chatglm-6b
https://huggingface.co/THUDM/chatglm-6b
\#download slim version 
git clone https://huggingface.co/silver/chatglm-6b-slim

python cli_demo.py
\#If out of memory
model = AutoModel.from_pretrained("THUDM/chatglm-6b", trust_remote_code=True).half().quantize(4).cuda()
python web_demo.py
Slim the version and all come to local and just 20G
(OpenChatKit) [raysheng@MONSTER:/opt/workspace/ai/ChatGLM-6B]$ cat cli_slim.py
import os
from transformers import AutoTokenizer, AutoModel
local_model_path = "./chatglm-6b-slim"
tokenizer = AutoTokenizer.from_pretrained(local_model_path,trust_remote_code=True)
model = AutoModel.from_pretrained(local_model_path,trust_remote_code=True)
model = model.half().quantize(8).cuda()
model = model.eval()
history = []
print("欢迎使用 ChatGLM-6B 模型，输入内容即可进行对话，clear 清空对话历史，stop 终止程序")
while True:
    query = input("\n用户：")
    if query == "stop":
        break
    if query == "clear":
        history = []
        os.system('clear')
        continue
    response, history = model.chat(tokenizer, query, history=history)
    print(f"ChatGLM-6B：{response}")
How to play it without GPU
n README.md
CPU部署( use CPU )
model = AutoModel.from_pretrained("THUDM/chatglm-6b", trust_remote_code=True).half().cuda()
change to
model = AutoModel.from_pretrained("THUDM/chatglm-6b", trust_remote_code=True).float()
How to fine tuning it
https://github.com/mymusise/ChatGLM-Tuning/issues/11\#issuecomment-1474880311
https://github.com/thaumstrial/FinetuneGLMWithPeft
https://github.com/ssbuild/chatglm_finetuning
https://github.com/lich99/ChatGLM-finetune-LoRA
\#Speedup
https://zhuanlan.zhihu.com/p/613538508?utm_id=0
\#install latest cuda package and it will install 12.1
conda install -c nvidia cuda-python
conda install tensorflow-gpu
\#install accelarate
conda install -c conda-forge accelerate
git clone https://github.com/TimDettmers/bitsandbytes.git
nvcc --version
cd /opt/workspace/bitsandbytes
export CUDA_VERSION=121
make
pip install .
\#install deepspeed
pip install deepspeed
\#install pytorch 2.0
conda install pytorch torchvision torchaudio -c pytorch -c nvidia
\#install lightning
https://lightning.ai/docs/pytorch/stable/starter/installation.html
conda install pytorch-lightning -c conda-forge
\#install others
conda install watermark transformers datasets torchmetrics

```
```Go
\#tensorflow GPU
conda create --name tf_gpu tensorflow-gpu
activate tf_gpu
import tensorflow as tf
sess = tf.Session(config=tf.ConfigProto(log_device_placement=True))
```
```Go
\#Test
给我用Python写一个扫描SSH端口的程序
Andrew is free from 11 am to 3 pm, Joanne is free from noon to 2 pm and then 3:30 pm to 5 pm. Hannah is available at noon for half an hour, and then 4 pm to 6 pm. What are some options for start times for a 30 minute meeting for Andrew, Hannah, and Joanne?
Employee (id, name, department_id);
Department (id, name, address);
Salary_Payments (id, employee_id, amount, date);
Please generate SQL and Calculate the sum of salaries from the last year
============================
### Postgres SQL tables, with their properties:
#
# Employee(id, name, department_id)
# Department(id, name, address)
# Salary_Payments(id, employee_id, amount, date)
#
### A query to list the names of the departments which employed more than 10 employees in the last 3 months
SELECT
```