---
Updated: 2024-04-06T09:41
tags:
  - AI->-Azure
  - AI->-Model
  - AI->-Voice
  - AI->-数字人
Created: 2024-04-06T09:41
---
[![](https://i0.hdslb.com/bfs/article/ec98ce63917419a71d519e3d43ca08f2f89e9b4b.png)](https://i0.hdslb.com/bfs/article/ec98ce63917419a71d519e3d43ca08f2f89e9b4b.png)
==阿虚一度是对听书不太感兴趣的，AI机械式的发音，固定的腔调、语速，总会丢失那些波澜起伏的剧情所带来的沉浸式情感体验==
[![](http://i0.hdslb.com/bfs/article/34f930a2aac9c59cc8def076eae9d3980b3729a3.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/34f930a2aac9c59cc8def076eae9d3980b3729a3.png@!web-article-pic.avif)
==但最近才知道微软在**//Build 2021大会**上，发布了旗下 TTS 2021版的人工语音，让我对听书这件事简直是有了革新式的看法==
[![](http://i0.hdslb.com/bfs/article/2f6594be73e13896bd82eb228b5c7d3a29544fb0.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/2f6594be73e13896bd82eb228b5c7d3a29544fb0.png@!web-article-pic.avif)
==大家可以听听官方网页的示例，新增的这5种中文语音，根本听不出是机器在念，逼真地吓人：https://techcommunity.microsoft.com/t5/azure-ai-blog/azure-text-to-speech-updates-at-build-2021/ba-p/2382981#:~:text=Five%20more%20Chinese%20voices%20are%20generally%20available==
[![](http://i0.hdslb.com/bfs/article/76d4ced6df6794f3f49da15cbec25e4666f55da4.jpg@1256w_1060h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/76d4ced6df6794f3f49da15cbec25e4666f55da4.jpg@1256w_1060h_!web-article-pic.avif)
==对于喜欢听书的人来说，估计和阿虚一样心里只有一个想法吧：赶紧让我体验一下！！！==
==GIF==
[![](http://i0.hdslb.com/bfs/article/7491f0d877954c0bf7055540c5b2d76fc5e83d4e.gif@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/7491f0d877954c0bf7055540c5b2d76fc5e83d4e.gif@!web-article-pic.avif)
==总之阿虚今天就准备来介绍一下安卓／iOS上分别怎么用上微软这些最新的TTS语音！==
## ==1 安卓端==
==TTS对于多数人，肯定还是用于手机上看小说，所以这里还是先介绍怎么在手机上体验==
[![](http://i0.hdslb.com/bfs/article/32904c0668b9f97376d02002d782b305e82dff17.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/32904c0668b9f97376d02002d782b305e82dff17.png@!web-article-pic.avif)
## ==1.1 TTS==
==目前最简单的方法就是@ag2s20150909制作的这款小APP了==
==APP的最新版发布在作者的Github：https://github.com/ag2s20150909/TTS/tree/master/release==
[![](http://i0.hdslb.com/bfs/article/5d50b673a13da7fdf3179ef0d586f10da8f4adb1.jpg@1256w_848h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/5d50b673a13da7fdf3179ef0d586f10da8f4adb1.jpg@1256w_848h_!web-article-pic.avif)
==如果你访问Github困难，建议了解《2022 Github加速访问教程》https://mp.weixin.qq.com/s/C5XUgzL3_KGIga2X-LHJuw==
==将APP下载并安装好之后，点击左上角「设置TTS」，将手机的默认的文字转语音引擎改成「TTS」==
[![](http://i0.hdslb.com/bfs/article/1942cfd61233c817c25ed76da84abab2f5f1f562.jpg@1256w_1100h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/1942cfd61233c817c25ed76da84abab2f5f1f562.jpg@1256w_1100h_!web-article-pic.avif)
==然后回到APP内，默认的AI是晓晓，如果你要切换成其他AI，要先勾选「自定义」，然后再点击切换！==
[![](http://i0.hdslb.com/bfs/article/e99d5c16bdaa7d01a10dd0266e38469334a7dbf6.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/e99d5c16bdaa7d01a10dd0266e38469334a7dbf6.png@!web-article-pic.avif)
==阿虚自己是比较喜欢云希这个AI 的声音，讲话风格你也可以自行选择（有新闻／客服／助手／闲聊等等），阿虚自己是用的默认==
[![](http://i0.hdslb.com/bfs/article/188028885145abd230f9093ff3edd554e53b8956.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/188028885145abd230f9093ff3edd554e53b8956.png@!web-article-pic.avif)
==然后因为TTS是系统级的功能，基本上的电子书阅读器都是支持调用系统TTS来朗读的==
[![](http://i0.hdslb.com/bfs/article/0e793e2fe9aabb2b604a2c00681defca400f98c1.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/0e793e2fe9aabb2b604a2c00681defca400f98c1.png@!web-article-pic.avif)
==你立马就能在你常用的阅读、静读天下这类APP上体验到微软TTS的牛逼了！==
[![](http://i0.hdslb.com/bfs/article/b213fd65feb0b2c3711eccc60293f2901a5a0049.jpg@1256w_1110h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/b213fd65feb0b2c3711eccc60293f2901a5a0049.jpg@1256w_1110h_!web-article-pic.avif)
==不过这个TTS据悉会时不时存在卡顿、跳读问题，网友们给出的解决方法有以下这些：==
1. ==换格式==
2. ==换AI发声人==
3. ==换时端==
==换格式的意思是，在TTS右上角的设置中，更改音频流的格式==
==说白了就是降低音频质量，32kbit／64kbit／128kbit 这些总能明白吧==
[![](http://i0.hdslb.com/bfs/article/d817e4acd3e4f14813eeb95776ddc4695d82e0ae.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/d817e4acd3e4f14813eeb95776ddc4695d82e0ae.png@!web-article-pic.avif)
==理论上来说如果是网络问题导致的朗读卡顿，降低音频质量就应该是能明显改善问题的==
[![](http://i0.hdslb.com/bfs/article/a30e7b64c2d589d5b020b90d9280f0fb389e88b1.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/a30e7b64c2d589d5b020b90d9280f0fb389e88b1.png@!web-article-pic.avif)
==而会出现以上问题的原因，大抵是因为大家用的都是作者的 API 导致的（或者是作者从某处挖来的），公共服务的缺点自然是用的人越多越不好用...==
## ==1.2 大声朗读==
==如果你想要长期稳定可用的微软TTS服务，还是建议各位用大声朗读这款APP==
[![](http://i0.hdslb.com/bfs/article/57fa81a910ef40e366bb9418dd8960b1a30a688b.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/57fa81a910ef40e366bb9418dd8960b1a30a688b.png@!web-article-pic.avif)
==不过使用这款APP的前提是你得满足这俩条件之一：有国外的 edu 邮箱或者有VISA、MasterCard这类双币卡==
==但其实没有也有办法，你也可以借助万能的淘宝==
[![](http://i0.hdslb.com/bfs/article/d817e4acd3e4f14813eeb95776ddc4695d82e0ae.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/d817e4acd3e4f14813eeb95776ddc4695d82e0ae.png@!web-article-pic.avif)
==现在还是能找到很多 edu 邮箱的卖家的，不过请注意：不是所有的 edu 邮箱都能订阅 Azure⚠️，买之前请自己详询卖家！==
[![](http://i0.hdslb.com/bfs/article/8e900181f4539f3feaa58e5f2625ab9b8f9886d9.jpg@1256w_550h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/8e900181f4539f3feaa58e5f2625ab9b8f9886d9.jpg@1256w_550h_!web-article-pic.avif)
==或者你也可以选择淘宝找一家虚拟信用卡用于过 Azure 的支付验证（后续使用并不会扣费）==
[![](http://i0.hdslb.com/bfs/article/495bb3edcbcf23adb379b488ade98414e479f8fb.jpg@1256w_530h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/495bb3edcbcf23adb379b488ade98414e479f8fb.jpg@1256w_530h_!web-article-pic.avif)
==总之若你满足以上条件，你就能自行注册微软 Azure 服务，然后借用大声朗读这款APP，从而获取到长期稳定可用的微软TTS服务啦！==
[![](http://i0.hdslb.com/bfs/article/73819786d8427fd341bd7ba4d7fd53d5cba7d5d9.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/73819786d8427fd341bd7ba4d7fd53d5cba7d5d9.png@!web-article-pic.avif)
==这个APP是由酷安大佬@I I I I I 开发的：http://www.coolapk.com/u/453544（可用酷安打开此链接，此链接为作者ID地址）==
==不过由于他现在已经隐藏了之前的动态，我没找到他原始发布的动态，你可以点击这里来获取这款APP：https://www.yuque.com/docs/share/276153fb-74a1-4024-b7c7-6d9681be58eb==
==APP现在已经支持自动更新了，后续也不担心找不到最新版这种问题了==
[![](http://i0.hdslb.com/bfs/article/c507b0c695e55f7c7e8677ce0eb4d4ea458130e0.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/c507b0c695e55f7c7e8677ce0eb4d4ea458130e0.png@!web-article-pic.avif)
==有国外 edu邮箱的话，你可以直接搜「面相学生的Azure」，然后填学校邮箱注册就行了，不需要信用卡（前提是你的 edu邮箱支持过Azure订阅）==
[![](http://i0.hdslb.com/bfs/article/e563c31e1e47f239c4ac26bb942cc09addcdb56a.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/e563c31e1e47f239c4ac26bb942cc09addcdb56a.png@!web-article-pic.avif)
==要国外 edu邮箱是因为，由于国人大量薅微软的羊毛，国内的 edu邮箱的优惠早被薅没了...==
[![](http://i0.hdslb.com/bfs/article/459f5a35f3d770e42874ef00a29aeb43df06e552.png@1256w_556h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/459f5a35f3d770e42874ef00a29aeb43df06e552.png@1256w_556h_!web-article-pic.avif)
==下面阿虚只介绍下有双币卡的情况怎么注册并使用==
==你别担心：双币卡只是用于过验证，并不会真的扣款，并且后续使用也并不会扣款==
[![](http://i0.hdslb.com/bfs/article/6f31be699227685afc82d45c67020cd37ffe6be1.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/6f31be699227685afc82d45c67020cd37ffe6be1.png@!web-article-pic.avif)
==第一步打开此地址：https://azure.microsoft.com/zh-cn/free/cognitive-services/==
==类似于谷歌云，首次注册 Azure 账号即微软云，会免费给你提供12个月的热门服务，还有200美元的服务付费额度，不过我们后续会用到的AI语音服务是有免费版提供的，所以并不用担心1年之后就要收费了==
[![](http://i0.hdslb.com/bfs/article/c13dc79ce8554d00abd8e864978113fbe0ffe67d.jpg@1256w_382h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/c13dc79ce8554d00abd8e864978113fbe0ffe67d.jpg@1256w_382h_!web-article-pic.avif)
==在上方地址「点击免费开始使用」后登录你的微软账号，并填写相关账号信息＋勾选协议＋填写双币信用卡信息后==
[![](http://i0.hdslb.com/bfs/article/8bef24a6e0425bac5e0bd624cc5a66a8722a104a.jpg@1256w_2280h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/8bef24a6e0425bac5e0bd624cc5a66a8722a104a.jpg@1256w_2280h_!web-article-pic.avif)
==耐心等待信用卡验证注册好账号后，打开此地址：https://azure.microsoft.com/zh-cn/services/cognitive-services/text-to-speech/==
==点击「已在使用Azure？立即免费试用此服务」==
[![](http://i0.hdslb.com/bfs/article/d908c78aa701484475c07d3d208c40dabcba88d8.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/d908c78aa701484475c07d3d208c40dabcba88d8.png@!web-article-pic.avif)
[![](http://i0.hdslb.com/bfs/article/ae4ced4e653f8dd73274ca51ff996920b19ba9de.jpg@1256w_504h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/ae4ced4e653f8dd73274ca51ff996920b19ba9de.jpg@1256w_504h_!web-article-pic.avif)
==在创建资源页面按下图方式进行，只需要注意订阅和Pricing tier处都选择免费选项即可==
==然后点击底部的「审阅并创建」，等待审阅通过，继续点击一下「创建」==
[![](http://i0.hdslb.com/bfs/article/16f0ede42f71f583855d938324592666d0e5f86e.jpg@1256w_1760h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/16f0ede42f71f583855d938324592666d0e5f86e.jpg@1256w_1760h_!web-article-pic.avif)
==创建好之后，点击「转到资源」==
[![](http://i0.hdslb.com/bfs/article/c0978c18ace27397faf814e032e0ae7b24e90939.jpg@1256w_912h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/c0978c18ace27397faf814e032e0ae7b24e90939.jpg@1256w_912h_!web-article-pic.avif)
==点击管理密钥==
[![](http://i0.hdslb.com/bfs/article/ae1e1aa4a08182c331b19baf2b69dc93f0dd0394.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/ae1e1aa4a08182c331b19baf2b69dc93f0dd0394.png@!web-article-pic.avif)
[![](http://i0.hdslb.com/bfs/article/cf861621807ac14a11bec6f81bf5da8e8b7b624c.jpg@1256w_604h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/cf861621807ac14a11bec6f81bf5da8e8b7b624c.jpg@1256w_604h_!web-article-pic.avif)
==你会看到两个密钥，随便用一个就行==
[![](http://i0.hdslb.com/bfs/article/4cd016afa4b28e3ae146cb99ab8b8dc3d81602a8.jpg@1256w_600h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/4cd016afa4b28e3ae146cb99ab8b8dc3d81602a8.jpg@1256w_600h_!web-article-pic.avif)
==把密钥填到大声朗读的APP里即可，然后点击「SSML语音合成标记语言」==
[![](http://i0.hdslb.com/bfs/article/e25a1c11453bf549bc3c87a0d5d7c67f1c3031e2.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/e25a1c11453bf549bc3c87a0d5d7c67f1c3031e2.png@!web-article-pic.avif)
[![](http://i0.hdslb.com/bfs/article/8b96d97510613af53b9090ead66e84f563edc993.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/8b96d97510613af53b9090ead66e84f563edc993.png@!web-article-pic.avif)
==输入下方代码并保存，然后就可以随心使用了 ~==
[![](http://i0.hdslb.com/bfs/article/505d2efab1f71089c618bf6a7b646b7d14add33b.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/505d2efab1f71089c618bf6a7b646b7d14add33b.png@!web-article-pic.avif)
==关于这段代码，你可以按需调整这３部分：==
1. ==name="zh-CN-YunxiNeural"，即阿虚上图使用的是Yunxi（云希）这款AI语音==
2. ==style="assistant"，表示讲话风格设置的则是assistant（以热情而轻松的语气对数字助理讲话）==
3. ==styledegree="2"，这个值可以指定说话风格的强度， 默认值为1，最小值0.01，最大值2==
    
    ==  
      
    ====  
      
    ====  
    <mstts:express-as style="assistant" styledegree="2">${text}  
    </mstts:express-as>  
      
    ====  
      
    ====  
      
    ==
    
==如果你想要自行更换以上配置，你需要参考微软官方的文档：https://docs.microsoft.com/zh-cn/azure/cognitive-services/speech-service/speech-synthesis-markup==
==你需要知道的是，每个AI所拥有的 Style 风格是不一样的，比如 Yunyang 目前就只有 customerservice 这一种风格==
[![](http://i0.hdslb.com/bfs/article/e563c31e1e47f239c4ac26bb942cc09addcdb56a.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/e563c31e1e47f239c4ac26bb942cc09addcdb56a.png@!web-article-pic.avif)
==而 Yunye 则有calm、cheerful、sad、angry等等多种风格可选==
==最后需要注意的是目前微软Azure的免费额度是每月50万文本转语音字符==
[![](http://i0.hdslb.com/bfs/article/f18b45589d2af3639887b90c184f4aa83f5d8f82.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/f18b45589d2af3639887b90c184f4aa83f5d8f82.png@!web-article-pic.avif)
==最新的免费额度见这里：https://azure.microsoft.com/zh-cn/pricing/details/cognitive-services/speech-services/==
## ==2 iOS端==
## ==2.1 源阅读==
==iOS上就没有像安卓端的TTS那样装上就能用的APP了==
==目前阿虚所知仅有「源阅读」这款APP支持自定义添加微软AI语音==
==具体来说就是必须自己去注册微软Azure账户申请Key才能使用，具体申请步骤就请自行参考本文1.2节了，在此阿虚便不再复述==
==但是这里有一点非常重要的步骤需要注意：目前iOS源阅读1.4.8版本还不能自定义TTS服务区域，导致目前想要用上的话，必须在最开始创建语音服务时选择东亚地区（East Asia）⚠️==
==就只有这一点需要注意，后续到是就没啥了==
==打开源阅读APP，找到设置 » 语音管理 » 创建云语音==
[![](http://i0.hdslb.com/bfs/article/2cefc7000b1113fc1a31d17230f19cc5a5fe872c.png@1256w_576h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/2cefc7000b1113fc1a31d17230f19cc5a5fe872c.png@1256w_576h_!web-article-pic.avif)
==服务商选择Azure，语音名称随便输入，然后填入你自己的Key，发音人的设置方法依旧请参考本文1.2节==
[![](http://i0.hdslb.com/bfs/article/086251eca4a64366675d72e7e3093b1c96005100.png@1256w_930h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/086251eca4a64366675d72e7e3093b1c96005100.png@1256w_930h_!web-article-pic.avif)
==随后就直接能在听书的时候选择并使用了==
[![](http://i0.hdslb.com/bfs/article/473fc18d4a8672bc42d3ef716a624b06ecd08b99.jpg@1256w_1668h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/473fc18d4a8672bc42d3ef716a624b06ecd08b99.jpg@1256w_1668h_!web-article-pic.avif)
==另外就是这款APP由于一些原因，在2022年1月的时候从APPStore的中国区下架了，目前想要下载这款APP，需要到国外区的APPStore下载==
==具体如何注册国外APPle ID，请自行参考我原来的这篇教程：https://zhuanlan.zhihu.com/p/49754407==
## ==2.2 云手机==
==另外一个可行的方法就是之前写iOS微信双开时候提到的，花钱使用云手机了==
[![](http://i0.hdslb.com/bfs/article/71010eadff7eab4c573d99cac2bf077f57cdc8d8.png@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/71010eadff7eab4c573d99cac2bf077f57cdc8d8.png@!web-article-pic.avif)
==https://mp.weixin.qq.com/s/fDtU19-QNB3tdDCaPaKOFw==
==云手机是安卓系统，那你就只需要回头去参考本文第一节的内容就行了==
==GIF==
[![](http://i0.hdslb.com/bfs/article/93dcce95faba5d0a1b4993ccc840c0024c9a98e1.gif@1256w_174h_!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/93dcce95faba5d0a1b4993ccc840c0024c9a98e1.gif@1256w_174h_!web-article-pic.avif)
==另外如果你不是想用于听书，而是想将微软AI语音用于自媒体配音，相关软件、网站请参看本文原文：https://mp.weixin.qq.com/s/RX3ZtPU4CjcH_tuKtNuxmA==
==GIF==
[![](http://i0.hdslb.com/bfs/article/eb6ba0b723a6e09353c8aeecf0857773acf6848d.gif@!web-article-pic.avif)](http://i0.hdslb.com/bfs/article/eb6ba0b723a6e09353c8aeecf0857773acf6848d.gif@!web-article-pic.avif)