---
Updated: 2024-08-09T23:11
tags:
  - AI->-OpenAI
  - AI->-Programming
Created: 2024-08-07T11:50
---
实验笔记
Structured Outputs for Multi-Agent Systems [[🦜🦜🦜OpenAI - Build Hour- Assistants & Agents]]
How to use Structured Outputs如何使用结构化输出
Safe Structured Outputs 安全的结构化输出
Native SDK support 本地 SDK 支持
Additional use cases 其他用例
Dynamically generating user interfaces based on the user’s intent根据用户意图动态生成用户界面
Separating a final answer from supporting reasoning or additional commentary将最终答案与支持性推理或补充评注分开
Extracting structured data from unstructured data从非结构化数据中提取结构化数据
Under the hood 引擎盖下
Constrained decoding 受限解码
Alternate approaches 替代方法
Limitations and restrictions限制和约束
Availability 可用性
Acknowledgements 致谢
Author 作者
Core contributors 核心贡献者
Acknowledgments 致谢
# 实验笔记
/opt/workspace/researcher/AllKindsTest.ipynb
  
```YAML
from pydantic import BaseModel
from openai import OpenAI
class Step(BaseModel):
    explanation: str
    output: str
class MathResponse(BaseModel):
    steps: list[Step]
    final_answer: str
client = OpenAI()
def format_step(step, step_number):
    return f"Step {step_number}:\n" \
           f"  Explanation: {step.explanation}\n" \
           f"  Result: {step.output}\n"
def format_solution(steps, final_answer):
    formatted_steps = "\n".join(format_step(step, i+1) for i, step in enumerate(steps))
    return f"{formatted_steps}\nFinal Answer: {final_answer}\n"

completion = client.beta.chat.completions.parse(
    model="gpt-4o-2024-08-06",
    messages=[
        {"role": "system", "content": "You are a helpful math tutor."},
        {"role": "user", "content": "solve 8x + 31 = 2"},
    ],
    response_format=MathResponse,
)
message = completion.choices[0].message
if message.parsed:
    print(format_solution(message.parsed.steps, message.parsed.final_answer))
    # print(message.parsed.steps)
    # print(message.parsed.final_answer)
else:
    print(message.refusal)
```
/opt/workspace/researcher/openai-cookbook/examples/Structured_Outputs_Intro.ipynb
```JavaScript
1. dedent 的使用
2. Math 的使用
```
```JavaScript
math_tutor_prompt = '''
    You are a helpful math tutor. You will be provided with a math problem,
    and your goal will be to output a step by step solution, along with a final answer.
    For each step, just provide the output as an equation use the explanation field to detail the reasoning.
'''
def get_math_solution(question):
    response = client.chat.completions.create(
    model=MODEL,
    messages=[
        {
            "role": "system", 
            "content": dedent(math_tutor_prompt)
        },
        {
            "role": "user", 
            "content": question
        }
    ],
    response_format={
        "type": "json_schema",
        "json_schema": {
            "name": "math_reasoning",
            "schema": {
                "type": "object",
                "properties": {
                    "steps": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "explanation": {"type": "string"},
                                "output": {"type": "string"}
                            },
                            "required": ["explanation", "output"],
                            "additionalProperties": False
                        }
                    },
                    "final_answer": {"type": "string"}
                },
                "required": ["steps", "final_answer"],
                "additionalProperties": False
            },
            "strict": True
        }
    }
    )
    return response.choices[0].message
    
from IPython.display import Math, display
def print_math_response(response):
    result = json.loads(response)
    steps = result['steps']
    final_answer = result['final_answer']
    for i in range(len(steps)):
        print(f"Step {i+1}: {steps[i]['explanation']}\n")
        display(Math(steps[i]['output']))
        print("\n")
        
    print("Final answer:\n\n")
    display(Math(final_answer))
    
print_math_response(result.content)        
```
  
More complex structure  
```JavaScript
summarization_prompt = '''
    You will be provided with content from an article about an invention.
    Your goal will be to summarize the article following the schema provided.
    Here is a description of the parameters:
    - invented_year: year in which the invention discussed in the article was invented
    - summary: one sentence summary of what the invention is
    - inventors: array of strings listing the inventor full names if present, otherwise just surname
    - concepts: array of key concepts related to the invention, each concept containing a title and a description
    - description: short description of the invention
'''
class ArticleSummary(BaseModel):
    invented_year: int
    summary: str
    inventors: list[str]
    description: str
    class Concept(BaseModel):
        title: str
        description: str
    concepts: list[Concept]
def get_article_summary(text: str):
    completion = client.beta.chat.completions.parse(
        model=MODEL,
        temperature=0.2,
        messages=[
            {"role": "system", "content": dedent(summarization_prompt)},
            {"role": "user", "content": text}
        ],
        response_format=ArticleSummary,
    )
    return completion.choices[0].message.parsed
summaries = []
for i in range(len(content)):
    print(f"Analyzing article #{i+1}...")
    summaries.append(get_article_summary(content[i]))
    print("Done.")
    
def print_summary(summary):
    print(f"Invented year: {summary.invented_year}\n")
    print(f"Summary: {summary.summary}\n")
    print("Inventors:")
    for i in summary.inventors:
        print(f"- {i}")
    print("\nConcepts:")
    for c in summary.concepts:
        print(f"- {c.title}: {c.description}")
    print(f"\nDescription: {summary.description}")
for i in range(len(summaries)):
    print(f"ARTICLE {i}\n")
    print_summary(summaries[i])
    print("\n\n")     
```
```JavaScript
from enum import Enum
from typing import Union
import openai
product_search_prompt = '''
    You are a clothes recommendation agent, specialized in finding the perfect match for a user.
    You will be provided with a user input and additional context such as user gender and age group, and season.
    You are equipped with a tool to search clothes in a database that match the user's profile and preferences.
    Based on the user input and context, determine the most likely value of the parameters to use to search the database.
    
    Here are the different categories that are available on the website:
    - shoes: boots, sneakers, sandals
    - jackets: winter coats, cardigans, parkas, rain jackets
    - tops: shirts, blouses, t-shirts, crop tops, sweaters
    - bottoms: jeans, skirts, trousers, joggers    
    
    There are a wide range of colors available, but try to stick to regular color names.
'''
class Category(str, Enum):
    shoes = "shoes"
    jackets = "jackets"
    tops = "tops"
    bottoms = "bottoms"
class ProductSearchParameters(BaseModel):
    category: Category
    subcategory: str
    color: str
def get_response(user_input, context):
    response = client.chat.completions.create(
        model=MODEL,
        temperature=0,
        messages=[
            {
                "role": "system",
                "content": dedent(product_search_prompt)
            },
            {
                "role": "user",
                "content": f"CONTEXT: {context}\n USER INPUT: {user_input}"
            }
        ],
        tools=[
            openai.pydantic_function_tool(ProductSearchParameters, name="product_search", description="Search for a match in the product database")
        ]
    )
    return response.choices[0].message.tool_calls

example_inputs = [
    {
        "user_input": "I'm looking for a new coat. I'm always cold so please something warm! Ideally something that matches my eyes.",
        "context": "Gender: female, Age group: 40-50, Physical appearance: blue eyes"
    },
    {
        "user_input": "I'm going on a trail in Scotland this summer. It's goind to be rainy. Help me find something.",
        "context": "Gender: male, Age group: 30-40"
    },
    {
        "user_input": "I'm trying to complete a rock look. I'm missing shoes. Any suggestions?",
        "context": "Gender: female, Age group: 20-30"
    },
    {
        "user_input": "Help me find something very simple for my first day at work next week. Something casual and neutral.",
        "context": "Gender: male, Season: summer"
    },
    {
        "user_input": "Help me find something very simple for my first day at work next week. Something casual and neutral.",
        "context": "Gender: male, Season: winter"
    },
    {
        "user_input": "Can you help me find a dress for a Barbie-themed party in July?",
        "context": "Gender: female, Age group: 20-30"
    }
]    

def print_tool_call(user_input, context, tool_call):
    args = tool_call[0].function.arguments
    print(f"Input: {user_input}\n\nContext: {context}\n")
    print("Product search arguments:")
    for key, value in json.loads(args).items():
        print(f"{key}: '{value}'")
    print("\n\n")

for ex in example_inputs:
    ex['result'] = get_response(ex['user_input'], ex['context'])
    
for ex in example_inputs:
    print_tool_call(ex['user_input'], ex['context'], ex['result'])      
```
## **Structured Outputs for Multi-Agent Systems**  
  
[[🦜🦜🦜OpenAI - Build Hour- Assistants & Agents]]
```JavaScript
from openai import OpenAI
from IPython.display import Image
import json
import pandas as pd
import matplotlib.pyplot as plt
from io import StringIO
import numpy as np
client = OpenAI()
MODEL = "gpt-4o-2024-08-06"
triaging_system_prompt = """You are a Triaging Agent. Your role is to assess the user's query and route it to the relevant agents. The agents available are:
- Data Processing Agent: Cleans, transforms, and aggregates data.
- Analysis Agent: Performs statistical, correlation, and regression analysis.
- Visualization Agent: Creates bar charts, line charts, and pie charts.
Use the send_query_to_agents tool to forward the user's query to the relevant agents. Also, use the speak_to_user tool to get more information from the user if needed."""
processing_system_prompt = """You are a Data Processing Agent. Your role is to clean, transform, and aggregate data using the following tools:
- clean_data
- transform_data
- aggregate_data"""
analysis_system_prompt = """You are an Analysis Agent. Your role is to perform statistical, correlation, and regression analysis using the following tools:
- stat_analysis
- correlation_analysis
- regression_analysis"""
visualization_system_prompt = """You are a Visualization Agent. Your role is to create bar charts, line charts, and pie charts using the following tools:
- create_bar_chart
- create_line_chart
- create_pie_chart"""
triage_tools = [
    {
        "type": "function",
        "function": {
            "name": "send_query_to_agents",
            "description": "Sends the user query to relevant agents based on their capabilities.",
            "parameters": {
                "type": "object",
                "properties": {
                    "agents": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "An array of agent names to send the query to."
                    },
                    "query": {
                        "type": "string",
                        "description": "The user query to send."
                    }
                },
                "required": ["agents", "query"]
            }
        },
        "strict": True
    }
]
preprocess_tools = [
    {
        "type": "function",
        "function": {
            "name": "clean_data",
            "description": "Cleans the provided data by removing duplicates and handling missing values.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The dataset to clean. Should be in a suitable format such as JSON or CSV."
                    }
                },
                "required": ["data"],
                "additionalProperties": False
            }
        },
        "strict": True
    },
    {
        "type": "function",
        "function": {
            "name": "transform_data",
            "description": "Transforms data based on specified rules.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The data to transform. Should be in a suitable format such as JSON or CSV."
                    },
                    "rules": {
                        "type": "string",
                        "description": "Transformation rules to apply, specified in a structured format."
                    }
                },
                "required": ["data", "rules"],
                "additionalProperties": False
            }
        },
        "strict": True
    },
    {
        "type": "function",
        "function": {
            "name": "aggregate_data",
            "description": "Aggregates data by specified columns and operations.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The data to aggregate. Should be in a suitable format such as JSON or CSV."
                    },
                    "group_by": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Columns to group by."
                    },
                    "operations": {
                        "type": "string",
                        "description": "Aggregation operations to perform, specified in a structured format."
                    }
                },
                "required": ["data", "group_by", "operations"],
                "additionalProperties": False
            }
        },
        "strict": True
    }
]

analysis_tools = [
    {
        "type": "function",
        "function": {
            "name": "stat_analysis",
            "description": "Performs statistical analysis on the given dataset.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The dataset to analyze. Should be in a suitable format such as JSON or CSV."
                    }
                },
                "required": ["data"],
                "additionalProperties": False
            }
        },
        "strict": True
    },
    {
        "type": "function",
        "function": {
            "name": "correlation_analysis",
            "description": "Calculates correlation coefficients between variables in the dataset.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The dataset to analyze. Should be in a suitable format such as JSON or CSV."
                    },
                    "variables": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of variables to calculate correlations for."
                    }
                },
                "required": ["data", "variables"],
                "additionalProperties": False
            }
        },
        "strict": True
    },
    {
        "type": "function",
        "function": {
            "name": "regression_analysis",
            "description": "Performs regression analysis on the dataset.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The dataset to analyze. Should be in a suitable format such as JSON or CSV."
                    },
                    "dependent_var": {
                        "type": "string",
                        "description": "The dependent variable for regression."
                    },
                    "independent_vars": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of independent variables."
                    }
                },
                "required": ["data", "dependent_var", "independent_vars"],
                "additionalProperties": False
            }
        },
        "strict": True
    }
]
visualization_tools = [
    {
        "type": "function",
        "function": {
            "name": "create_bar_chart",
            "description": "Creates a bar chart from the provided data.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The data for the bar chart. Should be in a suitable format such as JSON or CSV."
                    },
                    "x": {
                        "type": "string",
                        "description": "Column for the x-axis."
                    },
                    "y": {
                        "type": "string",
                        "description": "Column for the y-axis."
                    }
                },
                "required": ["data", "x", "y"],
                "additionalProperties": False
            }
        },
        "strict": True
    },
    {
        "type": "function",
        "function": {
            "name": "create_line_chart",
            "description": "Creates a line chart from the provided data.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The data for the line chart. Should be in a suitable format such as JSON or CSV."
                    },
                    "x": {
                        "type": "string",
                        "description": "Column for the x-axis."
                    },
                    "y": {
                        "type": "string",
                        "description": "Column for the y-axis."
                    }
                },
                "required": ["data", "x", "y"],
                "additionalProperties": False
            }
        },
        "strict": True
    },
    {
        "type": "function",
        "function": {
            "name": "create_pie_chart",
            "description": "Creates a pie chart from the provided data.",
            "parameters": {
                "type": "object",
                "properties": {
                    "data": {
                        "type": "string",
                        "description": "The data for the pie chart. Should be in a suitable format such as JSON or CSV."
                    },
                    "labels": {
                        "type": "string",
                        "description": "Column for the labels."
                    },
                    "values": {
                        "type": "string",
                        "description": "Column for the values."
                    }
                },
                "required": ["data", "labels", "values"],
                "additionalProperties": False
            }
        },
        "strict": True
    }
]
# Example query
user_query = """
Below is some data. I want you to first remove the duplicates then analyze the statistics of the data as well as plot a line chart.
house_size (m3), house_price ($)
90, 100
80, 90
100, 120
90, 100
"""

def clean_data(data):
    data_io = StringIO(data)
    df = pd.read_csv(data_io, sep=",")
    df_deduplicated = df.drop_duplicates()
    return df_deduplicated
def stat_analysis(data):
    data_io = StringIO(data)
    df = pd.read_csv(data_io, sep=",")
    return df.describe()
def plot_line_chart(data):
    data_io = StringIO(data)
    df = pd.read_csv(data_io, sep=",")
    
    x = df.iloc[:, 0]
    y = df.iloc[:, 1]
    
    coefficients = np.polyfit(x, y, 1)
    polynomial = np.poly1d(coefficients)
    y_fit = polynomial(x)
    
    plt.figure(figsize=(10, 6))
    plt.plot(x, y, 'o', label='Data Points')
    plt.plot(x, y_fit, '-', label='Best Fit Line')
    plt.title('Line Chart with Best Fit Line')
    plt.xlabel(df.columns[0])
    plt.ylabel(df.columns[1])
    plt.legend()
    plt.grid(True)
    plt.show()
# Define the function to execute the tools
def execute_tool(tool_calls, messages):
    for tool_call in tool_calls:
        tool_name = tool_call.function.name
        tool_arguments = json.loads(tool_call.function.arguments)
        if tool_name == 'clean_data':
            # Simulate data cleaning
            cleaned_df = clean_data(tool_arguments['data'])
            cleaned_data = {"cleaned_data": cleaned_df.to_dict()}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(cleaned_data)})
            print('Cleaned data: ', cleaned_df)
        elif tool_name == 'transform_data':
            # Simulate data transformation
            transformed_data = {"transformed_data": "sample_transformed_data"}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(transformed_data)})
        elif tool_name == 'aggregate_data':
            # Simulate data aggregation
            aggregated_data = {"aggregated_data": "sample_aggregated_data"}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(aggregated_data)})
        elif tool_name == 'stat_analysis':
            # Simulate statistical analysis
            stats_df = stat_analysis(tool_arguments['data'])
            stats = {"stats": stats_df.to_dict()}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(stats)})
            print('Statistical Analysis: ', stats_df)
        elif tool_name == 'correlation_analysis':
            # Simulate correlation analysis
            correlations = {"correlations": "sample_correlations"}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(correlations)})
        elif tool_name == 'regression_analysis':
            # Simulate regression analysis
            regression_results = {"regression_results": "sample_regression_results"}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(regression_results)})
        elif tool_name == 'create_bar_chart':
            # Simulate bar chart creation
            bar_chart = {"bar_chart": "sample_bar_chart"}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(bar_chart)})
        elif tool_name == 'create_line_chart':
            # Simulate line chart creation
            line_chart = {"line_chart": "sample_line_chart"}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(line_chart)})
            plot_line_chart(tool_arguments['data'])
        elif tool_name == 'create_pie_chart':
            # Simulate pie chart creation
            pie_chart = {"pie_chart": "sample_pie_chart"}
            messages.append({"role": "tool", "name": tool_name, "content": json.dumps(pie_chart)})
    return messages
# Define the functions to handle each agent's processing
def handle_data_processing_agent(query, conversation_messages):
    messages = [{"role": "system", "content": processing_system_prompt}]
    messages.append({"role": "user", "content": query})
    response = client.chat.completions.create(
        model=MODEL,
        messages=messages,
        temperature=0,
        tools=preprocess_tools,
    )
    conversation_messages.append([tool_call.function for tool_call in response.choices[0].message.tool_calls])
    execute_tool(response.choices[0].message.tool_calls, conversation_messages)
def handle_analysis_agent(query, conversation_messages):
    messages = [{"role": "system", "content": analysis_system_prompt}]
    messages.append({"role": "user", "content": query})
    response = client.chat.completions.create(
        model=MODEL,
        messages=messages,
        temperature=0,
        tools=analysis_tools,
    )
    conversation_messages.append([tool_call.function for tool_call in response.choices[0].message.tool_calls])
    execute_tool(response.choices[0].message.tool_calls, conversation_messages)
def handle_visualization_agent(query, conversation_messages):
    messages = [{"role": "system", "content": visualization_system_prompt}]
    messages.append({"role": "user", "content": query})
    response = client.chat.completions.create(
        model=MODEL,
        messages=messages,
        temperature=0,
        tools=visualization_tools,
    )
    conversation_messages.append([tool_call.function for tool_call in response.choices[0].message.tool_calls])
    execute_tool(response.choices[0].message.tool_calls, conversation_messages)
# Function to handle user input and triaging
def handle_user_message(user_query, conversation_messages=[]):
    user_message = {"role": "user", "content": user_query}
    conversation_messages.append(user_message)

    messages = [{"role": "system", "content": triaging_system_prompt}]
    messages.extend(conversation_messages)
    response = client.chat.completions.create(
        model=MODEL,
        messages=messages,
        temperature=0,
        tools=triage_tools,
    )
    conversation_messages.append([tool_call.function for tool_call in response.choices[0].message.tool_calls])
    for tool_call in response.choices[0].message.tool_calls:
        if tool_call.function.name == 'send_query_to_agents':
            agents = json.loads(tool_call.function.arguments)['agents']
            query = json.loads(tool_call.function.arguments)['query']
            for agent in agents:
                if agent == "Data Processing Agent":
                    handle_data_processing_agent(query, conversation_messages)
                elif agent == "Analysis Agent":
                    handle_analysis_agent(query, conversation_messages)
                elif agent == "Visualization Agent":
                    handle_visualization_agent(query, conversation_messages)
    return conversation_messages
handle_user_message(user_query)	
```
  
[![](https://images.ctfassets.net/kftzwdyauwt9/1XeXlBlWdUBSPFcVPsOmOD/dc8123f1031a0f9fe1b816790ee510a9/Structured_Outputs_Cover.png?w=1600&h=900&fit=fill)](https://images.ctfassets.net/kftzwdyauwt9/1XeXlBlWdUBSPFcVPsOmOD/dc8123f1031a0f9fe1b816790ee510a9/Structured_Outputs_Cover.png?w=1600&h=900&fit=fill)
==August 6, 2024 2024 年 8 月 6 日==
==We are introducing Structured Outputs in the API—model outputs now reliably adhere to developer-supplied JSON Schemas.====  
  
====我们在应用程序接口中引入了结构化输出（Structured Outputs）--模型输出现在可以可靠地遵循开发人员提供的 JSON 模式。==
[![](https://images.ctfassets.net/kftzwdyauwt9/1XeXlBlWdUBSPFcVPsOmOD/dc8123f1031a0f9fe1b816790ee510a9/Structured_Outputs_Cover.png?w=3840&q=90&fm=webp)](https://images.ctfassets.net/kftzwdyauwt9/1XeXlBlWdUBSPFcVPsOmOD/dc8123f1031a0f9fe1b816790ee510a9/Structured_Outputs_Cover.png?w=3840&q=90&fm=webp)
==Last year at DevDay, we introduced JSON mode—a useful building block for developers looking to build reliable applications with our models. While JSON mode improves model reliability for generating valid JSON outputs, it does not guarantee that the model’s response will conform to a particular schema. Today we’re introducing Structured Outputs in the API, a new feature designed to ensure model-generated outputs will exactly match JSON Schemas provided by developers.====  
  
====在去年的 DevDay 上，我们推出了 JSON 模式--对于希望使用我们的模型构建可靠应用程序的开发人员来说，这是一个非常有用的构建模块。虽然 JSON 模式提高了模型生成有效 JSON 输出的可靠性，但它并不能保证模型的响应符合特定模式。今天，我们在 API 中引入了结构化输出，这是一项新功能，旨在确保模型生成的输出与开发人员提供的 JSON 模式完全匹配。==
==Generating structured data from unstructured inputs is one of the core use cases for AI in today’s applications. Developers use the OpenAI API to build powerful assistants that have the ability to fetch data and answer questions via== [==function calling(opens in a new window)==](https://platform.openai.com/docs/guides/function-calling)==, extract structured data for data entry, and build multi-step agentic workflows that allow LLMs to take actions. Developers have long been working around the limitations of LLMs in this area via open source tooling, prompting, and retrying requests repeatedly to ensure that model outputs match the formats needed to interoperate with their systems. Structured Outputs solves this problem by constraining OpenAI models to match developer-supplied schemas and by training our models to better understand complicated schemas.====  
  
====从非结构化输入生成结构化数据是当今应用中人工智能的核心用例之一。开发人员使用 OpenAI API 构建功能强大的助手，这些助手能够通过== [==函数调用(opens in a new window)==](https://platform.openai.com/docs/guides/function-calling) ==获取数据并回答问题、提取用于数据录入的结构化数据，以及构建允许 LLMs 采取行动的多步骤代理工作流。长期以来，开发人员一直在通过开源工具、提示和反复重试请求来解决 LLMs 在这方面的局限性，以确保模型输出与系统互操作所需的格式相匹配。结构化输出解决了这一问题，它限制 OpenAI 模型与开发人员提供的模式相匹配，并训练我们的模型更好地理解复杂的模式。==
==On our evals of complex JSON schema following, our new model== ==`gpt-4o-2024-08-06`== ==with Structured Outputs scores a perfect 100%. In comparison,== ==`gpt-4-0613`== ==scores less than 40%.====  
  
====在我们对复杂 JSON 模式的跟踪评估中，我们的新模型====`gpt-4o-2024-08-06`== ==使用结构化输出获得了完美的 100% 高分。相比之下，====`gpt-4-0613`== ==的得分不到 40%。==
==Prompting Alone 单独提示==
==Structured Outputs (strict=false) 结构化输出（strict=false）==
==Structured Outputs (strict=true) 结构化输出（strict=true）==
==_With Structured Outputs,_== ==`_gpt-4o-2024-08-06_`== ==_achieves 100% reliability in our evals, perfectly matching the output schemas._====  
  
====_具有结构化输出、_====`_gpt-4o-2024-08-06_`== ==_在我们的验证中实现了 100% 的可靠性，与输出模式完全匹配。_==
## ==How to use Structured Outputs====  
  
====如何使用结构化输出==
==We’re introducing Structured Outputs in two forms in the API:== ==  
  
====我们将在应用程序接口中引入两种形式的结构化输出：==
==1.== ==**Function calling:**== ==Structured Outputs via== ==`tools`== ==is available by setting== ==`strict: true`== ==within your function definition. This feature works with all models that support tools, including all models== ==`gpt-4-0613`== ==and== ==`gpt-3.5-turbo-0613`== ==and later. When Structured Outputs are enabled, model outputs will match the supplied tool definition.====  
  
====1.**函数调用：**通过== ==`工具`== ==结构化输出可通过设置== ==`strict：true`== ==在您的函数定义中。此功能适用于所有支持工具的模型，包括所有模型== ==`gpt-4-0613`== ==和== ==`gpt-3.5-turbo-0613`== ==及更高版本。启用结构化输出后，模型输出将与提供的工具定义相匹配。==
==**JSON**==
==`1`==
==`POST /v1/chat/completions`==
==`2`==
==`{`==
==`3`==
==`"model": "gpt-4o-2024-08-06",`==
==`4`==
==`"messages": [`==
==`5`==
==`{`==
==`6`==
==`"role": "system",`==
==`7`==
==`"content": "You are a helpful assistant. The current date is August 6, 2024. You help users query for the data they are looking for by calling the query function."`==
==`8`==
==`},`==
==`9`==
==`{`==
==`10`==
==`"role": "user",`==
==`11`==
==`"content": "look up all my orders in may of last year that were fulfilled but not delivered on time"`==
==`12`==
==`}`==
==`13`==
==`],`==
==`14`==
==`"tools": [`==
==`15`==
==`{`==
==`16`==
==`"type": "function",`==
==`17`==
==`"function": {`==
==`18`==
==`"name": "query",`==
==`19`==
==`"description": "Execute a query.",`==
==`20`==
==`"strict": true,`==
==`21`==
==`"parameters": {`==
==`22`==
==`"type": "object",`==
==`23`==
==`"properties": {`==
==`24`==
==`"table_name": {`==
==`25`==
==`"type": "string",`==
==`26`==
==`"enum": ["orders"]`==
==`27`==
==`},`==
==`28`==
==`"columns": {`==
==`29`==
==`"type": "array",`==
==`30`==
==`"items": {`==
==`31`==
==`"type": "string",`==
==`32`==
==`"enum": [`==
==`33`==
==`"id",`==
==`34`==
==`"status",`==
==`35`==
==`"expected_delivery_date",`==
==`36`==
==`"delivered_at",`==
==`37`==
==`"shipped_at",`==
==`38`==
==`"ordered_at",`==
==`39`==
==`"canceled_at"`==
==`40`==
==`]`==
==`41`==
==`}`==
==`42`==
==`},`==
==`43`==
==`"conditions": {`==
==`44`==
==`"type": "array",`==
==`45`==
==`"items": {`==
==`46`==
==`"type": "object",`==
==`47`==
==`"properties": {`==
==`48`==
==`"column": {`==
==`49`==
==`"type": "string"`==
==`50`==
==`},`==
==`51`==
==`"operator": {`==
==`52`==
==`"type": "string",`==
==`53`==
==`"enum": ["=", ">", "<", ">=", "<=", "!="]`==
==`54`==
==`},`==
==`55`==
==`"value": {`==
==`56`==
==`"anyOf": [`==
==`57`==
==`{`==
==`58`==
==`"type": "string"`==
==`59`==
==`},`==
==`60`==
==`{`==
==`61`==
==`"type": "number"`==
==`62`==
==`},`==
==`63`==
==`{`==
==`64`==
==`"type": "object",`==
==`65`==
==`"properties": {`==
==`66`==
==`"column_name": {`==
==`67`==
==`"type": "string"`==
==`68`==
==`}`==
==`69`==
==`},`==
==`70`==
==`"required": ["column_name"],`==
==`71`==
==`"additionalProperties": false`==
==`72`==
==`}`==
==`73`==
==`]`==
==`74`==
==`}`==
==`75`==
==`},`==
==`76`==
==`"required": ["column", "operator", "value"],`==
==`77`==
==`"additionalProperties": false`==
==`78`==
==`}`==
==`79`==
==`},`==
==`80`==
==`"order_by": {`==
==`81`==
==`"type": "string",`==
==`82`==
==`"enum": ["asc", "desc"]`==
==`83`==
==`}`==
==`84`==
==`},`==
==`85`==
==`"required": ["table_name", "columns", "conditions", "order_by"],`==
==`86`==
==`"additionalProperties": false`==
==`87`==
==`}`==
==`88`==
==`}`==
==`89`==
==`}`==
==`90`==
==`]`==
==`91`==
==`}`==
1. ==**A new option for the**== ==`**response_format**`== ==**parameter:**== ==developers can now supply a JSON Schema via== ==`json_schema`====, a new option for the== ==`response_format`== ==parameter. This is useful when the model is not calling a tool, but rather, responding to the user in a structured way. This feature works with our newest GPT-4o models:== ==`gpt-4o-2024-08-06`====, released today, and== ==`gpt-4o-mini-2024-07-18`====. When a== ==`response_format`== ==is supplied with== ==`strict: true`====, model outputs will match the supplied schema.==
2. ==`**response_format**`== ==**参数的新选项：**开发人员现在可以通过== ==`json_schema`== ==提供 JSON 模式、====`response_format`== ==参数的新选项。当模型不是调用工具，而是以结构化的方式响应用户时，这一功能非常有用。此功能适用于我们最新的 GPT-4o 模型：====`gpt-4o-2024-08-06`== ==今天发布，以及== ==`gpt-4o-mini-2024-07-18`== ==今天发布。当提供的== ==`response_format`== ==中包含== ==`strict: true`== ==时，模型输出将与提供的模式相匹配。==
==**Request 要求**==
==`1`==
==`POST /v1/chat/completions`==
==`2`==
==`{`==
==`3`==
==`"model": "gpt-4o-2024-08-06",`==
==`4`==
==`"messages": [`==
==`5`==
==`{`==
==`6`==
==`"role": "system",`==
==`7`==
==`"content": "You are a helpful math tutor."`==
==`8`==
==`},`==
==`9`==
==`{`==
==`10`==
==`"role": "user",`==
==`11`==
==`"content": "solve 8x + 31 = 2"`==
==`12`==
==`}`==
==`13`==
==`],`==
==`14`==
==`"response_format": {`==
==`15`==
==`"type": "json_schema",`==
==`16`==
==`"json_schema": {`==
==`17`==
==`"name": "math_response",`==
==`18`==
==`"strict": true,`==
==`19`==
==`"schema": {`==
==`20`==
==`"type": "object",`==
==`21`==
==`"properties": {`==
==`22`==
==`"steps": {`==
==`23`==
==`"type": "array",`==
==`24`==
==`"items": {`==
==`25`==
==`"type": "object",`==
==`26`==
==`"properties": {`==
==`27`==
==`"explanation": {`==
==`28`==
==`"type": "string"`==
==`29`==
==`},`==
==`30`==
==`"output": {`==
==`31`==
==`"type": "string"`==
==`32`==
==`}`==
==`33`==
==`},`==
==`34`==
==`"required": ["explanation", "output"],`==
==`35`==
==`"additionalProperties": false`==
==`36`==
==`}`==
==`37`==
==`},`==
==`38`==
==`"final_answer": {`==
==`39`==
==`"type": "string"`==
==`40`==
==`}`==
==`41`==
==`},`==
==`42`==
==`"required": ["steps", "final_answer"],`==
==`43`==
==`"additionalProperties": false`==
==`44`==
==`}`==
==`45`==
==`}`==
==`46`==
==`}`==
==`47`==
==`}`==
## ==Safe Structured Outputs 安全的结构化输出==
==Safety is a top priority for OpenAI—the new Structured Outputs functionality will abide by our existing safety policies and will still allow the model to refuse an unsafe request. To make development simpler, there is a new== ==`refusal`== ==string value on API responses which allows developers to programmatically detect if the model has generated a refusal instead of output matching the schema. When the response does not include a refusal and the model’s response has not been prematurely interrupted (as indicated by== ==`finish_reason`====), then the model’s response will reliably produce valid JSON matching the supplied schema.====  
  
====安全是 OpenAI 的重中之重--新的结构化输出功能将遵守我们现有的安全策略，并仍然允许模型拒绝不安全的请求。为了简化开发，API 响应中新增了一个====`refusal`== ==字符串值，允许开发人员以编程方式检测模型是否生成了拒绝输出，而不是与模式匹配的输出。当响应不包括拒绝且模型的响应未被过早中断（如== ==`finish_reason`== ==所示）时，模型的响应将可靠地生成与提供的模式匹配的有效 JSON。==
==**JSON**==
==`1`==
==`{`==
==`2`==
==`"id": "chatcmpl-9nYAG9LPNonX8DAyrkwYfemr3C8HC",`==
==`3`==
==`"object": "chat.completion",`==
==`4`==
==`"created": 1721596428,`==
==`5`==
==`"model": "gpt-4o-2024-08-06",`==
==`6`==
==`"choices": [`==
==`7`==
==`{`==
==`8`==
==`"index": 0,`==
==`9`==
==`"message": {`==
==`10`==
==`"role": "assistant",`==
==`11`==
==`"refusal": "I'm sorry, I cannot assist with that request."`==
==`12`==
==`},`==
==`13`==
==`"logprobs": null,`==
==`14`==
==`"finish_reason": "stop"`==
==`15`==
==`}`==
==`16`==
==`],`==
==`17`==
==`"usage": {`==
==`18`==
==`"prompt_tokens": 81,`==
==`19`==
==`"completion_tokens": 11,`==
==`20`==
==`"total_tokens": 92`==
==`21`==
==`},`==
==`22`==
==`"system_fingerprint": "fp_3407719c7f"`==
==`23`==
==`}`==
## ==Native SDK support 本地 SDK 支持==
==Our Python and Node SDKs have been updated with native support for Structured Outputs. Supplying a schema for tools or as a response format is as easy as supplying a Pydantic or Zod object, and our SDKs will handle converting the data type to a supported JSON schema, deserializing the JSON response into the typed data structure automatically, and parsing refusals if they arise.====  
  
====我们的 Python 和 Node SDK 已更新为对结构化输出的本地支持。为工具提供模式或作为响应格式就像提供 Pydantic 或 Zod 对象一样简单，我们的 SDK 将处理将数据类型转换为受支持的 JSON 模式、自动将 JSON 响应反序列化为类型化数据结构，以及在出现拒绝时进行解析。==
==The following examples show native support for Structured Outputs with function calling.====  
  
====下面的示例展示了通过函数调用对结构化输出的本地支持。==
==**python 蟒蛇**==
==`1`==
==`from enum import Enum`==
==`2`==
==`from typing import Union`==
==`3`==
==`4`==
==`from pydantic import BaseModel`==
==`5`==
==`6`==
==`import openai`==
==`7`==
==`from openai import OpenAI`==
==`8`==
==`9`==
==`10`==
==`class Table(str, Enum):`==
==`11`==
==`orders = "orders"`==
==`12`==
==`customers = "customers"`==
==`13`==
==`products = "products"`==
==`14`==
==`15`==
==`16`==
==`class Column(str, Enum):`==
==`17`==
==`id = "id"`==
==`18`==
==`status = "status"`==
==`19`==
==`expected_delivery_date = "expected_delivery_date"`==
==`20`==
==`delivered_at = "delivered_at"`==
==`21`==
==`shipped_at = "shipped_at"`==
==`22`==
==`ordered_at = "ordered_at"`==
==`23`==
==`canceled_at = "canceled_at"`==
==`24`==
==`25`==
==`26`==
==`class Operator(str, Enum):`==
==`27`==
==`eq = "="`==
==`28`==
==`gt = ">"`==
==`29`==
==`lt = "<"`==
==`30`==
==`le = "<="`==
==`31`==
==`ge = ">="`==
==`32`==
==`ne = "!="`==
==`33`==
==`34`==
==`35`==
==`class OrderBy(str, Enum):`==
==`36`==
==`asc = "asc"`==
==`37`==
==`desc = "desc"`==
==`38`==
==`39`==
==`40`==
==`class DynamicValue(BaseModel):`==
==`41`==
==`column_name: str`==
==`42`==
==`43`==
==`44`==
==`class Condition(BaseModel):`==
==`45`==
==`column: str`==
==`46`==
==`operator: Operator`==
==`47`==
==`value: Union[str, int, DynamicValue]`==
==`48`==
==`49`==
==`50`==
==`class Query(BaseModel):`==
==`51`==
==`table_name: Table`==
==`52`==
==`columns: list[Column]`==
==`53`==
==`conditions: list[Condition]`==
==`54`==
==`order_by: OrderBy`==
==`55`==
==`56`==
==`57`==
==`client = OpenAI()`==
==`58`==
==`59`==
==`completion = client.beta.chat.completions.parse(`==
==`60`==
==`model="gpt-4o-2024-08-06",`==
==`61`==
==`messages=[`==
==`62`==
==`{`==
==`63`==
==`"role": "system",`==
==`64`==
==`"content": "You are a helpful assistant. The current date is August 6, 2024. You help users query for the data they are looking for by calling the query function.",`==
==`65`==
==`},`==
==`66`==
==`{`==
==`67`==
==`"role": "user",`==
==`68`==
==`"content": "look up all my orders in may of last year that were fulfilled but not delivered on time",`==
==`69`==
==`},`==
==`70`==
==`],`==
==`71`==
==`tools=[`==
==`72`==
==`openai.pydantic_function_tool(Query),`==
==`73`==
==`],`==
==`74`==
==`)`==
==`75`==
==`76`==
==`print(completion.choices[0].message.tool_calls[0].function.parsed_arguments)`==
==Native Structured Outputs support is also available for== ==`response_format`====.====  
  
====`response_format`====. 也支持本地结构化输出。==
==**python 蟒蛇**==
==`1`==
==`from pydantic import BaseModel`==
==`2`==
==`3`==
==`from openai import OpenAI`==
==`4`==
==`5`==
==`6`==
==`class Step(BaseModel):`==
==`7`==
==`explanation: str`==
==`8`==
==`output: str`==
==`9`==
==`10`==
==`11`==
==`class MathResponse(BaseModel):`==
==`12`==
==`steps: list[Step]`==
==`13`==
==`final_answer: str`==
==`14`==
==`15`==
==`16`==
==`client = OpenAI()`==
==`17`==
==`18`==
==`completion = client.beta.chat.completions.parse(`==
==`19`==
==`model="gpt-4o-2024-08-06",`==
==`20`==
==`messages=[`==
==`21`==
==`{"role": "system", "content": "You are a helpful math tutor."},`==
==`22`==
==`{"role": "user", "content": "solve 8x + 31 = 2"},`==
==`23`==
==`],`==
==`24`==
==`response_format=MathResponse,`==
==`25`==
==`)`==
==`26`==
==`27`==
==`message = completion.choices[0].message`==
==`28`==
==`if message.parsed:`==
==`29`==
==`print(message.parsed.steps)`==
==`30`==
==`print(message.parsed.final_answer)`==
==`31`==
==`else:`==
==`32`==
==`print(message.refusal)`==
## ==Additional use cases 其他用例==
==Developers frequently use OpenAI’s models to generate structured data for various use cases. Some additional examples include:====  
  
====开发人员经常使用 OpenAI 的模型为各种用例生成结构化数据。其他一些例子包括==
## ==Dynamically generating user interfaces based on the user’s intent====  
  
====根据用户意图动态生成用户界面==
==For example, developers can use Structured Outputs to create code- or UI-generating applications. All of the following examples use the same== ==`response_format`====, and can be used to generate varying UIs based on user input.====  
  
====例如，开发人员可以使用结构化输出创建代码或 UI 生成应用程序。以下所有示例均使用相同的====`response_format`== ==，并可用于根据用户输入生成不同的用户界面。==
==System 系统==
==`You are a user interface assistant. Your job is to help users visualize their website and app ideas.`==
==Response format 回复格式==
==Assistant 助理==
==`{`==
==`"type": "div",`==
==`"label": "",`==
==`"children": [`==
==`{`==
==`"type": "header",`==
==`"label": "",`==
==`"children": [`==
==`{`==
==`"type": "div",`==
==`"label": "Green Thumb Gardening",`==
==`"children": [],`==
==`"attributes": [{ "name": "className", "value": "site-title" }]`==
==`},`==
==`{`==
==`"type": "div",`==
==`"label": "Bringing Life to Your Garden",`==
==`"children": [],`==
==`"attributes": [{ "name": "className", "value": "site-tagline" }]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "header" }]`==
==`},`==
==`{`==
==`"type": "section",`==
==`"label": "",`==
==`"children": [`==
==`{`==
==`"type": "div",`==
==`"label": "",`==
==`"children": [`==
==`{`==
==`"type": "div",`==
==`"label": "About Us",`==
==`"children": [`==
==`{`==
==`"type": "div",`==
==`"label": "At Green Thumb Gardening, we specialize in transforming your outdoor spaces into beautiful, thriving gardens. Our team has decades of experience in horticulture and landscape design.",`==
==`"children": [],`==
==`"attributes": [`==
==`{ "name": "className", "value": "about-description" }`==
==`]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "about-section" }]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "content" }]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "about-container" }]`==
==`},`==
==`{`==
==`"type": "section",`==
==`"label": "",`==
==`"children": [`==
==`{`==
==`"type": "div",`==
==`"label": "",`==
==`"children": [`==
==`{`==
==`"type": "div",`==
==`"label": "Our Services",`==
==`"children": [`==
==`{`==
==`"type": "div",`==
==`"label": "Garden Design",`==
==`"children": [],`==
==`"attributes": [`==
==`{ "name": "className", "value": "service-item" }`==
==`]`==
==`},`==
==`{`==
==`"type": "div",`==
==`"label": "Plant Care & Maintenance",`==
==`"children": [],`==
==`"attributes": [`==
==`{ "name": "className", "value": "service-item" }`==
==`]`==
==`},`==
==`{`==
==`"type": "div",`==
==`"label": "Seasonal Cleanup",`==
==`"children": [],`==
==`"attributes": [`==
==`{ "name": "className", "value": "service-item" }`==
==`]`==
==`},`==
==`{`==
==`"type": "div",`==
==`"label": "Custom Landscaping",`==
==`"children": [],`==
==`"attributes": [`==
==`{ "name": "className", "value": "service-item" }`==
==`]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "services-list" }]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "content" }]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "services-container" }]`==
==`}`==
==`],`==
==`"attributes": [{ "name": "className", "value": "landing-page" }]`==
==`}`==
==Welcome to Green Thumb Gardening====  
  
====欢迎访问绿拇指园艺==
==Bringing Life to Your Garden====  
  
====为花园注入活力==
==At Green Thumb Gardening, we specialize in transforming your outdoor spaces into beautiful, thriving gardens. Our team has decades of experience in horticulture and landscape design.====  
  
====在 Green Thumb Gardening，我们专注于将您的室外空间改造成美丽、欣欣向荣的花园。我们的团队拥有数十年的园艺和景观设计经验。==
==Our services 我们的服务==
==Garden Design 花园设计==
==Plant Care & Maintenance 植物养护和维护==
==Seasonal Cleanup 季节性清洁==
==Custom Landscaping 定制景观==
## ==Separating a final answer from supporting reasoning or additional commentary====  
  
====将最终答案与支持性推理或补充评注分开==
==It can be useful to give the model a separate field for chain of thought to improve the final quality of the response.====  
  
====给模型一个单独的思维链字段可能会有所帮助，从而提高最终答复的质量。==
==**JSON**==
==`1`==
==`{`==
==`2`==
==`"model": "gpt-4o-2024-08-06",`==
==`3`==
==`"messages": [`==
==`4`==
==`{`==
==`5`==
==`"role": "system",`==
==`6`==
==`"content": "You are a helpful assistant"`==
==`7`==
==`},`==
==`8`==
==`{`==
==`9`==
==`"role": "user",`==
==`10`==
==`"content": "9.11 and 9.9 -- which is bigger?"`==
==`11`==
==`}`==
==`12`==
==`],`==
==`13`==
==`"response_format": {`==
==`14`==
==`"type": "json_schema",`==
==`15`==
==`"json_schema": {`==
==`16`==
==`"name": "reasoning_schema",`==
==`17`==
==`"strict": true,`==
==`18`==
==`"schema": {`==
==`19`==
==`"type": "object",`==
==`20`==
==`"properties": {`==
==`21`==
==`"reasoning_steps": {`==
==`22`==
==`"type": "array",`==
==`23`==
==`"items": {`==
==`24`==
==`"type": "string"`==
==`25`==
==`},`==
==`26`==
==`"description": "The reasoning steps leading to the final conclusion."`==
==`27`==
==`},`==
==`28`==
==`"answer": {`==
==`29`==
==`"type": "string",`==
==`30`==
==`"description": "The final answer, taking into account the reasoning steps."`==
==`31`==
==`}`==
==`32`==
==`},`==
==`33`==
==`"required": ["reasoning_steps", "answer"],`==
==`34`==
==`"additionalProperties": false`==
==`35`==
==`}`==
==`36`==
==`}`==
==`37`==
==`}`==
==`38`==
==`}`==
## ==Extracting structured data from unstructured data====  
  
====从非结构化数据中提取结构化数据==
==For example, instructing the model to extract things like to-dos, due dates, and assignments from meeting notes.====  
  
====例如，指示模型从会议记录中提取待办事项、到期日期和任务等内容。==
==**JSON**==
==`1`==
==`POST /v1/chat/completions`==
==`2`==
==`{`==
==`3`==
==`"model": "gpt-4o-2024-08-06",`==
==`4`==
==`"messages": [`==
==`5`==
==`{`==
==`6`==
==`"role": "system",`==
==`7`==
==`"content": "Extract action items, due dates, and owners from meeting notes."`==
==`8`==
==`},`==
==`9`==
==`{`==
==`10`==
==`"role": "user",`==
==`11`==
==`"content": "...meeting notes go here..."`==
==`12`==
==`}`==
==`13`==
==`],`==
==`14`==
==`"response_format": {`==
==`15`==
==`"type": "json_schema",`==
==`16`==
==`"json_schema": {`==
==`17`==
==`"name": "action_items",`==
==`18`==
==`"strict": true,`==
==`19`==
==`"schema": {`==
==`20`==
==`"type": "object",`==
==`21`==
==`"properties": {`==
==`22`==
==`"action_items": {`==
==`23`==
==`"type": "array",`==
==`24`==
==`"items": {`==
==`25`==
==`"type": "object",`==
==`26`==
==`"properties": {`==
==`27`==
==`"description": {`==
==`28`==
==`"type": "string",`==
==`29`==
==`"description": "Description of the action item."`==
==`30`==
==`},`==
==`31`==
==`"due_date": {`==
==`32`==
==`"type": ["string", "null"],`==
==`33`==
==`"description": "Due date for the action item, can be null if not specified."`==
==`34`==
==`},`==
==`35`==
==`"owner": {`==
==`36`==
==`"type": ["string", "null"],`==
==`37`==
==`"description": "Owner responsible for the action item, can be null if not specified."`==
==`38`==
==`}`==
==`39`==
==`},`==
==`40`==
==`"required": ["description", "due_date", "owner"],`==
==`41`==
==`"additionalProperties": false`==
==`42`==
==`},`==
==`43`==
==`"description": "List of action items from the meeting."`==
==`44`==
==`}`==
==`45`==
==`},`==
==`46`==
==`"required": ["action_items"],`==
==`47`==
==`"additionalProperties": false`==
==`48`==
==`}`==
==`49`==
==`}`==
==`50`==
==`}`==
==`51`==
==`}`==
## ==Under the hood 引擎盖下==
==We took a two part approach to improving reliability for model outputs that match JSON Schema. First, we trained our newest model== ==`gpt-4o-2024-08-06`== ==to understand complicated schemas and how best to produce outputs that match them. However, model behavior is inherently non-deterministic—despite this model’s performance improvements (93% on our benchmark), it still did not meet the reliability that developers need to build robust applications. So we also took a deterministic, engineering-based approach to constrain the model’s outputs to achieve 100% reliability.====  
  
====我们采取了两部分方法来提高与 JSON 模式匹配的模型输出的可靠性。首先，我们训练了最新的模型====`gpt-4o-2024-08-06`== ==以理解复杂的模式以及如何最好地生成与之匹配的输出。然而，模型行为本质上是非确定性的--尽管该模型的性能有所提高（在我们的基准测试中提高了 93%），但它仍然无法满足开发人员构建稳健应用程序所需的可靠性。因此，我们还采用了一种基于工程的确定性方法来限制模型的输出，以实现 100% 的可靠性。==
## ==Constrained decoding 受限解码==
==Our approach is based on a technique known as constrained sampling or constrained decoding. By default, when models are sampled to produce outputs, they are entirely unconstrained and can select any token from the vocabulary as the next output. This flexibility is what allows models to make mistakes; for example, they are generally free to sample a curly brace token at any time, even when that would not produce valid JSON. In order to force valid outputs, we constrain our models to only tokens that would be valid according to the supplied schema, rather than all available tokens.====  
  
====我们的方法基于一种称为受限采样或受限解码的技术。默认情况下，当模型被采样以产生输出时，它们是完全不受约束的，可以从词汇中选择任何标记作为下一个输出。这种灵活性允许模型犯错误；例如，它们通常可以随时采样大括号标记，即使这样做不会产生有效的 JSON。为了迫使输出有效，我们限制模型只使用根据所提供的模式有效的标记，而不是所有可用的标记。==
==It can be challenging to implement this constraining in practice, since the tokens that are valid differ throughout a model’s output. Let’s say we have the following schema:====  
  
====在实践中要实现这一约束可能很有挑战性，因为在整个模型输出中，有效的标记是不同的。假设我们有以下模式：==
==**JSON**==
==`1`==
==`{`==
==`2`==
==`"type": "object",`==
==`3`==
==`"properties": {`==
==`4`==
==`"value": { "type": "number" }`==
==`5`==
==`},`==
==`6`==
==`"required": ["value"],`==
==`7`==
==`"additionalProperties": false`==
==`8`==
==`}`==
==The tokens that are valid at the beginning of the output include things like== ==`{`====,== ==`{“`====,== ==`{\n`====, etc. However, once the model has already sampled== ==`{“val`====, then== ==`{`== ==is no longer a valid token. Thus we need to implement dynamic constrained decoding, and determine which tokens are valid after each token is generated, rather than upfront at the beginning of the response.====  
  
====在输出开始时有效的标记包括====`{`====、====`{"`====,== ==`{/n`== ==等。但是，一旦模型已经对== ==`{"val`== ==进行了采样，那么== ==`{`== ==就不再是有效的标记。因此，我们需要实现动态限制解码，并在生成每个标记后确定哪些标记有效，而不是在响应开始时预先确定。==
==To do this, we convert the supplied JSON Schema into a context-free grammar (CFG). A grammar is a set of rules that defines a language, and a context-free grammar is a grammar that conforms to specific rules. You can think of JSON and JSON Schema as particular languages with rules to define what is valid within the language. Just as it’s not valid in English to have a sentence with no verb, it is not valid in JSON to have a trailing comma.====  
  
====为此，我们将提供的 JSON 模式转换为无上下文语法（CFG）。语法是一组定义语言的规则，而无上下文语法则是一种符合特定规则的语法。你可以把 JSON 和 JSON Schema 看作是特定的语言，它们都有规则来定义什么在语言中是有效的。就像在英语中没有动词的句子是无效的一样，在 JSON 中没有尾部逗号也是无效的。==
==Thus, for each JSON Schema, we compute a grammar that represents that schema, and pre-process its components to make it easily accessible during model sampling. This is why the first request with a new schema incurs a latency penalty—we must preprocess the schema to generate this artifact that we can use efficiently during sampling.====  
  
====因此，对于每个 JSON 模式，我们都要计算一个代表该模式的语法，并对其组件进行预处理，使其在模型采样过程中易于访问。这就是为什么使用新模式的首次请求会产生延迟惩罚--我们必须对模式进行预处理，以生成我们可以在采样过程中高效使用的工件。==
==While sampling, after every token, our inference engine will determine which tokens are valid to be produced next based on the previously generated tokens and the rules within the grammar that indicate which tokens are valid next. We then use this list of tokens to mask the next sampling step, which effectively lowers the probability of invalid tokens to 0. Because we have preprocessed the schema, we can use a cached data structure to do this efficiently, with minimal latency overhead.====  
  
====在采样过程中，在每个标记之后，我们的推理引擎都会根据之前生成的标记和语法中指示下一个有效标记的规则，确定下一步要生成的有效标记。然后，我们会使用这个标记列表来屏蔽下一步的采样，从而有效地将无效标记的概率降至 0。由于我们已经对模式进行了预处理，因此可以使用缓存数据结构来高效地完成这项工作，并将延迟开销降至最低。==
## ==Alternate approaches 替代方法==
==Alternate approaches to this problem often use finite state machines (FSMs) or regexes (generally implemented with FSMs) for constrained decoding. These function similarly in that they dynamically update which tokens are valid after each token is produced, but they have some key differences from the CFG approach. Notably, CFGs can express a broader class of languages than FSMs. In practice, this doesn’t matter for very simple schemas like the== ==`value`== ==schema shown above. However, we find that the difference is meaningful for more complex schemas that involve nested or recursive data structures. As an example, FSMs cannot generally express recursive types, which means FSM based approaches may struggle to match parentheses in deeply nested JSON. The following is a sample recursive schema that is supported on the OpenAI API with Structured Outputs but would not be possible to express with a FSM.====  
  
====解决这一问题的其他方法通常使用有限状态机（FSM）或词法（通常使用 FSM 实现）进行约束解码。这些方法的功能类似，即在生成每个标记后动态更新哪些标记有效，但它们与 CFG 方法有一些关键区别。值得注意的是，与 FSM 相比，CFG 可表达的语言种类更多。在实践中，这对于非常简单的模式（如上图中的====`value`== ==模式）来说并不重要。然而，我们发现，对于涉及嵌套或递归数据结构的更复杂模式，这种差异是有意义的。例如，FSM 通常无法表达递归类型，这意味着基于 FSM 的方法可能难以匹配深嵌套 JSON 中的括号。以下是使用结构化输出的 OpenAI API 所支持的递归模式示例，但 FSM 无法表达该模式。==
==**JSON**==
==`1`==
==`{`==
==`2`==
==`"name": "ui",`==
==`3`==
==`"description": "Dynamically generated UI",`==
==`4`==
==`"strict": true,`==
==`5`==
==`"schema": {`==
==`6`==
==`"type": "object",`==
==`7`==
==`"properties": {`==
==`8`==
==`"type": {`==
==`9`==
==`"type": "string",`==
==`10`==
==`"description": "The type of the UI component",`==
==`11`==
==`"enum": ["div", "button", "header", "section", "field", "form"]`==
==`12`==
==`},`==
==`13`==
==`"label": {`==
==`14`==
==`"type": "string",`==
==`15`==
==`"description": "The label of the UI component, used for buttons or form fields"`==
==`16`==
==`},`==
==`17`==
==`"children": {`==
==`18`==
==`"type": "array",`==
==`19`==
==`"description": "Nested UI components",`==
==`20`==
==`"items": {`==
==`21`==
==`"$ref": "#"`==
==`22`==
==`}`==
==`23`==
==`},`==
==`24`==
==`"attributes": {`==
==`25`==
==`"type": "array",`==
==`26`==
==`"description": "Arbitrary attributes for the UI component, suitable for any element",`==
==`27`==
==`"items": {`==
==`28`==
==`"type": "object",`==
==`29`==
==`"properties": {`==
==`30`==
==`"name": {`==
==`31`==
==`"type": "string",`==
==`32`==
==`"description": "The name of the attribute, for example onClick or className"`==
==`33`==
==`},`==
==`34`==
==`"value": {`==
==`35`==
==`"type": "string",`==
==`36`==
==`"description": "The value of the attribute"`==
==`37`==
==`}`==
==`38`==
==`}`==
==`39`==
==`}`==
==`40`==
==`}`==
==`41`==
==`},`==
==`42`==
==`"required": ["type", "label", "children", "attributes"],`==
==`43`==
==`"additionalProperties": false`==
==`44`==
==`}`==
==`45`==
==`}`==
==Note that each UI element can have arbitrary children which reference the root schema recursively. This flexibility is something that the CFG approach affords.====  
  
====请注意，每个用户界面元素都可以有任意子元素，这些子元素可以递归引用根模式。这种灵活性正是 CFG 方法所提供的。==
## ==Limitations and restrictions====  
  
====限制和约束==
==There are a few limitations to keep in mind when using Structured Outputs:====  
  
====使用结构化输出时要注意一些限制：==
- ==Structured Outputs allows only a subset of JSON Schema, detailed== [==in our docs(opens in a new window)==](https://platform.openai.com/docs/guides/structured-outputs)==. This helps us ensure the best possible performance.====  
      
    ==[==在我们的文档中(opens in a new window)==](https://platform.openai.com/docs/guides/structured-outputs) ==仅允许结构化输出的 JSON 模式子集。这有助于我们确保最佳性能。==
- ==The first API response with a new schema will incur additional latency, but subsequent responses will be fast with no latency penalty. This is because during the first request, we process the schema as indicated above and then cache these artifacts for fast reuse later on. Typical schemas take under 10 seconds to process on the first request, but more complex schemas may take up to a minute.====  
      
    ====首次使用新模式的 API 响应会产生额外的延迟，但随后的响应速度会很快，不会产生延迟。这是因为在第一次请求中，我们会按照上文所述处理模式，然后缓存这些工件，以便以后快速重用。典型的模式在第一次请求时的处理时间不到 10 秒，但更复杂的模式处理时间可能长达一分钟。==
- ==The model can fail to follow the schema if the model chooses to refuse an unsafe request. If it chooses to refuse, the return message will have the== ==`refusal`== ==boolean set to true to indicate this.====  
      
    ====如果模型选择拒绝不安全请求，则模型可能无法遵循模式。如果选择拒绝，返回消息中的== ==`refusal`== ==布尔值将设置为 true 以表明这一点。==
- ==The model can fail to follow the schema if the generation reaches== ==`max_tokens`== ==or another stop condition before finishing.====  
      
    ====如果生成达到====`max_tokens`====或其他停止条件后才完成，模型可能无法遵循模式。==
- ==Structured Outputs doesn’t prevent all kinds of model mistakes. For example, the model may still make mistakes within the values of the JSON object (e.g., getting a step wrong in a mathematical equation). If developers find mistakes, we recommend providing examples in the system instructions or splitting tasks into simpler subtasks.====  
      
    ====结构化输出并不能避免模型的所有错误。例如，模型仍有可能在 JSON 对象的值中犯错（如在数学公式中走错一步）。如果开发人员发现错误，我们建议在系统说明中提供示例或将任务拆分成更简单的子任务。==
- ==Structured Outputs is not compatible with parallel function calls. When a parallel function call is generated, it may not match supplied schemas. Set== ==`parallel_tool_calls: false`== ==to disable parallel function calling.====  
      
    ====结构化输出与并行函数调用不兼容。生成并行函数调用时，它可能与提供的模式不匹配。设置== ==`parallel_tool_calls: false`== ==可禁用并行函数调用。==
- ==JSON Schemas supplied with Structured Outputs aren’t== [==Zero Data Retention(opens in a new window)==](https://platform.openai.com/docs/models/how-we-use-your-data) ==(ZDR) eligible.====  
      
    ==[==零数据保留（在新窗口中打开）==](https://platform.openai.com/docs/models/how-we-use-your-data) ==(ZDR) 资格。==
## ==Availability 可用性==
==Structured Outputs is generally available today in the API.== ==  
  
====目前，结构化输出一般可在应用程序接口中使用。==
==Structured Outputs with function calling is available on all models that support function calling in the API. This includes our newest models (==
==所有在 API 中支持函数调用的模型都可以使用带函数调用的结构化输出。这包括我们最新的模型====`gpt-4o`====,== ==`gpt-4o-mini`====), all models after and including== ==`gpt-4-0613`== ==and== ==`gpt-3.5-turbo-0613`====, and any fine-tuned models that support function calling. This functionality is available on the Chat Completions API, Assistants API, and Batch API. Structured Outputs with function calling is also compatible with vision inputs.====  
  
====gpt-4o、gpt-4o-mini）、gpt-4-0613 和 gpt-3.5-turbo-0613 之后的所有型号，以及任何支持函数调用的微调型号。此功能在聊天完成 API、助手 API 和批处理 API 中可用。具有函数调用功能的结构化输出也与视觉输入兼容。==
==Structured Outputs with response formats is available on==
==附有回复格式的结构化输出可从以下网站获取====`gpt-4o-mini`== ==and== ==`gpt-4o-2024-08-06`== ==and any fine tunes based on these models. This functionality is available on the Chat Completions API, Assistants API, and Batch API. Structured Outputs with response formats is also compatible with vision inputs.== ==  
  
====gpt-4o-mini 和 gpt-4o-2024-08-06 以及基于这些模型的任何微调。此功能在聊天完成 API、助手 API 和批处理 API 中均有提供。具有响应格式的结构化输出也与视觉输入兼容。==
==By switching to the new==
==通过切换到新的====`gpt-4o-2024-08-06`====, developers save 50% on inputs ($2.50/1M input tokens) and 33% on outputs ($10.00/1M output tokens) compared to== ==`gpt-4o-2024-05-13`====.====  
  
====与 gpt-4o-2024-05-13 相比，gpt-4o-2024-08-06 可为开发人员节省 50% 的输入（2.50 美元/100 万个输入代币）和 33% 的输出（10.00 美元/100 万个输出代币）。==
==To start using Structured Outputs, check out our==
==要开始使用结构化输出，请查看我们的==[==docs(opens in a new window)==](https://platform.openai.com/docs/guides/structured-outputs)==.==
## ==Acknowledgements 致谢==
==Structured Outputs takes inspiration from excellent work from the open source community: namely, the outlines, jsonformer, instructor, guidance, and lark libraries.====  
  
====《结构化输出》从开源社区的优秀作品中汲取灵感，这些作品包括 outlines、jsonformer、instructor、guidance 和 lark 库。==
## ==Author 作者==
[==Michelle Pokrass 米歇尔-波克拉斯==](https://openai.com/news/?author=michelle-pokrass#results)
## ==Core contributors 核心贡献者==
==Chris Colby, Melody Guan, Michelle Pokrass, Ted Sanders, Brian Zhang====  
  
====Chris Colby、Melody Guan、Michelle Pokrass、Ted Sanders、Brian Zhang==
## ==Acknowledgments 致谢==
==John Allard, Filipe de Avila Belbute Peres, Ilan Bigio, Owen Campbell-Moore, Chen Ding, Atty Eleti, Elie Georges, Katia Gil Guzman, Jeff Harris, Johannes Heidecke, Beth Hoover, Romain Huet, Tomer Kaftan, Jillian Khoo, Karolis Kosas, Ryan Liu, Kevin Lu, Lindsay McCallum, Rohan Nuttall, Joe Palermo, Leher Pathak, Ishaan Singal, Felipe Petroski Such, Freddie Sulit, David Weedon====  
  
====John Allard, Filipe de Avila Belbute Peres, Ilan Bigio, Owen Campbell-Moore, Chen Ding, Atty Eleti, Elie Georges, Katia Gil Guzman, Jeff Harris, Johannes Heidecke, Beth Hoover, Romain Huet、Tomer Kaftan、Jillian Khoo、Karolis Kosas、Ryan Liu、Kevin Lu、Lindsay McCallum、Rohan Nuttall、Joe Palermo、Leher Pathak、Ishaan Singal、Felipe Petroski Such、Freddie Sulit、David Weedon==