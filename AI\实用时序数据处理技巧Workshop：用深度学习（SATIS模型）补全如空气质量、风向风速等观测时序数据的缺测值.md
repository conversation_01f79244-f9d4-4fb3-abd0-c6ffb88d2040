---
DocFlag:
  - Reference
Updated: 2024-04-09T21:43
tags:
  - AI->-Model
  - AI->-Theory
  - AI->-TimeSeries
Created: 2024-04-09T21:43
---
[![](https://mmbiz.qpic.cn/mmbiz_jpg/oowicvenicBW70QGKr8F0yW01fytb8JnibGA0qwgLteuV5M60I3eULQl7r4WhuPA46y5t0icekoic7rQqN3ia36ft2cQ/0?wx_fmt=jpeg)](https://mmbiz.qpic.cn/mmbiz_jpg/oowicvenicBW70QGKr8F0yW01fytb8JnibGA0qwgLteuV5M60I3eULQl7r4WhuPA46y5t0icekoic7rQqN3ia36ft2cQ/0?wx_fmt=jpeg)
==本次 Python 地理空间系列 Workshop 是由和鲸社区发起的，聚焦 Python 地理空间数据分析，全程提供免费基础算力和上手即用的数据分析工具，面向所有人免费开放，欢迎所有地理空间领域人才和对地理空间数据科学感兴趣的数据分析师共同参与、交流。==
==除了本文介绍的《用深度学习补全时序数据的缺测值》，更有多个系列workshop活动可供报名学习：==
==**时间**==
==**预计主题**==
==**难度**==
==3.23==
==探究疫情期间一线城市 PM2.5 浓度变化规律==
==⭐==
==3.23==
==借助电子眼定位上海潜在违章区域：从数据爬取到空间建模==
==⭐⭐==
==4.27==
==借助深度学习补全时序数据缺测值：以北京多站点空气质量数据为例==
==⭐⭐⭐⭐==
==4.27==
==基于机器学习的城市内涝热点区域预测==
==⭐⭐⭐⭐⭐==
==5.25==
==基于多源数据融合的土地利用分类模型==
==⭐⭐⭐==
==5.25==
==面向复杂城市系统的大规模物流优化算法==
==⭐⭐⭐⭐==
==5.25==
==基于深度学习的街景图像语义分割==
==⭐⭐⭐⭐⭐==
==6月==
==Covid-19 期间美国不同尺度下人员流动性分析==
==⭐⭐⭐==
==8月==
==量化城市尺度二氧化碳排放网格制图的不确定性==
==⭐⭐⭐⭐==
==10月==
==基于梯度提升的地理加权集成学习方法==
==⭐⭐⭐⭐⭐==
==**地理空间基础**== ==**Python**== ==**知识**==
==长期开放==
==地理数据处理入门==
==Python 基础语法、常见数据读取、栅格数据尺度转换、栅格裁剪==
==⭐==
==空间数据处理==
==矢量、栅格、空间网络数据处理与可视化、大数据时空动态可视化==
==⭐⭐==
==地理空间数据分析基础：geopandas 使用==
==空间数据的表示与显示、选择与选取、案例1：公园分布分析、案例2：空间分析（二）新建公园选址==
==⭐⭐==
==遥感影像数据处理入门==
==普通数字图像、遥感影像、图像裁剪、坐标信息获取==
==⭐==
==xarray 使用==
==读写文件、数据结构和索引、可视化、运算==
==⭐⭐==
==空间聚类分析方法==
==编码、升降维、计算特征距离和相似性、K-均值聚类、层次聚类、高斯混合聚类、模糊聚类==
==⭐⭐==
==地理统计应用：金属元素含量空间建模==
==数据预处理、数据集划分、半变异函数计算、克里金插值==
==⭐⭐⭐==
==陆地基本地貌类型划分：DEM栅格数据处理和运算==
==读取DEM数据并镶嵌、计算起伏度、地貌类型划分==
==⭐⭐⭐==
==以下为活动介绍正文==
==在气象学等地球科学领域，多变量时间序列数据的应用无处不在，它帮助我们揭示气候、环境和生态系统背后的规律。然而在实际的时间序列数据采集过程中，常常会遇到传感器失效、传输错误等问题，导致部分数据缺失，给科学家们开展高级分析和模式识别任务带来了巨大挑战，比如气候变化分类或不同区域天气现象聚类。==
==借助 Wenjie Du 等人提出的 SAITS（基于自我注意力的时间序列补全）新型模型，我们可以通过一种联合优化的训练方法来学习缺失值，这种方法同时考虑了补全和重建的任务，从而更巧妙地处理缺测值。==
==本期 workshop 中，你将学习如何利用 SATIS 模型处理北京多站点空气质量数据集的缺测值，并尝试练习补全某机场的风向风速观测数据的缺测值。==
[![](https://mmbiz.qpic.cn/mmbiz_jpg/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfkOksJ6JlsJOWRicXibwnCHVhIB1azwnX84zD9I2ib0S8Ag3p02zCed8lg/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_jpg/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfkOksJ6JlsJOWRicXibwnCHVhIB1azwnX84zD9I2ib0S8Ag3p02zCed8lg/640?wx_fmt=jpeg&from=appmsg)
==**SATIS: 基于自我注意力的时间序列补全**==
### ==**联合优化训练方法（Joint-optimization Training Approach）**==
==在时间序列数据的处理中，我们经常会遇到数据缺失的情况。为了解决这个问题，SATIS引入了两种任务：掩蔽预测任务（MIT）和观测重建任务（ORT）。这两种任务相互补充，共同帮助我们更准确地补全缺失的数据。==
[![](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfxcf7wW9OqZXNBywKTsjciaWjVpOyPFu1fC7SiaUduN5lgz19TibN2fPNw/640?wx_fmt=png&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfxcf7wW9OqZXNBywKTsjciaWjVpOyPFu1fC7SiaUduN5lgz19TibN2fPNw/640?wx_fmt=png&from=appmsg)
### ==**掩蔽预测任务（MIT）**==
==掩蔽预测任务（MIT）有点像是我们和模型玩的一个“猜猜看”游戏。我们随机选取一部分观测到的数据，将其“掩蔽”起来，也就是不让模型看到这些数据。然后，我们让模型尝试去预测这些被掩蔽的数据应该是多少。这样做的目的是迫使模型学会如何准确预测缺失的值。我们通过计算模型预测值和真实值之间的平均绝对误差（MAE）来衡量模型的预测效果。==
### ==**观测重建任务（ORT）**==
==这个任务相对简单，就是让模型尽量准确地重建那些没有被掩蔽的观测数据。我们同样使用MAE来衡量模型重建的效果。这个任务的重要性在于，它不仅要求模型能够预测缺失的数据，还要求模型能够保持观测数据的准确性。==
### ==**SAITS模型**==
[![](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfq0xjibmYMwUOAMTNZqtuP9cLSJPXJf0ibKJJf66aVYI2qiauuTmDkCqZw/640?wx_fmt=png&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfq0xjibmYMwUOAMTNZqtuP9cLSJPXJf0ibKJJf66aVYI2qiauuTmDkCqZw/640?wx_fmt=png&from=appmsg)
==SAITS模型由两个加权对角掩蔽自我注意力（DMSA）块组成，这使得SAITS摆脱了RNN的束缚，能够显式地捕捉时间步之间的时序依赖性和特征相关性。==
## ==**· 特邀导师 ·**==
==Hollis 冬青 航天新气象科技有限公司工程师。毕业于南京信息工程大学大气科学专业，本科及研究生期间主要从事数值模式、气溶胶领域的研究，工作后从事数值模式、软件开发、人工智能等研究工作。擅长气象数据处理、数值天气模式、气象与深度学习结合等领域。==
## ==**· 课程大纲 ·**==
- ==传统的数据缺测处理方法：数据删除和数据差补==
- ==数据集：北京多站点空气质量数据集==
- ==使用 pandas 删除数据==
- ==使用 pandas 基于线性回归进行数据插补==
- ==SATIS：基于自我注意力的时间序列补全==
- ==联合优化训练方法（Joint-optimization Training Approach）==
- ==掩蔽预测任务（MIT）==
- ==观测重建任务（ORT）==
- ==SATIS 模型==
- ==使用 SATIS 对北京多站点空气质量数据集进行缺测补全==
- ==结果可视化==
- ==作业：清洗并补全北京机场观测资料==
==**课件预览**==
[![](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbf9iccRphnYpyhyjdqMwxnyvPRBf0r0YKxpbpkXNaqCfoDpEuwDRgZE5Q/640?wx_fmt=png&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbf9iccRphnYpyhyjdqMwxnyvPRBf0r0YKxpbpkXNaqCfoDpEuwDRgZE5Q/640?wx_fmt=png&from=appmsg)
[![](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfFHo0109nDe6c5x4yLBfuIHHkX02Y4yMdr7uOibXsUnWcCjKvmlmALIQ/640?wx_fmt=png&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfFHo0109nDe6c5x4yLBfuIHHkX02Y4yMdr7uOibXsUnWcCjKvmlmALIQ/640?wx_fmt=png&from=appmsg)
[![](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfLHRsYFmkvVQ1aoapn2yBicvZgG0ibbK6fDXBlicTmW1EcnRQWbwYo5wKQ/640?wx_fmt=png&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbfLHRsYFmkvVQ1aoapn2yBicvZgG0ibbK6fDXBlicTmW1EcnRQWbwYo5wKQ/640?wx_fmt=png&from=appmsg)
==**· 日程安排 ·**==
- ==即日起：报名、查看教案（需使用电脑访问）==
[![](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbf0nFR2dFpb1rqpQqH1NzfIKcplhPwHT5DibaGI6TekxSQPX9qIIgDDvA/640?wx_fmt=png&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_png/PMVXlicmgWicxBZCdnS26t9mnzWSGqXwbf0nFR2dFpb1rqpQqH1NzfIKcplhPwHT5DibaGI6TekxSQPX9qIIgDDvA/640?wx_fmt=png&from=appmsg)
- ==4.25 12:00前：完成作业，提交==
- ==4.27：准时参加 30 分钟的主题讲解交流会（会议号：111-883-801）==
==**· 报名 ·**==
==**活动页面**==
[==https://www.heywhale.com/u/529405==](https://www.heywhale.com/u/529405)
==**小程序**==
==**· 活动交流群 ·**==
==描下方二维码添加客服微信==
==回复== ==**地理**== ==即可等待客服分批拉入群聊==
[![](https://mmbiz.qpic.cn/mmbiz_jpg/PMVXlicmgWiczIkcMQPyDV4hAlmNxHM4by5xh1gZnYOlSuGEQB8CA4DMsiaia4nmWTQZ6ybaeGjSxaBc5Cg7OHMb1w/640?wx_fmt=jpeg&from=appmsg)](https://mmbiz.qpic.cn/mmbiz_jpg/PMVXlicmgWiczIkcMQPyDV4hAlmNxHM4by5xh1gZnYOlSuGEQB8CA4DMsiaia4nmWTQZ6ybaeGjSxaBc5Cg7OHMb1w/640?wx_fmt=jpeg&from=appmsg)