---
DocFlag:
  - Reference
  - Tested
Updated: 2024-03-31T13:06
tags:
  - AI->-Competition
  - AI->-Programming
  - AI->-Theory
URL: https://rukshanpramoditha.medium.com/12-types-of-non-linear-dimensionality-reduction-nldr-techniques-in-machine-learning-bf29663ea8f1
Created: 2024-03-31T12:48
---
[![](https://miro.medium.com/v2/resize:fit:1200/1*4yin-zD13iM_DFPalmPjCQ.jpeg)](https://miro.medium.com/v2/resize:fit:1200/1*4yin-zD13iM_DFPalmPjCQ.jpeg)
## ==A complete guide to manifold learning methods in unsupervised learning====  
  
====无监督学习中流形学习方法的完整指南==
[![](https://miro.medium.com/v2/resize:fill:88:88/1*KW2i_Pswra_7pSBl2yLZhA.jpeg)](https://miro.medium.com/v2/resize:fill:88:88/1*KW2i_Pswra_7pSBl2yLZhA.jpeg)
==Dimensionality reduction plays an important role in machine learning as many use cases exist.====  
  
====降维在机器学习中起着重要作用，因为存在许多使用情况。==
==Dimensionality reduction means that we reduce the number of features in the dataset to get so many advantages without losing much information.====  
  
====降维意味着我们减少数据集中的特征数量，以获得许多优势而不丢失太多信息。==
==Dimensionality reduction algorithms fall under the== ==**unsupervised learning**== ==category in which we train the algorithm with== ==_unlabelled_== ==data.====  
  
====降维算法属于无监督学习类别，我们使用无标签数据对算法进行训练。==
==Even if there are so many types of dimensionality reduction methods, all of them fall under two major categories: Linear and Non-linear.====  
  
====即使有很多种降维方法，它们都可以归为两大类：线性和非线性。==
==The linear methods== ==_linearly_== ==project data (hence the name, linear) from a high-dimensional space to a low-dimensional space. Examples include PCA and LDA.====  
  
====线性方法线性地将数据从高维空间投影到低维空间（因此被称为线性方法）。示例包括主成分分析（PCA）和线性判别分析（LDA）。==
==The non-linear methods provide an approach to perform non-linear dimensionality reduction (NLDR). We often perform NLDR to discover the non-linear structure of the original data. NLDR is also useful when the original data is not linearly separable.====  
  
====非线性方法提供了一种进行非线性降维（NLDR）的方法。我们经常进行 NLDR 来发现原始数据的非线性结构。当原始数据不是线性可分时，NLDR 也很有用。==
==In some contexts, non-linear dimensionality reduction is also known as== ==**manifold learning**====.====  
  
====在某些情境下，非线性降维也被称为流形学习。==
## ==NLDR methods NLDR 方法==
## ==01. Kernel PCA 01. 核主成分分析==
==You might be familiar with normal PCA which is a linear dimensionality reduction technique. Kernel PCA can be considered as the non-linear version of normal PCA.====  
  
====您可能对普通的主成分分析（PCA）比较熟悉，它是一种线性降维技术。核主成分分析可以被视为普通 PCA 的非线性版本。==
==Both normal PCA and kernel PCA can perform dimensionality reduction. However, only the kernel PCA works well with== ==_linearly inseparable_== ==data. Therefore,== ==**the main use of the kernel PCA algorithm is to make linearly inseparable data linearly separable while still reducing the dimensionality of the data!**====  
  
====普通的主成分分析（PCA）和核主成分分析（kernel PCA）都可以进行降维。然而，只有核主成分分析能够很好地处理线性不可分的数据。因此，核主成分分析算法的主要用途是将线性不可分的数据线性可分化，同时降低数据的维度！==
==Let’s see the intuition behind kernel PCA.====  
  
====让我们看一下核主成分分析背后的直觉。==
==Here is an example of linearly inseparable data.====  
  
====这是一个线性不可分的数据示例。==
==import matplotlib.pyplot as plt====  
  
====plt.figure(figsize=[7, 5])==
==from sklearn.datasets import make_moons====  
  
====X, y = make_moons(n_samples=100, noise=None,====  
  
====random_state=0)==
==plt.scatter(X[:, 0], X[:, 1], c=y, s=50, cmap='plasma')====  
  
====plt.title('Linearly inseparable data')==
[![](https://miro.medium.com/v2/resize:fit:596/1*qPPZoQnLS3dJRJkg9yZ3Kg.png)](https://miro.medium.com/v2/resize:fit:596/1*qPPZoQnLS3dJRJkg9yZ3Kg.png)
==(Image by author) （作者提供的图片）==
==The two colors represent two classes that are linearly inseparable. We can’t separate the two classes by drawing a straight line here.====  
  
====这两种颜色代表了两个线性不可分的类别。我们无法通过在这里画一条直线来分开这两个类别。==
==Now, we’ll apply normal PCA to the data by keeping only one component (the original data has two components or features).====  
  
====现在，我们将对数据应用普通的主成分分析（PCA），只保留一个成分（原始数据有两个成分或特征）。==
==import numpy as np====  
  
====from sklearn.decomposition import PCA==
==pca = PCA(n_components=1)====  
  
====X_pca = pca.fit_transform(X)==
==plt.figure(figsize=[7, 5])====  
  
====plt.scatter(X_pca[:, 0], np.zeros((100,1)), c=y, s=50, cmap='plasma')====  
  
====plt.title('First component after linear PCA')====  
  
====plt.xlabel('PC1')==
[![](https://miro.medium.com/v2/resize:fit:586/1*fPXVcylbJBuLRWr7SXR05w.png)](https://miro.medium.com/v2/resize:fit:586/1*fPXVcylbJBuLRWr7SXR05w.png)
==(Image by author) （作者提供的图片）==
==As you can see, the two classes are still linearly inseparable although PCA was able to reduce the dimensionality of the data by half.====  
  
====正如你所看到的，尽管 PCA 能够将数据的维度减少一半，但这两个类仍然是线性不可分的。==
==Now, we’ll apply kernel PCA to the data by keeping only one component.====  
  
====现在，我们将对数据应用核主成分分析，只保留一个成分。==
==import numpy as np====  
  
====from sklearn.decomposition import KernelPCA==
==kpca = KernelPCA(n_components=1, kernel='rbf', gamma=15)====  
  
====X_kpca = kpca.fit_transform(X)==
==plt.figure(figsize=[7, 5])====  
  
====plt.scatter(X_kpca[:, 0], np.zeros((100,1)), c=y, s=50, cmap='plasma')====  
  
====plt.axvline(x=0.0, linestyle='dashed', color='black', linewidth=1.2)====  
  
====plt.title('First component after kernel PCA')====  
  
====plt.xlabel('PC1')==
[![](https://miro.medium.com/v2/resize:fit:576/1*RG6K-16NqaZGh_W2ExIOrQ.png)](https://miro.medium.com/v2/resize:fit:576/1*RG6K-16NqaZGh_W2ExIOrQ.png)
==(Image by author) （作者提供的图片）==
==This time, the two classes are linearly separable. Only the kernel PCA can make the data linearly separable while still reducing the dimensionality of the data!====  
  
====这次，这两个类是线性可分的。只有核主成分分析可以使数据在降低维度的同时保持线性可分！==
==That’s the intuition behind kernel PCA. You’d also like to know how kernel PCA works behind the scenes.====  
  
====这就是核主成分分析背后的直觉。你也想知道核主成分分析在幕后是如何工作的。==
==The kernel PCA algorithm uses kernels which are functions that transform the data from one form to another. The kernel PCA is a two-step process. First, the kernel function temporarily projects the original data into a higher-dimensional space where the classes become linearly separable. Then, the algorithm projects that data back into a lower dimension as specified in the== ==**n_components**== ==hyperparameter (the number of dimensions that we want to keep).====  
  
====核主成分分析算法使用核函数将数据从一种形式转换为另一种形式。核主成分分析是一个两步过程。首先，核函数将原始数据临时投影到一个高维空间中，使得类别变得线性可分。然后，算法将该数据按照 n_components 超参数（我们想要保留的维度数量）指定的方式投影回较低的维度。==
==In Scikit-learn, kernel PCA is implemented by using the== ==**KernelPCA()**== ==class.====  
  
====在 Scikit-learn 中，使用 KernelPCA()类实现了核主成分分析。==
==from sklearn.decomposition import KernelPCA==
==kpca = KernelPCA(n_components, kernel, gamma)====  
  
====kpca_transformed = kpca.fit_transform(X)==
==There are three important hyperparameters in this class. The== ==**n_components**== ==hyperparameter specifies the number of dimensions that we want to keep just like in PCA!====  
  
====这个类中有三个重要的超参数。n_components 超参数指定了我们想要保留的维度数量，就像在 PCA 中一样！==
==The== ==**kernel**== ==hyperparameter specifies the type of kernel function that we want to use. There are four options:== ==_‘linear’_====,== ==_‘poly’_====,== ==_‘rbf’_== ==and== ==_‘sigmoid’_====. The normal PCA will be performed if we specify the kernel as== ==_‘linear’_====. Any of the other kernels will perform non-linear PCA. The== ==_rbf’_== ==(radial basis function) kernel is the most common one.====  
  
====核超参数指定我们想要使用的核函数类型。有四个选项：'linear'、'poly'、'rbf'和'sigmoid'。如果我们将核指定为'linear'，则将执行普通的 PCA。任何其他核函数都将执行非线性 PCA。'rbf'（径向基函数）核是最常用的核函数。==
==The== ==**gamma**== ==hyperparameter specifies the kernel coefficient for== ==_‘poly’_====,== ==_‘rbf’_== ==and== ==_‘sigmoid’_== ==kernels. A float value can be used for gamma. The default is== ==`None`====which uses the value calculated by (1/number of features).====  
  
====gamma 超参数指定了“poly”、“rbf”和“sigmoid”核函数的核系数。可以使用浮点值作为 gamma。默认值是== ==`None`== ==，它使用由（1/特征数量）计算得出的值。==
## ==02. Multidimensional Scaling (MDS)====  
  
====02. 多维尺度分析（MDS）==
==Multidimensional scaling is another non-linear dimensionality reduction technique that performs dimensionality reduction by maintaining the distances between data points in both higher and lower dimensions. For example, the closer points in the original dimension also appear closer in the low-dimensional form.====  
  
====多维缩放是另一种非线性降维技术，通过在高维和低维之间保持数据点之间的距离来进行降维。例如，在原始维度中较接近的点在低维形式中也更接近。==
==To implement MDS in Scikit-learn, we can use the== ==**MDS()**== ==class.====  
  
====要在 Scikit-learn 中实现 MDS，我们可以使用 MDS()类。==
==from sklearn.manifold import MDS==
==mds = MDS(n_components, metric)====  
  
====mds_transformed = mds.fit_transform(X)==
==The== ==**metric**== ==hyperparameter distinguishes between the two types of MDS algorithms: metric and non-metric. If== ==`metric=True`====, it performs metric MDS. Otherwise, it performs non-metric MDS.====  
  
====度量超参数区分了两种 MDS 算法：度量和非度量。如果== ==`metric=True`== ==，则执行度量 MDS。否则，执行非度量 MDS。==
==Now, we apply both types of MDS algorithms to the following non-linear data.====  
  
====现在，我们将两种 MDS 算法应用于以下非线性数据。==
[![](https://miro.medium.com/v2/resize:fit:596/1*xXGssRZSX67mE-4CdQoBWA.png)](https://miro.medium.com/v2/resize:fit:596/1*xXGssRZSX67mE-4CdQoBWA.png)
==(Image by author) （作者提供的图片）==
==import numpy as np====  
  
====from sklearn.manifold import MDS==
==mds = MDS(n_components=1, metric=True)====  
  
====X_mds = mds.fit_transform(X)==
==plt.figure(figsize=[7, 5])====  
  
====plt.scatter(X_mds[:, 0], np.zeros((100,1)), c=y, s=50, cmap='plasma')====  
  
====plt.title('Metric MDS')====  
  
====plt.xlabel('Component 1')==
[![](https://miro.medium.com/v2/resize:fit:613/1*l4_ogPsTl4pISXDSoxg4Eg.png)](https://miro.medium.com/v2/resize:fit:613/1*l4_ogPsTl4pISXDSoxg4Eg.png)
==**Mertic MDS**== ==(Image by author)====  
  
====Mertic MDS（作者提供的图片）==
==import numpy as np====  
  
====from sklearn.manifold import MDS==
==mds = MDS(n_components=1, metric=False)====  
  
====X_mds = mds.fit_transform(X)==
==plt.figure(figsize=[7, 5])====  
  
====plt.scatter(X_mds[:, 0], np.zeros((100,1)), c=y, s=50, cmap='plasma')====  
  
====plt.title('Non-metric MDS')====  
  
====plt.xlabel('Component 1')==
[![](https://miro.medium.com/v2/resize:fit:613/1*glIt7b24giQxY_rpI0CS4A.png)](https://miro.medium.com/v2/resize:fit:613/1*glIt7b24giQxY_rpI0CS4A.png)
==**Non-metric MDS**== ==(Image by author)====  
  
====非度量多维尺度分析（作者提供的图像）==
==Any of the methods does not make data linearly separable after applying MDS. The main difference between the two methods is that metric MDS tries to preserve the distance between similar data points closer in the lower dimensional form while non-metric MDS places more emphasis on maintaining the order of distances.====  
  
====应用 MDS 后，任何方法都不能使数据线性可分。两种方法的主要区别在于，度量 MDS 试图在较低维度形式中保持相似数据点之间的距离更近，而非度量 MDS 更注重保持距离的顺序。==
## ==03. Isometric mapping (Isomap)====  
  
====等距映射（Isomap）==
==The short name for isometric mapping is Isomap.====  
  
====等距映射的简称是 Isomap。==
==Isomap is a non-linear dimensionality reduction technique that uses K nearest neighbor (KNN) approach to calculate pairwise distances of the data point. Here, the distance is curved, not Euclidean as this is a non-linear method.====  
  
====Isomap 是一种非线性降维技术，它使用 K 最近邻（KNN）方法来计算数据点之间的成对距离。在这里，距离是曲线的，不是欧氏距离，因为这是一种非线性方法。==
==In Scikit-learn, Isomap is implemented by using the== ==**Isomap()**== ==class.====  
  
====在 Scikit-learn 中，Isomap 是通过使用 Isomap()类来实现的。==
==The most important hyperparameter is== ==**n_neighbors**== ==which specifies== ==**the**== ==number of neighbors to consider for each point. The== ==**n_components**== ==hyperparameter specifies the number of dimensions that we want to keep during the dimensionality reduction process.====  
  
====最重要的超参数是 n_neighbors，它指定了每个点要考虑的邻居数量。n_components 超参数指定了在降维过程中我们想要保留的维度数量。==
==from sklearn.manifold import Isomap==
==isomap = Isomap(n_neighbors, n_components)====  
  
====isomap_transformed = isomap.fit_transform(X)==
==Behind the scenes, this uses MDS to find the lower dimensional embedding. Therefore, Isomap is considered as an extension of MDS.====  
  
====幕后，这使用 MDS 来找到较低维度的嵌入。因此，Isomap 被认为是 MDS 的扩展。==
==Now, we apply Isomap to the following non-linear data.====  
  
====现在，我们将 Isomap 应用于以下非线性数据。==
[![](https://miro.medium.com/v2/resize:fit:596/1*xXGssRZSX67mE-4CdQoBWA.png)](https://miro.medium.com/v2/resize:fit:596/1*xXGssRZSX67mE-4CdQoBWA.png)
==(Image by author) （作者提供的图片）==
==import matplotlib.pyplot as plt====  
  
====plt.figure(figsize=[7, 5])==
==from sklearn.datasets import make_moons====  
  
====X, y = make_moons(n_samples=100, noise=None,====  
  
====random_state=0)==
==import numpy as np====  
  
====from sklearn.manifold import Isomap==
==isomap = Isomap(n_neighbors=5, n_components=1)====  
  
====X_isomap = isomap.fit_transform(X)==
==plt.figure(figsize=[7, 5])====  
  
====plt.scatter(X_isomap[:, 0], np.zeros((100,1)), c=y, s=50, cmap='plasma')====  
  
====plt.title('First component after applying Isomap')====  
  
====plt.xlabel('Component 1')==
[![](https://miro.medium.com/v2/resize:fit:585/1*6IyqyIY6KeGw3AZoRHFC4A.png)](https://miro.medium.com/v2/resize:fit:585/1*6IyqyIY6KeGw3AZoRHFC4A.png)
==(Image by author) （作者提供的图片）==
==Just like kernel PCA, the two classes are linearly separable after applying Isomap!====  
  
====就像核主成分分析一样，在应用 Isomap 之后，这两个类别是线性可分的！==
## ==04. Locally Linear Embedding (LLE)====  
  
====04. 本地线性嵌入（LLE）==
==LLE performs non-linear dimensionality reduction by preserving distances within local neighborhoods. This is something similar to Isomap.====  
  
====LLE 通过保持局部邻域内的距离来进行非线性降维。这与 Isomap 类似。==
==LLE can be performed with== ==**LocallyLinearEmbedding()**====.====  
  
====可以使用 LocallyLinearEmbedding()函数进行 LLE。==
==from sklearn.manifold import LocallyLinearEmbedding==
==lle = LocallyLinearEmbedding(n_neighbors, n_components, method)====  
  
====lle_transformed = lle.fit_transform(X)==
## ==05. Modified Locally Linear Embedding (MLLE)====  
  
====05. 修改的局部线性嵌入（MLLE）==
==When the number of neighbors (specified in== ==_n_neighbors_====) is greater than the number of input dimensions (specified in== ==_n_components_====) in the== ==**LocallyLinearEmbedding()**== ==class, the regularization problem will arise.====  
  
====当邻居数量（在 n_neighbors 中指定）大于输入维度数量（在 n_components 中指定）时，在 LocallyLinearEmbedding()类中会出现正则化问题。==
==One way to address this problem is to use MLLE which can be performed with the same== ==**LocallyLinearEmbedding()**== ==class by setting== ==`method='modified'`====.====  
  
====解决这个问题的一种方法是使用 MLLE，可以通过设置== ==`method='modified'`== ==来使用相同的 LocallyLinearEmbedding()类进行操作。==
==from sklearn.manifold import LocallyLinearEmbedding==
==mlle = LocallyLinearEmbedding(n_neighbors=N, n_components=C,====  
  
====method='modified')====  
  
====mlle_transformed = mlle.fit_transform(X)==
## ==06. Hessian Eigenmapping (Hessian-based LLE: HLLE)====  
  
====06. Hessian 特征映射（基于 Hessian 的局部线性嵌入：HLLE）==
==Another way to address the regularization problem in LLE is to use Hessian-based LLE which can be performed with the same== ==**LocallyLinearEmbedding()**== ==class by setting== ==`method='hessian'`====.====  
  
====解决 LLE 中正则化问题的另一种方法是使用基于 Hessian 的 LLE，可以通过设置== ==`method='hessian'`== ==来使用相同的 LocallyLinearEmbedding()类进行操作。==
==from sklearn.manifold import LocallyLinearEmbedding==
==hlle = LocallyLinearEmbedding(n_neighbors=N, n_components=C,====  
  
====method='hessian')====  
  
====hlle_transformed = hlle.fit_transform(X)==
## ==07. Spectral Embedding 07. 光谱嵌入==
==Spectral embedding is another non-linear dimensionality reduction technique that performs non-linear dimensionality reduction by preserving both local and global structures in the data.====  
  
====谱嵌入是另一种非线性降维技术，通过保留数据中的局部和全局结构来进行非线性降维。==
==Basically, it forms an affinity matrix by computing a graph of nearest neighbors. So, spectral embedding also uses a nearest-neighbors approach.====  
  
====基本上，它通过计算最近邻的图形来形成亲和力矩阵。因此，谱嵌入也使用了最近邻方法。==
==Spectral embedding can be performed with== ==**SpectralEmbedding()**====.====  
  
====可以使用 SpectralEmbedding()进行谱嵌入。==
==from sklearn.manifold import SpectralEmbedding==
==sp_emb = SpectralEmbedding(n_components, affinity='nearest_neighbors')====  
  
====sp_emb_transformed = sp_emb.fit_transform(X)==
==Now, we apply spectral embedding to the following non-linear data.====  
  
====现在，我们将谱嵌入应用于以下非线性数据。==
[![](https://miro.medium.com/v2/resize:fit:596/1*xXGssRZSX67mE-4CdQoBWA.png)](https://miro.medium.com/v2/resize:fit:596/1*xXGssRZSX67mE-4CdQoBWA.png)
==(Image by author) （作者提供的图片）==
==import matplotlib.pyplot as plt====  
  
====plt.figure(figsize=[7, 5])==
==from sklearn.datasets import make_moons====  
  
====X, y = make_moons(n_samples=100, noise=None,====  
  
====random_state=0)==
==import numpy as np====  
  
====from sklearn.manifold import SpectralEmbedding==
==sp_emb = SpectralEmbedding(n_components=1, affinity='nearest_neighbors')====  
  
====X_sp_emb = sp_emb.fit_transform(X)==
==plt.figure(figsize=[7, 5])====  
  
====plt.scatter(X_sp_emb[:, 0], np.zeros((100,1)), c=y, s=50, cmap='plasma')====  
  
====plt.title('Spectral Embedding')====  
  
====plt.xlabel('Component 1')==
[![](https://miro.medium.com/v2/resize:fit:613/1*K-aAxhJICrn8NylrsM7gTA.png)](https://miro.medium.com/v2/resize:fit:613/1*K-aAxhJICrn8NylrsM7gTA.png)
==(Image by author) （作者提供的图片）==
## ==08. t-Distributed Stochastic Neighbor Embedding (t-SNE)====  
  
====08. t 分布随机邻域嵌入（t-SNE）==
==t-SNE is an NLDR technique that is mostly used for visualizing high-dimensional data. The distances between data points will be preserved in both higher and lower dimensions.====  
  
====t-SNE 是一种主要用于可视化高维数据的非线性降维技术。数据点之间的距离在高维和低维空间中都会被保留。==
==In Python, t-SNE is implemented by using Scikit-learn’s== ==**TSNE()**== ==class.====  
  
====在 Python 中，t-SNE 是通过使用 Scikit-learn 的 TSNE()类来实现的。==
==from sklearn.manifold import TSNE==
==tsne = TSNE(n_components, perplexity, learning_rate='auto', init='pca')====  
  
====tsne_transformed = tsne.fit_transform(X)==
==The== ==**n_components**== ==hyperparameter defines the number of dimensions of the embedded space.== ==**Perplexity**== ==defines the number of nearest neighbors to consider when visualizing data. The default is 30.0. The== ==**init**== ==hyperparameter defines the initialization method. There are two options:== ==_“random”_== ==or== ==_“pca”_====. The default is PCA initialization which is more stable than random initialization and produces the same results across different executions.====  
  
====n_components 超参数定义了嵌入空间的维度数量。Perplexity 定义了在可视化数据时要考虑的最近邻数量。默认值为 30.0。init 超参数定义了初始化方法。有两个选项：“random”或“pca”。默认值为 PCA 初始化，比随机初始化更稳定，并在不同执行中产生相同的结果。==
## ==09. Random Trees Embedding====  
  
====09. 随机树嵌入==
==Random trees embedding performs non-linear dimensionality reduction using an ensemble (collection) of random trees. It can be implemented with== ==**RandomTreesEmbedding()**====.====  
  
====随机树嵌入使用一组随机树进行非线性降维。可以使用 RandomTreesEmbedding()来实现。==
==from sklearn.ensemble import RandomTreesEmbedding==
==rnd_emb = RandomTreesEmbedding(n_estimators=100, max_depth=5)====  
  
====rnd_emb_transformed = rnd_emb.fit_transform(X)==
## ==10. Dictionary Learning 10. 字典学习==
==Dictionary learning does not rely on distance metrics. It is an NLDR method that aims to find a dictionary (a matrix that can be considered as a set of vectors) that performs well on sparse data.====  
  
====字典学习不依赖于距离度量。它是一种非线性降维方法，旨在找到在稀疏数据上表现良好的字典（可以被视为一组向量的矩阵）。==
==To perform dimensionality reduction with dictionary learning, the number of features in the original data should be greater than the number of vectors in the dictionary. In this way, we can represent the original data with a fewer number of vectors. The number of vectors can be controlled using the== ==**n_components**== ==hyperparameter.====  
  
====使用字典学习进行降维时，原始数据的特征数量应大于字典中向量的数量。这样，我们可以用较少的向量表示原始数据。可以使用 n_components 超参数来控制向量的数量。==
==The== ==**DictionaryLearning()**== ==class can be used to implement dictionary learning in Scikit-learn.====  
  
====DictionaryLearning()类可以用于在 Scikit-learn 中实现字典学习。==
==from sklearn.decomposition import DictionaryLearning==
==dict_lr = DictionaryLearning(n_components)====  
  
====dict_lr_transformed = dict_lr.fit_transform(X)==
## ==11. Independent Component Analysis (ICA)====  
  
====独立成分分析（ICA）==
==ICA also does not rely on distance metrics. Although it is an NLDR method, it is not used for reducing the dimensionality of the data, but for separating clear signals from a noisy input which can be an audio clip or any other similar thing.====  
  
====ICA 也不依赖于距离度量。虽然它是一种非线性降维方法，但它不是用于降低数据的维度，而是用于从嘈杂的输入中分离出清晰的信号，这可以是音频剪辑或任何其他类似的东西。==
==The== ==**FastICA**== ==class can be used to implement ICA in Scikit-learn.====  
  
====FastICA 类可以用于在 Scikit-learn 中实现 ICA。==
==from sklearn.decomposition import FastICA==
==ica = FastICA(n_components, whiten='unit-variance')====  
  
====ica_transformed = ica.fit_transform(X)==
==The whitening must be applied using the== ==**whiten**== ==argument to ensure that each recovered data has a unit variance.====  
  
====必须使用 whiten 参数来应用白化，以确保每个恢复的数据具有单位方差。==
## ==12. Autoencoders (AEs) 自动编码器（AEs）==
==The NLDR techniques that we have discussed so far fall under the category of== ==_general machine learning algorithms_====. However, autoencoders are a type of neural network-based NLDR technique that works well with large and non-linear data. When the dataset is small, we should use PCA instead of autoencoders.====  
  
====我们到目前为止讨论的非线性降维（NLDR）技术属于通用的机器学习算法。然而，自编码器是一种基于神经网络的 NLDR 技术，适用于大规模和非线性数据。当数据集较小时，我们应该使用主成分分析（PCA）而不是自编码器。==
==Here is the architecture of a deep autoencoder.====  
  
====这是一个深度自编码器的架构。==
[![](https://miro.medium.com/v2/resize:fit:823/1*VjQ8OccK1HJ1djHPpMDkVw.png)](https://miro.medium.com/v2/resize:fit:823/1*VjQ8OccK1HJ1djHPpMDkVw.png)
==(Image by author) （作者提供的图片）==
==from tensorflow.keras.datasets import mnist==
==(train_images, train_labels), (test_images, test_labels) = mnist.load_data()==
==import numpy as np==
==train_images = np.reshape(train_images, (-1, 784))====  
  
====test_images = np.reshape(test_images, (-1, 784))==
==train_images = train_images.astype('float32') / 255====  
  
====test_images = test_images.astype('float32') / 255==
==from tensorflow.keras import Model, Input====  
  
====from tensorflow.keras.layers import Dense==
==input_dim = 28*28====  
  
====latent_vec_dim = 2==
==input_layer = Input(shape=(input_dim,))==
==enc_layer_1 = Dense(500, activation='sigmoid')(input_layer)====  
  
====enc_layer_2 = Dense(300, activation='sigmoid')(enc_layer_1)====  
  
====enc_layer_3 = Dense(100, activation='sigmoid')(enc_layer_2)====  
  
====enc_layer_4 = Dense(latent_vec_dim, activation='tanh')(enc_layer_3)====  
  
====encoder = enc_layer_4==
==dec_layer_1 = Dense(100, activation='sigmoid')(encoder)====  
  
====dec_layer_2 = Dense(300, activation='sigmoid')(dec_layer_1)====  
  
====dec_layer_3 = Dense(500, activation='sigmoid')(dec_layer_2)====  
  
====dec_layer_4 = Dense(input_dim, activation='sigmoid')(dec_layer_3)====  
  
====decoder = dec_layer_4==
==autoencoder = Model(input_layer, decoder, name="Deep_Autoencoder")==
==latent_model = Model(input_layer, encoder)==
==autoencoder.compile(loss='binary_crossentropy', optimizer='adam')==
==history = autoencoder.fit(train_images, train_images, epochs=70, batch_size=128,====  
  
====shuffle=True, validation_data=(test_images, test_images))==
==import matplotlib.pyplot as plt====  
  
====plt.plot(history.history['loss'], label='Train')====  
  
====plt.plot(history.history['val_loss'], label='Validation')====  
  
====plt.ylabel('Binary Cross Entropy Loss')====  
  
====plt.xlabel('Epoch')====  
  
====plt.title('Autoencoder Reconstruction Loss', pad=13)====  
  
====plt.legend(loc='upper right')==
[![](https://miro.medium.com/v2/resize:fit:496/1*zjhNtsu0b01YmniMfmA4Kg.png)](https://miro.medium.com/v2/resize:fit:496/1*zjhNtsu0b01YmniMfmA4Kg.png)
==(Image by author) （作者提供的图片）==